import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { DeviceDetector, type DeviceInfo } from '@/utils/device';
import { useAppStore, usePermissionStore } from '@/store';
import { DeviceEnum } from '@/enums/DeviceEnum';
import router from '@/router';

export function useDevice() {
  const appStore = useAppStore();
  const permissionStore = usePermissionStore();

  // 响应式设备信息
  const deviceInfo = ref<DeviceInfo>(DeviceDetector.getDeviceInfo());
  
  // 计算属性
  const isMobile = computed(() => appStore.device === DeviceEnum.MOBILE);
  const isTablet = computed(() => DeviceDetector.isTablet());
  const isDesktop = computed(() => appStore.device === DeviceEnum.DESKTOP);
  const isLandscape = computed(() => deviceInfo.value.orientation === 'landscape');
  const isPortrait = computed(() => deviceInfo.value.orientation === 'portrait');

  // 设备切换处理函数
  const handleDeviceChange = async (newDeviceInfo: DeviceInfo) => {
    const oldDeviceType = appStore.device;
    const newDeviceType = newDeviceInfo.type;
    
    // 更新设备信息
    deviceInfo.value = newDeviceInfo;
    
    if (oldDeviceType !== newDeviceType) {
      console.log(`Device switching: ${oldDeviceType} → ${newDeviceType}`);
      
      // 更新应用状态
      appStore.toggleDevice(newDeviceType);
      
      // 触发设备变化事件（后续步骤中会用到）
      await handleRouteRegeneration(newDeviceType);
    }
  };

  // 路由重新生成处理
  const handleRouteRegeneration = async (deviceType: DeviceEnum) => {
    // 如果路由已加载且用户已登录，重新生成路由
    if (permissionStore.isRoutesLoaded) {
      try {
        console.log('Regenerating routes for device change...');
        await permissionStore.regenerateRoutes();

        // 检查当前路由是否需要重定向
        await checkAndRedirectRoute(deviceType);

        console.log('Device change handled successfully');
      } catch (error) {
        console.error('Failed to handle device change:', error);
      }
    }
  };

  // 检查并重定向路由
  const checkAndRedirectRoute = async (deviceType: DeviceEnum) => {
    const currentRoute = router.currentRoute.value;
    
    // 检查当前路由是否与新设备兼容
    if (!isRouteCompatible(currentRoute, deviceType)) {
      const adaptedPath = findAdaptedRoute(currentRoute.path, deviceType);
      
      if (adaptedPath && adaptedPath !== currentRoute.path) {
        console.log(`Redirecting from ${currentRoute.path} to ${adaptedPath}`);
        await router.replace(adaptedPath);
      }
    }
  };

  // 路由兼容性检查
  const isRouteCompatible = (route: any, deviceType: DeviceEnum): boolean => {
    const platform = route.meta?.platform;
    
    if (!platform || platform === 'both') {
      return true;
    }
    
    return (deviceType === DeviceEnum.MOBILE && platform === 'mobile') ||
           (deviceType === DeviceEnum.DESKTOP && platform === 'desktop');
  };

  // 查找适配的路由
  const findAdaptedRoute = (currentPath: string, deviceType: DeviceEnum): string | null => {
    // 简单的路径映射逻辑
    const routeMap: Record<string, Record<string, string>> = {
      '/dashboard': {
        mobile: '/mobile/dashboard',
        desktop: '/dashboard'
      },
      '/mobile/dashboard': {
        mobile: '/mobile/dashboard',
        desktop: '/dashboard'
      },
      // 可以根据需要添加更多映射
    };
    
    const targetPlatform = deviceType === DeviceEnum.MOBILE ? 'mobile' : 'desktop';
    return routeMap[currentPath]?.[targetPlatform] || null;
  };

  // 获取当前设备的布局配置
  const getLayoutConfig = computed(() => ({
    sidebarWidth: isMobile.value ? 0 : 200,
    headerHeight: isMobile.value ? 44 : 60,
    tabbarHeight: isMobile.value ? 50 : 0,
    contentPadding: isMobile.value ? 12 : 24,
    showSidebar: !isMobile.value,
    showTabbar: isMobile.value,
    showNavbar: isMobile.value
  }));

  // 获取当前设备的主题配置
  const getThemeConfig = computed(() => ({
    fontSize: {
      small: isMobile.value ? '12px' : '12px',
      normal: isMobile.value ? '14px' : '14px',
      large: isMobile.value ? '16px' : '16px',
      title: isMobile.value ? '18px' : '20px'
    },
    spacing: {
      xs: '4px',
      sm: isMobile.value ? '8px' : '8px',
      md: isMobile.value ? '12px' : '16px',
      lg: isMobile.value ? '16px' : '24px',
      xl: isMobile.value ? '20px' : '32px'
    },
    borderRadius: isMobile.value ? '6px' : '4px',
    primaryColor: isMobile.value ? '#1989fa' : '#409eff'
  }));

  let cleanup: (() => void) | null = null;

  onMounted(() => {
    // 监听设备变化
    cleanup = DeviceDetector.onDeviceChange(handleDeviceChange);
    
    // 初始化设备状态
    const initialDevice = DeviceDetector.getComprehensiveDeviceType();
    if (appStore.device !== initialDevice) {
      appStore.toggleDevice(initialDevice);
    }
  });

  onUnmounted(() => {
    cleanup?.();
  });

  return {
    // 响应式状态
    deviceInfo: readonly(deviceInfo),
    
    // 计算属性
    isMobile,
    isTablet,
    isDesktop,
    isLandscape,
    isPortrait,
    
    // 配置
    layoutConfig: getLayoutConfig,
    themeConfig: getThemeConfig,
    
    // 方法
    handleDeviceChange,
    checkAndRedirectRoute,
    isRouteCompatible,
    findAdaptedRoute
  };
}
