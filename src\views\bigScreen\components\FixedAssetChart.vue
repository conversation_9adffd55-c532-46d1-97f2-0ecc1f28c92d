<template>
  <div class="chart-panel">
    <div class="panel-header">
      <div class="header-content">
        <div class="title-wrapper">
          <div class="title-icon">🏗️</div>
          <h3 class="title">固定资产分类统计</h3>
        </div>
        <div class="header-actions">
          <div class="view-toggle" @click="toggleView">
            <svg v-if="viewType === 'pie'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="3" y="3" width="7" height="7" stroke="currentColor" stroke-width="2"/>
              <rect x="14" y="3" width="7" height="7" stroke="currentColor" stroke-width="2"/>
              <rect x="14" y="14" width="7" height="7" stroke="currentColor" stroke-width="2"/>
              <rect x="3" y="14" width="7" height="7" stroke="currentColor" stroke-width="2"/>
            </svg>
            <svg v-else viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
              <path d="M12 6V12L16 14" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </div>
        </div>
      </div>
      <div class="header-decoration"></div>
    </div>
    
    <div class="panel-content">
      <div ref="chartContainer" class="chart-container"></div>
      
      <!-- 统计信息 -->
      <div class="stats-info">
        <div class="total-info">
          <div class="total-label">总计</div>
          <div class="total-value">{{ totalValue.toLocaleString() }}</div>
          <div class="total-unit">万元</div>
        </div>
        <div class="growth-info">
          <div class="growth-label">同比增长</div>
          <div class="growth-value" :class="{ positive: growthRate > 0, negative: growthRate < 0 }">
            {{ growthRate > 0 ? '+' : '' }}{{ growthRate }}%
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, computed } from 'vue'
import * as echarts from 'echarts'

const chartContainer = ref(null)
const viewType = ref('pie') // 'pie' 或 'bar'
let chartInstance = null

// 图表数据
const chartData = ref([
  { value: 14955, name: '房屋及构筑物', color: '#ffb800' },
  { value: 6844, name: '设备', color: '#00d4ff' },
  { value: 5954, name: '文物和陈列品', color: '#00ff88' },
  { value: 1154, name: '家具用具', color: '#8b5cf6' },
  { value: 796, name: '图书档案', color: '#ff6b6b' },
  { value: 298, name: '其他', color: '#06d6a0' }
])

const growthRate = ref(12.5)

// 计算总值
const totalValue = computed(() => {
  return chartData.value.reduce((sum, item) => sum + item.value, 0)
})

// 初始化饼图
const initPieChart = () => {
  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0, 20, 40, 0.9)',
      borderColor: '#00d4ff',
      borderWidth: 1,
      textStyle: { 
        color: '#fff',
        fontSize: 12
      },
      formatter: function(params) {
        return `${params.seriesName}<br/>
                ${params.name}: ${params.value.toLocaleString()}万元<br/>
                占比: ${params.percent}%`
      }
    },
    series: [
      {
        name: '固定资产',
        type: 'pie',
        radius: '70%',
        center: ['50%', '50%'],
        data: chartData.value.map(item => ({
          ...item,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [
              { offset: 0, color: item.color },
              { offset: 1, color: item.color + '60' }
            ]),
            borderRadius: 5,
            borderColor: 'rgba(0, 20, 40, 0.8)',
            borderWidth: 2
          }
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 20,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          },
          label: {
            show: true,
            fontSize: 14,
            fontWeight: 'bold',
            color: '#fff'
          }
        },
        label: {
          show: true,
          position: 'outside',
          color: '#fff',
          fontSize: 10,
          formatter: '{b}\n{d}%'
        },
        labelLine: {
          show: true,
          lineStyle: { 
            color: 'rgba(255, 255, 255, 0.5)',
            width: 1
          }
        },
        animationType: 'scale',
        animationEasing: 'elasticOut'
      }
    ]
  }
  
  chartInstance.setOption(option)
}

// 初始化柱状图
const initBarChart = () => {
  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 20, 40, 0.9)',
      borderColor: '#00d4ff',
      borderWidth: 1,
      textStyle: { 
        color: '#fff',
        fontSize: 12
      },
      formatter: function(params) {
        return `${params[0].name}<br/>
                ${params[0].value.toLocaleString()}万元`
      }
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '10%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: chartData.value.map(item => item.name),
      axisLine: { 
        lineStyle: { color: 'rgba(255, 255, 255, 0.3)' }
      },
      axisLabel: { 
        color: '#fff', 
        fontSize: 10,
        interval: 0,
        rotate: 45
      },
      splitLine: { show: false }
    },
    yAxis: {
      type: 'value',
      axisLine: { 
        lineStyle: { color: 'rgba(255, 255, 255, 0.3)' }
      },
      axisLabel: { 
        color: '#fff', 
        fontSize: 10,
        formatter: '{value}'
      },
      splitLine: { 
        lineStyle: { 
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        type: 'bar',
        data: chartData.value.map((item, index) => ({
          value: item.value,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
              { offset: 0, color: item.color + '40' },
              { offset: 1, color: item.color }
            ]),
            borderRadius: [4, 4, 0, 0]
          }
        })),
        barWidth: '60%',
        animationDelay: function (idx) {
          return idx * 100
        }
      }
    ]
  }
  
  chartInstance.setOption(option)
}

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return
  
  if (chartInstance) {
    chartInstance.dispose()
  }
  
  chartInstance = echarts.init(chartContainer.value)
  
  if (viewType.value === 'pie') {
    initPieChart()
  } else {
    initBarChart()
  }
}

// 切换视图
const toggleView = () => {
  viewType.value = viewType.value === 'pie' ? 'bar' : 'pie'
  initChart()
}

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

onMounted(async () => {
  await nextTick()
  initChart()
  
  window.addEventListener('dashboard-resize', handleResize)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('dashboard-resize', handleResize)
})
</script>

<style lang="scss" scoped>
.chart-panel {
  background: linear-gradient(135deg, 
    rgba(255, 184, 0, 0.05) 0%, 
    rgba(0, 212, 255, 0.03) 100%
  );
  border: 1px solid rgba(255, 184, 0, 0.2);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  
  &:hover {
    border-color: rgba(255, 184, 0, 0.4);
    box-shadow: 0 12px 40px rgba(255, 184, 0, 0.2);
  }
  
  .panel-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid rgba(255, 184, 0, 0.1);
    position: relative;
    
    .header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .title-wrapper {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .title-icon {
          font-size: 20px;
          filter: drop-shadow(0 0 8px rgba(255, 184, 0, 0.6));
        }
        
        .title {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #fff;
          text-shadow: 0 0 10px rgba(255, 184, 0, 0.3);
        }
      }
      
      .header-actions {
        .view-toggle {
          width: 32px;
          height: 32px;
          border-radius: 8px;
          background: rgba(255, 184, 0, 0.1);
          border: 1px solid rgba(255, 184, 0, 0.2);
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.3s ease;
          
          svg {
            width: 16px;
            height: 16px;
            color: #ffb800;
          }
          
          &:hover {
            background: rgba(255, 184, 0, 0.2);
            transform: scale(1.1);
          }
        }
      }
    }
    
    .header-decoration {
      position: absolute;
      bottom: 0;
      left: 24px;
      width: 60px;
      height: 2px;
      background: linear-gradient(90deg, #ffb800, #ff8c00);
      border-radius: 1px;
    }
  }
  
  .panel-content {
    padding: 20px 24px 24px;
    
    .chart-container {
      width: 100%;
      height: 200px;
      margin-bottom: 20px;
    }
    
    .stats-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      background: rgba(255, 184, 0, 0.05);
      border-radius: 12px;
      border: 1px solid rgba(255, 184, 0, 0.1);
      
      .total-info {
        text-align: left;
        
        .total-label {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.7);
          margin-bottom: 4px;
        }
        
        .total-value {
          font-size: 20px;
          font-weight: 700;
          color: #ffb800;
          margin-bottom: 2px;
        }
        
        .total-unit {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.6);
        }
      }
      
      .growth-info {
        text-align: right;
        
        .growth-label {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.7);
          margin-bottom: 4px;
        }
        
        .growth-value {
          font-size: 16px;
          font-weight: 600;
          
          &.positive {
            color: #00ff88;
          }
          
          &.negative {
            color: #ff6b6b;
          }
        }
      }
    }
  }
}
</style>
