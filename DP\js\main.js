﻿//var serviceUrl = "http://localhost:8569/WebService/WebService.asmx/";//根据时间地址加上虚拟目录
var serviceUrl = "https://zcgl.caa.edu.cn/ZC_CAA2/WebService/WebService.asmx/";//根据时间地址加上虚拟目录


function setYears() {
    var date = new Date();
    var year = date.getFullYear();
    var num = 0;
    html = ""; 
    while (year >= 2023 && num<5) {
        html += '<option value="' + year + '" ' + (num == 0 ? 'selected' : "") + '>' + year + '</option>';
        year--;
        num++;
    }
    $("#year").html(html);
}
$(function () {
    try {
        setYears();
    } catch { }
})