<template>
  <div class="adaptive-card">
    <!-- PC端卡片 -->
    <el-card
      v-if="!isMobile"
      v-bind="cardProps"
      @click="handleClick"
    >
      <template v-if="$slots.header" #header>
        <slot name="header" />
      </template>
      
      <slot />
      
      <template v-if="$slots.footer" #footer>
        <slot name="footer" />
      </template>
    </el-card>

    <!-- 移动端卡片 -->
    <van-card
      v-else
      v-bind="cardProps"
      @click="handleClick"
    >
      <template v-if="$slots.header" #header>
        <slot name="header" />
      </template>
      
      <template #default>
        <slot />
      </template>
      
      <template v-if="$slots.footer" #footer>
        <slot name="footer" />
      </template>
    </van-card>
  </div>
</template>

<script setup lang="ts">
import { useDevice } from '@/composables/useDevice';

interface AdaptiveCardProps {
  title?: string;
  subtitle?: string;
  shadow?: 'always' | 'hover' | 'never';
  bordered?: boolean;
  clickable?: boolean;
  mobileProps?: Record<string, any>;
  desktopProps?: Record<string, any>;
}

const props = withDefaults(defineProps<AdaptiveCardProps>(), {
  shadow: 'hover',
  bordered: true,
  clickable: false
});

const emit = defineEmits<{
  click: [event: Event];
}>();

const { isMobile } = useDevice();

// 卡片属性适配
const cardProps = computed(() => {
  const baseProps = {
    title: props.title,
    subtitle: props.subtitle
  };

  // 平台特定属性
  const platformProps = isMobile.value ? props.mobileProps : props.desktopProps;

  if (isMobile.value) {
    // Vant Card 属性
    return {
      ...baseProps,
      ...platformProps,
      // Vant 特有属性
      desc: props.subtitle,
      clickable: props.clickable
    };
  } else {
    // Element Plus Card 属性
    return {
      ...baseProps,
      ...platformProps,
      // Element Plus 特有属性
      shadow: props.shadow,
      'body-style': props.clickable ? { cursor: 'pointer' } : undefined
    };
  }
});

const handleClick = (event: Event) => {
  if (props.clickable) {
    emit('click', event);
  }
};
</script>

<style lang="scss" scoped>
.adaptive-card {
  width: 100%;
  
  // PC端卡片样式
  .adaptive-layout--desktop & {
    .el-card {
      border-radius: 8px;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
    }
  }
  
  // 移动端卡片样式
  .adaptive-layout--mobile & {
    .van-card {
      margin: 8px;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      
      &:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
      }
    }
  }
}
</style>
