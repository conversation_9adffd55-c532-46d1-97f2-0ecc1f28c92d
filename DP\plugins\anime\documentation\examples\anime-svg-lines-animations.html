<!DOCTYPE html>
<html>
<head>
  <title>SVG line drawing | anime.js</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <meta property="og:title" content="anime.js">
  <meta property="og:url" content="https://animejs.com">
  <meta property="og:description" content="Javascript Animation Engine">
  <meta property="og:image" content="https://animejs.com/documentation/assets/img/icons/og.png">
  <meta name="twitter:card" content="summary">
  <meta name="twitter:title" content="anime.js">
  <meta name="twitter:site" content="@juliangarnier">
  <meta name="twitter:description" content="Javascript Animation Engine">
  <meta name="twitter:image" content="https://animejs.com/documentation/assets/img/icons/twitter.png">
  <link rel="apple-touch-icon-precomposed" href="../assets/img/social-media-image.png">
  <link rel="icon" type="image/png" href="../assets/img/favicon.png">
  <link href="../assets/css/documentation.css" rel="stylesheet">
  <style>
    body {
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      overflow: hidden;
      width: 100%;
      height: 100%;
    }
    .anim {
      position: relative;
      width: 50%;
    }
    .anim svg {
      position: relative;
      width: 100%;
      border: 1px solid red;
    }
    .anim path {
      stroke-width: 2;
    }
    .item {
      position: absolute;
      left: 0;
      top: 0;
      width: 30px;
      height: 30px;
      margin-left: -15px;
      margin-top: -15px;
      background-color: white;
    }
  </style>
</head>
<body>
  <div class="anim">
    <div class="item"></div>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 600 400">
      <g fill="none" fill-rule="evenodd" stroke-width="2">
        <line class="line-1" stroke="#F96F82" x1="51.5" x2="149.5" y1="51.5" y2="149.5" stroke-linecap="square"/>
        <line class="line-2s" stroke="#F96F82" x1="149.5" x2="51.5" y1="51.5" y2="149.5" stroke-linecap="square"/>
        <circle cx="300" cy="100" r="50" stroke="#FED28B"/>
        <polygon stroke="#D1FA9E" points="500 130.381 464.772 149 471.5 109.563 443 81.634 482.386 75.881 500 40 517.614 75.881 557 81.634 528.5 109.563 535.228 149"/>
        <polyline stroke="#7BE6D6" points="63.053 343 43 281.815 95.5 244 95.5 244 148 281.815 127.947 343"/>
        <path class="path" stroke="#4E7EFC" d="M250 300a50 50 0 0 1 100 0v25h-25v-25a25 25 0 1 0-25 25v25a50 50 0 0 1-50-50z"/>
        <rect width="98" height="98" x="451" y="251" stroke="#C987FE" rx="25"/>
      </g>
    </svg>
  </div>
  <!-- <script type="module" src="../../src/index.js"></script> -->
  <script type="module">
    import anime from "../../src/index.js";
    anime({
      targets: ['line', 'circle', 'polygon', 'polyline', 'path', 'rect'],
      strokeDashoffset: [anime.setDashoffset, 0],
      easing: 'easeInOutSine',
      duration: 1500,
      direction: 'alternate',
      loop: true
    });
    var pathEl = anime.path('.path');
    anime({
      targets: '.item',
      translateX: pathEl('x'),
      translateY: pathEl('y'),
      rotate: pathEl('angle'),
      easing: 'easeInOutSine',
      duration: 1500,
      direction: 'alternate',
      loop: true
    });
  </script>
  <!-- <script src="../assets/js/vendors/stats.min.js"></script> -->
</body>
</html>
