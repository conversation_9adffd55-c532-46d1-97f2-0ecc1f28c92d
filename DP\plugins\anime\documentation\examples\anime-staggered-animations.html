<!DOCTYPE html>
<html>
<head>
  <title>Staggered loops | anime.js</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <meta property="og:title" content="anime.js">
  <meta property="og:url" content="https://animejs.com">
  <meta property="og:description" content="Javascript Animation Engine">
  <meta property="og:image" content="https://animejs.com/documentation/assets/img/icons/og.png">
  <meta name="twitter:card" content="summary">
  <meta name="twitter:title" content="anime.js">
  <meta name="twitter:site" content="@juliangarnier">
  <meta name="twitter:description" content="Javascript Animation Engine">
  <meta name="twitter:image" content="https://animejs.com/documentation/assets/img/icons/twitter.png">
  <link rel="apple-touch-icon-precomposed" href="../assets/img/social-media-image.png">
  <link rel="icon" type="image/png" href="../assets/img/favicon.png">
  <link href="../assets/css/documentation.css" rel="stylesheet">
  <script src="../../lib/anime.min.js"></script>
  <!-- <script src="../assets/js/anime/anime.2.2.0.js"></script> -->
  <!-- <script src="../assets/js/anime/anime.1.3.js"></script> -->
  <style>
    body {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100vh;
    }

    .wrapper {
      position: relative;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      width: 1px;
      height: 100vh;
    }

    .el {
      position: absolute;
      opacity: 1;
      width: 2px;
      height: 24vh;
      margin-top: -12vh;
      transform-origin: 50% 100%;
      background: white;
    }
  </style>
</head>
<body>
  <div class="wrapper"></div>
  <script>

    const wrapperEl = document.querySelector('.wrapper');
    const numberOfEls = 180;
    const duration = 6000;
    const delay = duration / numberOfEls;

    let tl = anime.timeline({
      duration: delay,
      complete: function() { tl.restart(); }
    });

    function createEl(i) {
      let el = document.createElement('div');
      const rotate = (360 / numberOfEls) * i;
      const translateY = -50;
      const hue = Math.round(360 / numberOfEls * i);
      el.classList.add('el');
      el.style.backgroundColor = 'hsl(' + hue + ', 40%, 60%)';
      el.style.transform = 'rotate(' + rotate + 'deg) translateY(' + translateY + '%)';
      tl.add({
        begin: function() {
          anime({
            targets: el,
            backgroundColor: ['hsl(' + hue + ', 40%, 60%)', 'hsl(' + hue + ', 60%, 80%)'],
            rotate: [rotate + 'deg', rotate + 10 +'deg'],
            translateY: [translateY + '%', translateY + 10 + '%'],
            scale: [1, 1.25],
            easing: 'easeInOutSine',
            direction: 'alternate',
            duration: duration * .1
          });
        }
      });
      wrapperEl.appendChild(el);
    };

    for (let i = 0; i < numberOfEls; i++) createEl(i);

  </script>
  <script src="../assets/js/vendors/stats.min.js"></script>
</body>
</html>
