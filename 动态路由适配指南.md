# 动态路由适配指南

## 📋 概述

基于您现有的数据库动态路由系统，本指南提供了完整的移动端和PC端路由适配方案，确保在不破坏现有功能的前提下，实现智能的设备适配。

## 🔍 现状分析

### 当前动态路由架构
```
数据库菜单表 → MenuAPI.getRoutes() → transformRoutes() → 动态添加路由
```

### 现有优势
- ✅ 完善的权限控制系统
- ✅ 动态路由生成机制
- ✅ 路由守卫和权限验证
- ✅ 菜单和路由的统一管理

### 需要增强的部分
- ❌ 缺少设备类型感知
- ❌ 组件路径不支持平台区分
- ❌ 路由元信息不包含设备适配信息

## 🚀 实施方案

### 第一步：数据库表结构扩展

#### 1.1 菜单表字段扩展

```sql
-- 在现有 sys_menu 表中添加字段
ALTER TABLE sys_menu ADD COLUMN platform VARCHAR(20) DEFAULT 'both' 
  COMMENT '平台类型: mobile-仅移动端, desktop-仅PC端, both-两端通用';

ALTER TABLE sys_menu ADD COLUMN adaptive TINYINT(1) DEFAULT 1 
  COMMENT '是否自适应: 0-使用平台特定组件, 1-使用自适应组件';

ALTER TABLE sys_menu ADD COLUMN mobile_component VARCHAR(255) 
  COMMENT '移动端专用组件路径';

ALTER TABLE sys_menu ADD COLUMN desktop_component VARCHAR(255) 
  COMMENT 'PC端专用组件路径';

ALTER TABLE sys_menu ADD COLUMN mobile_icon VARCHAR(100) 
  COMMENT '移动端图标';

ALTER TABLE sys_menu ADD COLUMN desktop_icon VARCHAR(100) 
  COMMENT 'PC端图标';

ALTER TABLE sys_menu ADD COLUMN mobile_meta JSON 
  COMMENT '移动端专用元信息';

ALTER TABLE sys_menu ADD COLUMN desktop_meta JSON 
  COMMENT 'PC端专用元信息';
```

#### 1.2 示例数据

```sql
-- 示例：用户管理菜单配置
INSERT INTO sys_menu (
  name, path, component, platform, adaptive, 
  mobile_component, desktop_component,
  mobile_icon, desktop_icon
) VALUES (
  '用户管理', '/system/user', 'system/user/index', 'both', 1,
  'mobile/system/user/index', 'desktop/system/user/index',
  'user-o', 'user'
);

-- 示例：仅移动端菜单
INSERT INTO sys_menu (
  name, path, component, platform, adaptive
) VALUES (
  '扫一扫', '/mobile/scan', 'mobile/scan/index', 'mobile', 0
);

-- 示例：仅PC端菜单
INSERT INTO sys_menu (
  name, path, component, platform, adaptive
) VALUES (
  '数据分析', '/desktop/analytics', 'desktop/analytics/index', 'desktop', 0
);
```

### 第二步：后端API增强

#### 2.1 路由接口扩展

```java
// 后端控制器示例 (Java Spring Boot)
@RestController
@RequestMapping("/api/v1/menus")
public class MenuController {
    
    @GetMapping("/routes")
    public Result<List<RouteVO>> getRoutes(
        @RequestParam(required = false) String platform,
        HttpServletRequest request
    ) {
        // 从请求头或参数获取设备类型
        String deviceType = getDeviceType(request, platform);
        
        // 获取用户权限路由，并根据设备类型过滤
        List<RouteVO> routes = menuService.getUserRoutes(getCurrentUserId(), deviceType);
        
        return Result.success(routes);
    }
    
    @GetMapping("/adaptive-routes")
    public Result<List<RouteVO>> getAdaptiveRoutes() {
        // 获取所有自适应路由配置
        List<RouteVO> routes = menuService.getAdaptiveRoutes(getCurrentUserId());
        return Result.success(routes);
    }
    
    private String getDeviceType(HttpServletRequest request, String platform) {
        if (platform != null) {
            return platform;
        }
        
        // 从User-Agent判断设备类型
        String userAgent = request.getHeader("User-Agent");
        if (userAgent != null && userAgent.toLowerCase().contains("mobile")) {
            return "mobile";
        }
        
        return "desktop";
    }
}
```

#### 2.2 服务层实现

```java
@Service
public class MenuService {
    
    public List<RouteVO> getUserRoutes(Long userId, String deviceType) {
        // 获取用户权限菜单
        List<Menu> menus = getMenusByUserId(userId);
        
        // 根据设备类型过滤菜单
        List<Menu> filteredMenus = menus.stream()
            .filter(menu -> isMenuCompatible(menu, deviceType))
            .collect(Collectors.toList());
        
        // 转换为路由VO
        return convertToRouteVO(filteredMenus, deviceType);
    }
    
    private boolean isMenuCompatible(Menu menu, String deviceType) {
        String platform = menu.getPlatform();
        return "both".equals(platform) || deviceType.equals(platform);
    }
    
    private List<RouteVO> convertToRouteVO(List<Menu> menus, String deviceType) {
        return menus.stream().map(menu -> {
            RouteVO route = new RouteVO();
            route.setPath(menu.getPath());
            
            // 根据设备类型选择组件
            String component = getComponentByDevice(menu, deviceType);
            route.setComponent(component);
            
            // 设置元信息
            RouteMetaVO meta = new RouteMetaVO();
            meta.setTitle(menu.getName());
            meta.setPlatform(deviceType);
            meta.setAdaptive(menu.getAdaptive());
            
            // 根据设备类型选择图标
            String icon = "mobile".equals(deviceType) 
                ? menu.getMobileIcon() 
                : menu.getDesktopIcon();
            meta.setIcon(icon != null ? icon : menu.getIcon());
            
            route.setMeta(meta);
            
            return route;
        }).collect(Collectors.toList());
    }
    
    private String getComponentByDevice(Menu menu, String deviceType) {
        if (menu.getAdaptive()) {
            // 自适应组件，返回原始路径
            return menu.getComponent();
        }
        
        // 平台特定组件
        if ("mobile".equals(deviceType) && menu.getMobileComponent() != null) {
            return menu.getMobileComponent();
        }
        
        if ("desktop".equals(deviceType) && menu.getDesktopComponent() != null) {
            return menu.getDesktopComponent();
        }
        
        return menu.getComponent();
    }
}
```

### 第三步：前端路由转换增强

#### 3.1 更新权限存储模块

```typescript
// src/store/modules/permission.ts (完整增强版)
import type { RouteRecordRaw } from "vue-router";
import { constantRoutes } from "@/router";
import { store } from "@/store";
import router from "@/router";
import { useAppStore } from "@/store";
import { DeviceEnum } from "@/enums/DeviceEnum";

import MenuAPI, { type RouteVO } from "@/api/system/menu";

const modules = import.meta.glob("../../views/**/**.vue");
const Layout = () => import("@/layout/index.vue");
const MobileLayout = () => import("@/layout/MobileLayout.vue");
const AdaptiveLayout = () => import("@/layout/AdaptiveLayout.vue");

export const usePermissionStore = defineStore("permission", () => {
  const routes = ref<RouteRecordRaw[]>([]);
  const mixLeftMenus = ref<RouteRecordRaw[]>([]);
  const isRoutesLoaded = ref(false);

  /**
   * 生成动态路由 - 支持设备适配
   */
  function generateRoutes() {
    return new Promise<RouteRecordRaw[]>((resolve, reject) => {
      const appStore = useAppStore();
      const platform = appStore.device === DeviceEnum.MOBILE ? 'mobile' : 'desktop';
      
      MenuAPI.getRoutes(platform)
        .then((data) => {
          const dynamicRoutes = transformRoutes(data);
          routes.value = constantRoutes.concat(dynamicRoutes);
          isRoutesLoaded.value = true;
          resolve(dynamicRoutes);
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  /**
   * 重新生成路由 - 设备切换时调用
   */
  function regenerateRoutes() {
    // 清除现有动态路由
    resetRouter();
    
    // 重新生成路由
    return generateRoutes().then((dynamicRoutes) => {
      dynamicRoutes.forEach((route: RouteRecordRaw) => router.addRoute(route));
      return dynamicRoutes;
    });
  }

  const setMixLeftMenus = (topMenuPath: string) => {
    const matchedItem = routes.value.find((item) => item.path === topMenuPath);
    if (matchedItem && matchedItem.children) {
      mixLeftMenus.value = matchedItem.children;
    }
  };

  const resetRouter = () => {
    routes.value.forEach((route) => {
      if (route.name && !constantRoutes.find((r) => r.name === route.name)) {
        router.removeRoute(route.name);
      }
    });

    routes.value = [];
    mixLeftMenus.value = [];
    isRoutesLoaded.value = false;
  };

  return {
    routes,
    generateRoutes,
    regenerateRoutes, // 新增
    mixLeftMenus,
    setMixLeftMenus,
    isRoutesLoaded,
    resetRouter,
  };
});

/**
 * 转换路由数据为组件 - 增强版
 */
const transformRoutes = (routes: RouteVO[]) => {
  const asyncRoutes: RouteRecordRaw[] = [];
  const appStore = useAppStore();
  const isMobile = appStore.device === DeviceEnum.MOBILE;

  routes.forEach((route) => {
    const tmpRoute = { ...route } as RouteRecordRaw;

    // 处理布局组件
    if (tmpRoute.component?.toString() === "Layout") {
      tmpRoute.component = getLayoutComponent(tmpRoute.meta, isMobile);
    } else {
      // 处理页面组件
      const componentPath = getComponentPath(tmpRoute.component as string, tmpRoute.meta, isMobile);
      const component = modules[`../../views/${componentPath}.vue`];
      
      if (component) {
        tmpRoute.component = component;
      } else {
        console.warn(`Component not found: ${componentPath}`);
        tmpRoute.component = modules["../../views/error-page/404.vue"];
      }
    }

    // 增强路由元信息
    tmpRoute.meta = {
      ...tmpRoute.meta,
      platform: isMobile ? 'mobile' : 'desktop',
      deviceType: isMobile ? DeviceEnum.MOBILE : DeviceEnum.DESKTOP
    };

    if (tmpRoute.children) {
      tmpRoute.children = transformRoutes(route.children);
    }

    asyncRoutes.push(tmpRoute);
  });

  return asyncRoutes;
};

/**
 * 获取布局组件
 */
function getLayoutComponent(meta: any, isMobile: boolean) {
  // 检查是否有平台特定配置
  if (meta?.mobileOnly && isMobile) {
    return MobileLayout;
  }
  
  if (meta?.desktopOnly && !isMobile) {
    return Layout;
  }
  
  // 检查是否为自适应布局
  if (meta?.adaptive !== false) {
    return AdaptiveLayout;
  }
  
  // 默认根据设备类型选择
  return isMobile ? MobileLayout : Layout;
}

/**
 * 获取组件路径
 */
function getComponentPath(originalPath: string, meta: any, isMobile: boolean): string {
  // 如果是自适应组件，直接返回原路径
  if (meta?.adaptive === true) {
    return originalPath;
  }
  
  // 平台特定组件路径
  const platformPath = isMobile 
    ? meta?.mobileComponent || `mobile/${originalPath}`
    : meta?.desktopComponent || `desktop/${originalPath}`;
  
  // 检查平台特定组件是否存在
  if (modules[`../../views/${platformPath}.vue`]) {
    return platformPath;
  }
  
  // 检查共享组件
  const sharedPath = `shared/${originalPath}`;
  if (modules[`../../views/${sharedPath}.vue`]) {
    return sharedPath;
  }
  
  // 返回原始路径
  return originalPath;
}

// 监听设备变化，重新生成路由
export function setupRouteDeviceListener() {
  const appStore = useAppStore();
  const permissionStore = usePermissionStore();
  
  watch(
    () => appStore.device,
    async (newDevice, oldDevice) => {
      if (oldDevice && newDevice !== oldDevice && permissionStore.isRoutesLoaded) {
        console.log(`Device changed from ${oldDevice} to ${newDevice}, regenerating routes...`);
        
        try {
          await permissionStore.regenerateRoutes();
          console.log('Routes regenerated successfully');
        } catch (error) {
          console.error('Failed to regenerate routes:', error);
        }
      }
    }
  );
}
```

### 第四步：设备切换处理

#### 4.1 设备状态监听

```typescript
// src/composables/useDevice.ts (增强版)
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { DeviceDetector } from '@/utils/device';
import { useAppStore } from '@/store';
import { usePermissionStore } from '@/store';

export function useDevice() {
  const appStore = useAppStore();
  const permissionStore = usePermissionStore();
  
  const isMobile = computed(() => appStore.device === DeviceEnum.MOBILE);
  const isTablet = computed(() => DeviceDetector.isTablet());
  const isDesktop = computed(() => appStore.device === DeviceEnum.DESKTOP);

  // 设备切换处理
  const handleDeviceChange = async (newDeviceType: DeviceEnum) => {
    const oldDeviceType = appStore.device;
    
    if (oldDeviceType !== newDeviceType) {
      console.log(`Device switching: ${oldDeviceType} → ${newDeviceType}`);
      
      // 更新设备状态
      appStore.toggleDevice(newDeviceType);
      
      // 如果路由已加载，重新生成路由
      if (permissionStore.isRoutesLoaded) {
        try {
          await permissionStore.regenerateRoutes();
          
          // 检查当前路由是否兼容新设备
          const currentRoute = router.currentRoute.value;
          if (!isRouteCompatible(currentRoute, newDeviceType)) {
            // 重定向到兼容的路由
            const adaptedPath = findAdaptedRoute(currentRoute.path, newDeviceType);
            if (adaptedPath) {
              router.replace(adaptedPath);
            }
          }
        } catch (error) {
          console.error('Failed to handle device change:', error);
        }
      }
    }
  };

  let cleanup: (() => void) | null = null;

  onMounted(() => {
    cleanup = DeviceDetector.onDeviceChange(handleDeviceChange);
  });

  onUnmounted(() => {
    cleanup?.();
  });

  return {
    isMobile,
    isTablet,
    isDesktop,
    handleDeviceChange
  };
}

/**
 * 检查路由是否与设备兼容
 */
function isRouteCompatible(route: any, deviceType: DeviceEnum): boolean {
  const platform = route.meta?.platform;
  
  if (!platform || platform === 'both') {
    return true;
  }
  
  return (deviceType === DeviceEnum.MOBILE && platform === 'mobile') ||
         (deviceType === DeviceEnum.DESKTOP && platform === 'desktop');
}

/**
 * 查找适配的路由路径
 */
function findAdaptedRoute(currentPath: string, deviceType: DeviceEnum): string | null {
  // 这里可以实现路由映射逻辑
  // 例如：/desktop/user → /mobile/user
  
  const platformPrefix = deviceType === DeviceEnum.MOBILE ? '/mobile' : '/desktop';
  
  // 简单的路径转换示例
  if (currentPath.startsWith('/desktop/') && deviceType === DeviceEnum.MOBILE) {
    return currentPath.replace('/desktop/', '/mobile/');
  }
  
  if (currentPath.startsWith('/mobile/') && deviceType === DeviceEnum.DESKTOP) {
    return currentPath.replace('/mobile/', '/desktop/');
  }
  
  return null;
}
```

## 🎯 实施检查清单

### 数据库层面
- [ ] 扩展菜单表字段
- [ ] 配置示例菜单数据
- [ ] 测试数据库查询

### 后端API层面
- [ ] 更新路由接口支持设备类型参数
- [ ] 实现设备类型检测逻辑
- [ ] 添加自适应路由接口

### 前端路由层面
- [ ] 增强 transformRoutes 函数
- [ ] 实现设备切换监听
- [ ] 添加路由兼容性检查
- [ ] 测试动态路由生成

### 组件层面
- [ ] 创建自适应布局组件
- [ ] 实现平台特定组件
- [ ] 测试组件加载逻辑

## 🔧 调试和测试

### 1. 路由生成测试
```typescript
// 在浏览器控制台测试
const permissionStore = usePermissionStore();
console.log('Current routes:', permissionStore.routes);

// 切换设备测试
const appStore = useAppStore();
appStore.toggleDevice('mobile');
```

### 2. 组件加载测试
```typescript
// 检查组件是否正确加载
const modules = import.meta.glob("../../views/**/**.vue");
console.log('Available components:', Object.keys(modules));
```

### 3. 设备切换测试
```typescript
// 模拟设备切换
window.dispatchEvent(new Event('resize'));
```

通过以上方案，您可以在现有的数据库动态路由系统基础上，无缝集成移动端和PC端的自适应功能，实现真正的一套代码多端运行。
