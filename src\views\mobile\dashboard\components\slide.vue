<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <van-swipe class="my-swipe" :autoplay="3000" indicator-color="white">
    <van-swipe-item v-for="img in images" :key="img">
      <van-image width="100%" height="4rem" :src="img" fit="fill" object-fit="contain" />
    </van-swipe-item>
  </van-swipe>
</template>

<script setup lang="ts">
import slide7 from "@/assets/images/slide7.jpg";
import slide8 from "@/assets/images/slide8.jpg";
import slide9 from "@/assets/images/slide9.jpg";
const images = [slide7, slide8, slide9];
</script>

<style scoped>
.my-swipe .van-swipe-item {
  color: #fff;
  font-size: 20px;
  height: 4rem;
  text-align: center;
  background-color: #39a9ed;
}
</style>
