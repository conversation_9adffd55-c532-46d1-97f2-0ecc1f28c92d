<template>
  <div class="mobile-disposal">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="资产处置"
      left-arrow
      fixed
      placeholder
      @click-left="goBack"
    />
    
    <!-- 搜索框 -->
    <div class="search-bar">
      <van-search
        v-model="searchValue"
        placeholder="请输入资产名称"
        shape="round"
        show-action
        @search="onSearch"
      >
        <template #action>
          <div @click="onSearch">搜索</div>
        </template>
      </van-search>
    </div>
    
    <!-- 标签页 -->
    <van-tabs v-model:active="activeTab" sticky>
      <van-tab title="待处置">
        <disposal-list :list="pendingList" :loading="loading" :finished="finished" @load="onLoad" />
      </van-tab>
      <van-tab title="处置中">
        <disposal-list :list="processingList" :loading="loading" :finished="finished" @load="onLoad" />
      </van-tab>
      <van-tab title="已处置">
        <disposal-list :list="completedList" :loading="loading" :finished="finished" @load="onLoad" />
      </van-tab>
    </van-tabs>
    
    <!-- 底部操作栏 -->
    <div class="bottom-bar">
      <van-button type="primary" block @click="createDisposal">申请资产处置</van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';

// 处置列表组件
const DisposalList = defineComponent({
  props: {
    list: Array,
    loading: Boolean,
    finished: Boolean
  },
  emits: ['load'],
  setup(props, { emit }) {
    const onLoad = () => {
      emit('load');
    };
    
    return () => (
      <van-list
        v-model:loading={props.loading}
        finished={props.finished}
        finished-text="没有更多了"
        onLoad={onLoad}
      >
        {props.list.map((item, index) => (
          <div key={index} class="disposal-item" onClick={() => goToDetail(item)}>
            <div class="disposal-title">
              <span class="disposal-id">[{item.id}]</span>
              {item.name}
            </div>
            <div class="disposal-info">
              <div class="info-row">
                <span class="label">处置方式:</span>
                <span class="value">{item.method}</span>
              </div>
              <div class="info-row">
                <span class="label">申请时间:</span>
                <span class="value">{item.time}</span>
              </div>
              <div class="info-row">
                <span class="label">资产原值:</span>
                <span class="value price">¥ {item.price}</span>
              </div>
              <div class="info-row">
                <span class="label">处置状态:</span>
                <span class={['value', `status-${item.status}`]}>{getStatusText(item.status)}</span>
              </div>
            </div>
            <div class="disposal-footer">
              <van-button 
                size="small" 
                type="primary" 
                plain
                onClick={(e) => { e.stopPropagation(); handleAction(item); }}
              >
                {getActionText(item.status)}
              </van-button>
              <van-button 
                size="small" 
                type="info" 
                plain
                onClick={(e) => { e.stopPropagation(); viewDetail(item); }}
              >
                查看详情
              </van-button>
            </div>
          </div>
        ))}
      </van-list>
    );
  }
});

const router = useRouter();
const searchValue = ref('');
const activeTab = ref(0);
const loading = ref(false);
const finished = ref(false);

// 待处置列表
const pendingList = ref([
  {
    id: '20250601001',
    name: '办公电脑（联想ThinkPad）',
    method: '报废处置',
    time: '2025-06-01',
    price: '5699.00',
    status: 'pending'
  },
  {
    id: '20250601002',
    name: '打印机（惠普LaserJet）',
    method: '报废处置',
    time: '2025-06-01',
    price: '2399.00',
    status: 'pending'
  },
  {
    id: '20250531003',
    name: '投影仪（爱普生）',
    method: '转让处置',
    time: '2025-05-31',
    price: '4599.00',
    status: 'pending'
  }
]);

// 处置中列表
const processingList = ref([
  {
    id: '20250530001',
    name: '办公桌椅（组合）',
    method: '内部调拨',
    time: '2025-05-30',
    price: '1299.00',
    status: 'processing'
  },
  {
    id: '20250529002',
    name: '空调（格力）',
    method: '报废处置',
    time: '2025-05-29',
    price: '3599.00',
    status: 'processing'
  }
]);

// 已处置列表
const completedList = ref([
  {
    id: '20250520001',
    name: '旧电脑显示器（三星）',
    method: '报废处置',
    time: '2025-05-20',
    price: '1299.00',
    status: 'completed'
  },
  {
    id: '20250515002',
    name: '办公椅（人体工学）',
    method: '内部调拨',
    time: '2025-05-15',
    price: '899.00',
    status: 'completed'
  },
  {
    id: '20250510003',
    name: '碎纸机（得力）',
    method: '报废处置',
    time: '2025-05-10',
    price: '399.00',
    status: 'completed'
  }
]);

// 返回上一页
const goBack = () => {
  router.go(-1);
};

// 搜索
const onSearch = () => {
  console.log('搜索:', searchValue.value);
  // 实现搜索逻辑
};

// 加载更多
const onLoad = () => {
  setTimeout(() => {
    // 模拟加载更多数据
    loading.value = false;
    finished.value = true;
  }, 1000);
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'pending': '待处置',
    'processing': '处置中',
    'completed': '已处置'
  };
  return statusMap[status] || '未知状态';
};

// 获取操作按钮文本
const getActionText = (status) => {
  const actionMap = {
    'pending': '开始处置',
    'processing': '确认完成',
    'completed': '查看结果'
  };
  return actionMap[status] || '操作';
};

// 跳转到详情页
const goToDetail = (item) => {
  console.log('跳转到详情页:', item);
  // router.push(`/mobile/disposal/detail/${item.id}`);
};

// 处理操作按钮点击
const handleAction = (item) => {
  console.log('处理操作:', item);
  if (item.status === 'pending') {
    // 开始处置
    // router.push(`/mobile/disposal/process/${item.id}`);
  } else if (item.status === 'processing') {
    // 确认完成
    // 显示确认对话框
  } else if (item.status === 'completed') {
    // 查看结果
    // router.push(`/mobile/disposal/result/${item.id}`);
  }
};

// 查看详情
const viewDetail = (item) => {
  console.log('查看详情:', item);
  // router.push(`/mobile/disposal/detail/${item.id}`);
};

// 申请资产处置
const createDisposal = () => {
  console.log('申请资产处置');
  // router.push('/mobile/disposal/create');
};
</script>

<style lang="scss" scoped>
.mobile-disposal {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 60px; // 为底部操作栏留出空间
  
  .search-bar {
    padding: 8px 0;
  }
  
  .disposal-item {
    margin: 12px;
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    
    .disposal-title {
      padding: 12px;
      font-size: 15px;
      font-weight: 500;
      border-bottom: 1px solid #f5f5f5;
      
      .disposal-id {
        color: #1989fa;
        margin-right: 4px;
      }
    }
    
    .disposal-info {
      padding: 12px;
      
      .info-row {
        display: flex;
        margin-bottom: 8px;
        font-size: 14px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .label {
          color: #666;
          width: 80px;
        }
        
        .value {
          flex: 1;
          color: #333;
          
          &.price {
            color: #ee0a24;
            font-weight: 500;
          }
          
          &.status-pending {
            color: #ff976a;
          }
          
          &.status-processing {
            color: #1989fa;
          }
          
          &.status-completed {
            color: #07c160;
          }
        }
      }
    }
    
    .disposal-footer {
      padding: 12px;
      display: flex;
      justify-content: flex-end;
      gap: 8px;
      border-top: 1px solid #f5f5f5;
    }
  }
  
  .bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 10px 12px;
    background-color: #fff;
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
    z-index: 10;
  }
}

:deep(.van-nav-bar) {
  background-color: #1989fa;
  
  .van-nav-bar__title {
    color: #fff;
  }
  
  .van-icon {
    color: #fff;
  }
}

:deep(.van-search__action) {
  color: #1989fa;
}

:deep(.van-tabs__line) {
  background-color: #1989fa;
}
</style>
