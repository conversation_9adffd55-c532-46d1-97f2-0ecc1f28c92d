<template>
  <div class="mobile-layout">
    <!-- 主要内容区域 -->
    <div class="mobile-content" :class="{ 'has-navbar': showNavBar, 'has-tabbar': showTabBar }">
      <router-view />
    </div>

    <!-- 底部Tab导航 -->
    <van-tabbar v-model="active" route>
      <van-tabbar-item @click="toIndex" name="home" icon="home-o">首页</van-tabbar-item>
      <!-- <van-tabbar-item @click="callScan" name="scan" icon="scan">扫一扫</van-tabbar-item> -->
      <van-tabbar-item @click="toMyinfo" name="my" icon="apps-o">公物仓</van-tabbar-item>
      <!-- <van-tabbar-item @click="back" name="revoke" icon="revoke">返回</van-tabbar-item> -->
    </van-tabbar>
  </div>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from "vue-router";
import { useAppStore } from "@/store";
import router from "@/router";
defineOptions({
  name: "MobileLayout",
});
const active = ref(0);

const toIndex = () => {
  //window.location.href = import.meta.env.VITE_HOME_PAGE;
  router.replace({
    path: "/mobile/dashboard",
  });
};

const toMyinfo = () => {
  // window.location.href = import.meta.env.VITE_MY_INFO;
  router.replace({
    path: "/mobile/gwcmain",
  });
};

// 处理返回按钮
const handleBack = () => {
  router.back();
};
</script>

<style lang="scss" scoped>
.mobile-layout {
  display: flex;
  flex-direction: column;
  width: 100vw;
  height: 100vh;
  background-color: #f5f5f5;
}

.mobile-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  height: 44px;
  background-color: #fff;
  border-bottom: 1px solid #e5e5e5;

  .navbar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 16px;
  }

  .navbar-left,
  .navbar-right {
    display: flex;
    align-items: center;
    min-width: 60px;
  }

  .navbar-left {
    justify-content: flex-start;
  }

  .navbar-right {
    justify-content: flex-end;
  }

  .back-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s;

    &:hover {
      background-color: #f5f5f5;
    }
  }

  .navbar-title {
    flex: 1;
    font-size: 16px;
    font-weight: 500;
    text-align: center;
    color: #333;
  }
}

.mobile-content {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;

  &.has-navbar {
    padding-top: 44px;
  }

  &.has-tabbar {
    padding-bottom: 50px;
  }
}

.mobile-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  height: 50px;
  background-color: #fff;
  border-top: 1px solid #e5e5e5;

  .tabbar-content {
    display: flex;
    height: 100%;
  }

  .tab-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: color 0.2s;

    &.active {
      color: var(--el-color-primary);
    }

    &:not(.active) {
      color: #999;
    }
  }

  .tab-icon {
    margin-bottom: 2px;
  }

  .tab-title {
    font-size: 10px;
    line-height: 1;
  }
}

// 移动端适配
@media screen and (max-width: 992px) {
  .mobile-layout {
    display: flex;
  }
}
</style>
