<template>
  <div class="adaptive-button">
    <!-- PC端按钮 -->
    <el-button
      v-if="!isMobile"
      v-bind="buttonProps"
      @click="handleClick"
    >
      <slot />
    </el-button>

    <!-- 移动端按钮 -->
    <van-button
      v-else
      v-bind="buttonProps"
      @click="handleClick"
    >
      <slot />
    </van-button>
  </div>
</template>

<script setup lang="ts">
import { useDevice } from '@/composables/useDevice';

interface AdaptiveButtonProps {
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'default';
  size?: 'large' | 'default' | 'small';
  disabled?: boolean;
  loading?: boolean;
  round?: boolean;
  circle?: boolean;
  plain?: boolean;
  block?: boolean;
  mobileProps?: Record<string, any>;
  desktopProps?: Record<string, any>;
}

const props = withDefaults(defineProps<AdaptiveButtonProps>(), {
  type: 'default',
  size: 'default',
  disabled: false,
  loading: false,
  round: false,
  circle: false,
  plain: false,
  block: false
});

const emit = defineEmits<{
  click: [event: Event];
}>();

const { isMobile } = useDevice();

// 按钮属性适配
const buttonProps = computed(() => {
  const baseProps = {
    type: props.type,
    size: props.size,
    disabled: props.disabled,
    loading: props.loading,
    round: props.round,
    plain: props.plain
  };

  // 平台特定属性
  const platformProps = isMobile.value ? props.mobileProps : props.desktopProps;

  // 移动端特殊处理
  if (isMobile.value) {
    return {
      ...baseProps,
      ...platformProps,
      block: props.block,
      // Vant 按钮特有属性
      square: props.circle ? false : undefined,
      hairline: props.plain
    };
  } else {
    return {
      ...baseProps,
      ...platformProps,
      // Element Plus 按钮特有属性
      circle: props.circle
    };
  }
});

const handleClick = (event: Event) => {
  if (!props.disabled && !props.loading) {
    emit('click', event);
  }
};
</script>

<style lang="scss" scoped>
.adaptive-button {
  display: inline-block;
  
  // 移动端按钮样式调整
  .adaptive-layout--mobile & {
    .van-button {
      min-height: 44px;
      font-size: 16px;
      
      &.van-button--block {
        width: 100%;
      }
    }
  }
  
  // 桌面端按钮样式调整
  .adaptive-layout--desktop & {
    .el-button {
      min-height: 32px;
      
      &.el-button--large {
        min-height: 40px;
      }
      
      &.el-button--small {
        min-height: 24px;
      }
    }
  }
}
</style>
