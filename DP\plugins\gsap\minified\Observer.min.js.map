{"version": 3, "file": "Observer.min.js", "sources": ["../src/Observer.js"], "sourcesContent": ["/*!\n * Observer 3.11.3\n * https://greensock.com\n *\n * @license Copyright 2008-2022, GreenSock. All rights reserved.\n * Subject to the terms at https://greensock.com/standard-license or for\n * Club GreenSock members, the agreement issued with that membership.\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nlet gsap, _coreInitted, _clamp, _win, _doc, _docEl, _body, _isTouch, _pointerType, ScrollTrigger, _root, _normalizer, _eventTypes,\n\t_getGSAP = () => gsap || (typeof(window) !== \"undefined\" && (gsap = window.gsap) && gsap.registerPlugin && gsap),\n\t_passThrough = p => p,\n\t_startup = 1,\n\t_observers = [],\n\t_scrollers = [],\n\t_proxies = [],\n\t_getTime = Date.now,\n\t_bridge = (name, value) => value,\n\t_integrate = () => {\n\t\tlet core = ScrollTrigger.core,\n\t\t\tdata = core.bridge || {},\n\t\t\tscrollers = core._scrollers,\n\t\t\tproxies = core._proxies;\n\t\tscrollers.push(..._scrollers);\n\t\tproxies.push(..._proxies);\n\t\t_scrollers = scrollers;\n\t\t_proxies = proxies;\n\t\t_bridge = (name, value) => data[name](value);\n\t},\n\t_getProxyProp = (element, property) => ~_proxies.indexOf(element) && _proxies[_proxies.indexOf(element) + 1][property],\n\t_isViewport = el => !!~_root.indexOf(el),\n\t_addListener = (element, type, func, nonPassive, capture) => element.addEventListener(type, func, {passive: !nonPassive, capture: !!capture}),\n\t_removeListener = (element, type, func, capture) => element.removeEventListener(type, func, !!capture),\n\t_scrollLeft = \"scrollLeft\",\n\t_scrollTop = \"scrollTop\",\n\t_onScroll = () => (_normalizer && _normalizer.isPressed) || _scrollers.cache++,\n\t_scrollCacheFunc = (f, doNotCache) => {\n\t\tlet cachingFunc = value => { // since reading the scrollTop/scrollLeft/pageOffsetY/pageOffsetX can trigger a layout, this function allows us to cache the value so it only gets read fresh after a \"scroll\" event fires (or while we're refreshing because that can lengthen the page and alter the scroll position). when \"soft\" is true, that means don't actually set the scroll, but cache the new value instead (useful in ScrollSmoother)\n\t\t\tif (value || value === 0) {\n\t\t\t\t_startup && (_win.history.scrollRestoration = \"manual\"); // otherwise the new position will get overwritten by the browser onload.\n\t\t\t\tlet isNormalizing = _normalizer && _normalizer.isPressed;\n\t\t\t\tvalue = cachingFunc.v = Math.round(value) || (_normalizer && _normalizer.iOS ? 1 : 0); //TODO: iOS Bug: if you allow it to go to 0, Safari can start to report super strange (wildly inaccurate) touch positions!\n\t\t\t\tf(value);\n\t\t\t\tcachingFunc.cacheID = _scrollers.cache;\n\t\t\t\tisNormalizing && _bridge(\"ss\", value); // set scroll (notify ScrollTrigger so it can dispatch a \"scrollStart\" event if necessary\n\t\t\t} else if (doNotCache || _scrollers.cache !== cachingFunc.cacheID || _bridge(\"ref\")) {\n\t\t\t\tcachingFunc.cacheID = _scrollers.cache;\n\t\t\t\tcachingFunc.v = f();\n\t\t\t}\n\t\t\treturn cachingFunc.v + cachingFunc.offset;\n\t\t};\n\t\tcachingFunc.offset = 0;\n\t\treturn f && cachingFunc;\n\t},\n\t_horizontal = {s: _scrollLeft, p: \"left\", p2: \"Left\", os: \"right\", os2: \"Right\", d: \"width\", d2: \"Width\", a: \"x\", sc: _scrollCacheFunc(function(value) { return arguments.length ? _win.scrollTo(value, _vertical.sc()) : _win.pageXOffset || _doc[_scrollLeft] || _docEl[_scrollLeft] || _body[_scrollLeft] || 0})},\n\t_vertical = {s: _scrollTop, p: \"top\", p2: \"Top\", os: \"bottom\", os2: \"Bottom\", d: \"height\", d2: \"Height\", a: \"y\", op: _horizontal, sc: _scrollCacheFunc(function(value) { return arguments.length ? _win.scrollTo(_horizontal.sc(), value) : _win.pageYOffset || _doc[_scrollTop] || _docEl[_scrollTop] || _body[_scrollTop] || 0})},\n\t_getTarget = t => gsap.utils.toArray(t)[0] || (typeof(t) === \"string\" && gsap.config().nullTargetWarn !== false ? console.warn(\"Element not found:\", t) : null),\n\n\t_getScrollFunc = (element, {s, sc}) => { // we store the scroller functions in an alternating sequenced Array like [element, verticalScrollFunc, horizontalScrollFunc, ...] so that we can minimize memory, maximize performance, and we also record the last position as a \".rec\" property in order to revert to that after refreshing to ensure things don't shift around.\n\t\t_isViewport(element) && (element = _doc.scrollingElement || _docEl);\n\t\tlet i = _scrollers.indexOf(element),\n\t\t\toffset = sc === _vertical.sc ? 1 : 2;\n\t\t!~i && (i = _scrollers.push(element) - 1);\n\t\t_scrollers[i + offset] || element.addEventListener(\"scroll\", _onScroll); // clear the cache when a scroll occurs\n\t\tlet prev = _scrollers[i + offset],\n\t\t\tfunc = prev || (_scrollers[i + offset] = _scrollCacheFunc(_getProxyProp(element, s), true) || (_isViewport(element) ? sc : _scrollCacheFunc(function(value) { return arguments.length ? (element[s] = value) : element[s]; })));\n\t\tfunc.target = element;\n\t\tprev || (func.smooth = gsap.getProperty(element, \"scrollBehavior\") === \"smooth\"); // only set it the first time (don't reset every time a scrollFunc is requested because perhaps it happens during a refresh() when it's disabled in ScrollTrigger.\n\t\treturn func;\n\t},\n\t_getVelocityProp = (value, minTimeRefresh, useDelta) => {\n\t\tlet v1 = value,\n\t\t\tv2 = value,\n\t\t\tt1 = _getTime(),\n\t\t\tt2 = t1,\n\t\t\tmin = minTimeRefresh || 50,\n\t\t\tdropToZeroTime = Math.max(500, min * 3),\n\t\t\tupdate = (value, force) => {\n\t\t\t\tlet t = _getTime();\n\t\t\t\tif (force || t - t1 > min) {\n\t\t\t\t\tv2 = v1;\n\t\t\t\t\tv1 = value;\n\t\t\t\t\tt2 = t1;\n\t\t\t\t\tt1 = t;\n\t\t\t\t} else if (useDelta) {\n\t\t\t\t\tv1 += value;\n\t\t\t\t} else { // not totally necessary, but makes it a bit more accurate by adjusting the v1 value according to the new slope. This way we're not just ignoring the incoming data. Removing for now because it doesn't seem to make much practical difference and it's probably not worth the kb.\n\t\t\t\t\tv1 = v2 + (value - v2) / (t - t2) * (t1 - t2);\n\t\t\t\t}\n\t\t\t},\n\t\t\treset = () => { v2 = v1 = useDelta ? 0 : v1; t2 = t1 = 0; },\n\t\t\tgetVelocity = latestValue => {\n\t\t\t\tlet tOld = t2,\n\t\t\t\t\tvOld = v2,\n\t\t\t\t\tt = _getTime();\n\t\t\t\t(latestValue || latestValue === 0) && latestValue !== v1 && update(latestValue);\n\t\t\t\treturn (t1 === t2 || t - t2 > dropToZeroTime) ? 0 : (v1 + (useDelta ? vOld : -vOld)) / ((useDelta ? t : t1) - tOld) * 1000;\n\t\t\t};\n\t\treturn {update, reset, getVelocity};\n\t},\n\t_getEvent = (e, preventDefault) => {\n\t\tpreventDefault && !e._gsapAllow && e.preventDefault();\n\t\treturn e.changedTouches ? e.changedTouches[0] : e;\n\t},\n\t_getAbsoluteMax = a => {\n\t\tlet max = Math.max(...a),\n\t\t\tmin = Math.min(...a);\n\t\treturn Math.abs(max) >= Math.abs(min) ? max : min;\n\t},\n\t_setScrollTrigger = () => {\n\t\tScrollTrigger = gsap.core.globals().ScrollTrigger;\n\t\tScrollTrigger && ScrollTrigger.core && _integrate();\n\t},\n\t_initCore = core => {\n\t\tgsap = core || _getGSAP();\n\t\tif (gsap && typeof(document) !== \"undefined\" && document.body) {\n\t\t\t_win = window;\n\t\t\t_doc = document;\n\t\t\t_docEl = _doc.documentElement;\n\t\t\t_body = _doc.body;\n\t\t\t_root = [_win, _doc, _docEl, _body];\n\t\t\t_clamp = gsap.utils.clamp;\n\t\t\t_pointerType = \"onpointerenter\" in _body ? \"pointer\" : \"mouse\";\n\t\t\t// isTouch is 0 if no touch, 1 if ONLY touch, and 2 if it can accommodate touch but also other types like mouse/pointer.\n\t\t\t_isTouch = Observer.isTouch = _win.matchMedia && _win.matchMedia(\"(hover: none), (pointer: coarse)\").matches ? 1 : (\"ontouchstart\" in _win || navigator.maxTouchPoints > 0 || navigator.msMaxTouchPoints > 0) ? 2 : 0;\n\t\t\t_eventTypes = Observer.eventTypes = (\"ontouchstart\" in _docEl ? \"touchstart,touchmove,touchcancel,touchend\" : !(\"onpointerdown\" in _docEl) ? \"mousedown,mousemove,mouseup,mouseup\" : \"pointerdown,pointermove,pointercancel,pointerup\").split(\",\");\n\t\t\tsetTimeout(() => _startup = 0, 500);\n\t\t\t_setScrollTrigger();\n\t\t\t_coreInitted = 1;\n\t\t}\n\t\treturn _coreInitted;\n\t};\n\n_horizontal.op = _vertical;\n_scrollers.cache = 0;\n\nexport class Observer {\n\tconstructor(vars) {\n\t\tthis.init(vars);\n\t}\n\n\tinit(vars) {\n\t\t_coreInitted || _initCore(gsap) || console.warn(\"Please gsap.registerPlugin(Observer)\");\n\t\tScrollTrigger || _setScrollTrigger();\n\t\tlet {tolerance, dragMinimum, type, target, lineHeight, debounce, preventDefault, onStop, onStopDelay, ignore, wheelSpeed, event, onDragStart, onDragEnd, onDrag, onPress, onRelease, onRight, onLeft, onUp, onDown, onChangeX, onChangeY, onChange, onToggleX, onToggleY, onHover, onHoverEnd, onMove, ignoreCheck, isNormalizer, onGestureStart, onGestureEnd, onWheel, onEnable, onDisable, onClick, scrollSpeed, capture, allowClicks, lockAxis, onLockAxis} = vars;\n\t\tthis.target = target = _getTarget(target) || _docEl;\n\t\tthis.vars = vars;\n\t\tignore && (ignore = gsap.utils.toArray(ignore));\n\t\ttolerance = tolerance || 1e-9;\n\t\tdragMinimum = dragMinimum || 0;\n\t\twheelSpeed = wheelSpeed || 1;\n\t\tscrollSpeed = scrollSpeed || 1;\n\t\ttype = type || \"wheel,touch,pointer\";\n\t\tdebounce = debounce !== false;\n\t\tlineHeight || (lineHeight = parseFloat(_win.getComputedStyle(_body).lineHeight) || 22); // note: browser may report \"normal\", so default to 22.\n\t\tlet id, onStopDelayedCall, dragged, moved, wheeled, locked, axis,\n\t\t\tself = this,\n\t\t\tprevDeltaX = 0,\n\t\t\tprevDeltaY = 0,\n\t\t\tscrollFuncX = _getScrollFunc(target, _horizontal),\n\t\t\tscrollFuncY = _getScrollFunc(target, _vertical),\n\t\t\tscrollX = scrollFuncX(),\n\t\t\tscrollY = scrollFuncY(),\n\t\t\tlimitToTouch = ~type.indexOf(\"touch\") && !~type.indexOf(\"pointer\") && _eventTypes[0] === \"pointerdown\", // for devices that accommodate mouse events and touch events, we need to distinguish.\n\t\t\tisViewport = _isViewport(target),\n\t\t\townerDoc = target.ownerDocument || _doc,\n\t\t\tdeltaX = [0, 0, 0], // wheel, scroll, pointer/touch\n\t\t\tdeltaY = [0, 0, 0],\n\t\t\tonClickTime = 0,\n\t\t\tclickCapture = () => onClickTime = _getTime(),\n\t\t\t_ignoreCheck = (e, isPointerOrTouch) => (self.event = e) && (ignore && ~ignore.indexOf(e.target)) || (isPointerOrTouch && limitToTouch && e.pointerType !== \"touch\") || (ignoreCheck && ignoreCheck(e, isPointerOrTouch)),\n\t\t\tonStopFunc = () => {\n\t\t\t\tself._vx.reset();\n\t\t\t\tself._vy.reset();\n\t\t\t\tonStopDelayedCall.pause();\n\t\t\t\tonStop && onStop(self);\n\t\t\t},\n\t\t\tupdate = () => {\n\t\t\t\tlet dx = self.deltaX = _getAbsoluteMax(deltaX),\n\t\t\t\t\tdy = self.deltaY = _getAbsoluteMax(deltaY),\n\t\t\t\t\tchangedX = Math.abs(dx) >= tolerance,\n\t\t\t\t\tchangedY = Math.abs(dy) >= tolerance;\n\t\t\t\tonChange && (changedX || changedY) && onChange(self, dx, dy, deltaX, deltaY); // in ScrollTrigger.normalizeScroll(), we need to know if it was touch/pointer so we need access to the deltaX/deltaY Arrays before we clear them out.\n\t\t\t\tif (changedX) {\n\t\t\t\t\tonRight && self.deltaX > 0 && onRight(self);\n\t\t\t\t\tonLeft && self.deltaX < 0 && onLeft(self);\n\t\t\t\t\tonChangeX && onChangeX(self);\n\t\t\t\t\tonToggleX && ((self.deltaX < 0) !== (prevDeltaX < 0)) && onToggleX(self);\n\t\t\t\t\tprevDeltaX = self.deltaX;\n\t\t\t\t\tdeltaX[0] = deltaX[1] = deltaX[2] = 0\n\t\t\t\t}\n\t\t\t\tif (changedY) {\n\t\t\t\t\tonDown && self.deltaY > 0 && onDown(self);\n\t\t\t\t\tonUp && self.deltaY < 0 && onUp(self);\n\t\t\t\t\tonChangeY && onChangeY(self);\n\t\t\t\t\tonToggleY && ((self.deltaY < 0) !== (prevDeltaY < 0)) && onToggleY(self);\n\t\t\t\t\tprevDeltaY = self.deltaY;\n\t\t\t\t\tdeltaY[0] = deltaY[1] = deltaY[2] = 0\n\t\t\t\t}\n\t\t\t\tif (moved || dragged) {\n\t\t\t\t\tonMove && onMove(self);\n\t\t\t\t\tif (dragged) {\n\t\t\t\t\t\tonDrag(self);\n\t\t\t\t\t\tdragged = false;\n\t\t\t\t\t}\n\t\t\t\t\tmoved = false;\n\t\t\t\t}\n\t\t\t\tlocked && !(locked = false) && onLockAxis && onLockAxis(self);\n\t\t\t\tif (wheeled) {\n\t\t\t\t\tonWheel(self);\n\t\t\t\t\twheeled = false;\n\t\t\t\t}\n\t\t\t\tid = 0;\n\t\t\t},\n\t\t\tonDelta = (x, y, index) => {\n\t\t\t\tdeltaX[index] += x;\n\t\t\t\tdeltaY[index] += y;\n\t\t\t\tself._vx.update(x);\n\t\t\t\tself._vy.update(y);\n\t\t\t\tdebounce ? id || (id = requestAnimationFrame(update)) : update();\n\t\t\t},\n\t\t\tonTouchOrPointerDelta = (x, y) => {\n\t\t\t\tif (lockAxis && !axis) {\n\t\t\t\t\tself.axis = axis = Math.abs(x) > Math.abs(y) ? \"x\" : \"y\";\n\t\t\t\t\tlocked = true;\n\t\t\t\t}\n\t\t\t\tif (axis !== \"y\") {\n\t\t\t\t\tdeltaX[2] += x;\n\t\t\t\t\tself._vx.update(x, true); // update the velocity as frequently as possible instead of in the debounced function so that very quick touch-scrolls (flicks) feel natural. If it's the mouse/touch/pointer, force it so that we get snappy/accurate momentum scroll.\n\t\t\t\t}\n\t\t\t\tif (axis !== \"x\") {\n\t\t\t\t\tdeltaY[2] += y;\n\t\t\t\t\tself._vy.update(y, true);\n\t\t\t\t}\n\t\t\t\tdebounce ? id || (id = requestAnimationFrame(update)) : update();\n\t\t\t},\n\t\t\t_onDrag = e => {\n\t\t\t\tif (_ignoreCheck(e, 1)) {return;}\n\t\t\t\te = _getEvent(e, preventDefault);\n\t\t\t\tlet x = e.clientX,\n\t\t\t\t\ty = e.clientY,\n\t\t\t\t\tdx = x - self.x,\n\t\t\t\t\tdy = y - self.y,\n\t\t\t\t\tisDragging = self.isDragging;\n\t\t\t\tself.x = x;\n\t\t\t\tself.y = y;\n\t\t\t\tif (isDragging || Math.abs(self.startX - x) >= dragMinimum || Math.abs(self.startY - y) >= dragMinimum) {\n\t\t\t\t\tonDrag && (dragged = true);\n\t\t\t\t\tisDragging || (self.isDragging = true);\n\t\t\t\t\tonTouchOrPointerDelta(dx, dy);\n\t\t\t\t\tisDragging || onDragStart && onDragStart(self);\n\t\t\t\t}\n\t\t\t},\n\t\t\t_onPress = self.onPress = e => {\n\t\t\t\tif (_ignoreCheck(e, 1)) {return;}\n\t\t\t\tself.axis = axis = null;\n\t\t\t\tonStopDelayedCall.pause();\n\t\t\t\tself.isPressed = true;\n\t\t\t\te = _getEvent(e); // note: may need to preventDefault(?) Won't side-scroll on iOS Safari if we do, though.\n\t\t\t\tprevDeltaX = prevDeltaY = 0;\n\t\t\t\tself.startX = self.x = e.clientX;\n\t\t\t\tself.startY = self.y = e.clientY;\n\t\t\t\tself._vx.reset(); // otherwise the t2 may be stale if the user touches and flicks super fast and releases in less than 2 requestAnimationFrame ticks, causing velocity to be 0.\n\t\t\t\tself._vy.reset();\n\t\t\t\t_addListener(isNormalizer ? target : ownerDoc, _eventTypes[1], _onDrag, preventDefault, true);\n\t\t\t\tself.deltaX = self.deltaY = 0;\n\t\t\t\tonPress && onPress(self);\n\t\t\t},\n\t\t\t_onRelease = e => {\n\t\t\t\tif (_ignoreCheck(e, 1)) {return;}\n\t\t\t\t_removeListener(isNormalizer ? target : ownerDoc, _eventTypes[1], _onDrag, true);\n\t\t\t\tlet wasDragging = self.isDragging && (Math.abs(self.x - self.startX) > 3 || Math.abs(self.y - self.startY) > 3), // some touch devices need some wiggle room in terms of sensing clicks - the finger may move a few pixels.\n\t\t\t\t\teventData = _getEvent(e);\n\t\t\t\tif (!wasDragging) {\n\t\t\t\t\tself._vx.reset();\n\t\t\t\t\tself._vy.reset();\n\t\t\t\t\tif (preventDefault && allowClicks) {\n\t\t\t\t\t\tgsap.delayedCall(0.08, () => { // some browsers (like Firefox) won't trust script-generated clicks, so if the user tries to click on a video to play it, for example, it simply won't work. Since a regular \"click\" event will most likely be generated anyway (one that has its isTrusted flag set to true), we must slightly delay our script-generated click so that the \"real\"/trusted one is prioritized. Remember, when there are duplicate events in quick succession, we suppress all but the first one. Some browsers don't even trigger the \"real\" one at all, so our synthetic one is a safety valve that ensures that no matter what, a click event does get dispatched.\n\t\t\t\t\t\t\tif (_getTime() - onClickTime > 300 && !e.defaultPrevented) {\n\t\t\t\t\t\t\t\tif (e.target.click) { //some browsers (like mobile Safari) don't properly trigger the click event\n\t\t\t\t\t\t\t\t\te.target.click();\n\t\t\t\t\t\t\t\t} else if (ownerDoc.createEvent) {\n\t\t\t\t\t\t\t\t\tlet syntheticEvent = ownerDoc.createEvent(\"MouseEvents\");\n\t\t\t\t\t\t\t\t\tsyntheticEvent.initMouseEvent(\"click\", true, true, _win, 1, eventData.screenX, eventData.screenY, eventData.clientX, eventData.clientY, false, false, false, false, 0, null);\n\t\t\t\t\t\t\t\t\te.target.dispatchEvent(syntheticEvent);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tself.isDragging = self.isGesturing = self.isPressed = false;\n\t\t\t\tonStop && !isNormalizer && onStopDelayedCall.restart(true);\n\t\t\t\tonDragEnd && wasDragging && onDragEnd(self);\n\t\t\t\tonRelease && onRelease(self, wasDragging);\n\t\t\t},\n\t\t\t_onGestureStart = e => e.touches && e.touches.length > 1 && (self.isGesturing = true) && onGestureStart(e, self.isDragging),\n\t\t\t_onGestureEnd = () => (self.isGesturing = false) || onGestureEnd(self),\n\t\t\tonScroll = e => {\n\t\t\t\tif (_ignoreCheck(e)) {return;}\n\t\t\t\tlet x = scrollFuncX(),\n\t\t\t\t\ty = scrollFuncY();\n\t\t\t\tonDelta((x - scrollX) * scrollSpeed, (y - scrollY) * scrollSpeed, 1);\n\t\t\t\tscrollX = x;\n\t\t\t\tscrollY = y;\n\t\t\t\tonStop && onStopDelayedCall.restart(true);\n\t\t\t},\n\t\t\t_onWheel = e => {\n\t\t\t\tif (_ignoreCheck(e)) {return;}\n\t\t\t\te = _getEvent(e, preventDefault);\n\t\t\t\tonWheel && (wheeled = true);\n\t\t\t\tlet multiplier = (e.deltaMode === 1 ? lineHeight : e.deltaMode === 2 ? _win.innerHeight : 1) * wheelSpeed;\n\t\t\t\tonDelta(e.deltaX * multiplier, e.deltaY * multiplier, 0);\n\t\t\t\tonStop && !isNormalizer && onStopDelayedCall.restart(true);\n\t\t\t},\n\t\t\t_onMove = e => {\n\t\t\t\tif (_ignoreCheck(e)) {return;}\n\t\t\t\tlet x = e.clientX,\n\t\t\t\t\ty = e.clientY,\n\t\t\t\t\tdx = x - self.x,\n\t\t\t\t\tdy = y - self.y;\n\t\t\t\tself.x = x;\n\t\t\t\tself.y = y;\n\t\t\t\tmoved = true;\n\t\t\t\t(dx || dy) && onTouchOrPointerDelta(dx, dy);\n\t\t\t},\n\t\t\t_onHover = e => {self.event = e; onHover(self);},\n\t\t\t_onHoverEnd = e => {self.event = e; onHoverEnd(self);},\n\t\t\t_onClick = e => _ignoreCheck(e) || (_getEvent(e, preventDefault) && onClick(self));\n\n\t\tonStopDelayedCall = self._dc = gsap.delayedCall(onStopDelay || 0.25, onStopFunc).pause();\n\n\t\tself.deltaX = self.deltaY = 0;\n\t\tself._vx = _getVelocityProp(0, 50, true);\n\t\tself._vy = _getVelocityProp(0, 50, true);\n\t\tself.scrollX = scrollFuncX;\n\t\tself.scrollY = scrollFuncY;\n\t\tself.isDragging = self.isGesturing = self.isPressed = false;\n\t\tself.enable = e => {\n\t\t\tif (!self.isEnabled) {\n\t\t\t\t_addListener(isViewport ? ownerDoc : target, \"scroll\", _onScroll);\n\t\t\t\ttype.indexOf(\"scroll\") >= 0 && _addListener(isViewport ? ownerDoc : target, \"scroll\", onScroll, preventDefault, capture);\n\t\t\t\ttype.indexOf(\"wheel\") >= 0 && _addListener(target, \"wheel\", _onWheel, preventDefault, capture);\n\t\t\t\tif ((type.indexOf(\"touch\") >= 0 && _isTouch) || type.indexOf(\"pointer\") >= 0) {\n\t\t\t\t\t_addListener(target, _eventTypes[0], _onPress, preventDefault, capture);\n\t\t\t\t\t_addListener(ownerDoc, _eventTypes[2], _onRelease);\n\t\t\t\t\t_addListener(ownerDoc, _eventTypes[3], _onRelease);\n\t\t\t\t\tallowClicks && _addListener(target, \"click\", clickCapture, false, true);\n\t\t\t\t\tonClick && _addListener(target, \"click\", _onClick);\n\t\t\t\t\tonGestureStart && _addListener(ownerDoc, \"gesturestart\", _onGestureStart);\n\t\t\t\t\tonGestureEnd && _addListener(ownerDoc, \"gestureend\", _onGestureEnd);\n\t\t\t\t\tonHover && _addListener(target, _pointerType + \"enter\", _onHover);\n\t\t\t\t\tonHoverEnd && _addListener(target, _pointerType + \"leave\", _onHoverEnd);\n\t\t\t\t\tonMove && _addListener(target, _pointerType + \"move\", _onMove);\n\t\t\t\t}\n\t\t\t\tself.isEnabled = true;\n\t\t\t\te && e.type && _onPress(e);\n\t\t\t\tonEnable && onEnable(self);\n\t\t\t}\n\t\t\treturn self;\n\t\t};\n\t\tself.disable = () => {\n\t\t\tif (self.isEnabled) {\n\t\t\t\t// only remove the _onScroll listener if there aren't any others that rely on the functionality.\n\t\t\t\t_observers.filter(o => o !== self && _isViewport(o.target)).length || _removeListener(isViewport ? ownerDoc : target, \"scroll\", _onScroll);\n\t\t\t\tif (self.isPressed) {\n\t\t\t\t\tself._vx.reset();\n\t\t\t\t\tself._vy.reset();\n\t\t\t\t\t_removeListener(isNormalizer ? target : ownerDoc, _eventTypes[1], _onDrag, true);\n\t\t\t\t}\n\t\t\t\t_removeListener(isViewport ? ownerDoc : target, \"scroll\", onScroll, capture);\n\t\t\t\t_removeListener(target, \"wheel\", _onWheel, capture);\n\t\t\t\t_removeListener(target, _eventTypes[0], _onPress, capture);\n\t\t\t\t_removeListener(ownerDoc, _eventTypes[2], _onRelease);\n\t\t\t\t_removeListener(ownerDoc, _eventTypes[3], _onRelease);\n\t\t\t\t_removeListener(target, \"click\", clickCapture, true);\n\t\t\t\t_removeListener(target, \"click\", _onClick);\n\t\t\t\t_removeListener(ownerDoc, \"gesturestart\", _onGestureStart);\n\t\t\t\t_removeListener(ownerDoc, \"gestureend\", _onGestureEnd);\n\t\t\t\t_removeListener(target, _pointerType + \"enter\", _onHover);\n\t\t\t\t_removeListener(target, _pointerType + \"leave\", _onHoverEnd);\n\t\t\t\t_removeListener(target, _pointerType + \"move\", _onMove);\n\t\t\t\tself.isEnabled = self.isPressed = self.isDragging = false;\n\t\t\t\tonDisable && onDisable(self);\n\t\t\t}\n\t\t};\n\n\t\tself.kill = () => {\n\t\t\tself.disable();\n\t\t\tlet i = _observers.indexOf(self);\n\t\t\ti >= 0 && _observers.splice(i, 1);\n\t\t\t_normalizer === self && (_normalizer = 0);\n\t\t}\n\n\t\t_observers.push(self);\n\t\tisNormalizer && _isViewport(target) && (_normalizer = self);\n\n\t\tself.enable(event);\n\t}\n\n\tget velocityX() {\n\t\treturn this._vx.getVelocity();\n\t}\n\tget velocityY() {\n\t\treturn this._vy.getVelocity();\n\t}\n\n}\n\nObserver.version = \"3.11.3\";\nObserver.create = vars => new Observer(vars);\nObserver.register = _initCore;\nObserver.getAll = () => _observers.slice();\nObserver.getById = id => _observers.filter(o => o.vars.id === id)[0];\n\n_getGSAP() && gsap.registerPlugin(Observer);\n\nexport { Observer as default, _isViewport, _scrollers, _getScrollFunc, _getProxyProp, _proxies, _getVelocityProp, _vertical, _horizontal, _getTarget };"], "names": ["_getGSAP", "gsap", "window", "registerPlugin", "_coreInitted", "_win", "_doc", "_docEl", "_body", "_isTouch", "_pointerType", "ScrollTrigger", "_root", "_normalizer", "_eventTypes", "_startup", "_observers", "_scrollers", "_proxies", "_getProxyProp", "element", "property", "indexOf", "_isViewport", "el", "_addListener", "type", "func", "nonPassive", "capture", "addEventListener", "passive", "_removeListener", "removeEventListener", "_onScroll", "isPressed", "cache", "_scrollCacheFunc", "f", "doNotCache", "cachingFunc", "value", "history", "scrollRestoration", "isNormalizing", "v", "Math", "round", "iOS", "cacheID", "_bridge", "offset", "_getTarget", "t", "utils", "toArray", "config", "null<PERSON><PERSON><PERSON><PERSON><PERSON>n", "console", "warn", "_getScrollFunc", "s", "sc", "scrollingElement", "i", "_vertical", "push", "prev", "arguments", "length", "target", "smooth", "getProperty", "_getVelocityProp", "minTimeRefresh", "useDel<PERSON>", "update", "force", "_getTime", "min", "t1", "v2", "v1", "t2", "dropToZeroTime", "max", "reset", "getVelocity", "latestValue", "tOld", "vOld", "_getEvent", "e", "preventDefault", "_gsapAllow", "changedTouches", "_getAbsoluteMax", "a", "abs", "_setScrollTrigger", "core", "globals", "_integrate", "data", "bridge", "scrollers", "proxies", "name", "_initCore", "document", "body", "documentElement", "clamp", "Observer", "is<PERSON><PERSON>ch", "matchMedia", "matches", "navigator", "maxTouchPoints", "msMaxTouchPoints", "eventTypes", "split", "setTimeout", "Date", "now", "_scrollLeft", "_scrollTop", "_horizontal", "p", "p2", "os", "os2", "d", "d2", "scrollTo", "pageXOffset", "op", "pageYOffset", "init", "vars", "tolerance", "dragMinimum", "lineHeight", "debounce", "onStop", "onStopDelay", "ignore", "wheelSpeed", "event", "onDragStart", "onDragEnd", "onDrag", "onPress", "onRelease", "onRight", "onLeft", "onUp", "onDown", "onChangeX", "onChangeY", "onChange", "onToggleX", "onToggleY", "onHover", "onHoverEnd", "onMove", "<PERSON><PERSON><PERSON><PERSON>", "isNormalizer", "onGestureStart", "onGestureEnd", "onWheel", "onEnable", "onDisable", "onClick", "scrollSpeed", "allowClicks", "lockAxis", "onLockAxis", "clickCapture", "onClickTime", "_ignore<PERSON>heck", "isPointerOr<PERSON>ouch", "self", "limitToTouch", "pointerType", "dx", "deltaX", "dy", "deltaY", "changedX", "changedY", "prevDeltaX", "prevDeltaY", "moved", "dragged", "locked", "wheeled", "id", "onDelta", "x", "y", "index", "_vx", "_vy", "requestAnimationFrame", "onTouchOrPointerDelta", "axis", "_onDrag", "clientX", "clientY", "isDragging", "startX", "startY", "_onRelease", "ownerDoc", "wasDragging", "eventData", "delayedCall", "defaultPrevented", "click", "createEvent", "syntheticEvent", "initMouseEvent", "screenX", "screenY", "dispatchEvent", "isGesturing", "onStopDelayedCall", "restart", "_onGestureStart", "touches", "_onGestureEnd", "onScroll", "scrollFuncX", "scrollFuncY", "scrollX", "scrollY", "_onWheel", "multiplier", "deltaMode", "innerHeight", "_onMove", "_onHover", "_onHoverEnd", "_onClick", "parseFloat", "getComputedStyle", "this", "isViewport", "ownerDocument", "_onPress", "pause", "_dc", "onStopFunc", "enable", "isEnabled", "disable", "filter", "o", "kill", "splice", "version", "create", "register", "getAll", "slice", "getById"], "mappings": ";;;;;;;;;mYAYY,SAAXA,WAAiBC,IAA4B,oBAAZC,SAA4BD,GAAOC,OAAOD,OAASA,GAAKE,gBAAkBF,OADxGA,GAAMG,GAAsBC,GAAMC,GAAMC,GAAQC,GAAOC,GAAUC,GAAcC,GAAeC,EAAOC,GAAaC,GAGrHC,EAAW,EACXC,GAAa,GACbC,aAAa,GACbC,WAAW,GAcK,SAAhBC,EAAiBC,EAASC,UAAcH,WAASI,QAAQF,IAAYF,WAASA,WAASI,QAAQF,GAAW,GAAGC,GAC/F,SAAdE,EAAcC,YAASZ,EAAMU,QAAQE,GACtB,SAAfC,EAAgBL,EAASM,EAAMC,EAAMC,EAAYC,UAAYT,EAAQU,iBAAiBJ,EAAMC,EAAM,CAACI,SAAUH,EAAYC,UAAWA,IAClH,SAAlBG,EAAmBZ,EAASM,EAAMC,EAAME,UAAYT,EAAQa,oBAAoBP,EAAMC,IAAQE,GAGlF,SAAZK,WAAmBrB,IAAeA,GAAYsB,WAAclB,aAAWmB,QACpD,SAAnBC,EAAoBC,EAAGC,GACJ,SAAdC,GAAcC,MACbA,GAAmB,IAAVA,EAAa,CACzB1B,IAAaV,GAAKqC,QAAQC,kBAAoB,cAC1CC,EAAgB/B,IAAeA,GAAYsB,UAC/CM,EAAQD,GAAYK,EAAIC,KAAKC,MAAMN,KAAW5B,IAAeA,GAAYmC,IAAM,EAAI,GACnFV,EAAEG,GACFD,GAAYS,QAAUhC,aAAWmB,MACjCQ,GAAiBM,EAAQ,KAAMT,QACrBF,GAActB,aAAWmB,QAAUI,GAAYS,SAAWC,EAAQ,UAC5EV,GAAYS,QAAUhC,aAAWmB,MACjCI,GAAYK,EAAIP,YAEVE,GAAYK,EAAIL,GAAYW,cAEpCX,GAAYW,OAAS,EACdb,GAAKE,GAIA,SAAbY,EAAaC,UAAKpD,GAAKqD,MAAMC,QAAQF,GAAG,KAAqB,iBAAPA,IAAoD,IAAjCpD,GAAKuD,SAASC,eAA2BC,QAAQC,KAAK,qBAAsBN,GAAK,MAEzI,SAAjBO,EAAkBxC,SAAUyC,IAAAA,EAAGC,IAAAA,GAC9BvC,EAAYH,KAAaA,EAAUd,GAAKyD,kBAAoBxD,QACxDyD,EAAI/C,aAAWK,QAAQF,GAC1B+B,EAASW,IAAOG,GAAUH,GAAK,EAAI,GAClCE,IAAMA,EAAI/C,aAAWiD,KAAK9C,GAAW,GACvCH,aAAW+C,EAAIb,IAAW/B,EAAQU,iBAAiB,SAAUI,OACzDiC,EAAOlD,aAAW+C,EAAIb,GACzBxB,EAAOwC,IAASlD,aAAW+C,EAAIb,GAAUd,EAAiBlB,EAAcC,EAASyC,IAAI,KAAUtC,EAAYH,GAAW0C,EAAKzB,EAAiB,SAASI,UAAgB2B,UAAUC,OAAUjD,EAAQyC,GAAKpB,EAASrB,EAAQyC,cACxNlC,EAAK2C,OAASlD,EACd+C,IAASxC,EAAK4C,OAAyD,WAAhDtE,GAAKuE,YAAYpD,EAAS,mBAC1CO,EAEW,SAAnB8C,EAAoBhC,EAAOiC,EAAgBC,GAOhC,SAATC,GAAUnC,EAAOoC,OACZxB,EAAIyB,KACJD,GAAkBE,EAAT1B,EAAI2B,GAChBC,EAAKC,EACLA,EAAKzC,EACL0C,EAAKH,EACLA,EAAK3B,GACKsB,EACVO,GAAMzC,EAENyC,EAAKD,GAAMxC,EAAQwC,IAAO5B,EAAI8B,IAAOH,EAAKG,OAhBzCD,EAAKzC,EACRwC,EAAKxC,EACLuC,EAAKF,KACLK,EAAKH,EACLD,EAAML,GAAkB,GACxBU,EAAiBtC,KAAKuC,IAAI,IAAW,EAANN,SAsBzB,CAACH,OAAAA,GAAQU,MARP,SAARA,QAAgBL,EAAKC,EAAKP,EAAW,EAAIO,EAAIC,EAAKH,EAAK,GAQjCO,YAPR,SAAdA,YAAcC,OACTC,EAAON,EACVO,EAAOT,EACP5B,EAAIyB,YACJU,GAA+B,IAAhBA,GAAsBA,IAAgBN,GAAMN,GAAOY,GAC3DR,IAAOG,GAAeC,EAAT/B,EAAI8B,EAAuB,GAAKD,GAAMP,EAAWe,GAAQA,MAAWf,EAAWtB,EAAI2B,GAAMS,GAAQ,MAI7G,SAAZE,EAAaC,EAAGC,UACfA,IAAmBD,EAAEE,YAAcF,EAAEC,iBAC9BD,EAAEG,eAAiBH,EAAEG,eAAe,GAAKH,EAE/B,SAAlBI,EAAkBC,OACbZ,EAAMvC,KAAKuC,UAALvC,KAAYmD,GACrBlB,EAAMjC,KAAKiC,UAALjC,KAAYmD,UACZnD,KAAKoD,IAAIb,IAAQvC,KAAKoD,IAAInB,GAAOM,EAAMN,EAE3B,SAApBoB,KACCxF,GAAgBV,GAAKmG,KAAKC,UAAU1F,gBACnBA,GAAcyF,MA7FnB,SAAbE,iBACKF,EAAOzF,GAAcyF,KACxBG,EAAOH,EAAKI,QAAU,GACtBC,EAAYL,EAAKnF,WACjByF,EAAUN,EAAKlF,SAChBuF,EAAUvC,WAAVuC,EAAkBxF,cAClByF,EAAQxC,WAARwC,EAAgBxF,YAChBD,aAAawF,EACbvF,WAAWwF,EACXxD,EAAU,iBAACyD,EAAMlE,UAAU8D,EAAKI,GAAMlE,IAoFC6D,GAE5B,SAAZM,EAAYR,UACXnG,GAAOmG,GAAQpG,MACkB,oBAAd6G,UAA6BA,SAASC,OACxDzG,GAAOH,OAEPK,IADAD,GAAOuG,UACOE,gBACdvG,GAAQF,GAAKwG,KACblG,EAAQ,CAACP,GAAMC,GAAMC,GAAQC,IACpBP,GAAKqD,MAAM0D,MACpBtG,GAAe,mBAAoBF,GAAQ,UAAY,QAEvDC,GAAWwG,EAASC,QAAU7G,GAAK8G,YAAc9G,GAAK8G,WAAW,oCAAoCC,QAAU,EAAK,iBAAkB/G,IAAmC,EAA3BgH,UAAUC,gBAAmD,EAA7BD,UAAUE,iBAAwB,EAAI,EACpNzG,GAAcmG,EAASO,YAAc,iBAAkBjH,GAAS,4CAAgD,kBAAmBA,GAAkD,kDAAxC,uCAA2FkH,MAAM,KAC9OC,WAAW,kBAAM3G,EAAW,GAAG,KAC/BoF,IACA/F,GAAe,GAETA,OAlHR0E,GAAW6C,KAAKC,IAChB1E,EAAU,iBAACyD,EAAMlE,UAAUA,GAgB3BoF,EAAc,aACdC,EAAa,YAoBbC,GAAc,CAAClE,EAAGgE,EAAaG,EAAG,OAAQC,GAAI,OAAQC,GAAI,QAASC,IAAK,QAASC,EAAG,QAASC,GAAI,QAASpC,EAAG,IAAKnC,GAAIzB,EAAiB,SAASI,UAAgB2B,UAAUC,OAAShE,GAAKiI,SAAS7F,EAAOwB,GAAUH,MAAQzD,GAAKkI,aAAejI,GAAKuH,IAAgBtH,GAAOsH,IAAgBrH,GAAMqH,IAAgB,KAChT5D,GAAY,CAACJ,EAAGiE,EAAYE,EAAG,MAAOC,GAAI,MAAOC,GAAI,SAAUC,IAAK,SAAUC,EAAG,SAAUC,GAAI,SAAUpC,EAAG,IAAKuC,GAAIT,GAAajE,GAAIzB,EAAiB,SAASI,UAAgB2B,UAAUC,OAAShE,GAAKiI,SAASP,GAAYjE,KAAMrB,GAASpC,GAAKoI,aAAenI,GAAKwH,IAAevH,GAAOuH,IAAetH,GAAMsH,IAAe,KA8EhUC,GAAYS,GAAKvE,gBACN7B,MAAQ,MAEN6E,sBAKZyB,KAAA,cAAKC,GACJvI,IAAgBwG,EAAU3G,KAASyD,QAAQC,KAAK,wCAChDhD,IAAiBwF,QACZyC,EAA6bD,EAA7bC,UAAWC,EAAkbF,EAAlbE,YAAanH,EAAqaiH,EAArajH,KAAM4C,EAA+ZqE,EAA/ZrE,OAAQwE,EAAuZH,EAAvZG,WAAYC,EAA2YJ,EAA3YI,SAAUlD,EAAiY8C,EAAjY9C,eAAgBmD,EAAiXL,EAAjXK,OAAQC,EAAyWN,EAAzWM,YAAaC,EAA4VP,EAA5VO,OAAQC,EAAoVR,EAApVQ,WAAYC,EAAwUT,EAAxUS,MAAOC,EAAiUV,EAAjUU,YAAaC,EAAoTX,EAApTW,UAAWC,EAAySZ,EAAzSY,OAAQC,EAAiSb,EAAjSa,QAASC,EAAwRd,EAAxRc,UAAWC,EAA6Qf,EAA7Qe,QAASC,EAAoQhB,EAApQgB,OAAQC,EAA4PjB,EAA5PiB,KAAMC,EAAsPlB,EAAtPkB,OAAQC,EAA8OnB,EAA9OmB,UAAWC,EAAmOpB,EAAnOoB,UAAWC,EAAwNrB,EAAxNqB,SAAUC,EAA8MtB,EAA9MsB,UAAWC,EAAmMvB,EAAnMuB,UAAWC,EAAwLxB,EAAxLwB,QAASC,EAA+KzB,EAA/KyB,WAAYC,EAAmK1B,EAAnK0B,OAAQC,EAA2J3B,EAA3J2B,YAAaC,EAA8I5B,EAA9I4B,aAAcC,EAAgI7B,EAAhI6B,eAAgBC,EAAgH9B,EAAhH8B,aAAcC,EAAkG/B,EAAlG+B,QAASC,EAAyFhC,EAAzFgC,SAAUC,EAA+EjC,EAA/EiC,UAAWC,EAAoElC,EAApEkC,QAASC,EAA2DnC,EAA3DmC,YAAajJ,EAA8C8G,EAA9C9G,QAASkJ,EAAqCpC,EAArCoC,YAAaC,EAAwBrC,EAAxBqC,SAAUC,EAActC,EAAdsC,WAyBpa,SAAfC,YAAqBC,GAAcrG,KACpB,SAAfsG,GAAgBxF,EAAGyF,UAAsBC,GAAKlC,MAAQxD,IAAOsD,IAAWA,EAAO5H,QAAQsE,EAAEtB,SAAa+G,GAAoBE,IAAkC,UAAlB3F,EAAE4F,aAA6BlB,GAAeA,EAAY1E,EAAGyF,GAO9L,SAATzG,SACK6G,EAAKH,GAAKI,OAAS1F,EAAgB0F,IACtCC,EAAKL,GAAKM,OAAS5F,EAAgB4F,IACnCC,EAAW/I,KAAKoD,IAAIuF,IAAO7C,EAC3BkD,EAAWhJ,KAAKoD,IAAIyF,IAAO/C,EAC5BoB,IAAa6B,GAAYC,IAAa9B,EAASsB,GAAMG,EAAIE,EAAID,GAAQE,IACjEC,IACHnC,GAAyB,EAAd4B,GAAKI,QAAchC,EAAQ4B,IACtC3B,GAAU2B,GAAKI,OAAS,GAAK/B,EAAO2B,IACpCxB,GAAaA,EAAUwB,IACvBrB,GAAeqB,GAAKI,OAAS,GAAQK,GAAa,GAAO9B,EAAUqB,IACnES,GAAaT,GAAKI,OAClBA,GAAO,GAAKA,GAAO,GAAKA,GAAO,GAAK,GAEjCI,IACHjC,GAAwB,EAAdyB,GAAKM,QAAc/B,EAAOyB,IACpC1B,GAAQ0B,GAAKM,OAAS,GAAKhC,EAAK0B,IAChCvB,GAAaA,EAAUuB,IACvBpB,GAAeoB,GAAKM,OAAS,GAAQI,GAAa,GAAO9B,EAAUoB,IACnEU,GAAaV,GAAKM,OAClBA,GAAO,GAAKA,GAAO,GAAKA,GAAO,GAAK,IAEjCK,IAASC,MACZ7B,GAAUA,EAAOiB,IACbY,KACH3C,EAAO+B,IACPY,IAAU,GAEXD,IAAQ,GAETE,MAAYA,IAAS,IAAUlB,GAAcA,EAAWK,IACpDc,KACH1B,EAAQY,IACRc,IAAU,GAEXC,GAAK,EAEI,SAAVC,GAAWC,EAAGC,EAAGC,GAChBf,GAAOe,IAAUF,EACjBX,GAAOa,IAAUD,EACjBlB,GAAKoB,IAAI9H,OAAO2H,GAChBjB,GAAKqB,IAAI/H,OAAO4H,GAChBzD,EAAkBsD,GAAPA,IAAYO,sBAAsBhI,IAAWA,KAEjC,SAAxBiI,GAAyBN,EAAGC,GACvBxB,IAAa8B,KAChBxB,GAAKwB,KAAOA,GAAOhK,KAAKoD,IAAIqG,GAAKzJ,KAAKoD,IAAIsG,GAAK,IAAM,IACrDL,IAAS,GAEG,MAATW,KACHpB,GAAO,IAAMa,EACbjB,GAAKoB,IAAI9H,OAAO2H,GAAG,IAEP,MAATO,KACHlB,GAAO,IAAMY,EACblB,GAAKqB,IAAI/H,OAAO4H,GAAG,IAEpBzD,EAAkBsD,GAAPA,IAAYO,sBAAsBhI,IAAWA,KAE/C,SAAVmI,GAAUnH,OACLwF,GAAaxF,EAAG,QAEhB2G,GADJ3G,EAAID,EAAUC,EAAGC,IACPmH,QACTR,EAAI5G,EAAEqH,QACNxB,EAAKc,EAAIjB,GAAKiB,EACdZ,EAAKa,EAAIlB,GAAKkB,EACdU,EAAa5B,GAAK4B,WACnB5B,GAAKiB,EAAIA,EACTjB,GAAKkB,EAAIA,GACLU,GAAcpK,KAAKoD,IAAIoF,GAAK6B,OAASZ,IAAM1D,GAAe/F,KAAKoD,IAAIoF,GAAK8B,OAASZ,IAAM3D,KAC1FU,IAAW2C,IAAU,GACrBgB,IAAe5B,GAAK4B,YAAa,GACjCL,GAAsBpB,EAAIE,GAC1BuB,GAAc7D,GAAeA,EAAYiC,MAkB9B,SAAb+B,GAAazH,OACRwF,GAAaxF,EAAG,IACpB5D,EAAgBuI,EAAejG,EAASgJ,GAAUxM,GAAY,GAAIiM,IAAS,OACvEQ,EAAcjC,GAAK4B,aAAgD,EAAjCpK,KAAKoD,IAAIoF,GAAKiB,EAAIjB,GAAK6B,SAAgD,EAAjCrK,KAAKoD,IAAIoF,GAAKkB,EAAIlB,GAAK8B,SAClGI,EAAY7H,EAAUC,GAClB2H,IACJjC,GAAKoB,IAAIpH,QACTgG,GAAKqB,IAAIrH,QACLO,GAAkBkF,GACrB9K,GAAKwN,YAAY,IAAM,cACS,IAA3B3I,KAAaqG,KAAsBvF,EAAE8H,oBACpC9H,EAAEtB,OAAOqJ,MACZ/H,EAAEtB,OAAOqJ,aACH,GAAIL,GAASM,YAAa,KAC5BC,EAAiBP,GAASM,YAAY,eAC1CC,EAAeC,eAAe,SAAS,GAAM,EAAMzN,GAAM,EAAGmN,EAAUO,QAASP,EAAUQ,QAASR,EAAUR,QAASQ,EAAUP,SAAS,GAAO,GAAO,GAAO,EAAO,EAAG,MACvKrH,EAAEtB,OAAO2J,cAAcJ,OAM5BvC,GAAK4B,WAAa5B,GAAK4C,YAAc5C,GAAKnJ,WAAY,EACtD6G,IAAWuB,GAAgB4D,GAAkBC,SAAQ,GACrD9E,GAAaiE,GAAejE,EAAUgC,IACtC7B,GAAaA,EAAU6B,GAAMiC,IAEZ,SAAlBc,GAAkBzI,UAAKA,EAAE0I,SAA8B,EAAnB1I,EAAE0I,QAAQjK,SAAeiH,GAAK4C,aAAc,IAAS1D,EAAe5E,EAAG0F,GAAK4B,YAChG,SAAhBqB,YAAuBjD,GAAK4C,aAAc,IAAUzD,EAAaa,IACtD,SAAXkD,GAAW5I,OACNwF,GAAaxF,QACb2G,EAAIkC,KACPjC,EAAIkC,KACLpC,IAASC,EAAIoC,IAAW7D,GAAc0B,EAAIoC,IAAW9D,EAAa,GAClE6D,GAAUpC,EACVqC,GAAUpC,EACVxD,GAAUmF,GAAkBC,SAAQ,IAE1B,SAAXS,GAAWjJ,OACNwF,GAAaxF,IACjBA,EAAID,EAAUC,EAAGC,GACjB6E,IAAY0B,IAAU,OAClB0C,GAA8B,IAAhBlJ,EAAEmJ,UAAkBjG,EAA6B,IAAhBlD,EAAEmJ,UAAkB1O,GAAK2O,YAAc,GAAK7F,EAC/FmD,GAAQ1G,EAAE8F,OAASoD,EAAYlJ,EAAEgG,OAASkD,EAAY,GACtD9F,IAAWuB,GAAgB4D,GAAkBC,SAAQ,IAE5C,SAAVa,GAAUrJ,OACLwF,GAAaxF,QACb2G,EAAI3G,EAAEoH,QACTR,EAAI5G,EAAEqH,QACNxB,EAAKc,EAAIjB,GAAKiB,EACdZ,EAAKa,EAAIlB,GAAKkB,EACflB,GAAKiB,EAAIA,EACTjB,GAAKkB,EAAIA,EACTP,IAAQ,GACPR,GAAME,IAAOkB,GAAsBpB,EAAIE,IAE9B,SAAXuD,GAAWtJ,GAAM0F,GAAKlC,MAAQxD,EAAGuE,EAAQmB,IAC3B,SAAd6D,GAAcvJ,GAAM0F,GAAKlC,MAAQxD,EAAGwE,EAAWkB,IACpC,SAAX8D,GAAWxJ,UAAKwF,GAAaxF,IAAOD,EAAUC,EAAGC,IAAmBgF,EAAQS,SAtLxEhH,OAASA,EAASlB,EAAWkB,IAAW/D,QACxCoI,KAAOA,EACDO,EAAXA,GAAoBjJ,GAAKqD,MAAMC,QAAQ2F,GACvCN,EAAYA,GAAa,KACzBC,EAAcA,GAAe,EAC7BM,EAAaA,GAAc,EAC3B2B,EAAcA,GAAe,EAC7BpJ,EAAOA,GAAQ,sBACfqH,GAAwB,IAAbA,EACID,EAAfA,GAA4BuG,WAAWhP,GAAKiP,iBAAiB9O,IAAOsI,aAAe,OAC/EuD,GAAI8B,GAAmBjC,GAASD,GAAOG,GAASD,GAAQW,GAC3DxB,GAAOiE,KACPxD,GAAa,EACbC,GAAa,EACbyC,GAAc7K,EAAeU,EAAQyD,IACrC2G,GAAc9K,EAAeU,EAAQL,IACrC0K,GAAUF,KACVG,GAAUF,KACVnD,IAAgB7J,EAAKJ,QAAQ,YAAcI,EAAKJ,QAAQ,YAAiC,gBAAnBR,GAAY,GAClF0O,GAAajO,EAAY+C,GACzBgJ,GAAWhJ,EAAOmL,eAAiBnP,GACnCoL,GAAS,CAAC,EAAG,EAAG,GAChBE,GAAS,CAAC,EAAG,EAAG,GAChBT,GAAc,EAqFduE,GAAWpE,GAAK9B,QAAU,SAAA5D,GACrBwF,GAAaxF,EAAG,KACpB0F,GAAKwB,KAAOA,GAAO,KACnBqB,GAAkBwB,QAClBrE,GAAKnJ,WAAY,EACjByD,EAAID,EAAUC,GACdmG,GAAaC,GAAa,EAC1BV,GAAK6B,OAAS7B,GAAKiB,EAAI3G,EAAEoH,QACzB1B,GAAK8B,OAAS9B,GAAKkB,EAAI5G,EAAEqH,QACzB3B,GAAKoB,IAAIpH,QACTgG,GAAKqB,IAAIrH,QACT7D,EAAa8I,EAAejG,EAASgJ,GAAUxM,GAAY,GAAIiM,GAASlH,GAAgB,GACxFyF,GAAKI,OAASJ,GAAKM,OAAS,EAC5BpC,GAAWA,EAAQ8B,MA+DrB6C,GAAoB7C,GAAKsE,IAAM3P,GAAKwN,YAAYxE,GAAe,IA9JjD,SAAb4G,aACCvE,GAAKoB,IAAIpH,QACTgG,GAAKqB,IAAIrH,QACT6I,GAAkBwB,QAClB3G,GAAUA,EAAOsC,MA0J8DqE,QAEjFrE,GAAKI,OAASJ,GAAKM,OAAS,EAC5BN,GAAKoB,IAAMjI,EAAiB,EAAG,IAAI,GACnC6G,GAAKqB,IAAMlI,EAAiB,EAAG,IAAI,GACnC6G,GAAKqD,QAAUF,GACfnD,GAAKsD,QAAUF,GACfpD,GAAK4B,WAAa5B,GAAK4C,YAAc5C,GAAKnJ,WAAY,EACtDmJ,GAAKwE,OAAS,SAAAlK,UACR0F,GAAKyE,YACTtO,EAAa+N,GAAalC,GAAWhJ,EAAQ,SAAUpC,GAC7B,GAA1BR,EAAKJ,QAAQ,WAAkBG,EAAa+N,GAAalC,GAAWhJ,EAAQ,SAAUkK,GAAU3I,EAAgBhE,GACvF,GAAzBH,EAAKJ,QAAQ,UAAiBG,EAAa6C,EAAQ,QAASuK,GAAUhJ,EAAgBhE,IACxD,GAAzBH,EAAKJ,QAAQ,UAAiBb,IAAwC,GAA3BiB,EAAKJ,QAAQ,cAC5DG,EAAa6C,EAAQxD,GAAY,GAAI4O,GAAU7J,EAAgBhE,GAC/DJ,EAAa6L,GAAUxM,GAAY,GAAIuM,IACvC5L,EAAa6L,GAAUxM,GAAY,GAAIuM,IACvCtC,GAAetJ,EAAa6C,EAAQ,QAAS4G,IAAc,GAAO,GAClEL,GAAWpJ,EAAa6C,EAAQ,QAAS8K,IACzC5E,GAAkB/I,EAAa6L,GAAU,eAAgBe,IACzD5D,GAAgBhJ,EAAa6L,GAAU,aAAciB,IACrDpE,GAAW1I,EAAa6C,EAAQ5D,GAAe,QAASwO,IACxD9E,GAAc3I,EAAa6C,EAAQ5D,GAAe,QAASyO,IAC3D9E,GAAU5I,EAAa6C,EAAQ5D,GAAe,OAAQuO,KAEvD3D,GAAKyE,WAAY,EACjBnK,GAAKA,EAAElE,MAAQgO,GAAS9J,GACxB+E,GAAYA,EAASW,KAEfA,IAERA,GAAK0E,QAAU,WACV1E,GAAKyE,YAER/O,GAAWiP,OAAO,SAAAC,UAAKA,IAAM5E,IAAQ/J,EAAY2O,EAAE5L,UAASD,QAAUrC,EAAgBwN,GAAalC,GAAWhJ,EAAQ,SAAUpC,GAC5HoJ,GAAKnJ,YACRmJ,GAAKoB,IAAIpH,QACTgG,GAAKqB,IAAIrH,QACTtD,EAAgBuI,EAAejG,EAASgJ,GAAUxM,GAAY,GAAIiM,IAAS,IAE5E/K,EAAgBwN,GAAalC,GAAWhJ,EAAQ,SAAUkK,GAAU3M,GACpEG,EAAgBsC,EAAQ,QAASuK,GAAUhN,GAC3CG,EAAgBsC,EAAQxD,GAAY,GAAI4O,GAAU7N,GAClDG,EAAgBsL,GAAUxM,GAAY,GAAIuM,IAC1CrL,EAAgBsL,GAAUxM,GAAY,GAAIuM,IAC1CrL,EAAgBsC,EAAQ,QAAS4G,IAAc,GAC/ClJ,EAAgBsC,EAAQ,QAAS8K,IACjCpN,EAAgBsL,GAAU,eAAgBe,IAC1CrM,EAAgBsL,GAAU,aAAciB,IACxCvM,EAAgBsC,EAAQ5D,GAAe,QAASwO,IAChDlN,EAAgBsC,EAAQ5D,GAAe,QAASyO,IAChDnN,EAAgBsC,EAAQ5D,GAAe,OAAQuO,IAC/C3D,GAAKyE,UAAYzE,GAAKnJ,UAAYmJ,GAAK4B,YAAa,EACpDtC,GAAaA,EAAUU,MAIzBA,GAAK6E,KAAO,WACX7E,GAAK0E,cACDhM,EAAIhD,GAAWM,QAAQgK,IACtB,GAALtH,GAAUhD,GAAWoP,OAAOpM,EAAG,GAC/BnD,KAAgByK,KAASzK,GAAc,IAGxCG,GAAWkD,KAAKoH,IAChBf,GAAgBhJ,EAAY+C,KAAYzD,GAAcyK,IAEtDA,GAAKwE,OAAO1G,8JAILmG,KAAK7C,IAAInH,2DAGTgK,KAAK5C,IAAIpH,8CA1QLoD,QACND,KAAKC,GA8QZ1B,EAASoJ,QAAU,SACnBpJ,EAASqJ,OAAS,SAAA3H,UAAQ,IAAI1B,EAAS0B,IACvC1B,EAASsJ,SAAW3J,EACpBK,EAASuJ,OAAS,kBAAMxP,GAAWyP,SACnCxJ,EAASyJ,QAAU,SAAArE,UAAMrL,GAAWiP,OAAO,SAAAC,UAAKA,EAAEvH,KAAK0D,KAAOA,IAAI,IAElErM,KAAcC,GAAKE,eAAe8G"}