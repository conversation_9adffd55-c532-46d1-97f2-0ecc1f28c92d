{"version": 3, "file": "Flip.min.js", "sources": ["../src/utils/matrix.js", "../src/Flip.js"], "sourcesContent": ["/*!\n * matrix 3.11.3\n * https://greensock.com\n *\n * Copyright 2008-2022, GreenSock. All rights reserved.\n * Subject to the terms at https://greensock.com/standard-license or for\n * Club GreenSock members, the agreement issued with that membership.\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nlet _doc, _win, _docElement, _body,\t_div<PERSON><PERSON>r, _svg<PERSON><PERSON>r, _identityMatrix, _gEl,\n\t_transformProp = \"transform\",\n\t_transformOriginProp = _transformProp + \"Origin\",\n\t_hasOffsetBug,\n\t_setDoc = element => {\n\t\tlet doc = element.ownerDocument || element;\n\t\tif (!(_transformProp in element.style) && \"msTransform\" in element.style) { //to improve compatibility with old Microsoft browsers\n\t\t\t_transformProp = \"msTransform\";\n\t\t\t_transformOriginProp = _transformProp + \"Origin\";\n\t\t}\n\t\twhile (doc.parentNode && (doc = doc.parentNode)) {\t}\n\t\t_win = window;\n\t\t_identityMatrix = new Matrix2D();\n\t\tif (doc) {\n\t\t\t_doc = doc;\n\t\t\t_docElement = doc.documentElement;\n\t\t\t_body = doc.body;\n\t\t\t_gEl = _doc.createElementNS(\"http://www.w3.org/2000/svg\", \"g\");\n\t\t\t// prevent any existing CSS from transforming it\n\t\t\t_gEl.style.transform = \"none\";\n\t\t\t// now test for the offset reporting bug. Use feature detection instead of browser sniffing to make things more bulletproof and future-proof. Hopefully Safari will fix their bug soon but it's 2020 and it's still not fixed.\n\t\t\tlet d1 = doc.createElement(\"div\"),\n\t\t\t\td2 = doc.createElement(\"div\");\n\t\t\t_body.appendChild(d1);\n\t\t\td1.appendChild(d2);\n\t\t\td1.style.position = \"static\";\n\t\t\td1.style[_transformProp] = \"translate3d(0,0,1px)\";\n\t\t\t_hasOffsetBug = (d2.offsetParent !== d1);\n\t\t\t_body.removeChild(d1);\n\t\t}\n\t\treturn doc;\n\t},\n\t_forceNonZeroScale = e => { // walks up the element's ancestors and finds any that had their scale set to 0 via GSAP, and changes them to 0.0001 to ensure that measurements work. Firefox has a bug that causes it to incorrectly report getBoundingClientRect() when scale is 0.\n\t\tlet a, cache;\n\t\twhile (e && e !== _body) {\n\t\t\tcache = e._gsap;\n\t\t\tcache && cache.uncache && cache.get(e, \"x\"); // force re-parsing of transforms if necessary\n\t\t\tif (cache && !cache.scaleX && !cache.scaleY && cache.renderTransform) {\n\t\t\t\tcache.scaleX = cache.scaleY = 1e-4;\n\t\t\t\tcache.renderTransform(1, cache);\n\t\t\t\ta ? a.push(cache) : (a = [cache]);\n\t\t\t}\n\t\t\te = e.parentNode;\n\t\t}\n\t\treturn a;\n\t},\n\t// possible future addition: pass an element to _forceDisplay() and it'll walk up all its ancestors and make sure anything with display: none is set to display: block, and if there's no parentNode, it'll add it to the body. It returns an Array that you can then feed to _revertDisplay() to have it revert all the changes it made.\n\t// _forceDisplay = e => {\n\t// \tlet a = [],\n\t// \t\tparent;\n\t// \twhile (e && e !== _body) {\n\t// \t\tparent = e.parentNode;\n\t// \t\t(_win.getComputedStyle(e).display === \"none\" || !parent) && a.push(e, e.style.display, parent) && (e.style.display = \"block\");\n\t// \t\tparent || _body.appendChild(e);\n\t// \t\te = parent;\n\t// \t}\n\t// \treturn a;\n\t// },\n\t// _revertDisplay = a => {\n\t// \tfor (let i = 0; i < a.length; i+=3) {\n\t// \t\ta[i+1] ? (a[i].style.display = a[i+1]) : a[i].style.removeProperty(\"display\");\n\t// \t\ta[i+2] || a[i].parentNode.removeChild(a[i]);\n\t// \t}\n\t// },\n\t_svgTemps = [], //we create 3 elements for SVG, and 3 for other DOM elements and cache them for performance reasons. They get nested in _divContainer and _svgContainer so that just one element is added to the DOM on each successive attempt. Again, performance is key.\n\t_divTemps = [],\n\t_getDocScrollTop = () => _win.pageYOffset  || _doc.scrollTop || _docElement.scrollTop || _body.scrollTop || 0,\n\t_getDocScrollLeft = () => _win.pageXOffset || _doc.scrollLeft || _docElement.scrollLeft || _body.scrollLeft || 0,\n\t_svgOwner = element => element.ownerSVGElement || ((element.tagName + \"\").toLowerCase() === \"svg\" ? element : null),\n\t_isFixed = element => {\n\t\tif (_win.getComputedStyle(element).position === \"fixed\") {\n\t\t\treturn true;\n\t\t}\n\t\telement = element.parentNode;\n\t\tif (element && element.nodeType === 1) { // avoid document fragments which will throw an error.\n\t\t\treturn _isFixed(element);\n\t\t}\n\t},\n\t_createSibling = (element, i) => {\n\t\tif (element.parentNode && (_doc || _setDoc(element))) {\n\t\t\tlet svg = _svgOwner(element),\n\t\t\t\tns = svg ? (svg.getAttribute(\"xmlns\") || \"http://www.w3.org/2000/svg\") : \"http://www.w3.org/1999/xhtml\",\n\t\t\t\ttype = svg ? (i ? \"rect\" : \"g\") : \"div\",\n\t\t\t\tx = i !== 2 ? 0 : 100,\n\t\t\t\ty = i === 3 ? 100 : 0,\n\t\t\t\tcss = \"position:absolute;display:block;pointer-events:none;margin:0;padding:0;\",\n\t\t\t\te = _doc.createElementNS ? _doc.createElementNS(ns.replace(/^https/, \"http\"), type) : _doc.createElement(type);\n\t\t\tif (i) {\n\t\t\t\tif (!svg) {\n\t\t\t\t\tif (!_divContainer) {\n\t\t\t\t\t\t_divContainer = _createSibling(element);\n\t\t\t\t\t\t_divContainer.style.cssText = css;\n\t\t\t\t\t}\n\t\t\t\t\te.style.cssText = css + \"width:0.1px;height:0.1px;top:\" + y + \"px;left:\" + x + \"px\";\n\t\t\t\t\t_divContainer.appendChild(e);\n\n\t\t\t\t} else {\n\t\t\t\t\t_svgContainer || (_svgContainer = _createSibling(element));\n\t\t\t\t\te.setAttribute(\"width\", 0.01);\n\t\t\t\t\te.setAttribute(\"height\", 0.01);\n\t\t\t\t\te.setAttribute(\"transform\", \"translate(\" + x + \",\" + y + \")\");\n\t\t\t\t\t_svgContainer.appendChild(e);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn e;\n\t\t}\n\t\tthrow \"Need document and parent.\";\n\t},\n\t_consolidate = m => { // replaces SVGTransformList.consolidate() because a bug in Firefox causes it to break pointer events. See https://greensock.com/forums/topic/23248-touch-is-not-working-on-draggable-in-firefox-windows-v324/?tab=comments#comment-109800\n\t\tlet c = new Matrix2D(),\n\t\t\ti = 0;\n\t\tfor (; i < m.numberOfItems; i++) {\n\t\t\tc.multiply(m.getItem(i).matrix);\n\t\t}\n\t\treturn c;\n\t},\n\t_getCTM = svg => {\n\t\tlet m = svg.getCTM(),\n\t\t\ttransform;\n\t\tif (!m) { // Firefox returns null for getCTM() on root <svg> elements, so this is a workaround using a <g> that we temporarily append.\n\t\t\ttransform = svg.style[_transformProp];\n\t\t\tsvg.style[_transformProp] = \"none\"; // a bug in Firefox causes css transforms to contaminate the getCTM()\n\t\t\tsvg.appendChild(_gEl);\n\t\t\tm = _gEl.getCTM();\n\t\t\tsvg.removeChild(_gEl);\n\t\t\ttransform ? (svg.style[_transformProp] = transform) : svg.style.removeProperty(_transformProp.replace(/([A-Z])/g, \"-$1\").toLowerCase());\n\t\t}\n\t\treturn m || _identityMatrix.clone(); // Firefox will still return null if the <svg> has a width/height of 0 in the browser.\n\t},\n\t_placeSiblings = (element, adjustGOffset) => {\n\t\tlet svg = _svgOwner(element),\n\t\t\tisRootSVG = element === svg,\n\t\t\tsiblings = svg ? _svgTemps : _divTemps,\n\t\t\tparent = element.parentNode,\n\t\t\tcontainer, m, b, x, y, cs;\n\t\tif (element === _win) {\n\t\t\treturn element;\n\t\t}\n\t\tsiblings.length || siblings.push(_createSibling(element, 1), _createSibling(element, 2), _createSibling(element, 3));\n\t\tcontainer = svg ? _svgContainer : _divContainer;\n\t\tif (svg) {\n\t\t\tif (isRootSVG) {\n\t\t\t\tb = _getCTM(element);\n\t\t\t\tx = -b.e / b.a;\n\t\t\t\ty = -b.f / b.d;\n\t\t\t\tm = _identityMatrix;\n\t\t\t} else if (element.getBBox) {\n\t\t\t\tb = element.getBBox();\n\t\t\t\tm = element.transform ? element.transform.baseVal : {}; // IE11 doesn't follow the spec.\n\t\t\t\tm = !m.numberOfItems ? _identityMatrix : m.numberOfItems > 1 ? _consolidate(m) : m.getItem(0).matrix; // don't call m.consolidate().matrix because a bug in Firefox makes pointer events not work when consolidate() is called on the same tick as getBoundingClientRect()! See https://greensock.com/forums/topic/23248-touch-is-not-working-on-draggable-in-firefox-windows-v324/?tab=comments#comment-109800\n\t\t\t\tx = m.a * b.x + m.c * b.y;\n\t\t\t\ty = m.b * b.x + m.d * b.y;\n\t\t\t} else { // may be a <mask> which has no getBBox() so just use defaults instead of throwing errors.\n\t\t\t\tm = new Matrix2D();\n\t\t\t\tx = y = 0;\n\t\t\t}\n\t\t\tif (adjustGOffset && element.tagName.toLowerCase() === \"g\") {\n\t\t\t\tx = y = 0;\n\t\t\t}\n\t\t\t(isRootSVG ? svg : parent).appendChild(container);\n\t\t\tcontainer.setAttribute(\"transform\", \"matrix(\" + m.a + \",\" + m.b + \",\" + m.c + \",\" + m.d + \",\" + (m.e + x) + \",\" + (m.f + y) + \")\");\n\t\t} else {\n\t\t\tx = y = 0;\n\t\t\tif (_hasOffsetBug) { // some browsers (like Safari) have a bug that causes them to misreport offset values. When an ancestor element has a transform applied, it's supposed to treat it as if it's position: relative (new context). Safari botches this, so we need to find the closest ancestor (between the element and its offsetParent) that has a transform applied and if one is found, grab its offsetTop/Left and subtract them to compensate.\n\t\t\t\tm = element.offsetParent;\n\t\t\t\tb = element;\n\t\t\t\twhile (b && (b = b.parentNode) && b !== m && b.parentNode) {\n\t\t\t\t\tif ((_win.getComputedStyle(b)[_transformProp] + \"\").length > 4) {\n\t\t\t\t\t\tx = b.offsetLeft;\n\t\t\t\t\t\ty = b.offsetTop;\n\t\t\t\t\t\tb = 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tcs = _win.getComputedStyle(element);\n\t\t\tif (cs.position !== \"absolute\" && cs.position !== \"fixed\") {\n\t\t\t\tm = element.offsetParent;\n\t\t\t\twhile (parent && parent !== m) { // if there's an ancestor element between the element and its offsetParent that's scrolled, we must factor that in.\n\t\t\t\t\tx += parent.scrollLeft || 0;\n\t\t\t\t\ty += parent.scrollTop || 0;\n\t\t\t\t\tparent = parent.parentNode;\n\t\t\t\t}\n\t\t\t}\n\t\t\tb = container.style;\n\t\t\tb.top = (element.offsetTop - y) + \"px\";\n\t\t\tb.left = (element.offsetLeft - x) + \"px\";\n\t\t\tb[_transformProp] = cs[_transformProp];\n\t\t\tb[_transformOriginProp] = cs[_transformOriginProp];\n\t\t\t// b.border = m.border;\n\t\t\t// b.borderLeftStyle = m.borderLeftStyle;\n\t\t\t// b.borderTopStyle = m.borderTopStyle;\n\t\t\t// b.borderLeftWidth = m.borderLeftWidth;\n\t\t\t// b.borderTopWidth = m.borderTopWidth;\n\t\t\tb.position = cs.position === \"fixed\" ? \"fixed\" : \"absolute\";\n\t\t\telement.parentNode.appendChild(container);\n\t\t}\n\t\treturn container;\n\t},\n\t_setMatrix = (m, a, b, c, d, e, f) => {\n\t\tm.a = a;\n\t\tm.b = b;\n\t\tm.c = c;\n\t\tm.d = d;\n\t\tm.e = e;\n\t\tm.f = f;\n\t\treturn m;\n\t};\n\nexport class Matrix2D {\n\tconstructor(a=1, b=0, c=0, d=1, e=0, f=0) {\n\t\t_setMatrix(this, a, b, c, d, e, f);\n\t}\n\n\tinverse() {\n\t\tlet {a, b, c, d, e, f} = this,\n\t\t\tdeterminant = (a * d - b * c) || 1e-10;\n\t\treturn _setMatrix(\n\t\t\tthis,\n\t\t\td / determinant,\n\t\t\t-b / determinant,\n\t\t\t-c / determinant,\n\t\t\ta / determinant,\n\t\t\t(c * f - d * e) / determinant,\n\t\t\t-(a * f - b * e) / determinant\n\t\t);\n\t}\n\n\tmultiply(matrix) {\n\t\tlet {a, b, c, d, e, f} = this,\n\t\t\ta2 = matrix.a,\n\t\t\tb2 = matrix.c,\n\t\t\tc2 = matrix.b,\n\t\t\td2 = matrix.d,\n\t\t\te2 = matrix.e,\n\t\t\tf2 = matrix.f;\n\t\treturn _setMatrix(this,\n\t\t\ta2 * a + c2 * c,\n\t\t\ta2 * b + c2 * d,\n\t\t\tb2 * a + d2 * c,\n\t\t\tb2 * b + d2 * d,\n\t\t\te + e2 * a + f2 * c,\n\t\t\tf + e2 * b + f2 * d);\n\t}\n\n\tclone() {\n\t\treturn new Matrix2D(this.a, this.b, this.c, this.d, this.e, this.f);\n\t}\n\n\tequals(matrix) {\n\t\tlet {a, b, c, d, e, f} = this;\n\t\treturn (a === matrix.a && b === matrix.b && c === matrix.c && d === matrix.d && e === matrix.e && f === matrix.f);\n\t}\n\n\tapply(point, decoratee={}) {\n\t\tlet {x, y} = point,\n\t\t\t{a, b, c, d, e, f} = this;\n\t\tdecoratee.x = (x * a + y * c + e) || 0;\n\t\tdecoratee.y = (x * b + y * d + f) || 0;\n\t\treturn decoratee;\n\t}\n\n}\n\n// Feed in an element and it'll return a 2D matrix (optionally inverted) so that you can translate between coordinate spaces.\n// Inverting lets you translate a global point into a local coordinate space. No inverting lets you go the other way.\n// We needed this to work around various browser bugs, like Firefox doesn't accurately report getScreenCTM() when there\n// are transforms applied to ancestor elements.\n// The matrix math to convert any x/y coordinate is as follows, which is wrapped in a convenient apply() method of Matrix2D above:\n//     tx = m.a * x + m.c * y + m.e\n//     ty = m.b * x + m.d * y + m.f\nexport function getGlobalMatrix(element, inverse, adjustGOffset, includeScrollInFixed) { // adjustGOffset is typically used only when grabbing an element's PARENT's global matrix, and it ignores the x/y offset of any SVG <g> elements because they behave in a special way.\n\tif (!element || !element.parentNode || (_doc || _setDoc(element)).documentElement === element) {\n\t\treturn new Matrix2D();\n\t}\n\tlet zeroScales = _forceNonZeroScale(element),\n\t\tsvg = _svgOwner(element),\n\t\ttemps = svg ? _svgTemps : _divTemps,\n\t\tcontainer = _placeSiblings(element, adjustGOffset),\n\t\tb1 = temps[0].getBoundingClientRect(),\n\t\tb2 = temps[1].getBoundingClientRect(),\n\t\tb3 = temps[2].getBoundingClientRect(),\n\t\tparent = container.parentNode,\n\t\tisFixed = !includeScrollInFixed && _isFixed(element),\n\t\tm = new Matrix2D(\n\t\t\t(b2.left - b1.left) / 100,\n\t\t\t(b2.top - b1.top) / 100,\n\t\t\t(b3.left - b1.left) / 100,\n\t\t\t(b3.top - b1.top) / 100,\n\t\t\tb1.left + (isFixed ? 0 : _getDocScrollLeft()),\n\t\t\tb1.top + (isFixed ? 0 : _getDocScrollTop())\n\t\t);\n\tparent.removeChild(container);\n\tif (zeroScales) {\n\t\tb1 = zeroScales.length;\n\t\twhile (b1--) {\n\t\t\tb2 = zeroScales[b1];\n\t\t\tb2.scaleX = b2.scaleY = 0;\n\t\t\tb2.renderTransform(1, b2);\n\t\t}\n\t}\n\treturn inverse ? m.inverse() : m;\n}\n\nexport { _getDocScrollTop, _getDocScrollLeft, _setDoc, _isFixed, _getCTM };\n\n// export function getMatrix(element) {\n// \t_doc || _setDoc(element);\n// \tlet m = (_win.getComputedStyle(element)[_transformProp] + \"\").substr(7).match(/[-.]*\\d+[.e\\-+]*\\d*[e\\-\\+]*\\d*/g),\n// \t\tis2D = m && m.length === 6;\n// \treturn !m || m.length < 6 ? new Matrix2D() : new Matrix2D(+m[0], +m[1], +m[is2D ? 2 : 4], +m[is2D ? 3 : 5], +m[is2D ? 4 : 12], +m[is2D ? 5 : 13]);\n// }", "/*!\n * Flip 3.11.3\n * https://greensock.com\n *\n * @license Copyright 2008-2022, GreenSock. All rights reserved.\n * Subject to the terms at https://greensock.com/standard-license or for\n * Club GreenSock members, the agreement issued with that membership.\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nimport { getGlobalMatrix, _getDocScrollTop, _getDocScrollLeft, Matrix2D, _setDoc, _getCTM } from \"./utils/matrix.js\";\n\nlet _id = 1,\n\t_toArray, gsap, _batch, _batchAction, _body, _closestTenth,\n\t_forEachBatch = (batch, name) => batch.actions.forEach(a => a.vars[name] && a.vars[name](a)),\n\t_batchLookup = {},\n\t_RAD2DEG = 180 / Math.PI,\n\t_DEG2RAD = Math.PI / 180,\n\t_emptyObj = {},\n\t_dashedNameLookup = {},\n\t_memoizedRemoveProps = {},\n\t_listToArray = list => typeof(list) === \"string\" ? list.split(\" \").join(\"\").split(\",\") : list, // removes extra spaces contaminating the names, returns an Array.\n\t_callbacks = _listToArray(\"onStart,onUpdate,onComplete,onReverseComplete,onInterrupt\"),\n\t_removeProps = _listToArray(\"transform,transformOrigin,width,height,position,top,left,opacity,zIndex,maxWidth,maxHeight,minWidth,minHeight\"),\n\t_getEl = target => _toArray(target)[0] || console.warn(\"Element not found:\", target),\n\t_round = value => Math.round(value * 10000) / 10000 || 0,\n\t_toggleClass = (targets, className, action) => targets.forEach(el => el.classList[action](className)),\n\t_reserved = {zIndex:1, kill:1, simple:1, spin:1, clearProps:1, targets:1, toggleClass:1, onComplete:1, onUpdate:1, onInterrupt:1, onStart:1, delay:1, repeat:1, repeatDelay:1, yoyo:1, scale:1, fade:1, absolute:1, props:1, onEnter:1, onLeave:1, custom:1, paused:1, nested:1, prune:1, absoluteOnLeave: 1},\n\t_fitReserved = {zIndex:1, simple:1, clearProps:1, scale:1, absolute:1, fitChild:1, getVars:1, props:1},\n\t_camelToDashed = p => p.replace(/([A-Z])/g, \"-$1\").toLowerCase(),\n\t_copy = (obj, exclude) => {\n\t\tlet result = {}, p;\n\t\tfor (p in obj) {\n\t\t\texclude[p] || (result[p] = obj[p]);\n\t\t}\n\t\treturn result;\n\t},\n\t_memoizedProps = {},\n\t_memoizeProps = props => {\n\t\tlet p = _memoizedProps[props] = _listToArray(props);\n\t\t_memoizedRemoveProps[props] = p.concat(_removeProps);\n\t\treturn p;\n\t},\n\t_getInverseGlobalMatrix = el => { // integrates caching for improved performance\n\t\tlet cache = el._gsap || gsap.core.getCache(el);\n\t\tif (cache.gmCache === gsap.ticker.frame) {\n\t\t\treturn cache.gMatrix;\n\t\t}\n\t\tcache.gmCache = gsap.ticker.frame;\n\t\treturn (cache.gMatrix = getGlobalMatrix(el, true, false, true));\n\t},\n\t_getDOMDepth = (el, invert, level = 0) => { // In invert is true, the sibling depth is increments of 1, and parent/nesting depth is increments of 1000. This lets us order elements in an Array to reflect document flow.\n\t\tlet parent = el.parentNode,\n\t\t\tinc = 1000 * (10 ** level) * (invert ? -1 : 1),\n\t\t\tl = invert ? -inc * 900 : 0;\n\t\twhile (el) {\n\t\t\tl += inc;\n\t\t\tel = el.previousSibling;\n\t\t}\n\t\treturn parent ? l + _getDOMDepth(parent, invert, level + 1) : l;\n\t},\n\t_orderByDOMDepth = (comps, invert, isElStates) => {\n\t\tcomps.forEach(comp => comp.d = _getDOMDepth(isElStates ? comp.element : comp.t, invert));\n\t\tcomps.sort((c1, c2) => c1.d - c2.d);\n\t\treturn comps;\n\t},\n\t_recordInlineStyles = (elState, props) => { // records the current inline CSS properties into an Array in alternating name/value pairs that's stored in a \"css\" property on the state object so that we can revert later.\n\t\tlet style = elState.element.style,\n\t\t\ta = elState.css = elState.css || [],\n\t\t\ti = props.length,\n\t\t\tp, v;\n\t\twhile (i--) {\n\t\t\tp = props[i];\n\t\t\tv = style[p] || style.getPropertyValue(p);\n\t\t\ta.push(v ? p : _dashedNameLookup[p] || (_dashedNameLookup[p] = _camelToDashed(p)), v);\n\t\t}\n\t\treturn style;\n\t},\n\t_applyInlineStyles = state => {\n\t\tlet css = state.css,\n\t\t\tstyle = state.element.style,\n\t\t\ti = 0;\n\t\tstate.cache.uncache = 1;\n\t\tfor (; i < css.length; i+=2) {\n\t\t\tcss[i+1] ? (style[css[i]] = css[i+1]) : style.removeProperty(css[i]);\n\t\t}\n\t},\n\t_setFinalStates = (comps, onlyTransforms) => {\n\t\tcomps.forEach(c => c.a.cache.uncache = 1);\n\t\tonlyTransforms || comps.finalStates.forEach(_applyInlineStyles);\n\t},\n\t_absoluteProps = \"paddingTop,paddingRight,paddingBottom,paddingLeft,gridArea,transition\".split(\",\"), // properties that we must record just\n\t_makeAbsolute = (elState, fallbackNode, ignoreBatch) => {\n\t\tlet { element, width, height, uncache, getProp } = elState,\n\t\t\tstyle = element.style,\n\t\t\ti = 4,\n\t\t\tresult, displayIsNone, cs;\n\t\t(typeof(fallbackNode) !== \"object\") && (fallbackNode = elState);\n\t\tif (_batch && ignoreBatch !== 1) {\n\t\t\t_batch._abs.push({t: element, b: elState, a: elState, sd: 0});\n\t\t\t_batch._final.push(() => (elState.cache.uncache = 1) && _applyInlineStyles(elState));\n\t\t\treturn element;\n\t\t}\n\t\tdisplayIsNone = getProp(\"display\") === \"none\";\n\n\t\tif (!elState.isVisible || displayIsNone) {\n\t\t\tdisplayIsNone && (_recordInlineStyles(elState, [\"display\"]).display = fallbackNode.display);\n\t\t\telState.matrix = fallbackNode.matrix;\n\t\t\telState.width = width = elState.width || fallbackNode.width;\n\t\t\telState.height = height = elState.height || fallbackNode.height;\n\t\t}\n\n\t\t_recordInlineStyles(elState, _absoluteProps);\n\t\tcs = window.getComputedStyle(element);\n\t\twhile (i--) {\n\t\t\tstyle[_absoluteProps[i]] = cs[_absoluteProps[i]]; // record paddings as px-based because if removed from grid, percentage-based ones could be altered.\n\t\t}\n\t\tstyle.gridArea = \"1 / 1 / 1 / 1\";\n\t\tstyle.transition = \"none\";\n\n\t\tstyle.position = \"absolute\";\n\t\tstyle.width = width + \"px\";\n\t\tstyle.height = height + \"px\";\n\t\tstyle.top || (style.top = \"0px\");\n\t\tstyle.left || (style.left = \"0px\");\n\t\tif (uncache) {\n\t\t\tresult = new ElementState(element);\n\t\t} else { // better performance\n\t\t\tresult = _copy(elState, _emptyObj);\n\t\t\tresult.position = \"absolute\";\n\t\t\tif (elState.simple) {\n\t\t\t\tlet bounds = element.getBoundingClientRect();\n\t\t\t\tresult.matrix = new Matrix2D(1, 0, 0, 1, bounds.left + _getDocScrollLeft(), bounds.top + _getDocScrollTop());\n\t\t\t} else {\n\t\t\t\tresult.matrix = getGlobalMatrix(element, false, false, true);\n\t\t\t}\n\t\t}\n\t\tresult = _fit(result, elState, true);\n\t\telState.x = _closestTenth(result.x, 0.01);\n\t\telState.y = _closestTenth(result.y, 0.01);\n\t\treturn element;\n\t},\n\t_filterComps = (comps, targets) => {\n\t\tif (targets !== true) {\n\t\t\ttargets = _toArray(targets);\n\t\t\tcomps = comps.filter(c => {\n\t\t\t\tif (targets.indexOf((c.sd < 0 ? c.b : c.a).element) !== -1) {\n\t\t\t\t    return true;\n\t\t\t\t} else {\n\t\t\t\t\tc.t._gsap.renderTransform(1); // we must force transforms to render on anything that isn't being made position: absolute, otherwise the absolute position happens and then when animation begins it applies transforms which can create a new stacking context, throwing off positioning!\n\t\t\t\t\tif (c.b.isVisible) {\n\t\t\t\t\t\tc.t.style.width = c.b.width + \"px\"; // otherwise things can collapse when contents are made position: absolute.\n\t\t\t\t\t\tc.t.style.height = c.b.height + \"px\";\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t\treturn comps;\n\t},\n\t_makeCompsAbsolute = comps => _orderByDOMDepth(comps, true).forEach(c => (c.a.isVisible || c.b.isVisible) && _makeAbsolute(c.sd < 0 ? c.b : c.a, c.b, 1)),\n\t_findElStateInState = (state, other) => (other && state.idLookup[_parseElementState(other).id]) || state.elementStates[0],\n\t_parseElementState = (elOrNode, props, simple, other) => elOrNode instanceof ElementState ? elOrNode : elOrNode instanceof FlipState ? _findElStateInState(elOrNode, other) : new ElementState(typeof(elOrNode) === \"string\" ? _getEl(elOrNode) || console.warn(elOrNode + \" not found\") : elOrNode, props, simple),\n\t_recordProps = (elState, props) => {\n\t\tlet getProp = gsap.getProperty(elState.element, null, \"native\"),\n\t\t\tobj = elState.props = {},\n\t\t\ti = props.length;\n\t\twhile (i--) {\n\t\t\tobj[props[i]] = (getProp(props[i]) + \"\").trim();\n\t\t}\n\t\tobj.zIndex && (obj.zIndex = parseFloat(obj.zIndex) || 0);\n\t\treturn elState;\n\t},\n\t_applyProps = (element, props) => {\n\t\tlet style = element.style || element, // could pass in a vars object.\n\t\t\tp;\n\t\tfor (p in props) {\n\t\t\tstyle[p] = props[p];\n\t\t}\n\t},\n\t_getID = el => {\n\t\tlet id = el.getAttribute(\"data-flip-id\");\n\t\tid || el.setAttribute(\"data-flip-id\", (id = \"auto-\" + _id++));\n\t\treturn id;\n\t},\n\t_elementsFromElementStates = elStates => elStates.map(elState => elState.element),\n\t_handleCallback = (callback, elStates, tl) => callback && elStates.length && tl.add(callback(_elementsFromElementStates(elStates), tl, new FlipState(elStates, 0, true)), 0),\n\n\t_fit = (fromState, toState, scale, applyProps, fitChild, vars) => {\n\t\tlet { element, cache, parent, x, y } = fromState,\n\t\t\t{ width, height, scaleX, scaleY, rotation, bounds } = toState,\n\t\t\tcssText = vars && element.style.cssText,\n\t\t\ttransform = vars && element.getBBox && element.getAttribute(\"transform\"),\n\t\t\tdimensionState = fromState,\n\t\t\t{e, f} = toState.matrix,\n\t\t\tdeep = fromState.bounds.width !== bounds.width || fromState.bounds.height !== bounds.height || fromState.scaleX !== scaleX || fromState.scaleY !== scaleY || fromState.rotation !== rotation,\n\t\t\tsimple = !deep && fromState.simple && toState.simple && !fitChild,\n\t\t\tskewX, fromPoint, toPoint, getProp, parentMatrix, matrix, bbox;\n\t\tif (simple || !parent) {\n\t\t\tscaleX = scaleY = 1;\n\t\t\trotation = skewX = 0;\n\t\t} else {\n\t\t\tparentMatrix = _getInverseGlobalMatrix(parent);\n\t\t\tmatrix = parentMatrix.clone().multiply(toState.ctm ? toState.matrix.clone().multiply(toState.ctm) : toState.matrix); // root SVG elements have a ctm that we must factor out (for example, viewBox:\"0 0 94 94\" with a width of 200px would scale the internals by 2.127 but when we're matching the size of the root <svg> element itself, that scaling shouldn't factor in!)\n\t\t\trotation = _round(Math.atan2(matrix.b, matrix.a) * _RAD2DEG);\n\t\t\tskewX = _round(Math.atan2(matrix.c, matrix.d) * _RAD2DEG + rotation) % 360; // in very rare cases, minor rounding might end up with 360 which should be 0.\n\t\t\tscaleX = Math.sqrt(matrix.a ** 2 + matrix.b ** 2);\n\t\t\tscaleY = Math.sqrt(matrix.c ** 2 + matrix.d ** 2) * Math.cos(skewX * _DEG2RAD);\n\t\t\tif (fitChild) {\n\t\t\t\tfitChild = _toArray(fitChild)[0];\n\t\t\t\tgetProp = gsap.getProperty(fitChild);\n\t\t\t\tbbox = fitChild.getBBox && typeof(fitChild.getBBox) === \"function\" && fitChild.getBBox();\n\t\t\t\tdimensionState = {scaleX: getProp(\"scaleX\"), scaleY: getProp(\"scaleY\"), width: bbox ? bbox.width : Math.ceil(parseFloat(getProp(\"width\", \"px\"))), height: bbox ? bbox.height : parseFloat(getProp(\"height\", \"px\")) };\n\t\t\t}\n\t\t\tcache.rotation = rotation + \"deg\";\n\t\t\tcache.skewX = skewX + \"deg\";\n\t\t}\n\t\tif (scale) {\n\t\t\tscaleX *= width === dimensionState.width || !dimensionState.width ? 1 : width / dimensionState.width; // note if widths are both 0, we should make scaleX 1 - some elements have box-sizing that incorporates padding, etc. and we don't want it to collapse in that case.\n\t\t\tscaleY *= height === dimensionState.height || !dimensionState.height ? 1 : height / dimensionState.height;\n\t\t\tcache.scaleX = scaleX;\n\t\t\tcache.scaleY = scaleY;\n\t\t} else {\n\t\t\twidth = _closestTenth(width * scaleX / dimensionState.scaleX, 0);\n\t\t\theight = _closestTenth(height * scaleY / dimensionState.scaleY, 0);\n\t\t\telement.style.width = width + \"px\";\n\t\t\telement.style.height = height + \"px\";\n\t\t}\n\t\t// if (fromState.isFixed) { // commented out because it's now taken care of in getGlobalMatrix() with a flag at the end.\n\t\t// \te -= _getDocScrollLeft();\n\t\t// \tf -= _getDocScrollTop();\n\t\t// }\n\t\tapplyProps && _applyProps(element, toState.props);\n\t\tif (simple || !parent) {\n\t\t\tx += e - fromState.matrix.e;\n\t\t\ty += f - fromState.matrix.f;\n\t\t} else if (deep || parent !== toState.parent) {\n\t\t\tcache.renderTransform(1, cache);\n\t\t\tmatrix = getGlobalMatrix(fitChild || element, false, false, true);\n\t\t\tfromPoint = parentMatrix.apply({x: matrix.e, y: matrix.f});\n\t\t\ttoPoint = parentMatrix.apply({x: e, y: f});\n\t\t\tx += toPoint.x - fromPoint.x;\n\t\t\ty += toPoint.y - fromPoint.y;\n\t\t} else { // use a faster/cheaper algorithm if we're just moving x/y\n\t\t\tparentMatrix.e = parentMatrix.f = 0;\n\t\t\ttoPoint = parentMatrix.apply({x: e - fromState.matrix.e, y: f - fromState.matrix.f});\n\t\t\tx += toPoint.x;\n\t\t\ty += toPoint.y;\n\t\t}\n\t\tx = _closestTenth(x, 0.02);\n\t\ty = _closestTenth(y, 0.02);\n\t\tif (vars && !(vars instanceof ElementState)) { // revert\n\t\t\telement.style.cssText = cssText;\n\t\t\telement.getBBox && element.setAttribute(\"transform\", transform || \"\");\n\t\t\tcache.uncache = 1;\n\t\t} else { // or apply the transform immediately\n\t\t\tcache.x = x + \"px\";\n\t\t\tcache.y = y + \"px\";\n\t\t\tcache.renderTransform(1, cache);\n\t\t}\n\t\tif (vars) {\n\t\t\tvars.x = x;\n\t\t\tvars.y = y;\n\t\t\tvars.rotation = rotation;\n\t\t\tvars.skewX = skewX;\n\t\t\tif (scale) {\n\t\t\t\tvars.scaleX = scaleX;\n\t\t\t\tvars.scaleY = scaleY;\n\t\t\t} else {\n\t\t\t\tvars.width = width;\n\t\t\t\tvars.height = height;\n\t\t\t}\n\t\t}\n\t\treturn vars || cache;\n\t},\n\n\t_parseState = (targetsOrState, vars) => targetsOrState instanceof FlipState ? targetsOrState : new FlipState(targetsOrState, vars),\n\t_getChangingElState = (toState, fromState, id) => {\n\t\tlet to1 = toState.idLookup[id],\n\t\t\tto2 = toState.alt[id];\n\t\treturn to2.isVisible && (!(fromState.getElementState(to2.element) || to2).isVisible || !to1.isVisible) ? to2 : to1;\n\t},\n\t_bodyMetrics = [], _bodyProps = \"width,height,overflowX,overflowY\".split(\",\"), _bodyLocked,\n\t_lockBodyScroll = lock => { // if there's no scrollbar, we should lock that so that measurements don't get affected by temporary repositioning, like if something is centered in the window.\n\t\tif (lock !== _bodyLocked) {\n\t\t\tlet s = _body.style,\n\t\t\t\tw = _body.clientWidth === window.outerWidth,\n\t\t\t\th = _body.clientHeight === window.outerHeight,\n\t\t\t\ti = 4;\n\t\t\tif (lock && (w || h)) {\n\t\t\t\twhile (i--) {\n\t\t\t\t\t_bodyMetrics[i] = s[_bodyProps[i]];\n\t\t\t\t}\n\t\t\t\tif (w) {\n\t\t\t\t\ts.width = _body.clientWidth + \"px\";\n\t\t\t\t\ts.overflowY = \"hidden\";\n\t\t\t\t}\n\t\t\t\tif (h) {\n\t\t\t\t\ts.height = _body.clientHeight + \"px\";\n\t\t\t\t\ts.overflowX = \"hidden\";\n\t\t\t\t}\n\t\t\t\t_bodyLocked= lock;\n\t\t\t} else if (_bodyLocked) {\n\t\t\t\twhile (i--) {\n\t\t\t\t\t_bodyMetrics[i] ? (s[_bodyProps[i]] = _bodyMetrics[i]) : s.removeProperty(_camelToDashed(_bodyProps[i]));\n\t\t\t\t}\n\t\t\t\t_bodyLocked = lock;\n\t\t\t}\n\t\t}\n\t},\n\n\t_fromTo = (fromState, toState, vars, relative) => { // relative is -1 if \"from()\", and 1 if \"to()\"\n\t\t(fromState instanceof FlipState && toState instanceof FlipState) || console.warn(\"Not a valid state object.\");\n\t\tvars = vars || {};\n\t\tlet { clearProps, onEnter, onLeave, absolute, absoluteOnLeave, custom, delay, paused, repeat, repeatDelay, yoyo, toggleClass, nested, zIndex, scale, fade, stagger, spin, prune } = vars,\n\t\t\tprops = (\"props\" in vars ? vars : fromState).props,\n\t\t\ttweenVars = _copy(vars, _reserved),\n\t\t\tanimation = gsap.timeline({ delay, paused, repeat, repeatDelay, yoyo, data: \"isFlip\" }),\n\t\t\tremainingProps = tweenVars,\n\t\t\tentering = [],\n\t\t\tleaving = [],\n\t\t\tcomps = [],\n\t\t\tswapOutTargets = [],\n\t\t\tspinNum = spin === true ? 1 : spin || 0,\n\t\t\tspinFunc = typeof(spin) === \"function\" ? spin : () => spinNum,\n\t\t\tinterrupted = fromState.interrupted || toState.interrupted,\n\t\t\taddFunc = animation[relative !== 1 ? \"to\" : \"from\"],\n\t\t\tv, p, endTime, i, el, comp, state, targets, finalStates, fromNode, toNode, run, a, b;\n\t\t//relative || (toState = (new FlipState(toState.targets, {props: props})).fit(toState, scale));\n\t\tfor (p in toState.idLookup) {\n\t\t\ttoNode = !toState.alt[p] ? toState.idLookup[p] : _getChangingElState(toState, fromState, p);\n\t\t\tel = toNode.element;\n\t\t\tfromNode = fromState.idLookup[p];\n\t\t\tfromState.alt[p] && el === fromNode.element && (fromState.alt[p].isVisible || !toNode.isVisible) && (fromNode = fromState.alt[p]);\n\t\t\tif (fromNode) {\n\t\t\t\tcomp = {t: el, b: fromNode, a: toNode, sd: fromNode.element === el ? 0 : toNode.isVisible ? 1 : -1};\n\t\t\t\tcomps.push(comp);\n\t\t\t\tif (comp.sd) {\n\t\t\t\t\tif (comp.sd < 0) {\n\t\t\t\t\t\tcomp.b = toNode;\n\t\t\t\t\t\tcomp.a = fromNode;\n\t\t\t\t\t}\n\t\t\t\t\t// for swapping elements that got interrupted, we must re-record the inline styles to ensure they're not tainted. Remember, .batch() permits getState() not to force in-progress flips to their end state.\n\t\t\t\t\tinterrupted && _recordInlineStyles(comp.b, props ? _memoizedRemoveProps[props] : _removeProps);\n\t\t\t\t\tfade && comps.push(comp.swap = {t: fromNode.element, b: comp.b, a: comp.a, sd: -comp.sd, swap: comp});\n\t\t\t\t}\n\t\t\t\tel._flip = fromNode.element._flip = _batch ? _batch.timeline : animation;\n\t\t\t} else if (toNode.isVisible) {\n\t\t\t\tcomps.push({t: el, b: _copy(toNode, {isVisible:1}), a: toNode, sd: 0, entering: 1}); // to include it in the \"entering\" Array and do absolute positioning if necessary\n\t\t\t\tel._flip = _batch ? _batch.timeline : animation;\n\t\t\t}\n\t\t}\n\n\t\tprops && (_memoizedProps[props] || _memoizeProps(props)).forEach(p => tweenVars[p] = i => comps[i].a.props[p]);\n\t\tcomps.finalStates = finalStates = [];\n\n\t\trun = () => {\n\t\t\t_orderByDOMDepth(comps);\n\t\t\t_lockBodyScroll(true); // otherwise, measurements may get thrown off when things get fit.\n\t\t\t// TODO: cache the matrix, especially for parent because it'll probably get reused quite a bit, but lock it to a particular cycle(?).\n\t\t\tfor (i = 0; i < comps.length; i++) {\n\t\t\t\tcomp = comps[i];\n\t\t\t\ta = comp.a;\n\t\t\t\tb = comp.b;\n\t\t\t\tif (prune && !a.isDifferent(b) && !comp.entering) { // only flip if things changed! Don't omit it from comps initially because that'd prevent the element from being positioned absolutely (if necessary)\n\t\t\t\t\tcomps.splice(i--, 1);\n\t\t\t\t} else {\n\t\t\t\t\tel = comp.t;\n\t\t\t\t\tnested && !(comp.sd < 0) && i && (a.matrix = getGlobalMatrix(el, false, false, true)); // moving a parent affects the position of children\n\t\t\t\t\tif (b.isVisible && a.isVisible) {\n\t\t\t\t\t\tif (comp.sd < 0) { // swapping OUT (swap direction of -1 is out)\n\t\t\t\t\t\t\tstate = new ElementState(el, props, fromState.simple);\n\t\t\t\t\t\t\t_fit(state, a, scale, 0, 0, state);\n\t\t\t\t\t\t\tstate.matrix = getGlobalMatrix(el, false, false, true);\n\t\t\t\t\t\t\tstate.css = comp.b.css;\n\t\t\t\t\t\t\tcomp.a = a = state;\n\t\t\t\t\t\t\tfade && (el.style.opacity = interrupted ? b.opacity : a.opacity);\n\t\t\t\t\t\t\tstagger && swapOutTargets.push(el);\n\t\t\t\t\t\t} else if (comp.sd > 0 && fade) { // swapping IN (swap direction of 1 is in)\n\t\t\t\t\t\t\tel.style.opacity = interrupted ? a.opacity - b.opacity : \"0\";\n\t\t\t\t\t\t}\n\t\t\t\t\t\t_fit(a, b, scale, props);\n\n\t\t\t\t\t} else if (b.isVisible !== a.isVisible) { // either entering or leaving (one side is invisible)\n\t\t\t\t\t\tif (!b.isVisible) { // entering\n\t\t\t\t\t\t\ta.isVisible && entering.push(a);\n\t\t\t\t\t\t\tcomps.splice(i--, 1);\n\t\t\t\t\t\t} else if (!a.isVisible) { // leaving\n\t\t\t\t\t\t\tb.css = a.css;\n\t\t\t\t\t\t\tleaving.push(b);\n\t\t\t\t\t\t\tcomps.splice(i--, 1);\n\t\t\t\t\t\t\tabsolute && nested && _fit(a, b, scale, props);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif (!scale) {\n\t\t\t\t\t\tel.style.maxWidth = Math.max(a.width, b.width) + \"px\";\n\t\t\t\t\t\tel.style.maxHeight = Math.max(a.height, b.height) + \"px\";\n\t\t\t\t\t\tel.style.minWidth = Math.min(a.width, b.width) + \"px\";\n\t\t\t\t\t\tel.style.minHeight = Math.min(a.height, b.height) + \"px\";\n\t\t\t\t\t}\n\t\t\t\t\tnested && toggleClass && el.classList.add(toggleClass);\n\t\t\t\t}\n\t\t\t\tfinalStates.push(a);\n\t\t\t}\n\t\t\tlet classTargets;\n\t\t\tif (toggleClass) {\n\t\t\t\tclassTargets = finalStates.map(s => s.element);\n\t\t\t\tnested && classTargets.forEach(e => e.classList.remove(toggleClass)); // there could be a delay, so don't leave the classes applied (we'll do it in a timeline callback)\n\t\t\t}\n\n\t\t\t_lockBodyScroll(false);\n\n\t\t\tif (scale) {\n\t\t\t\ttweenVars.scaleX = i => comps[i].a.scaleX;\n\t\t\t\ttweenVars.scaleY = i => comps[i].a.scaleY;\n\t\t\t} else {\n\t\t\t\ttweenVars.width = i => comps[i].a.width + \"px\";\n\t\t\t\ttweenVars.height = i => comps[i].a.height + \"px\";\n\t\t\t\ttweenVars.autoRound = vars.autoRound || false;\n\t\t\t}\n\t\t\ttweenVars.x = i => comps[i].a.x + \"px\";\n\t\t\ttweenVars.y = i => comps[i].a.y + \"px\";\n\t\t\ttweenVars.rotation = i => comps[i].a.rotation + (spin ? spinFunc(i, targets[i], targets) * 360 : 0);\n\t\t\ttweenVars.skewX = i => comps[i].a.skewX;\n\n\t\t\ttargets = comps.map(c => c.t);\n\n\t\t\tif (zIndex || zIndex === 0) {\n\t\t\t\ttweenVars.modifiers = {zIndex: () => zIndex};\n\t\t\t\ttweenVars.zIndex = zIndex;\n\t\t\t\ttweenVars.immediateRender = vars.immediateRender !== false;\n\t\t\t}\n\n\t\t\tfade && (tweenVars.opacity = i => comps[i].sd < 0 ? 0 : comps[i].sd > 0 ? comps[i].a.opacity : \"+=0\");\n\n\t\t\tif (swapOutTargets.length) {\n\t\t\t\tstagger = gsap.utils.distribute(stagger);\n\t\t\t\tlet dummyArray = targets.slice(swapOutTargets.length);\n\t\t\t\ttweenVars.stagger = (i, el) => stagger(~swapOutTargets.indexOf(el) ? targets.indexOf(comps[i].swap.t) : i, el, dummyArray);\n\t\t\t}\n\n\t\t\t// // for testing...\n\t\t\t// gsap.delayedCall(vars.data ? 50 : 1, function() {\n\t\t\t// \tanimation.eventCallback(\"onComplete\", () => _setFinalStates(comps, !clearProps));\n\t\t\t// \taddFunc.call(animation, targets, tweenVars, 0).play();\n\t\t\t// });\n\t\t\t// return;\n\n\t\t\t_callbacks.forEach(name => vars[name] && animation.eventCallback(name, vars[name], vars[name + \"Params\"])); // apply callbacks to the timeline, not tweens (because \"custom\" timing can make multiple tweens)\n\n\t\t\tif (custom && targets.length) { // bust out the custom properties as their own tweens so they can use different eases, durations, etc.\n\t\t\t\tremainingProps = _copy(tweenVars, _reserved);\n\t\t\t\tif (\"scale\" in custom) {\n\t\t\t\t\tcustom.scaleX = custom.scaleY = custom.scale;\n\t\t\t\t\tdelete custom.scale;\n\t\t\t\t}\n\t\t\t\tfor (p in custom) {\n\t\t\t\t\tv = _copy(custom[p], _fitReserved);\n\t\t\t\t\tv[p] = tweenVars[p];\n\t\t\t\t\t!(\"duration\" in v) && (\"duration\" in tweenVars) && (v.duration = tweenVars.duration);\n\t\t\t\t\tv.stagger = tweenVars.stagger;\n\t\t\t\t\taddFunc.call(animation, targets, v, 0);\n\t\t\t\t\tdelete remainingProps[p];\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (targets.length || leaving.length || entering.length) {\n\t\t\t\ttoggleClass && animation.add(() => _toggleClass(classTargets, toggleClass, animation._zTime < 0 ? \"remove\" : \"add\"), 0) && !paused && _toggleClass(classTargets, toggleClass, \"add\");\n\t\t\t\ttargets.length && addFunc.call(animation, targets, remainingProps, 0);\n\t\t\t}\n\n\t\t\t_handleCallback(onEnter, entering, animation);\n\t\t\t_handleCallback(onLeave, leaving, animation);\n\n\t\t\tlet batchTl = _batch && _batch.timeline;\n\n\t\t\tif (batchTl) {\n\t\t\t\tbatchTl.add(animation, 0);\n\t\t\t\t_batch._final.push(() => _setFinalStates(comps, !clearProps));\n\t\t\t}\n\n\t\t\tendTime = animation.duration();\n\t\t\tanimation.call(() => {\n\t\t\t\tlet forward = animation.time() >= endTime;\n\t\t\t\tforward && !batchTl && _setFinalStates(comps, !clearProps);\n\t\t\t\ttoggleClass && _toggleClass(classTargets, toggleClass, forward ? \"remove\" : \"add\");\n\t\t\t});\n\t\t};\n\n\t\tabsoluteOnLeave && (absolute = comps.filter(comp => !comp.sd && !comp.a.isVisible && comp.b.isVisible).map(comp => comp.a.element));\n\t\tif (_batch) {\n\t\t\tabsolute && _batch._abs.push(..._filterComps(comps, absolute));\n\t\t\t_batch._run.push(run);\n\t\t} else {\n\t\t\tabsolute && _makeCompsAbsolute(_filterComps(comps, absolute)); // when making absolute, we must go in a very particular order so that document flow changes don't affect things. Don't make it visible if both the before and after states are invisible! There's no point, and it could make things appear visible during the flip that shouldn't be.\n\t\t\trun();\n\t\t}\n\n\t\tlet anim = _batch ? _batch.timeline : animation;\n\t\tanim.revert = () => _killFlip(anim, 1); // a Flip timeline should behave very different when reverting - it should actually jump to the end so that styles get cleared out.\n\n\t\treturn anim;\n\t},\n\t_interrupt = tl => {\n\t\ttl.vars.onInterrupt && tl.vars.onInterrupt.apply(tl, tl.vars.onInterruptParams || []);\n\t\ttl.getChildren(true, false, true).forEach(_interrupt);\n\t},\n\t_killFlip = (tl, action) => { // action: 0 = nothing, 1 = complete, 2 = only kill (don't complete)\n\t\tif (tl && tl.progress() < 1 && !tl.paused()) {\n\t\t\tif (action) {\n\t\t\t\t_interrupt(tl);\n\t\t\t\taction < 2 && tl.progress(1); // we should also kill it in case it was added to a parent timeline.\n\t\t\t\ttl.kill();\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\t},\n\t_createLookup = state => {\n\t\tlet lookup = state.idLookup = {},\n\t\t\talt = state.alt = {},\n\t\t\telStates = state.elementStates,\n\t\t\ti = elStates.length,\n\t\t\telState;\n\t\twhile (i--) {\n\t\t\telState = elStates[i];\n\t\t\tlookup[elState.id] ? (alt[elState.id] = elState) : (lookup[elState.id] = elState);\n\t\t}\n\t};\n\n\n\n\n\n\nclass FlipState {\n\n\tconstructor(targets, vars, targetsAreElementStates) {\n\t\tthis.props = vars && vars.props;\n\t\tthis.simple = !!(vars && vars.simple);\n\t\tif (targetsAreElementStates) {\n\t\t\tthis.targets = _elementsFromElementStates(targets);\n\t\t\tthis.elementStates = targets;\n\t\t\t_createLookup(this);\n\t\t} else {\n\t\t\tthis.targets = _toArray(targets);\n\t\t\tlet soft = vars && (vars.kill === false || (vars.batch && !vars.kill));\n\t\t\t_batch && !soft && _batch._kill.push(this);\n\t\t\tthis.update(soft || !!_batch); // when batching, don't force in-progress flips to their end; we need to do that AFTER all getStates() are called.\n\t\t}\n\t}\n\n\tupdate(soft) {\n\t\tthis.elementStates = this.targets.map(el => new ElementState(el, this.props, this.simple));\n\t\t_createLookup(this);\n\t\tthis.interrupt(soft);\n\t\tthis.recordInlineStyles();\n\t\treturn this;\n\t}\n\n\tclear() {\n\t\tthis.targets.length = this.elementStates.length = 0;\n\t\t_createLookup(this);\n\t\treturn this;\n\t}\n\n\tfit(state, scale, nested) {\n\t\tlet elStatesInOrder = _orderByDOMDepth(this.elementStates.slice(0), false, true),\n\t\t\ttoElStates = (state || this).idLookup,\n\t\t\ti = 0,\n\t\t\tfromNode, toNode;\n\t\tfor (; i < elStatesInOrder.length; i++) {\n\t\t\tfromNode = elStatesInOrder[i];\n\t\t\tnested && (fromNode.matrix = getGlobalMatrix(fromNode.element, false, false, true)); // moving a parent affects the position of children\n\t\t\ttoNode = toElStates[fromNode.id];\n\t\t\ttoNode && _fit(fromNode, toNode, scale, true, 0, fromNode);\n\t\t\tfromNode.matrix = getGlobalMatrix(fromNode.element, false, false, true);\n\t\t}\n\t\treturn this;\n\t}\n\n\tgetProperty(element, property) {\n\t\tlet es = this.getElementState(element) || _emptyObj;\n\t\treturn (property in es ? es : es.props || _emptyObj)[property];\n\t}\n\n\tadd(state) {\n\t\tlet i = state.targets.length,\n\t\t\tlookup = this.idLookup,\n\t\t\talt = this.alt,\n\t\t\tindex, es, es2;\n\t\twhile (i--) {\n\t\t\tes = state.elementStates[i];\n\t\t\tes2 = lookup[es.id];\n\t\t\tif (es2 && (es.element === es2.element || (alt[es.id] && alt[es.id].element === es.element))) { // if the flip id is already in this FlipState, replace it!\n\t\t\t\tindex = this.elementStates.indexOf(es.element === es2.element ? es2 : alt[es.id]);\n\t\t\t\tthis.targets.splice(index, 1, state.targets[i]);\n\t\t\t\tthis.elementStates.splice(index, 1, es);\n\t\t\t} else {\n\t\t\t\tthis.targets.push(state.targets[i]);\n\t\t\t\tthis.elementStates.push(es);\n\t\t\t}\n\t\t}\n\t\tstate.interrupted && (this.interrupted = true);\n\t\tstate.simple || (this.simple = false);\n\t\t_createLookup(this);\n\t\treturn this;\n\t}\n\n\tcompare(state) {\n\t\tlet l1 = state.idLookup,\n\t\t\tl2 = this.idLookup,\n\t\t\tunchanged = [],\n\t\t\tchanged = [],\n\t\t\tenter = [],\n\t\t\tleave = [],\n\t\t\ttargets = [],\n\t\t\ta1 = state.alt,\n\t\t\ta2 = this.alt,\n\t\t\tplace = (s1, s2, el) => (s1.isVisible !== s2.isVisible ? (s1.isVisible ? enter : leave) : s1.isVisible ? changed : unchanged).push(el) && targets.push(el),\n\t\t\tplaceIfDoesNotExist = (s1, s2, el) => targets.indexOf(el) < 0 && place(s1, s2, el),\n\t\t\ts1, s2, p, el, s1Alt, s2Alt, c1, c2;\n\t\tfor (p in l1) {\n\t\t\ts1Alt = a1[p];\n\t\t\ts2Alt = a2[p];\n\t\t\ts1 = !s1Alt ? l1[p] : _getChangingElState(state, this, p);\n\t\t\tel = s1.element;\n\t\t\ts2 = l2[p];\n\t\t\tif (s2Alt) {\n\t\t\t\tc2 = s2.isVisible || (!s2Alt.isVisible && el === s2.element) ? s2 : s2Alt;\n\t\t\t\tc1 = s1Alt && !s1.isVisible && !s1Alt.isVisible && c2.element === s1Alt.element ? s1Alt : s1;\n\t\t\t\t//c1.element !== c2.element && c1.element === s2.element && (c2 = s2);\n\t\t\t\tif (c1.isVisible && c2.isVisible && c1.element !== c2.element) { // swapping, so force into \"changed\" array\n\t\t\t\t\t(c1.isDifferent(c2) ? changed : unchanged).push(c1.element, c2.element);\n\t\t\t\t\ttargets.push(c1.element, c2.element);\n\t\t\t\t} else {\n\t\t\t\t\tplace(c1, c2, c1.element);\n\t\t\t\t}\n\t\t\t\ts1Alt && c1.element === s1Alt.element && (s1Alt = l1[p]);\n\t\t\t\tplaceIfDoesNotExist(c1.element !== s2.element && s1Alt ? s1Alt : c1, s2, s2.element);\n\t\t\t\tplaceIfDoesNotExist(s1Alt && s1Alt.element === s2Alt.element ? s1Alt : c1, s2Alt, s2Alt.element);\n\t\t\t\ts1Alt && placeIfDoesNotExist(s1Alt, s2Alt.element === s1Alt.element ? s2Alt : s2, s1Alt.element);\n\t\t\t} else {\n\t\t\t\t!s2 ? enter.push(el) : !s2.isDifferent(s1) ? unchanged.push(el) : place(s1, s2, el);\n\t\t\t\ts1Alt && placeIfDoesNotExist(s1Alt, s2, s1Alt.element);\n\t\t\t}\n\t\t}\n\t\tfor (p in l2) {\n\t\t\tif (!l1[p]) {\n\t\t\t\tleave.push(l2[p].element);\n\t\t\t\ta2[p] && leave.push(a2[p].element);\n\t\t\t}\n\t\t}\n\t\treturn {changed, unchanged, enter, leave};\n\t}\n\n\trecordInlineStyles() {\n\t\tlet props = _memoizedRemoveProps[this.props] || _removeProps,\n\t\t\ti = this.elementStates.length;\n\t\twhile (i--) {\n\t\t\t_recordInlineStyles(this.elementStates[i], props);\n\t\t}\n\t}\n\n\tinterrupt(soft) { // soft = DON'T force in-progress flip animations to completion (like when running a batch, we can't immediately kill flips when getting states because it could contaminate positioning and other .getState() calls that will run in the batch (we kill AFTER all the .getState() calls complete).\n\t\tlet timelines = [];\n\t\tthis.targets.forEach(t => {\n\t\t\tlet tl = t._flip,\n\t\t\t\tfoundInProgress = _killFlip(tl, soft ? 0 : 1);\n\t\t\tsoft && foundInProgress && timelines.indexOf(tl) < 0 && tl.add(() => this.updateVisibility());\n\t\t\tfoundInProgress && timelines.push(tl);\n\t\t});\n\t\t!soft && timelines.length && this.updateVisibility(); // if we found an in-progress Flip animation, we must record all the values in their current state at that point BUT we should update the isVisible value AFTER pushing that flip to completion so that elements that are entering or leaving will populate those Arrays properly.\n\t\tthis.interrupted || (this.interrupted = !!timelines.length);\n\t}\n\n\tupdateVisibility() {\n\t\tthis.elementStates.forEach(es => {\n\t\t\tlet b = es.element.getBoundingClientRect();\n\t\t\tes.isVisible = !!(b.width || b.height || b.top || b.left);\n\t\t\tes.uncache = 1;\n\t\t});\n\t}\n\n\tgetElementState(element) {\n\t\treturn this.elementStates[this.targets.indexOf(_getEl(element))];\n\t}\n\n\tmakeAbsolute() {\n\t\treturn _orderByDOMDepth(this.elementStates.slice(0), true, true).map(_makeAbsolute);\n\t}\n\n}\n\n\n\nclass ElementState {\n\n\tconstructor(element, props, simple) {\n\t\tthis.element = element;\n\t\tthis.update(props, simple);\n\t}\n\n\tisDifferent(state) {\n\t\tlet b1 = this.bounds,\n\t\t\tb2 = state.bounds;\n\t\treturn b1.top !== b2.top || b1.left !== b2.left || b1.width !== b2.width || b1.height !== b2.height || !this.matrix.equals(state.matrix) || this.opacity !== state.opacity || (this.props && state.props && JSON.stringify(this.props) !== JSON.stringify(state.props));\n\t}\n\n\tupdate(props, simple) {\n\t\tlet self = this,\n\t\t\telement = self.element,\n\t\t\tgetProp = gsap.getProperty(element),\n\t\t\tcache = gsap.core.getCache(element),\n\t\t\tbounds = element.getBoundingClientRect(),\n\t\t\tbbox = element.getBBox && typeof(element.getBBox) === \"function\" && element.nodeName.toLowerCase() !== \"svg\" && element.getBBox(),\n\t\t\tm = simple ? new Matrix2D(1, 0, 0, 1, bounds.left + _getDocScrollLeft(), bounds.top + _getDocScrollTop()) : getGlobalMatrix(element, false, false, true);\n\t\tself.getProp = getProp;\n\t\tself.element = element;\n\t\tself.id = _getID(element);\n\t\tself.matrix = m;\n\t\tself.cache = cache;\n\t\tself.bounds = bounds;\n\t\tself.isVisible = !!(bounds.width || bounds.height || bounds.left || bounds.top);\n\t\tself.display = getProp(\"display\");\n\t\tself.position = getProp(\"position\");\n\t\tself.parent = element.parentNode;\n\t\tself.x = getProp(\"x\");\n\t\tself.y = getProp(\"y\");\n\t\tself.scaleX = cache.scaleX;\n\t\tself.scaleY = cache.scaleY;\n\t\tself.rotation = getProp(\"rotation\");\n\t\tself.skewX = getProp(\"skewX\");\n\t\tself.opacity = getProp(\"opacity\");\n\t\tself.width =  bbox ? bbox.width : _closestTenth(getProp(\"width\", \"px\"), 0.04); // round up to the closest 0.1 so that text doesn't wrap.\n\t\tself.height = bbox ? bbox.height : _closestTenth(getProp(\"height\", \"px\"), 0.04);\n\t\tprops && _recordProps(self, _memoizedProps[props] || _memoizeProps(props));\n\t\tself.ctm = element.getCTM && element.nodeName.toLowerCase() === \"svg\" && _getCTM(element).inverse();\n\t\tself.simple = simple || (_round(m.a) === 1 && !_round(m.b) && !_round(m.c) && _round(m.d) === 1); // allows us to speed through some other tasks if it's not scale/rotated\n\t\tself.uncache = 0;\n\t}\n\n}\n\nclass FlipAction {\n\tconstructor(vars, batch) {\n\t\tthis.vars = vars;\n\t\tthis.batch = batch;\n\t\tthis.states = [];\n\t\tthis.timeline = batch.timeline;\n\t}\n\n\tgetStateById(id) {\n\t\tlet i = this.states.length;\n\t\twhile (i--) {\n\t\t\tif (this.states[i].idLookup[id]) {\n\t\t\t\treturn this.states[i];\n\t\t\t}\n\t\t}\n\t}\n\n\tkill() {\n\t\tthis.batch.remove(this);\n\t}\n}\n\nclass FlipBatch {\n\tconstructor(id) {\n\t\tthis.id = id;\n\t\tthis.actions = [];\n\t\tthis._kill = [];\n\t\tthis._final = [];\n\t\tthis._abs = [];\n\t\tthis._run = [];\n\t\tthis.data = {};\n\t\tthis.state = new FlipState();\n\t\tthis.timeline = gsap.timeline();\n\t}\n\n\tadd(config) {\n\t\tlet result = this.actions.filter(action => action.vars === config);\n\t\tif (result.length) {\n\t\t\treturn result[0];\n\t\t}\n\t\tresult = new FlipAction(typeof(config) === \"function\" ? {animate: config} : config, this);\n\t\tthis.actions.push(result);\n\t\treturn result;\n\t}\n\n\tremove(action) {\n\t\tlet i = this.actions.indexOf(action);\n\t\ti >= 0 && this.actions.splice(i, 1);\n\t\treturn this;\n\t}\n\n\tgetState(merge) {\n\t\tlet prevBatch = _batch,\n\t\t\tprevAction = _batchAction;\n\t\t_batch = this;\n\t\tthis.state.clear();\n\t\tthis._kill.length = 0;\n\t\tthis.actions.forEach(action => {\n\t\t\tif (action.vars.getState) {\n\t\t\t\taction.states.length = 0;\n\t\t\t\t_batchAction = action;\n\t\t\t\taction.state = action.vars.getState(action);\n\t\t\t}\n\t\t\tmerge && action.states.forEach(s => this.state.add(s));\n\t\t});\n\t\t_batchAction = prevAction;\n\t\t_batch = prevBatch;\n\t\tthis.killConflicts();\n\t\treturn this;\n\t}\n\n\tanimate() {\n\t\tlet prevBatch = _batch,\n\t\t\ttl = this.timeline,\n\t\t\ti = this.actions.length,\n\t\t\tfinalStates, endTime;\n\t\t_batch = this;\n\t\ttl.clear();\n\t\tthis._abs.length = this._final.length = this._run.length = 0;\n\t\tthis.actions.forEach(a => {\n\t\t\ta.vars.animate && a.vars.animate(a);\n\t\t\tlet onEnter = a.vars.onEnter,\n\t\t\t\tonLeave = a.vars.onLeave,\n\t\t\t\ttargets = a.targets, s, result;\n\t\t\tif (targets && targets.length && (onEnter || onLeave)) {\n\t\t\t\ts = new FlipState();\n\t\t\t\ta.states.forEach(state => s.add(state));\n\t\t\t\tresult = s.compare(Flip.getState(targets));\n\t\t\t\tresult.enter.length && onEnter && onEnter(result.enter);\n\t\t\t\tresult.leave.length && onLeave && onLeave(result.leave);\n\t\t\t}\n\t\t});\n\t\t_makeCompsAbsolute(this._abs);\n\t\tthis._run.forEach(f => f());\n\t\tendTime = tl.duration();\n\t\tfinalStates = this._final.slice(0);\n\t\ttl.add(() => {\n\t\t\tif (endTime <= tl.time()) { // only call if moving forward in the timeline (in case it's nested in a timeline that gets reversed)\n\t\t\t\tfinalStates.forEach(f => f());\n\t\t\t\t_forEachBatch(this, \"onComplete\");\n\t\t\t}\n\t\t});\n\t\t_batch = prevBatch;\n\t\twhile (i--) {\n\t\t\tthis.actions[i].vars.once && this.actions[i].kill();\n\t\t}\n\t\t_forEachBatch(this, \"onStart\");\n\t\ttl.restart();\n\t\treturn this;\n\t}\n\n\tloadState(done) {\n\t\tdone || (done = () => 0);\n\t\tlet queue = [];\n\t\tthis.actions.forEach(c => {\n\t\t\tif (c.vars.loadState) {\n\t\t\t\tlet i, f = targets => {\n\t\t\t\t\ttargets && (c.targets = targets);\n\t\t\t\t\ti = queue.indexOf(f);\n\t\t\t\t\tif (~i) {\n\t\t\t\t\t\tqueue.splice(i, 1);\n\t\t\t\t\t\tqueue.length || done();\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t\tqueue.push(f);\n\t\t\t\tc.vars.loadState(f);\n\t\t\t}\n\t\t});\n\t\tqueue.length || done();\n\t\treturn this;\n\t}\n\n\tsetState() {\n\t\tthis.actions.forEach(c => c.targets = c.vars.setState && c.vars.setState(c));\n\t\treturn this;\n\t}\n\n\tkillConflicts(soft) {\n\t\tthis.state.interrupt(soft);\n\t\tthis._kill.forEach(state => state.interrupt(soft));\n\t\treturn this;\n\t}\n\n\trun(skipGetState, merge) {\n\t\tif (this !== _batch) {\n\t\t\tskipGetState || this.getState(merge);\n\t\t\tthis.loadState(() => {\n\t\t\t\tif (!this._killed) {\n\t\t\t\t\tthis.setState();\n\t\t\t\t\tthis.animate();\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t\treturn this;\n\t}\n\n\tclear(stateOnly) {\n\t\tthis.state.clear();\n\t\tstateOnly || (this.actions.length = 0);\n\t}\n\n\tgetStateById(id) {\n\t\tlet i = this.actions.length,\n\t\t\ts;\n\t\twhile (i--) {\n\t\t\ts = this.actions[i].getStateById(id);\n\t\t\tif (s) {\n\t\t\t\treturn s;\n\t\t\t}\n\t\t}\n\t\treturn this.state.idLookup[id] && this.state;\n\t}\n\n\tkill() {\n\t\tthis._killed = 1;\n\t\tthis.clear();\n\t\tdelete _batchLookup[this.id];\n\t}\n}\n\n\nexport class Flip {\n\n\tstatic getState(targets, vars) {\n\t\tlet state = _parseState(targets, vars);\n\t\t_batchAction && _batchAction.states.push(state);\n\t\tvars && vars.batch && Flip.batch(vars.batch).state.add(state);\n\t\treturn state;\n\t}\n\n\tstatic from(state, vars) {\n\t\tvars = vars || {};\n\t\t(\"clearProps\" in vars) || (vars.clearProps = true);\n\t\treturn _fromTo(state, _parseState(vars.targets || state.targets, {props: vars.props || state.props, simple: vars.simple, kill: !!vars.kill}), vars, -1);\n\t}\n\n\tstatic to(state, vars) {\n\t\treturn _fromTo(state, _parseState(vars.targets || state.targets, {props: vars.props || state.props, simple: vars.simple, kill: !!vars.kill}), vars, 1);\n\t}\n\n\tstatic fromTo(fromState, toState, vars) {\n\t\treturn _fromTo(fromState, toState, vars);\n\t}\n\n\tstatic fit(fromEl, toEl, vars) {\n\t\tlet v = vars ? _copy(vars, _fitReserved) : {},\n\t\t\t{absolute, scale, getVars, props, runBackwards, onComplete, simple} = vars || v,\n\t\t\tfitChild = vars && vars.fitChild && _getEl(vars.fitChild),\n\t\t\tbefore = _parseElementState(toEl, props, simple, fromEl),\n\t\t\tafter = _parseElementState(fromEl, 0, simple, before),\n\t\t\tinlineProps = props ? _memoizedRemoveProps[props] : _removeProps;\n\t\tprops && _applyProps(v, before.props);\n\t\tif (runBackwards) {\n\t\t\t_recordInlineStyles(after, inlineProps);\n\t\t\t(\"immediateRender\" in v) || (v.immediateRender = true);\n\t\t\tv.onComplete = function() {\n\t\t\t\t_applyInlineStyles(after);\n\t\t\t\tonComplete && onComplete.apply(this, arguments);\n\t\t\t};\n\t\t}\n\t\tabsolute && _makeAbsolute(after, before);\n\t\tv = _fit(after, before, scale || fitChild, props, fitChild, v.duration || getVars ? v : 0);\n\t\treturn getVars ? v : v.duration ? gsap.to(after.element, v) : null;\n\t}\n\n\tstatic makeAbsolute(targetsOrStates, vars) {\n\t\treturn (targetsOrStates instanceof FlipState ? targetsOrStates : new FlipState(targetsOrStates, vars)).makeAbsolute();\n\t}\n\n\tstatic batch(id) {\n\t\tid || (id = \"default\");\n\t\treturn _batchLookup[id] || (_batchLookup[id] = new FlipBatch(id));\n\t}\n\n\tstatic killFlipsOf(targets, complete) {\n\t\t(targets instanceof FlipState ? targets.targets : _toArray(targets)).forEach(t => t && _killFlip(t._flip, complete !== false ? 1 : 2));\n\t}\n\n\tstatic isFlipping(target) {\n\t\tlet f = Flip.getByTarget(target);\n\t\treturn !!f && f.isActive();\n\t}\n\n\tstatic getByTarget(target) {\n\t\treturn (_getEl(target) || _emptyObj)._flip;\n\t}\n\n\tstatic getElementState(target, props) {\n\t\treturn new ElementState(_getEl(target), props);\n\t}\n\n\tstatic convertCoordinates(fromElement, toElement, point) {\n\t\tlet m = getGlobalMatrix(toElement, true, true).multiply(getGlobalMatrix(fromElement));\n\t\treturn point ? m.apply(point) : m;\n\t}\n\n\n\tstatic register(core) {\n\t\t_body = typeof(document) !== \"undefined\" && document.body;\n\t\tif (_body) {\n\t\t\tgsap = core;\n\t\t\t_setDoc(_body);\n\t\t\t_toArray = gsap.utils.toArray;\n\t\t\tlet snap = gsap.utils.snap(0.1);\n\t\t\t_closestTenth = (value, add) => snap(parseFloat(value) + add);\n\t\t}\n\t}\n}\n\nFlip.version = \"3.11.3\";\n\n// function whenImagesLoad(el, func) {\n// \tlet pending = [],\n// \t\tonLoad = e => {\n// \t\t\tpending.splice(pending.indexOf(e.target), 1);\n// \t\t\te.target.removeEventListener(\"load\", onLoad);\n// \t\t\tpending.length || func();\n// \t\t};\n// \tgsap.utils.toArray(el.tagName.toLowerCase() === \"img\" ? el : el.querySelectorAll(\"img\")).forEach(img => img.complete || img.addEventListener(\"load\", onLoad) || pending.push(img));\n// \tpending.length || func();\n// }\n\ntypeof(window) !== \"undefined\" && window.gsap && window.gsap.registerPlugin(Flip);\n\nexport { Flip as default };"], "names": ["_setDoc", "element", "doc", "ownerDocument", "_transformProp", "style", "_transformOriginProp", "parentNode", "_win", "window", "_identityMatrix", "Matrix2D", "_doc<PERSON>lement", "_doc", "documentElement", "_body", "body", "_gEl", "createElementNS", "transform", "d1", "createElement", "d2", "append<PERSON><PERSON><PERSON>", "position", "_hasOffsetBug", "offsetParent", "<PERSON><PERSON><PERSON><PERSON>", "_getDocScrollTop", "pageYOffset", "scrollTop", "_getDocScrollLeft", "pageXOffset", "scrollLeft", "_svgOwner", "ownerSVGElement", "tagName", "toLowerCase", "_createSibling", "i", "svg", "ns", "getAttribute", "type", "x", "y", "css", "e", "replace", "_svgContainer", "setAttribute", "_divContainer", "cssText", "_getCTM", "m", "getCTM", "removeProperty", "clone", "_placeSiblings", "adjustGOffset", "container", "b", "cs", "isRootSVG", "siblings", "_svgTemps", "_divTemps", "parent", "length", "push", "a", "f", "d", "getBBox", "baseVal", "numberOfItems", "_consolidate", "c", "multiply", "getItem", "matrix", "getComputedStyle", "offsetLeft", "offsetTop", "top", "left", "_setMatrix", "inverse", "this", "determinant", "a2", "b2", "c2", "e2", "f2", "equals", "apply", "point", "decoratee", "getGlobalMatrix", "includeScrollInFixed", "zeroScales", "_forceNonZeroScale", "cache", "_gsap", "uncache", "get", "scaleX", "scaleY", "renderTransform", "temps", "b1", "getBoundingClientRect", "b3", "isFixed", "_isFixed", "nodeType", "_forEachBatch", "batch", "name", "actions", "for<PERSON>ach", "vars", "_listToArray", "list", "split", "join", "_getEl", "target", "_toArray", "console", "warn", "_round", "value", "Math", "round", "_toggleClass", "targets", "className", "action", "el", "classList", "_camelToDashed", "p", "_copy", "obj", "exclude", "result", "_memoizeProps", "props", "_memoizedProps", "_memoizedRemoveProps", "concat", "_removeProps", "_orderByDOMDepth", "comps", "invert", "isElStates", "comp", "_getD<PERSON><PERSON><PERSON>h", "level", "inc", "l", "previousSibling", "t", "sort", "c1", "_recordInlineStyles", "elState", "v", "getPropertyValue", "_dashedNameLookup", "_applyInlineStyles", "state", "_setFinalStates", "onlyTransforms", "finalStates", "_makeAbsolute", "fallbackNode", "ignoreBatch", "displayIsNone", "width", "height", "getProp", "_batch", "_abs", "sd", "_final", "isVisible", "display", "_absoluteProps", "gridArea", "transition", "ElementState", "_emptyObj", "simple", "bounds", "_fit", "_closestTenth", "_filterComps", "filter", "indexOf", "_makeCompsAbsolute", "_applyProps", "_elementsFromElementStates", "elStates", "map", "_handleCallback", "callback", "tl", "add", "FlipState", "_parseState", "targetsOrState", "_getChangingElState", "toState", "fromState", "id", "to1", "idLookup", "to2", "alt", "getElementState", "_lockBodyScroll", "lock", "_bodyLocked", "s", "w", "clientWidth", "outerWidth", "h", "clientHeight", "outerHeight", "_bodyMetrics", "_bodyProps", "overflowY", "overflowX", "_fromTo", "relative", "endTime", "fromNode", "toNode", "run", "clearProps", "onEnter", "onLeave", "absolute", "absoluteOnLeave", "custom", "delay", "paused", "repeat", "repeatDelay", "yoyo", "toggleClass", "nested", "zIndex", "scale", "fade", "stagger", "spin", "prune", "tweenVars", "_reserved", "animation", "gsap", "timeline", "data", "remainingProps", "entering", "leaving", "swapOutTargets", "spinNum", "spinFunc", "interrupted", "addFunc", "swap", "_flip", "isDifferent", "opacity", "splice", "max<PERSON><PERSON><PERSON>", "max", "maxHeight", "min<PERSON><PERSON><PERSON>", "min", "minHeight", "classTargets", "remove", "autoRound", "rotation", "skewX", "modifiers", "immediateRender", "utils", "distribute", "dummy<PERSON><PERSON><PERSON>", "slice", "_callbacks", "eventCallback", "_fitReserved", "duration", "call", "_zTime", "batchTl", "forward", "time", "_run", "anim", "revert", "_killFlip", "_createLookup", "lookup", "elementStates", "_batchAction", "_id", "_batchLookup", "_RAD2DEG", "PI", "_DEG2RAD", "kill", "onComplete", "onUpdate", "onInterrupt", "onStart", "<PERSON><PERSON><PERSON><PERSON>", "getVars", "_parseElementState", "elOrNode", "other", "_findElStateInState", "applyProps", "fromPoint", "toPoint", "parentMatrix", "bbox", "dimensionState", "deep", "_getInverseGlobalMatrix", "core", "getCache", "gmCache", "ticker", "frame", "gMatrix", "ctm", "atan2", "sqrt", "cos", "getProperty", "ceil", "parseFloat", "progress", "_interrupt", "onInterruptParams", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "update", "soft", "_this", "interrupt", "recordInlineStyles", "clear", "fit", "elStatesInOrder", "toElStates", "property", "es", "index", "es2", "compare", "place", "s1", "s2", "enter", "leave", "changed", "unchanged", "placeIfDoesNotExist", "s1Alt", "s2Alt", "l1", "l2", "a1", "timelines", "foundInProgress", "_this2", "updateVisibility", "makeAbsolute", "targetsAreElementStates", "_kill", "JSON", "stringify", "self", "nodeName", "_getID", "_recordProps", "trim", "FlipAction", "getStateById", "states", "FlipBatch", "config", "animate", "getState", "merge", "prevBatch", "prevAction", "_this3", "killConflicts", "Flip", "_this4", "once", "restart", "loadState", "done", "queue", "setState", "skipGetState", "_this5", "_killed", "stateOnly", "from", "to", "fromTo", "fromEl", "toEl", "runBackwards", "before", "after", "inlineProps", "arguments", "targetsOrStates", "killFlipsOf", "complete", "isFlipping", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isActive", "convertCoordinates", "fromElement", "toElement", "register", "document", "toArray", "snap", "version", "registerPlugin"], "mappings": ";;;;;;;;;6MAeW,SAAVA,EAAUC,OACLC,EAAMD,EAAQE,eAAiBF,IAC7BG,KAAkBH,EAAQI,QAAU,gBAAiBJ,EAAQI,QAElEC,GADAF,EAAiB,eACuB,eAElCF,EAAIK,aAAeL,EAAMA,EAAIK,iBACpCC,EAAOC,OACPC,EAAkB,IAAIC,EAClBT,EAAK,CAERU,GADAC,EAAOX,GACWY,gBAClBC,EAAQb,EAAIc,MACZC,EAAOJ,EAAKK,gBAAgB,6BAA8B,MAErDb,MAAMc,UAAY,WAEnBC,EAAKlB,EAAImB,cAAc,OAC1BC,EAAKpB,EAAImB,cAAc,OACxBN,EAAMQ,YAAYH,GAClBA,EAAGG,YAAYD,GACfF,EAAGf,MAAMmB,SAAW,SACpBJ,EAAGf,MAAMD,GAAkB,uBAC3BqB,EAAiBH,EAAGI,eAAiBN,EACrCL,EAAMY,YAAYP,UAEZlB,EAoCW,SAAnB0B,WAAyBpB,EAAKqB,aAAgBhB,EAAKiB,WAAalB,EAAYkB,WAAaf,EAAMe,WAAa,EACxF,SAApBC,WAA0BvB,EAAKwB,aAAenB,EAAKoB,YAAcrB,EAAYqB,YAAclB,EAAMkB,YAAc,EACnG,SAAZC,EAAYjC,UAAWA,EAAQkC,kBAA6D,SAAxClC,EAAQmC,QAAU,IAAIC,cAA0BpC,EAAU,MAU7F,SAAjBqC,EAAkBrC,EAASsC,MACtBtC,EAAQM,aAAeM,GAAQb,EAAQC,IAAW,KACjDuC,EAAMN,EAAUjC,GACnBwC,EAAKD,EAAOA,EAAIE,aAAa,UAAY,6BAAgC,+BACzEC,EAAOH,EAAOD,EAAI,OAAS,IAAO,MAClCK,EAAU,IAANL,EAAU,EAAI,IAClBM,EAAU,IAANN,EAAU,IAAM,EACpBO,EAAM,0EACNC,EAAIlC,EAAKK,gBAAkBL,EAAKK,gBAAgBuB,EAAGO,QAAQ,SAAU,QAASL,GAAQ9B,EAAKQ,cAAcsB,UACtGJ,IACEC,GAScS,EAAlBA,GAAkCX,EAAerC,GACjD8C,EAAEG,aAAa,QAAS,KACxBH,EAAEG,aAAa,SAAU,KACzBH,EAAEG,aAAa,YAAa,aAAeN,EAAI,IAAMC,EAAI,KACzDI,EAAc1B,YAAYwB,KAZrBI,KACJA,EAAgBb,EAAerC,IACjBI,MAAM+C,QAAUN,GAE/BC,EAAE1C,MAAM+C,QAAUN,EAAM,gCAAkCD,EAAI,WAAaD,EAAI,KAC/EO,EAAc5B,YAAYwB,KAUrBA,OAEF,4BAUG,SAAVM,EAAUb,OAERrB,EADGmC,EAAId,EAAIe,gBAEPD,IACJnC,EAAYqB,EAAInC,MAAMD,GACtBoC,EAAInC,MAAMD,GAAkB,OAC5BoC,EAAIjB,YAAYN,GAChBqC,EAAIrC,EAAKsC,SACTf,EAAIb,YAAYV,GAChBE,EAAaqB,EAAInC,MAAMD,GAAkBe,EAAaqB,EAAInC,MAAMmD,eAAepD,EAAe4C,QAAQ,WAAY,OAAOX,gBAEnHiB,GAAK5C,EAAgB+C,QAEZ,SAAjBC,EAAkBzD,EAAS0D,OAKzBC,EAAWN,EAAGO,EAAGjB,EAAGC,EAAGiB,EAJpBtB,EAAMN,EAAUjC,GACnB8D,EAAY9D,IAAYuC,EACxBwB,EAAWxB,EAAMyB,EAAYC,EAC7BC,EAASlE,EAAQM,cAEdN,IAAYO,SACRP,KAER+D,EAASI,QAAUJ,EAASK,KAAK/B,EAAerC,EAAS,GAAIqC,EAAerC,EAAS,GAAIqC,EAAerC,EAAS,IACjH2D,EAAYpB,EAAMS,EAAgBE,EAC9BX,EACCuB,GAEHnB,IADAiB,EAAIR,EAAQpD,IACL8C,EAAIc,EAAES,EACbzB,GAAKgB,EAAEU,EAAIV,EAAEW,EACblB,EAAI5C,GACMT,EAAQwE,SAClBZ,EAAI5D,EAAQwE,UAGZ7B,GADAU,GADAA,EAAIrD,EAAQkB,UAAYlB,EAAQkB,UAAUuD,QAAU,IAC7CC,cAAoD,EAAlBrB,EAAEqB,cAzC/B,SAAfC,aAAetB,WACVuB,EAAI,IAAIlE,EACX4B,EAAI,EACEA,EAAIe,EAAEqB,cAAepC,IAC3BsC,EAAEC,SAASxB,EAAEyB,QAAQxC,GAAGyC,eAElBH,EAmC0DD,CAAatB,GAAKA,EAAEyB,QAAQ,GAAGC,OAAvEtE,GACjB4D,EAAIT,EAAEjB,EAAIU,EAAEuB,EAAIhB,EAAEhB,EACxBA,EAAIS,EAAEO,EAAIA,EAAEjB,EAAIU,EAAEkB,EAAIX,EAAEhB,IAExBS,EAAI,IAAI3C,EACRiC,EAAIC,EAAI,GAELc,GAAmD,MAAlC1D,EAAQmC,QAAQC,gBACpCO,EAAIC,EAAI,IAERkB,EAAYvB,EAAM2B,GAAQ5C,YAAYqC,GACvCA,EAAUV,aAAa,YAAa,UAAYI,EAAEgB,EAAI,IAAMhB,EAAEO,EAAI,IAAMP,EAAEuB,EAAI,IAAMvB,EAAEkB,EAAI,KAAOlB,EAAEP,EAAIH,GAAK,KAAOU,EAAEiB,EAAI1B,GAAK,SACxH,IACND,EAAIC,EAAI,EACJpB,MACH6B,EAAIrD,EAAQyB,aACZmC,EAAI5D,GACS4D,EAANA,GAAUA,EAAEtD,aAAesD,IAAMP,GAAKO,EAAEtD,YACe,GAAxDC,EAAKyE,iBAAiBpB,GAAGzD,GAAkB,IAAIgE,SACnDxB,EAAIiB,EAAEqB,WACNrC,EAAIgB,EAAEsB,UACNtB,EAAI,MAKa,cADpBC,EAAKtD,EAAKyE,iBAAiBhF,IACpBuB,UAA2C,UAAhBsC,EAAGtC,aACpC8B,EAAIrD,EAAQyB,aACLyC,GAAUA,IAAWb,GAC3BV,GAAKuB,EAAOlC,YAAc,EAC1BY,GAAKsB,EAAOrC,WAAa,EACzBqC,EAASA,EAAO5D,YAGlBsD,EAAID,EAAUvD,OACZ+E,IAAOnF,EAAQkF,UAAYtC,EAAK,KAClCgB,EAAEwB,KAAQpF,EAAQiF,WAAatC,EAAK,KACpCiB,EAAEzD,GAAkB0D,EAAG1D,GACvByD,EAAEvD,GAAwBwD,EAAGxD,GAM7BuD,EAAErC,SAA2B,UAAhBsC,EAAGtC,SAAuB,QAAU,WACjDvB,EAAQM,WAAWgB,YAAYqC,UAEzBA,EAEK,SAAb0B,EAAchC,EAAGgB,EAAGT,EAAGgB,EAAGL,EAAGzB,EAAGwB,UAC/BjB,EAAEgB,EAAIA,EACNhB,EAAEO,EAAIA,EACNP,EAAEuB,EAAIA,EACNvB,EAAEkB,EAAIA,EACNlB,EAAEP,EAAIA,EACNO,EAAEiB,EAAIA,EACCjB,EA7MT,IAAIzC,EAAML,EAAMI,EAAaG,EAAOoC,EAAeF,EAAevC,EAAiBO,EAGlFQ,IAFArB,EAAiB,YACjBE,EAAuBF,EAAiB,SA8DxC6D,EAAY,GACZC,EAAY,GA+IAvD,0BAKZ4E,QAAA,uBACMjB,EAAoBkB,KAApBlB,EAAGT,EAAiB2B,KAAjB3B,EAAGgB,EAAcW,KAAdX,EAAGL,EAAWgB,KAAXhB,EAAGzB,EAAQyC,KAARzC,EAAGwB,EAAKiB,KAALjB,EACnBkB,EAAenB,EAAIE,EAAIX,EAAIgB,GAAM,aAC3BS,EACNE,KACAhB,EAAIiB,GACH5B,EAAI4B,GACJZ,EAAIY,EACLnB,EAAImB,GACHZ,EAAIN,EAAIC,EAAIzB,GAAK0C,IAChBnB,EAAIC,EAAIV,EAAId,GAAK0C,MAIrBX,SAAA,kBAASE,OACHV,EAAoBkB,KAApBlB,EAAGT,EAAiB2B,KAAjB3B,EAAGgB,EAAcW,KAAdX,EAAGL,EAAWgB,KAAXhB,EAAGzB,EAAQyC,KAARzC,EAAGwB,EAAKiB,KAALjB,EACnBmB,EAAKV,EAAOV,EACZqB,EAAKX,EAAOH,EACZe,EAAKZ,EAAOnB,EACZvC,EAAK0D,EAAOR,EACZqB,EAAKb,EAAOjC,EACZ+C,EAAKd,EAAOT,SACNe,EAAWE,KACjBE,EAAKpB,EAAIsB,EAAKf,EACda,EAAK7B,EAAI+B,EAAKpB,EACdmB,EAAKrB,EAAIhD,EAAKuD,EACdc,EAAK9B,EAAIvC,EAAKkD,EACdzB,EAAI8C,EAAKvB,EAAIwB,EAAKjB,EAClBN,EAAIsB,EAAKhC,EAAIiC,EAAKtB,MAGpBf,MAAA,wBACQ,IAAI9C,SAAS6E,KAAKlB,EAAGkB,KAAK3B,EAAG2B,KAAKX,EAAGW,KAAKhB,EAAGgB,KAAKzC,EAAGyC,KAAKjB,MAGlEwB,OAAA,gBAAOf,OACDV,EAAoBkB,KAApBlB,EAAGT,EAAiB2B,KAAjB3B,EAAGgB,EAAcW,KAAdX,EAAGL,EAAWgB,KAAXhB,EAAGzB,EAAQyC,KAARzC,EAAGwB,EAAKiB,KAALjB,SACZD,IAAMU,EAAOV,GAAKT,IAAMmB,EAAOnB,GAAKgB,IAAMG,EAAOH,GAAKL,IAAMQ,EAAOR,GAAKzB,IAAMiC,EAAOjC,GAAKwB,IAAMS,EAAOT,KAGhHyB,MAAA,eAAMC,EAAOC,YAAAA,IAAAA,EAAU,QACjBtD,EAAQqD,EAARrD,EAAGC,EAAKoD,EAALpD,EACNyB,EAAoBkB,KAApBlB,EAAGT,EAAiB2B,KAAjB3B,EAAGgB,EAAcW,KAAdX,EAAGL,EAAWgB,KAAXhB,EAAGzB,EAAQyC,KAARzC,EAAGwB,EAAKiB,KAALjB,SACjB2B,EAAUtD,EAAKA,EAAI0B,EAAIzB,EAAIgC,EAAI9B,GAAM,EACrCmD,EAAUrD,EAAKD,EAAIiB,EAAIhB,EAAI2B,EAAID,GAAM,EAC9B2B,+BAjDI5B,EAAKT,EAAKgB,EAAKL,EAAKzB,EAAKwB,YAAzBD,IAAAA,EAAE,YAAGT,IAAAA,EAAE,YAAGgB,IAAAA,EAAE,YAAGL,IAAAA,EAAE,YAAGzB,IAAAA,EAAE,YAAGwB,IAAAA,EAAE,GACtCe,EAAWE,KAAMlB,EAAGT,EAAGgB,EAAGL,EAAGzB,EAAGwB,GA4D3B,SAAS4B,gBAAgBlG,EAASsF,EAAS5B,EAAeyC,OAC3DnG,IAAYA,EAAQM,aAAeM,GAAQb,EAAQC,IAAUa,kBAAoBb,SAC9E,IAAIU,MAER0F,EAlPiB,SAArBC,mBAAqBvD,WAChBuB,EAAGiC,EACAxD,GAAKA,IAAMhC,IACjBwF,EAAQxD,EAAEyD,QACDD,EAAME,SAAWF,EAAMG,IAAI3D,EAAG,KACnCwD,IAAUA,EAAMI,SAAWJ,EAAMK,QAAUL,EAAMM,kBACpDN,EAAMI,OAASJ,EAAMK,OAAS,KAC9BL,EAAMM,gBAAgB,EAAGN,GACzBjC,EAAIA,EAAED,KAAKkC,GAAUjC,EAAI,CAACiC,IAE3BxD,EAAIA,EAAExC,kBAEA+D,EAsOSgC,CAAmBrG,GAEnC6G,EADM5E,EAAUjC,GACFgE,EAAYC,EAC1BN,EAAYF,EAAezD,EAAS0D,GACpCoD,EAAKD,EAAM,GAAGE,wBACdrB,EAAKmB,EAAM,GAAGE,wBACdC,EAAKH,EAAM,GAAGE,wBACd7C,EAASP,EAAUrD,WACnB2G,GAAWd,GArND,SAAXe,SAAWlH,SACsC,UAA5CO,EAAKyE,iBAAiBhF,GAASuB,YAGnCvB,EAAUA,EAAQM,aACkB,IAArBN,EAAQmH,SACfD,SAASlH,WA+MkBkH,CAASlH,GAC5CqD,EAAI,IAAI3C,GACNgF,EAAGN,KAAO0B,EAAG1B,MAAQ,KACrBM,EAAGP,IAAM2B,EAAG3B,KAAO,KACnB6B,EAAG5B,KAAO0B,EAAG1B,MAAQ,KACrB4B,EAAG7B,IAAM2B,EAAG3B,KAAO,IACpB2B,EAAG1B,MAAQ6B,EAAU,EAAInF,KACzBgF,EAAG3B,KAAO8B,EAAU,EAAItF,SAE1BuC,EAAOxC,YAAYiC,GACfyC,MACHU,EAAKV,EAAWjC,OACT2C,MACNpB,EAAKU,EAAWU,IACbJ,OAAShB,EAAGiB,OAAS,EACxBjB,EAAGkB,gBAAgB,EAAGlB,UAGjBJ,EAAUjC,EAAEiC,UAAYjC,ECxSf,SAAhB+D,EAAiBC,EAAOC,UAASD,EAAME,QAAQC,QAAQ,SAAAnD,UAAKA,EAAEoD,KAAKH,IAASjD,EAAEoD,KAAKH,GAAMjD,KAO1E,SAAfqD,EAAeC,SAAyB,iBAAVA,EAAqBA,EAAKC,MAAM,KAAKC,KAAK,IAAID,MAAM,KAAOD,EAGhF,SAATG,EAASC,UAAUC,EAASD,GAAQ,IAAME,QAAQC,KAAK,qBAAsBH,GACpE,SAATI,EAASC,UAASC,KAAKC,MAAc,IAARF,GAAiB,KAAS,EACxC,SAAfG,EAAgBC,EAASC,EAAWC,UAAWF,EAAQhB,QAAQ,SAAAmB,UAAMA,EAAGC,UAAUF,GAAQD,KAGzE,SAAjBI,EAAiBC,UAAKA,EAAE/F,QAAQ,WAAY,OAAOX,cAC3C,SAAR2G,EAASC,EAAKC,OACIH,EAAbI,EAAS,OACRJ,KAAKE,EACTC,EAAQH,KAAOI,EAAOJ,GAAKE,EAAIF,WAEzBI,EAGQ,SAAhBC,GAAgBC,OACXN,EAAIO,GAAeD,GAAS1B,EAAa0B,UAC7CE,GAAqBF,GAASN,EAAES,OAAOC,IAChCV,EAoBW,SAAnBW,GAAoBC,EAAOC,EAAQC,UAClCF,EAAMlC,QAAQ,SAAAqC,UAAQA,EAAKtF,EAXb,SAAfuF,aAAgBnB,EAAIgB,EAAQI,YAAAA,IAAAA,EAAQ,WAC/B7F,EAASyE,EAAGrI,WACf0J,EAAM,aAAQ,GAAMD,IAAUJ,GAAU,EAAI,GAC5CM,EAAIN,EAAgB,KAANK,EAAY,EACpBrB,GACNsB,GAAKD,EACLrB,EAAKA,EAAGuB,uBAEFhG,EAAS+F,EAAIH,aAAa5F,EAAQyF,EAAQI,EAAQ,GAAKE,EAG/BH,CAAaF,EAAaC,EAAK7J,QAAU6J,EAAKM,EAAGR,KAChFD,EAAMU,KAAK,SAACC,EAAI1E,UAAO0E,EAAG9F,EAAIoB,EAAGpB,IAC1BmF,EAEc,SAAtBY,GAAuBC,EAASnB,WAI9BN,EAAG0B,EAHApK,EAAQmK,EAAQvK,QAAQI,MAC3BiE,EAAIkG,EAAQ1H,IAAM0H,EAAQ1H,KAAO,GACjCP,EAAI8G,EAAMjF,OAEJ7B,KAENkI,EAAIpK,EADJ0I,EAAIM,EAAM9G,KACMlC,EAAMqK,iBAAiB3B,GACvCzE,EAAED,KAAKoG,EAAI1B,EAAI4B,EAAkB5B,KAAO4B,EAAkB5B,GAAKD,EAAeC,IAAK0B,UAE7EpK,EAEa,SAArBuK,GAAqBC,OAChB/H,EAAM+H,EAAM/H,IACfzC,EAAQwK,EAAM5K,QAAQI,MACtBkC,EAAI,MACLsI,EAAMtE,MAAME,QAAU,EACflE,EAAIO,EAAIsB,OAAQ7B,GAAG,EACzBO,EAAIP,EAAE,GAAMlC,EAAMyC,EAAIP,IAAMO,EAAIP,EAAE,GAAMlC,EAAMmD,eAAeV,EAAIP,IAGjD,SAAlBuI,GAAmBnB,EAAOoB,GACzBpB,EAAMlC,QAAQ,SAAA5C,UAAKA,EAAEP,EAAEiC,MAAME,QAAU,IACvCsE,GAAkBpB,EAAMqB,YAAYvD,QAAQmD,IAG7B,SAAhBK,GAAiBT,EAASU,EAAcC,OAItChC,EAAQiC,EAAetH,EAHlB7D,EAA6CuK,EAA7CvK,QAASoL,EAAoCb,EAApCa,MAAOC,EAA6Bd,EAA7Bc,OAAQ7E,EAAqB+D,EAArB/D,QAAS8E,EAAYf,EAAZe,QACtClL,EAAQJ,EAAQI,MAChBkC,EAAI,KAEqB,iBAAlB2I,IAAgCA,EAAeV,GACnDgB,IAA0B,IAAhBL,SACbK,GAAOC,KAAKpH,KAAK,CAAC+F,EAAGnK,EAAS4D,EAAG2G,EAASlG,EAAGkG,EAASkB,GAAI,IAC1DF,GAAOG,OAAOtH,KAAK,kBAAOmG,EAAQjE,MAAME,QAAU,IAAMmE,GAAmBJ,KACpEvK,MAERmL,EAAuC,SAAvBG,EAAQ,WAEnBf,EAAQoB,YAAaR,IACzBA,IAAkBb,GAAoBC,EAAS,CAAC,YAAYqB,QAAUX,EAAaW,SACnFrB,EAAQxF,OAASkG,EAAalG,OAC9BwF,EAAQa,MAAQA,EAAQb,EAAQa,OAASH,EAAaG,MACtDb,EAAQc,OAASA,EAASd,EAAQc,QAAUJ,EAAaI,QAG1Df,GAAoBC,EAASsB,GAC7BhI,EAAKrD,OAAOwE,iBAAiBhF,GACtBsC,KACNlC,EAAMyL,EAAevJ,IAAMuB,EAAGgI,EAAevJ,OAE9ClC,EAAM0L,SAAW,gBACjB1L,EAAM2L,WAAa,OAEnB3L,EAAMmB,SAAW,WACjBnB,EAAMgL,MAAQA,EAAQ,KACtBhL,EAAMiL,OAASA,EAAS,KACxBjL,EAAM+E,MAAQ/E,EAAM+E,IAAM,OAC1B/E,EAAMgF,OAAShF,EAAMgF,KAAO,OACxBoB,EACH0C,EAAS,IAAI8C,GAAahM,YAE1BkJ,EAASH,EAAMwB,EAAS0B,IACjB1K,SAAW,WACdgJ,EAAQ2B,OAAQ,KACfC,EAASnM,EAAQ+G,wBACrBmC,EAAOnE,OAAS,IAAIrE,EAAS,EAAG,EAAG,EAAG,EAAGyL,EAAO/G,KAAOtD,IAAqBqK,EAAOhH,IAAMxD,UAEzFuH,EAAOnE,OAASmB,gBAAgBlG,GAAS,GAAO,GAAO,UAGzDkJ,EAASkD,GAAKlD,EAAQqB,GAAS,GAC/BA,EAAQ5H,EAAI0J,EAAcnD,EAAOvG,EAAG,KACpC4H,EAAQ3H,EAAIyJ,EAAcnD,EAAOtG,EAAG,KAC7B5C,EAEO,SAAfsM,GAAgB5C,EAAOlB,UACN,IAAZA,IACHA,EAAUR,EAASQ,GACnBkB,EAAQA,EAAM6C,OAAO,SAAA3H,OACqC,IAArD4D,EAAQgE,SAAS5H,EAAE6G,GAAK,EAAI7G,EAAEhB,EAAIgB,EAAEP,GAAGrE,gBAChC,EAEV4E,EAAEuF,EAAE5D,MAAMK,gBAAgB,GACtBhC,EAAEhB,EAAE+H,YACP/G,EAAEuF,EAAE/J,MAAMgL,MAAQxG,EAAEhB,EAAEwH,MAAQ,KAC9BxG,EAAEuF,EAAE/J,MAAMiL,OAASzG,EAAEhB,EAAEyH,OAAS,SAK7B3B,EAEa,SAArB+C,GAAqB/C,UAASD,GAAiBC,GAAO,GAAMlC,QAAQ,SAAA5C,UAAMA,EAAEP,EAAEsH,WAAa/G,EAAEhB,EAAE+H,YAAcX,GAAcpG,EAAE6G,GAAK,EAAI7G,EAAEhB,EAAIgB,EAAEP,EAAGO,EAAEhB,EAAG,KAaxI,SAAd8I,GAAe1M,EAASoJ,OAEtBN,EADG1I,EAAQJ,EAAQI,OAASJ,MAExB8I,KAAKM,EACThJ,EAAM0I,GAAKM,EAAMN,GAQU,SAA7B6D,GAA6BC,UAAYA,EAASC,IAAI,SAAAtC,UAAWA,EAAQvK,UACvD,SAAlB8M,GAAmBC,EAAUH,EAAUI,UAAOD,GAAYH,EAASzI,QAAU6I,EAAGC,IAAIF,EAASJ,GAA2BC,GAAWI,EAAI,IAAIE,GAAUN,EAAU,GAAG,IAAQ,GA0F5J,SAAdO,GAAeC,EAAgB3F,UAAS2F,aAA0BF,GAAYE,EAAiB,IAAIF,GAAUE,EAAgB3F,GACvG,SAAtB4F,GAAuBC,EAASC,EAAWC,OACtCC,EAAMH,EAAQI,SAASF,GAC1BG,EAAML,EAAQM,IAAIJ,UACZG,EAAIhC,YAAgB4B,EAAUM,gBAAgBF,EAAI3N,UAAY2N,GAAKhC,WAAc8B,EAAI9B,UAAmB8B,EAANE,EAGxF,SAAlBG,GAAkBC,MACbA,IAASC,EAAa,KACrBC,EAAInN,EAAMV,MACb8N,EAAIpN,EAAMqN,cAAgB3N,OAAO4N,WACjCC,EAAIvN,EAAMwN,eAAiB9N,OAAO+N,YAClCjM,EAAI,KACDyL,IAASG,GAAKG,GAAI,MACd/L,KACNkM,EAAalM,GAAK2L,EAAEQ,EAAWnM,IAE5B4L,IACHD,EAAE7C,MAAQtK,EAAMqN,YAAc,KAC9BF,EAAES,UAAY,UAEXL,IACHJ,EAAE5C,OAASvK,EAAMwN,aAAe,KAChCL,EAAEU,UAAY,UAEfX,EAAaD,OACP,GAAIC,EAAa,MAChB1L,KACNkM,EAAalM,GAAM2L,EAAEQ,EAAWnM,IAAMkM,EAAalM,GAAM2L,EAAE1K,eAAesF,EAAe4F,EAAWnM,KAErG0L,EAAcD,IAKP,SAAVa,GAAWrB,EAAWD,EAAS7F,EAAMoH,GACnCtB,aAAqBL,IAAaI,aAAmBJ,IAAcjF,QAAQC,KAAK,iCAehFsC,EAAG1B,EAAGgG,EAASxM,EAAGqG,EAAIkB,EAAMe,EAAOpC,EAASuC,EAAagE,EAAUC,EAAQC,EAAK5K,EAAGT,IAb9EsL,GADNzH,EAAOA,GAAQ,IACTyH,WAAYC,EAAkK1H,EAAlK0H,QAASC,EAAyJ3H,EAAzJ2H,QAASC,EAAgJ5H,EAAhJ4H,SAAUC,EAAsI7H,EAAtI6H,gBAAiBC,EAAqH9H,EAArH8H,OAAQC,EAA6G/H,EAA7G+H,MAAOC,EAAsGhI,EAAtGgI,OAAQC,EAA8FjI,EAA9FiI,OAAQC,EAAsFlI,EAAtFkI,YAAaC,EAAyEnI,EAAzEmI,KAAMC,EAAmEpI,EAAnEoI,YAAaC,EAAsDrI,EAAtDqI,OAAQC,EAA8CtI,EAA9CsI,OAAQC,EAAsCvI,EAAtCuI,MAAOC,EAA+BxI,EAA/BwI,KAAMC,EAAyBzI,EAAzByI,QAASC,EAAgB1I,EAAhB0I,KAAMC,EAAU3I,EAAV2I,MACzKhH,GAAS,UAAW3B,EAAOA,EAAO8F,GAAWnE,MAC7CiH,EAAYtH,EAAMtB,EAAM6I,IACxBC,EAAYC,EAAKC,SAAS,CAAEjB,MAAAA,EAAOC,OAAAA,EAAQC,OAAAA,EAAQC,YAAAA,EAAaC,KAAAA,EAAMc,KAAM,WAC5EC,EAAiBN,EACjBO,EAAW,GACXC,EAAU,GACVnH,EAAQ,GACRoH,EAAiB,GACjBC,GAAmB,IAATZ,EAAgB,EAAIA,GAAQ,EACtCa,EAA4B,mBAAVb,EAAuBA,EAAO,kBAAMY,GACtDE,EAAc1D,EAAU0D,aAAe3D,EAAQ2D,YAC/CC,EAAUX,EAAuB,IAAb1B,EAAiB,KAAO,YAGxC/F,KAAKwE,EAAQI,SACjBsB,EAAU1B,EAAQM,IAAI9E,GAA2BuE,GAAoBC,EAASC,EAAWzE,GAA9DwE,EAAQI,SAAS5E,GAC5CH,EAAKqG,EAAOhP,QACZ+O,EAAWxB,EAAUG,SAAS5E,IAC9ByE,EAAUK,IAAI9E,IAAMH,IAAOoG,EAAS/O,UAAYuN,EAAUK,IAAI9E,GAAG6C,WAAcqD,EAAOrD,YAAeoD,EAAWxB,EAAUK,IAAI9E,IAC1HiG,GACHlF,EAAO,CAACM,EAAGxB,EAAI/E,EAAGmL,EAAU1K,EAAG2K,EAAQvD,GAAIsD,EAAS/O,UAAY2I,EAAK,EAAIqG,EAAOrD,UAAY,GAAK,GACjGjC,EAAMtF,KAAKyF,GACPA,EAAK4B,KACJ5B,EAAK4B,GAAK,IACb5B,EAAKjG,EAAIoL,EACTnF,EAAKxF,EAAI0K,GAGVkC,GAAe3G,GAAoBT,EAAKjG,EAAGwF,EAAQE,GAAqBF,GAASI,IACjFyG,GAAQvG,EAAMtF,KAAKyF,EAAKsH,KAAO,CAAChH,EAAG4E,EAAS/O,QAAS4D,EAAGiG,EAAKjG,EAAGS,EAAGwF,EAAKxF,EAAGoH,IAAK5B,EAAK4B,GAAI0F,KAAMtH,KAEhGlB,EAAGyI,MAAQrC,EAAS/O,QAAQoR,MAAQ7F,GAASA,GAAOkF,SAAWF,GACrDvB,EAAOrD,YACjBjC,EAAMtF,KAAK,CAAC+F,EAAGxB,EAAI/E,EAAGmF,EAAMiG,EAAQ,CAACrD,UAAU,IAAKtH,EAAG2K,EAAQvD,GAAI,EAAGmF,SAAU,IAChFjI,EAAGyI,MAAQ7F,GAASA,GAAOkF,SAAWF,GAIxCnH,IAAUC,GAAeD,IAAUD,GAAcC,IAAQ5B,QAAQ,SAAAsB,UAAKuH,EAAUvH,GAAK,SAAAxG,UAAKoH,EAAMpH,GAAG+B,EAAE+E,MAAMN,MAC3GY,EAAMqB,YAAcA,EAAc,GAElCkE,EAAM,mBACLxF,GAAiBC,GACjBoE,IAAgB,GAEXxL,EAAI,EAAGA,EAAIoH,EAAMvF,OAAQ7B,IAC7BuH,EAAOH,EAAMpH,GACb+B,EAAIwF,EAAKxF,EACTT,EAAIiG,EAAKjG,GACLwM,GAAU/L,EAAEgN,YAAYzN,IAAOiG,EAAK+G,UAGvCjI,EAAKkB,EAAKM,GACV2F,GAAYjG,EAAK4B,GAAK,IAAMnJ,IAAM+B,EAAEU,OAASmB,gBAAgByC,GAAI,GAAO,GAAO,IAC3E/E,EAAE+H,WAAatH,EAAEsH,WAChB9B,EAAK4B,GAAK,GACbb,EAAQ,IAAIoB,GAAarD,EAAIS,EAAOmE,EAAUrB,QAC9CE,GAAKxB,EAAOvG,EAAG2L,EAAO,EAAG,EAAGpF,GAC5BA,EAAM7F,OAASmB,gBAAgByC,GAAI,GAAO,GAAO,GACjDiC,EAAM/H,IAAMgH,EAAKjG,EAAEf,IACnBgH,EAAKxF,EAAIA,EAAIuG,EACbqF,IAAStH,EAAGvI,MAAMkR,QAAUL,EAAcrN,EAAE0N,QAAUjN,EAAEiN,SACxDpB,GAAWY,EAAe1M,KAAKuE,IACX,EAAVkB,EAAK4B,IAAUwE,IACzBtH,EAAGvI,MAAMkR,QAAUL,EAAc5M,EAAEiN,QAAU1N,EAAE0N,QAAU,KAE1DlF,GAAK/H,EAAGT,EAAGoM,EAAO5G,IAERxF,EAAE+H,YAActH,EAAEsH,YACvB/H,EAAE+H,UAGKtH,EAAEsH,YACb/H,EAAEf,IAAMwB,EAAExB,IACVgO,EAAQzM,KAAKR,GACb8F,EAAM6H,OAAOjP,IAAK,GAClB+M,GAAYS,GAAU1D,GAAK/H,EAAGT,EAAGoM,EAAO5G,KANxC/E,EAAEsH,WAAaiF,EAASxM,KAAKC,GAC7BqF,EAAM6H,OAAOjP,IAAK,KAQf0N,IACJrH,EAAGvI,MAAMoR,SAAWnJ,KAAKoJ,IAAIpN,EAAE+G,MAAOxH,EAAEwH,OAAS,KACjDzC,EAAGvI,MAAMsR,UAAYrJ,KAAKoJ,IAAIpN,EAAEgH,OAAQzH,EAAEyH,QAAU,KACpD1C,EAAGvI,MAAMuR,SAAWtJ,KAAKuJ,IAAIvN,EAAE+G,MAAOxH,EAAEwH,OAAS,KACjDzC,EAAGvI,MAAMyR,UAAYxJ,KAAKuJ,IAAIvN,EAAEgH,OAAQzH,EAAEyH,QAAU,MAErDyE,GAAUD,GAAelH,EAAGC,UAAUqE,IAAI4C,IAnC1CnG,EAAM6H,OAAOjP,IAAK,GAqCnByI,EAAY3G,KAAKC,OAEdyN,KACAjC,IACHiC,EAAe/G,EAAY8B,IAAI,SAAAoB,UAAKA,EAAEjO,UACtC8P,GAAUgC,EAAatK,QAAQ,SAAA1E,UAAKA,EAAE8F,UAAUmJ,OAAOlC,MAGxD/B,IAAgB,GAEZkC,GACHK,EAAU3J,OAAS,SAAApE,UAAKoH,EAAMpH,GAAG+B,EAAEqC,QACnC2J,EAAU1J,OAAS,SAAArE,UAAKoH,EAAMpH,GAAG+B,EAAEsC,UAEnC0J,EAAUjF,MAAQ,SAAA9I,UAAKoH,EAAMpH,GAAG+B,EAAE+G,MAAQ,MAC1CiF,EAAUhF,OAAS,SAAA/I,UAAKoH,EAAMpH,GAAG+B,EAAEgH,OAAS,MAC5CgF,EAAU2B,UAAYvK,EAAKuK,YAAa,GAEzC3B,EAAU1N,EAAI,SAAAL,UAAKoH,EAAMpH,GAAG+B,EAAE1B,EAAI,MAClC0N,EAAUzN,EAAI,SAAAN,UAAKoH,EAAMpH,GAAG+B,EAAEzB,EAAI,MAClCyN,EAAU4B,SAAW,SAAA3P,UAAKoH,EAAMpH,GAAG+B,EAAE4N,UAAY9B,EAA0C,IAAnCa,EAAS1O,EAAGkG,EAAQlG,GAAIkG,GAAiB,IACjG6H,EAAU6B,MAAQ,SAAA5P,UAAKoH,EAAMpH,GAAG+B,EAAE6N,OAElC1J,EAAUkB,EAAMmD,IAAI,SAAAjI,UAAKA,EAAEuF,KAEvB4F,GAAqB,IAAXA,IACbM,EAAU8B,UAAY,CAACpC,OAAQ,yBAAMA,IACrCM,EAAUN,OAASA,EACnBM,EAAU+B,iBAA2C,IAAzB3K,EAAK2K,iBAGlCnC,IAASI,EAAUiB,QAAU,SAAAhP,UAAKoH,EAAMpH,GAAGmJ,GAAK,EAAI,EAAkB,EAAd/B,EAAMpH,GAAGmJ,GAAS/B,EAAMpH,GAAG+B,EAAEiN,QAAU,QAE3FR,EAAe3M,OAAQ,CAC1B+L,EAAUM,EAAK6B,MAAMC,WAAWpC,OAC5BqC,EAAa/J,EAAQgK,MAAM1B,EAAe3M,QAC9CkM,EAAUH,QAAU,SAAC5N,EAAGqG,UAAOuH,GAASY,EAAetE,QAAQ7D,GAAMH,EAAQgE,QAAQ9C,EAAMpH,GAAG6O,KAAKhH,GAAK7H,EAAGqG,EAAI4J,OAUhHE,GAAWjL,QAAQ,SAAAF,UAAQG,EAAKH,IAASiJ,EAAUmC,cAAcpL,EAAMG,EAAKH,GAAOG,EAAKH,EAAO,aAE3FiI,GAAU/G,EAAQrE,WAMhB2E,KALL6H,EAAiB5H,EAAMsH,EAAWC,IAC9B,UAAWf,IACdA,EAAO7I,OAAS6I,EAAO5I,OAAS4I,EAAOS,aAChCT,EAAOS,OAELT,GACT/E,EAAIzB,EAAMwG,EAAOzG,GAAI6J,KACnB7J,GAAKuH,EAAUvH,KACf,aAAc0B,IAAO,aAAc6F,IAAe7F,EAAEoI,SAAWvC,EAAUuC,UAC3EpI,EAAE0F,QAAUG,EAAUH,QACtBgB,EAAQ2B,KAAKtC,EAAW/H,EAASgC,EAAG,UAC7BmG,EAAe7H,IAGpBN,EAAQrE,QAAU0M,EAAQ1M,QAAUyM,EAASzM,UAChD0L,GAAeU,EAAUtD,IAAI,kBAAM1E,EAAauJ,EAAcjC,EAAaU,EAAUuC,OAAS,EAAI,SAAW,QAAQ,KAAOrD,GAAUlH,EAAauJ,EAAcjC,EAAa,OAC9KrH,EAAQrE,QAAU+M,EAAQ2B,KAAKtC,EAAW/H,EAASmI,EAAgB,IAGpE7D,GAAgBqC,EAASyB,EAAUL,GACnCzD,GAAgBsC,EAASyB,EAASN,OAE9BwC,EAAUxH,IAAUA,GAAOkF,SAE3BsC,IACHA,EAAQ9F,IAAIsD,EAAW,GACvBhF,GAAOG,OAAOtH,KAAK,kBAAMyG,GAAgBnB,GAAQwF,MAGlDJ,EAAUyB,EAAUqC,WACpBrC,EAAUsC,KAAK,eACVG,EAAUzC,EAAU0C,QAAUnE,EAClCkE,IAAYD,GAAWlI,GAAgBnB,GAAQwF,GAC/CW,GAAetH,EAAauJ,EAAcjC,EAAamD,EAAU,SAAW,UAI9E1D,IAAoBD,EAAW3F,EAAM6C,OAAO,SAAA1C,UAASA,EAAK4B,KAAO5B,EAAKxF,EAAEsH,WAAa9B,EAAKjG,EAAE+H,YAAWkB,IAAI,SAAAhD,UAAQA,EAAKxF,EAAErE,WACtHuL,IACH8D,MAAY9D,GAAOC,MAAKpH,aAAQkI,GAAa5C,EAAO2F,IACpD9D,GAAO2H,KAAK9O,KAAK6K,KAEjBI,GAAY5C,GAAmBH,GAAa5C,EAAO2F,IACnDJ,SAGGkE,EAAO5H,GAASA,GAAOkF,SAAWF,SACtC4C,EAAKC,OAAS,kBAAMC,GAAUF,EAAM,IAE7BA,EAgBQ,SAAhBG,GAAgB1I,WAKdL,EAJGgJ,EAAS3I,EAAM8C,SAAW,GAC7BE,EAAMhD,EAAMgD,IAAM,GAClBhB,EAAWhC,EAAM4I,cACjBlR,EAAIsK,EAASzI,OAEP7B,KAENiR,GADAhJ,EAAUqC,EAAStK,IACJkL,IAAOI,EAAIrD,EAAQiD,IAAMjD,EAAYgJ,EAAOhJ,EAAQiD,IAAMjD,EA/f5E,IACCvC,EAAUwI,EAAMjF,GAAQkI,EAAc3S,EAAOuL,EA4QkC2B,IA7Q5E0F,EAAM,EAGTC,EAAe,GACfC,EAAW,IAAMvL,KAAKwL,GACtBC,EAAWzL,KAAKwL,GAAK,IACrB5H,EAAY,GACZvB,EAAoB,GACpBpB,GAAuB,GAEvBmJ,GAAa/K,EAAa,6DAC1B8B,GAAe9B,EAAa,iHAI5B4I,GAAY,CAACP,OAAO,EAAGgE,KAAK,EAAG7H,OAAO,EAAGiE,KAAK,EAAGjB,WAAW,EAAG1G,QAAQ,EAAGqH,YAAY,EAAGmE,WAAW,EAAGC,SAAS,EAAGC,YAAY,EAAGC,QAAQ,EAAG3E,MAAM,EAAGE,OAAO,EAAGC,YAAY,EAAGC,KAAK,EAAGI,MAAM,EAAGC,KAAK,EAAGZ,SAAS,EAAGjG,MAAM,EAAG+F,QAAQ,EAAGC,QAAQ,EAAGG,OAAO,EAAGE,OAAO,EAAGK,OAAO,EAAGM,MAAM,EAAGd,gBAAiB,GAC3SqD,GAAe,CAAC5C,OAAO,EAAG7D,OAAO,EAAGgD,WAAW,EAAGc,MAAM,EAAGX,SAAS,EAAG+E,SAAS,EAAGC,QAAQ,EAAGjL,MAAM,GASpGC,GAAiB,GAsDjBwC,EAAiB,wEAAwEjE,MAAM,KAsE/F0M,EAAqB,SAArBA,mBAAsBC,EAAUnL,EAAO8C,EAAQsI,UAAUD,aAAoBvI,GAAeuI,EAAWA,aAAoBrH,GADrG,SAAtBuH,oBAAuB7J,EAAO4J,UAAWA,GAAS5J,EAAM8C,SAAS4G,EAAmBE,GAAOhH,KAAQ5C,EAAM4I,cAAc,GACgBiB,CAAoBF,EAAUC,GAAS,IAAIxI,GAAkC,iBAAduI,EAAyBzM,EAAOyM,IAAatM,QAAQC,KAAKqM,EAAW,cAAgBA,EAAUnL,EAAO8C,IA0B5SE,GAAO,SAAPA,KAAQmB,EAAWD,EAAS0C,EAAO0E,EAAYN,EAAU3M,OASvDyK,EAAOyC,EAAWC,EAAStJ,EAASuJ,EAAc9P,EAAQ+P,EARrD9U,EAAiCuN,EAAjCvN,QAASsG,EAAwBiH,EAAxBjH,MAAOpC,EAAiBqJ,EAAjBrJ,OAAQvB,EAAS4K,EAAT5K,EAAGC,EAAM2K,EAAN3K,EAC9BwI,EAAoDkC,EAApDlC,MAAOC,EAA6CiC,EAA7CjC,OAAQ3E,EAAqC4G,EAArC5G,OAAQC,EAA6B2G,EAA7B3G,OAAQsL,EAAqB3E,EAArB2E,SAAU9F,EAAWmB,EAAXnB,OAC3ChJ,EAAUsE,GAAQzH,EAAQI,MAAM+C,QAChCjC,EAAYuG,GAAQzH,EAAQwE,SAAWxE,EAAQyC,aAAa,aAC5DsS,EAAiBxH,IACRD,EAAQvI,OAAhBjC,IAAAA,EAAGwB,IAAAA,EACJ0Q,EAAOzH,EAAUpB,OAAOf,QAAUe,EAAOf,OAASmC,EAAUpB,OAAOd,SAAWc,EAAOd,QAAUkC,EAAU7G,SAAWA,GAAU6G,EAAU5G,SAAWA,GAAU4G,EAAU0E,WAAaA,EACpL/F,GAAU8I,GAAQzH,EAAUrB,QAAUoB,EAAQpB,SAAWkI,SAEtDlI,IAAWhI,GACdwC,EAASC,EAAS,EAClBsL,EAAWC,EAAQ,IAGnBnN,GADA8P,EA9JwB,SAA1BI,wBAA0BtM,OACrBrC,EAAQqC,EAAGpC,OAASiK,EAAK0E,KAAKC,SAASxM,UACvCrC,EAAM8O,UAAY5E,EAAK6E,OAAOC,MAC1BhP,EAAMiP,SAEdjP,EAAM8O,QAAU5E,EAAK6E,OAAOC,MACpBhP,EAAMiP,QAAUrP,gBAAgByC,GAAI,GAAM,GAAO,IAwJzCsM,CAAwB/Q,IACjBV,QAAQqB,SAASyI,EAAQkI,IAAMlI,EAAQvI,OAAOvB,QAAQqB,SAASyI,EAAQkI,KAAOlI,EAAQvI,QAC5GkN,EAAW9J,EAAOE,KAAKoN,MAAM1Q,EAAOnB,EAAGmB,EAAOV,GAAKuP,GACnD1B,EAAQ/J,EAAOE,KAAKoN,MAAM1Q,EAAOH,EAAGG,EAAOR,GAAKqP,EAAW3B,GAAY,IACvEvL,EAAS2B,KAAKqN,KAAKrN,SAAAtD,EAAOV,EAAK,YAAIU,EAAOnB,EAAK,IAC/C+C,EAAS0B,KAAKqN,KAAKrN,SAAAtD,EAAOH,EAAK,YAAIG,EAAOR,EAAK,IAAK8D,KAAKsN,IAAIzD,EAAQ4B,GACjEM,IACHA,EAAWpM,EAASoM,GAAU,GAC9B9I,EAAUkF,EAAKoF,YAAYxB,GAC3BU,EAAOV,EAAS5P,SAAwC,mBAAtB4P,EAAS5P,SAA2B4P,EAAS5P,UAC/EuQ,EAAiB,CAACrO,OAAQ4E,EAAQ,UAAW3E,OAAQ2E,EAAQ,UAAWF,MAAO0J,EAAOA,EAAK1J,MAAQ/C,KAAKwN,KAAKC,WAAWxK,EAAQ,QAAS,QAASD,OAAQyJ,EAAOA,EAAKzJ,OAASyK,WAAWxK,EAAQ,SAAU,SAE7MhF,EAAM2L,SAAWA,EAAW,MAC5B3L,EAAM4L,MAAQA,EAAQ,OAEnBlC,GACHtJ,GAAU0E,IAAU2J,EAAe3J,OAAU2J,EAAe3J,MAAYA,EAAQ2J,EAAe3J,MAA3B,EACpEzE,GAAU0E,IAAW0J,EAAe1J,QAAW0J,EAAe1J,OAAaA,EAAS0J,EAAe1J,OAA5B,EACvE/E,EAAMI,OAASA,EACfJ,EAAMK,OAASA,IAEfyE,EAAQiB,EAAcjB,EAAQ1E,EAASqO,EAAerO,OAAQ,GAC9D2E,EAASgB,EAAchB,EAAS1E,EAASoO,EAAepO,OAAQ,GAChE3G,EAAQI,MAAMgL,MAAQA,EAAQ,KAC9BpL,EAAQI,MAAMiL,OAASA,EAAS,MAMjCqJ,GAAchI,GAAY1M,EAASsN,EAAQlE,OACvC8C,IAAWhI,GACdvB,GAAKG,EAAIyK,EAAUxI,OAAOjC,EAC1BF,GAAK0B,EAAIiJ,EAAUxI,OAAOT,GAChB0Q,GAAQ9Q,IAAWoJ,EAAQpJ,QACrCoC,EAAMM,gBAAgB,EAAGN,GACzBvB,EAASmB,gBAAgBkO,GAAYpU,GAAS,GAAO,GAAO,GAC5D2U,EAAYE,EAAa9O,MAAM,CAACpD,EAAGoC,EAAOjC,EAAGF,EAAGmC,EAAOT,IAEvD3B,IADAiS,EAAUC,EAAa9O,MAAM,CAACpD,EAAGG,EAAGF,EAAG0B,KAC1B3B,EAAIgS,EAAUhS,EAC3BC,GAAKgS,EAAQhS,EAAI+R,EAAU/R,IAE3BiS,EAAa/R,EAAI+R,EAAavQ,EAAI,EAElC3B,IADAiS,EAAUC,EAAa9O,MAAM,CAACpD,EAAGG,EAAIyK,EAAUxI,OAAOjC,EAAGF,EAAG0B,EAAIiJ,EAAUxI,OAAOT,KACpE3B,EACbC,GAAKgS,EAAQhS,GAEdD,EAAI0J,EAAc1J,EAAG,KACrBC,EAAIyJ,EAAczJ,EAAG,MACjB6E,GAAUA,aAAgBuE,IAK7B1F,EAAM3D,EAAIA,EAAI,KACd2D,EAAM1D,EAAIA,EAAI,KACd0D,EAAMM,gBAAgB,EAAGN,KANzBtG,EAAQI,MAAM+C,QAAUA,EACxBnD,EAAQwE,SAAWxE,EAAQiD,aAAa,YAAa/B,GAAa,IAClEoF,EAAME,QAAU,GAMbiB,IACHA,EAAK9E,EAAIA,EACT8E,EAAK7E,EAAIA,EACT6E,EAAKwK,SAAWA,EAChBxK,EAAKyK,MAAQA,EACTlC,GACHvI,EAAKf,OAASA,EACde,EAAKd,OAASA,IAEdc,EAAK2D,MAAQA,EACb3D,EAAK4D,OAASA,IAGT5D,GAAQnB,GAShBkI,EAAe,GAAIC,EAAa,mCAAmC7G,MAAM,KAgOzEyL,GAAY,SAAZA,UAAarG,EAAItE,MACZsE,GAAMA,EAAG+I,WAAa,IAAM/I,EAAGyC,gBAC9B/G,IANO,SAAbsN,WAAahJ,GACZA,EAAGvF,KAAKyM,aAAelH,EAAGvF,KAAKyM,YAAYnO,MAAMiH,EAAIA,EAAGvF,KAAKwO,mBAAqB,IAClFjJ,EAAGkJ,aAAY,GAAM,GAAO,GAAM1O,QAAQwO,YAKxCA,CAAWhJ,GACXtE,EAAS,GAAKsE,EAAG+I,SAAS,GAC1B/I,EAAG+G,SAEG,GAoBJ7G,4BAiBLiJ,OAAA,gBAAOC,0BACD5C,cAAgBjO,KAAKiD,QAAQqE,IAAI,SAAAlE,UAAM,IAAIqD,GAAarD,EAAI0N,EAAKjN,MAAOiN,EAAKnK,UAClFoH,GAAc/N,WACT+Q,UAAUF,QACVG,qBACEhR,QAGRiR,MAAA,6BACMhO,QAAQrE,OAASoB,KAAKiO,cAAcrP,OAAS,EAClDmP,GAAc/N,MACPA,QAGRkR,IAAA,aAAI7L,EAAOoF,EAAOF,WAIhBf,EAAUC,EAHP0H,EAAkBjN,GAAiBlE,KAAKiO,cAAchB,MAAM,IAAI,GAAO,GAC1EmE,GAAc/L,GAASrF,MAAMmI,SAC7BpL,EAAI,EAEEA,EAAIoU,EAAgBvS,OAAQ7B,IAClCyM,EAAW2H,EAAgBpU,GAC3BwN,IAAWf,EAAShK,OAASmB,gBAAgB6I,EAAS/O,SAAS,GAAO,GAAO,KAC7EgP,EAAS2H,EAAW5H,EAASvB,MACnBpB,GAAK2C,EAAUC,EAAQgB,GAAO,EAAM,EAAGjB,GACjDA,EAAShK,OAASmB,gBAAgB6I,EAAS/O,SAAS,GAAO,GAAO,UAE5DuF,QAGRqQ,YAAA,qBAAY5V,EAAS4W,OAChBC,EAAKtR,KAAKsI,gBAAgB7N,IAAYiM,SAClC2K,KAAYC,EAAKA,EAAKA,EAAGzN,OAAS6C,GAAW2K,MAGtD3J,IAAA,aAAIrC,WAIFkM,EAAOD,EAAIE,EAHRzU,EAAIsI,EAAMpC,QAAQrE,OACrBoP,EAAShO,KAAKmI,SACdE,EAAMrI,KAAKqI,IAELtL,MAENyU,EAAMxD,GADNsD,EAAKjM,EAAM4I,cAAclR,IACTkL,OACJqJ,EAAG7W,UAAY+W,EAAI/W,SAAY4N,EAAIiJ,EAAGrJ,KAAOI,EAAIiJ,EAAGrJ,IAAIxN,UAAY6W,EAAG7W,UAClF8W,EAAQvR,KAAKiO,cAAchH,QAAQqK,EAAG7W,UAAY+W,EAAI/W,QAAU+W,EAAMnJ,EAAIiJ,EAAGrJ,UACxEhF,QAAQ+I,OAAOuF,EAAO,EAAGlM,EAAMpC,QAAQlG,SACvCkR,cAAcjC,OAAOuF,EAAO,EAAGD,UAE/BrO,QAAQpE,KAAKwG,EAAMpC,QAAQlG,SAC3BkR,cAAcpP,KAAKyS,WAG1BjM,EAAMqG,cAAgB1L,KAAK0L,aAAc,GACzCrG,EAAMsB,SAAW3G,KAAK2G,QAAS,GAC/BoH,GAAc/N,MACPA,QAGRyR,QAAA,iBAAQpM,GAUE,SAARqM,GAASC,EAAIC,EAAIxO,UAAQuO,EAAGvL,YAAcwL,EAAGxL,UAAauL,EAAGvL,UAAYyL,EAAQC,EAASH,EAAGvL,UAAY2L,EAAUC,GAAWnT,KAAKuE,IAAOH,EAAQpE,KAAKuE,GACjI,SAAtB6O,GAAuBN,EAAIC,EAAIxO,UAAOH,EAAQgE,QAAQ7D,GAAM,GAAKsO,GAAMC,EAAIC,EAAIxO,OAC/EuO,EAAIC,EAAIrO,EAAGH,EAAI8O,EAAOC,EAAOrN,EAAI1E,EAX9BgS,EAAK/M,EAAM8C,SACdkK,EAAKrS,KAAKmI,SACV6J,EAAY,GACZD,EAAU,GACVF,EAAQ,GACRC,EAAQ,GACR7O,EAAU,GACVqP,EAAKjN,EAAMgD,IACXnI,EAAKF,KAAKqI,QAIN9E,KAAK6O,EACTF,EAAQI,EAAG/O,GACX4O,EAAQjS,EAAGqD,GAEXH,GADAuO,EAAMO,EAAgBpK,GAAoBzC,EAAOrF,KAAMuD,GAAzC6O,EAAG7O,IACT9I,QACRmX,EAAKS,EAAG9O,GACJ4O,GACH/R,EAAKwR,EAAGxL,YAAe+L,EAAM/L,WAAahD,IAAOwO,EAAGnX,QAAWmX,EAAKO,GACpErN,GAAKoN,GAAUP,EAAGvL,WAAc8L,EAAM9L,WAAahG,EAAG3F,UAAYyX,EAAMzX,QAAkBkX,EAARO,GAE3E9L,WAAahG,EAAGgG,WAAatB,EAAGrK,UAAY2F,EAAG3F,UACpDqK,EAAGgH,YAAY1L,GAAM2R,EAAUC,GAAWnT,KAAKiG,EAAGrK,QAAS2F,EAAG3F,SAC/DwI,EAAQpE,KAAKiG,EAAGrK,QAAS2F,EAAG3F,UAE5BiX,GAAM5M,EAAI1E,EAAI0E,EAAGrK,SAElByX,GAASpN,EAAGrK,UAAYyX,EAAMzX,UAAYyX,EAAQE,EAAG7O,IACrD0O,GAAoBnN,EAAGrK,UAAYmX,EAAGnX,SAAWyX,EAAQA,EAAQpN,EAAI8M,EAAIA,EAAGnX,SAC5EwX,GAAoBC,GAASA,EAAMzX,UAAY0X,EAAM1X,QAAUyX,EAAQpN,EAAIqN,EAAOA,EAAM1X,SACxFyX,GAASD,GAAoBC,EAAOC,EAAM1X,UAAYyX,EAAMzX,QAAU0X,EAAQP,EAAIM,EAAMzX,WAEvFmX,EAAuBA,EAAG9F,YAAY6F,GAA2BD,GAAMC,EAAIC,EAAIxO,GAAnC4O,EAAUnT,KAAKuE,GAAtDyO,EAAMhT,KAAKuE,GACjB8O,GAASD,GAAoBC,EAAON,EAAIM,EAAMzX,cAG3C8I,KAAK8O,EACJD,EAAG7O,KACPuO,EAAMjT,KAAKwT,EAAG9O,GAAG9I,SACjByF,EAAGqD,IAAMuO,EAAMjT,KAAKqB,EAAGqD,GAAG9I,gBAGrB,CAACsX,QAAAA,EAASC,UAAAA,EAAWH,MAAAA,EAAOC,MAAAA,MAGpCd,mBAAA,sCACKnN,EAAQE,GAAqB/D,KAAK6D,QAAUI,GAC/ClH,EAAIiD,KAAKiO,cAAcrP,OACjB7B,KACNgI,GAAoB/E,KAAKiO,cAAclR,GAAI8G,MAI7CkN,UAAA,mBAAUF,cACL0B,EAAY,QACXtP,QAAQhB,QAAQ,SAAA2C,OAChB6C,EAAK7C,EAAEiH,MACV2G,EAAkB1E,GAAUrG,EAAIoJ,EAAO,EAAI,GAC5CA,GAAQ2B,GAAmBD,EAAUtL,QAAQQ,GAAM,GAAKA,EAAGC,IAAI,kBAAM+K,EAAKC,qBAC1EF,GAAmBD,EAAU1T,KAAK4I,MAElCoJ,GAAQ0B,EAAU3T,QAAUoB,KAAK0S,wBAC7BhH,cAAgB1L,KAAK0L,cAAgB6G,EAAU3T,WAGrD8T,iBAAA,iCACMzE,cAAchM,QAAQ,SAAAqP,OACtBjT,EAAIiT,EAAG7W,QAAQ+G,wBACnB8P,EAAGlL,aAAe/H,EAAEwH,OAASxH,EAAEyH,QAAUzH,EAAEuB,KAAOvB,EAAEwB,MACpDyR,EAAGrQ,QAAU,OAIfqH,gBAAA,yBAAgB7N,UACRuF,KAAKiO,cAAcjO,KAAKiD,QAAQgE,QAAQ1E,EAAO9H,QAGvDkY,aAAA,+BACQzO,GAAiBlE,KAAKiO,cAAchB,MAAM,IAAI,GAAM,GAAM3F,IAAI7B,mCAxJ1DxC,EAASf,EAAM0Q,WACrB/O,MAAQ3B,GAAQA,EAAK2B,WACrB8C,UAAYzE,IAAQA,EAAKyE,QAC1BiM,OACE3P,QAAUmE,GAA2BnE,QACrCgL,cAAgBhL,EACrB8K,GAAc/N,UACR,MACDiD,QAAUR,EAASQ,OACpB4N,EAAO3O,KAAuB,IAAdA,EAAKsM,MAAmBtM,EAAKJ,QAAUI,EAAKsM,MAChExI,KAAW6K,GAAQ7K,GAAO6M,MAAMhU,KAAKmB,WAChC4Q,OAAOC,KAAU7K,WAoJnBS,+BAOLqF,YAAA,qBAAYzG,OACP9D,EAAKvB,KAAK4G,OACbzG,EAAKkF,EAAMuB,cACLrF,EAAG3B,MAAQO,EAAGP,KAAO2B,EAAG1B,OAASM,EAAGN,MAAQ0B,EAAGsE,QAAU1F,EAAG0F,OAAStE,EAAGuE,SAAW3F,EAAG2F,SAAW9F,KAAKR,OAAOe,OAAO8E,EAAM7F,SAAWQ,KAAK+L,UAAY1G,EAAM0G,SAAY/L,KAAK6D,OAASwB,EAAMxB,OAASiP,KAAKC,UAAU/S,KAAK6D,SAAWiP,KAAKC,UAAU1N,EAAMxB,UAGjQ+M,OAAA,gBAAO/M,EAAO8C,OACTqM,EAAOhT,KACVvF,EAAUuY,EAAKvY,QACfsL,EAAUkF,EAAKoF,YAAY5V,GAC3BsG,EAAQkK,EAAK0E,KAAKC,SAASnV,GAC3BmM,EAASnM,EAAQ+G,wBACjB+N,EAAO9U,EAAQwE,SAAuC,mBAArBxE,EAAQwE,SAA8D,QAAnCxE,EAAQwY,SAASpW,eAA2BpC,EAAQwE,UACxHnB,EAAI6I,EAAS,IAAIxL,EAAS,EAAG,EAAG,EAAG,EAAGyL,EAAO/G,KAAOtD,IAAqBqK,EAAOhH,IAAMxD,KAAsBuE,gBAAgBlG,GAAS,GAAO,GAAO,GACpJuY,EAAKjN,QAAUA,EACfiN,EAAKvY,QAAUA,EACfuY,EAAK/K,GAzhBG,SAATiL,OAAS9P,OACJ6E,EAAK7E,EAAGlG,aAAa,uBACzB+K,GAAM7E,EAAG1F,aAAa,eAAiBuK,EAAK,QAAUkG,KAC/ClG,EAshBGiL,CAAOzY,GACjBuY,EAAKxT,OAAS1B,EACdkV,EAAKjS,MAAQA,EACbiS,EAAKpM,OAASA,EACdoM,EAAK5M,aAAeQ,EAAOf,OAASe,EAAOd,QAAUc,EAAO/G,MAAQ+G,EAAOhH,KAC3EoT,EAAK3M,QAAUN,EAAQ,WACvBiN,EAAKhX,SAAW+J,EAAQ,YACxBiN,EAAKrU,OAASlE,EAAQM,WACtBiY,EAAK5V,EAAI2I,EAAQ,KACjBiN,EAAK3V,EAAI0I,EAAQ,KACjBiN,EAAK7R,OAASJ,EAAMI,OACpB6R,EAAK5R,OAASL,EAAMK,OACpB4R,EAAKtG,SAAW3G,EAAQ,YACxBiN,EAAKrG,MAAQ5G,EAAQ,SACrBiN,EAAKjH,QAAUhG,EAAQ,WACvBiN,EAAKnN,MAAS0J,EAAOA,EAAK1J,MAAQiB,EAAcf,EAAQ,QAAS,MAAO,KACxEiN,EAAKlN,OAASyJ,EAAOA,EAAKzJ,OAASgB,EAAcf,EAAQ,SAAU,MAAO,KAC1ElC,GA3jBc,SAAfsP,aAAgBnO,EAASnB,WACpBkC,EAAUkF,EAAKoF,YAAYrL,EAAQvK,QAAS,KAAM,UACrDgJ,EAAMuB,EAAQnB,MAAQ,GACtB9G,EAAI8G,EAAMjF,OACJ7B,KACN0G,EAAII,EAAM9G,KAAOgJ,EAAQlC,EAAM9G,IAAM,IAAIqW,OAE1C3P,EAAI+G,SAAW/G,EAAI+G,OAAS+F,WAAW9M,EAAI+G,SAAW,GAojB7C2I,CAAaH,EAAMlP,GAAeD,IAAUD,GAAcC,IACnEmP,EAAK/C,IAAMxV,EAAQsD,QAA6C,QAAnCtD,EAAQwY,SAASpW,eAA2BgB,EAAQpD,GAASsF,UAC1FiT,EAAKrM,OAASA,GAA2B,IAAhB/D,EAAO9E,EAAEgB,KAAa8D,EAAO9E,EAAEO,KAAOuE,EAAO9E,EAAEuB,IAAsB,IAAhBuD,EAAO9E,EAAEkB,GACvFgU,EAAK/R,QAAU,uCAzCJxG,EAASoJ,EAAO8C,QACtBlM,QAAUA,OACVmW,OAAO/M,EAAO8C,SA4Cf0M,4BAQLC,aAAA,sBAAarL,WACRlL,EAAIiD,KAAKuT,OAAO3U,OACb7B,QACFiD,KAAKuT,OAAOxW,GAAGoL,SAASF,UACpBjI,KAAKuT,OAAOxW,MAKtByR,KAAA,qBACM1M,MAAM0K,OAAOxM,uCAjBPkC,EAAMJ,QACZI,KAAOA,OACPJ,MAAQA,OACRyR,OAAS,QACTrI,SAAWpJ,EAAMoJ,eAiBlBsI,2BAaL9L,IAAA,aAAI+L,OACC9P,EAAS3D,KAAKgC,QAAQgF,OAAO,SAAA7D,UAAUA,EAAOjB,OAASuR,WACvD9P,EAAO/E,OACH+E,EAAO,IAEfA,EAAS,IAAI0P,EAA8B,mBAAZI,EAAyB,CAACC,QAASD,GAAUA,EAAQzT,WAC/EgC,QAAQnD,KAAK8E,GACXA,MAGR6I,OAAA,gBAAOrJ,OACFpG,EAAIiD,KAAKgC,QAAQiF,QAAQ9D,UACxB,GAALpG,GAAUiD,KAAKgC,QAAQgK,OAAOjP,EAAG,GAC1BiD,QAGR2T,SAAA,kBAASC,cACJC,EAAY7N,GACf8N,EAAa5F,SACdlI,GAAShG,MACJqF,MAAM4L,aACN4B,MAAMjU,OAAS,OACfoD,QAAQC,QAAQ,SAAAkB,GAChBA,EAAOjB,KAAKyR,WACfxQ,EAAOoQ,OAAO3U,OAAS,GACvBsP,EAAe/K,GACRkC,MAAQlC,EAAOjB,KAAKyR,SAASxQ,IAErCyQ,GAASzQ,EAAOoQ,OAAOtR,QAAQ,SAAAyG,UAAKqL,EAAK1O,MAAMqC,IAAIgB,OAEpDwF,EAAe4F,EACf9N,GAAS6N,OACJG,gBACEhU,QAGR0T,QAAA,uBAIElO,EAAa+D,SAHVsK,EAAY7N,GACfyB,EAAKzH,KAAKkL,SACVnO,EAAIiD,KAAKgC,QAAQpD,WAElBoH,GAAShG,KACTyH,EAAGwJ,aACEhL,KAAKrH,OAASoB,KAAKmG,OAAOvH,OAASoB,KAAK2N,KAAK/O,OAAS,OACtDoD,QAAQC,QAAQ,SAAAnD,GACpBA,EAAEoD,KAAKwR,SAAW5U,EAAEoD,KAAKwR,QAAQ5U,OAGX4J,EAAG/E,EAFrBiG,EAAU9K,EAAEoD,KAAK0H,QACpBC,EAAU/K,EAAEoD,KAAK2H,QACjB5G,EAAUnE,EAAEmE,QACTA,GAAWA,EAAQrE,SAAWgL,GAAWC,KAC5CnB,EAAI,IAAIf,GACR7I,EAAEyU,OAAOtR,QAAQ,SAAAoD,UAASqD,EAAEhB,IAAIrC,MAChC1B,EAAS+E,EAAE+I,QAAQwC,EAAKN,SAAS1Q,KAC1B4O,MAAMjT,QAAUgL,GAAWA,EAAQjG,EAAOkO,OACjDlO,EAAOmO,MAAMlT,QAAUiL,GAAWA,EAAQlG,EAAOmO,UAGnD5K,GAAmBlH,KAAKiG,WACnB0H,KAAK1L,QAAQ,SAAAlD,UAAKA,MACvBwK,EAAU9B,EAAG4F,WACb7H,EAAcxF,KAAKmG,OAAO8G,MAAM,GAChCxF,EAAGC,IAAI,WACF6B,GAAW9B,EAAGiG,SACjBlI,EAAYvD,QAAQ,SAAAlD,UAAKA,MACzB8C,EAAcqS,EAAM,iBAGtBlO,GAAS6N,EACF9W,UACDiF,QAAQjF,GAAGmF,KAAKiS,MAAQnU,KAAKgC,QAAQjF,GAAGyR,cAE9C3M,EAAc7B,KAAM,WACpByH,EAAG2M,UACIpU,QAGRqU,UAAA,mBAAUC,GACAA,EAATA,GAAgB,uBAAM,OAClBC,EAAQ,eACPvS,QAAQC,QAAQ,SAAA5C,MAChBA,EAAE6C,KAAKmS,UAAW,KACjBtX,EAAGgC,EAAI,SAAJA,EAAIkE,GACVA,IAAY5D,EAAE4D,QAAUA,KACxBlG,EAAIwX,EAAMtN,QAAQlI,MAEjBwV,EAAMvI,OAAOjP,EAAG,GAChBwX,EAAM3V,QAAU0V,MAGlBC,EAAM1V,KAAKE,GACXM,EAAE6C,KAAKmS,UAAUtV,MAGnBwV,EAAM3V,QAAU0V,IACTtU,QAGRwU,SAAA,gCACMxS,QAAQC,QAAQ,SAAA5C,UAAKA,EAAE4D,QAAU5D,EAAE6C,KAAKsS,UAAYnV,EAAE6C,KAAKsS,SAASnV,KAClEW,QAGRgU,cAAA,uBAAcnD,eACRxL,MAAM0L,UAAUF,QAChBgC,MAAM5Q,QAAQ,SAAAoD,UAASA,EAAM0L,UAAUF,KACrC7Q,QAGR0J,IAAA,aAAI+K,EAAcb,qBACb5T,OAASgG,KACZyO,GAAgBzU,KAAK2T,SAASC,QACzBS,UAAU,WACTK,EAAKC,UACTD,EAAKF,WACLE,EAAKhB,cAID1T,QAGRiR,MAAA,eAAM2D,QACAvP,MAAM4L,QACX2D,IAAc5U,KAAKgC,QAAQpD,OAAS,MAGrC0U,aAAA,sBAAarL,WAEXS,EADG3L,EAAIiD,KAAKgC,QAAQpD,OAEd7B,QACN2L,EAAI1I,KAAKgC,QAAQjF,GAAGuW,aAAarL,UAEzBS,SAGF1I,KAAKqF,MAAM8C,SAASF,IAAOjI,KAAKqF,SAGxCmJ,KAAA,qBACMmG,QAAU,OACV1D,eACE7C,EAAapO,KAAKiI,mCAzJdA,QACNA,GAAKA,OACLjG,QAAU,QACV6Q,MAAQ,QACR1M,OAAS,QACTF,KAAO,QACP0H,KAAO,QACPxC,KAAO,QACP9F,MAAQ,IAAIsC,QACZuD,SAAWD,EAAKC,eAqJV+I,QAELN,SAAP,kBAAgB1Q,EAASf,OACpBmD,EAAQuC,GAAY3E,EAASf,UACjCgM,GAAgBA,EAAaqF,OAAO1U,KAAKwG,GACzCnD,GAAQA,EAAKJ,OAASmS,KAAKnS,MAAMI,EAAKJ,OAAOuD,MAAMqC,IAAIrC,GAChDA,QAGDwP,KAAP,cAAYxP,EAAOnD,wBAClBA,EAAOA,GAAQ,MACYA,EAAKyH,YAAa,GACtCN,GAAQhE,EAAOuC,GAAY1F,EAAKe,SAAWoC,EAAMpC,QAAS,CAACY,MAAO3B,EAAK2B,OAASwB,EAAMxB,MAAO8C,OAAQzE,EAAKyE,OAAQ6H,OAAQtM,EAAKsM,OAAQtM,GAAO,SAG/I4S,GAAP,YAAUzP,EAAOnD,UACTmH,GAAQhE,EAAOuC,GAAY1F,EAAKe,SAAWoC,EAAMpC,QAAS,CAACY,MAAO3B,EAAK2B,OAASwB,EAAMxB,MAAO8C,OAAQzE,EAAKyE,OAAQ6H,OAAQtM,EAAKsM,OAAQtM,EAAM,SAG9I6S,OAAP,gBAAc/M,EAAWD,EAAS7F,UAC1BmH,GAAQrB,EAAWD,EAAS7F,SAG7BgP,IAAP,aAAW8D,EAAQC,EAAM/S,OACpB+C,EAAI/C,EAAOsB,EAAMtB,EAAMkL,IAAgB,KAC4BlL,GAAQ+C,EAA7E6E,IAAAA,SAAUW,IAAAA,MAAOqE,IAAAA,QAASjL,IAAAA,MAAOqR,IAAAA,aAAczG,IAAAA,WAAY9H,IAAAA,OAC5DkI,EAAW3M,GAAQA,EAAK2M,UAAYtM,EAAOL,EAAK2M,UAChDsG,EAASpG,EAAmBkG,EAAMpR,EAAO8C,EAAQqO,GACjDI,EAAQrG,EAAmBiG,EAAQ,EAAGrO,EAAQwO,GAC9CE,EAAcxR,EAAQE,GAAqBF,GAASI,UACrDJ,GAASsD,GAAYlC,EAAGkQ,EAAOtR,OAC3BqR,IACHnQ,GAAoBqQ,EAAOC,uBACLpQ,IAAOA,EAAE4H,iBAAkB,GACjD5H,EAAEwJ,WAAa,WACdrJ,GAAmBgQ,GACnB3G,GAAcA,EAAWjO,MAAMR,KAAMsV,aAGvCxL,GAAYrE,GAAc2P,EAAOD,GACjClQ,EAAI4B,GAAKuO,EAAOD,EAAQ1K,GAASoE,EAAUhL,EAAOgL,EAAU5J,EAAEoI,UAAYyB,EAAU7J,EAAI,GACjF6J,EAAU7J,EAAIA,EAAEoI,SAAWpC,EAAK6J,GAAGM,EAAM3a,QAASwK,GAAK,WAGxD0N,aAAP,sBAAoB4C,EAAiBrT,UAC5BqT,aAA2B5N,GAAY4N,EAAkB,IAAI5N,GAAU4N,EAAiBrT,IAAOyQ,qBAGjG7Q,MAAP,eAAamG,UAELmG,EADAnG,EAAPA,GAAY,aACgBmG,EAAanG,GAAM,IAAIuL,EAAUvL,UAGvDuN,YAAP,qBAAmBvS,EAASwS,IAC1BxS,aAAmB0E,GAAY1E,EAAQA,QAAUR,EAASQ,IAAUhB,QAAQ,SAAA2C,UAAKA,GAAKkJ,GAAUlJ,EAAEiH,OAAoB,IAAb4J,EAAqB,EAAI,WAG7HC,WAAP,oBAAkBlT,OACbzD,EAAIkV,KAAK0B,YAAYnT,WAChBzD,GAAKA,EAAE6W,iBAGVD,YAAP,qBAAmBnT,UACVD,EAAOC,IAAWkE,GAAWmF,YAG/BvD,gBAAP,yBAAuB9F,EAAQqB,UACvB,IAAI4C,GAAalE,EAAOC,GAASqB,SAGlCgS,mBAAP,4BAA0BC,EAAaC,EAAWtV,OAC7C3C,EAAI6C,gBAAgBoV,GAAW,GAAM,GAAMzW,SAASqB,gBAAgBmV,WACjErV,EAAQ3C,EAAE0C,MAAMC,GAAS3C,QAI1BkY,SAAP,kBAAgBrG,MACfpU,EAA6B,oBAAd0a,UAA6BA,SAASza,KAC1C,CACVyP,EAAO0E,EACPnV,EAAQe,GACRkH,EAAWwI,EAAK6B,MAAMoJ,YAClBC,EAAOlL,EAAK6B,MAAMqJ,KAAK,IAC3BrP,EAAgB,uBAACjE,EAAO6E,UAAQyO,EAAK5F,WAAW1N,GAAS6E,6BAK5DuM,EAAKmC,QAAU,SAaI,oBAAZnb,QAA2BA,OAAOgQ,MAAQhQ,OAAOgQ,KAAKoL,eAAepC"}