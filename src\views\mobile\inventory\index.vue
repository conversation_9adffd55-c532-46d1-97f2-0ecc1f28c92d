<template>
  <div class="mobile-inventory">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="资产盘点"
      left-arrow
      fixed
      placeholder
      @click-left="goBack"
    />
    
    <!-- 搜索框 -->
    <div class="search-bar">
      <van-search
        v-model="searchValue"
        placeholder="请输入资产名称"
        shape="round"
        show-action
        @search="onSearch"
      >
        <template #action>
          <div @click="onSearch">搜索</div>
        </template>
      </van-search>
    </div>
    
    <!-- 盘点任务列表 -->
    <div class="inventory-list">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <div 
            v-for="(item, index) in inventoryList" 
            :key="index" 
            class="inventory-item"
            @click="goToDetail(item)"
          >
            <div class="inventory-title">
              <span class="task-id">[{{ item.taskId }}]</span>
              {{ item.name }}
            </div>
            <div class="inventory-info">
              <div class="info-row">
                <span class="label">盘点状态:</span>
                <span class="value" :class="getStatusClass(item.status)">{{ getStatusText(item.status) }}</span>
              </div>
              <div class="info-row">
                <span class="label">盘点时间:</span>
                <span class="value">{{ item.time }}</span>
              </div>
              <div class="info-row">
                <span class="label">盘点部门:</span>
                <span class="value">{{ item.department }}</span>
              </div>
              <div class="info-row">
                <span class="label">资产数量:</span>
                <span class="value">{{ item.count }}件</span>
              </div>
            </div>
            <div class="inventory-footer">
              <van-button 
                size="small" 
                type="primary" 
                plain
                @click.stop="startInventory(item)"
              >
                开始盘点
              </van-button>
              <van-button 
                size="small" 
                type="info" 
                plain
                @click.stop="viewDetail(item)"
              >
                查看详情
              </van-button>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
    
    <!-- 底部操作栏 -->
    <div class="bottom-bar">
      <van-button type="primary" block @click="createInventory">新建盘点任务</van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();
const searchValue = ref('');
const refreshing = ref(false);
const loading = ref(false);
const finished = ref(false);
const inventoryList = ref([
  {
    taskId: '20250601001',
    name: '办公设备季度盘点',
    status: 'pending',
    time: '2025-06-01 ~ 2025-06-15',
    department: '资产管理部',
    count: 128
  },
  {
    taskId: '20250501002',
    name: '教学设备年度盘点',
    status: 'in_progress',
    time: '2025-05-01 ~ 2025-05-30',
    department: '教务处',
    count: 256
  },
  {
    taskId: '20250401003',
    name: '实验室设备专项盘点',
    status: 'completed',
    time: '2025-04-01 ~ 2025-04-15',
    department: '实验室管理中心',
    count: 189
  },
  {
    taskId: '20250301004',
    name: '图书馆资产盘点',
    status: 'pending',
    time: '2025-03-01 ~ 2025-03-20',
    department: '图书馆',
    count: 320
  }
]);

// 返回上一页
const goBack = () => {
  router.go(-1);
};

// 搜索
const onSearch = () => {
  console.log('搜索:', searchValue.value);
  // 实现搜索逻辑
};

// 下拉刷新
const onRefresh = () => {
  setTimeout(() => {
    refreshing.value = false;
    // 重置列表数据
    finished.value = false;
    inventoryList.value = [...inventoryList.value];
  }, 1000);
};

// 加载更多
const onLoad = () => {
  setTimeout(() => {
    // 模拟加载更多数据
    loading.value = false;
    finished.value = true;
  }, 1000);
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'pending': '待盘点',
    'in_progress': '盘点中',
    'completed': '已完成'
  };
  return statusMap[status] || '未知状态';
};

// 获取状态样式类
const getStatusClass = (status) => {
  const classMap = {
    'pending': 'status-pending',
    'in_progress': 'status-progress',
    'completed': 'status-completed'
  };
  return classMap[status] || '';
};

// 跳转到详情页
const goToDetail = (item) => {
  console.log('跳转到详情页:', item);
  // router.push(`/mobile/inventory/detail/${item.taskId}`);
};

// 开始盘点
const startInventory = (item) => {
  console.log('开始盘点:', item);
  // router.push(`/mobile/inventory/start/${item.taskId}`);
};

// 查看详情
const viewDetail = (item) => {
  console.log('查看详情:', item);
  // router.push(`/mobile/inventory/detail/${item.taskId}`);
};

// 新建盘点任务
const createInventory = () => {
  console.log('新建盘点任务');
  // router.push('/mobile/inventory/create');
};
</script>

<style lang="scss" scoped>
.mobile-inventory {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 60px; // 为底部操作栏留出空间
  
  .search-bar {
    padding: 8px 0;
  }
  
  .inventory-list {
    padding: 0 12px;
    
    .inventory-item {
      margin-bottom: 12px;
      background-color: #fff;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      
      .inventory-title {
        padding: 12px;
        font-size: 15px;
        font-weight: 500;
        border-bottom: 1px solid #f5f5f5;
        
        .task-id {
          color: #1989fa;
          margin-right: 4px;
        }
      }
      
      .inventory-info {
        padding: 12px;
        
        .info-row {
          display: flex;
          margin-bottom: 8px;
          font-size: 14px;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .label {
            color: #666;
            width: 80px;
          }
          
          .value {
            flex: 1;
            color: #333;
            
            &.status-pending {
              color: #ff976a;
            }
            
            &.status-progress {
              color: #1989fa;
            }
            
            &.status-completed {
              color: #07c160;
            }
          }
        }
      }
      
      .inventory-footer {
        padding: 12px;
        display: flex;
        justify-content: flex-end;
        gap: 8px;
        border-top: 1px solid #f5f5f5;
      }
    }
  }
  
  .bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 10px 12px;
    background-color: #fff;
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
    z-index: 10;
  }
}

:deep(.van-nav-bar) {
  background-color: #1989fa;
  
  .van-nav-bar__title {
    color: #fff;
  }
  
  .van-icon {
    color: #fff;
  }
}

:deep(.van-search__action) {
  color: #1989fa;
}
</style>
