<template>
  <div class="ranking-panel">
    <div class="panel-header">
      <div class="header-content">
        <div class="title-wrapper">
          <div class="title-icon">🏆</div>
          <h3 class="title">部门资产金额排行</h3>
        </div>
        <div class="header-actions">
          <div class="view-toggle" @click="toggleView">
            <svg v-if="viewType === 'chart'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <line x1="8" y1="6" x2="21" y2="6" stroke="currentColor" stroke-width="2"/>
              <line x1="8" y1="12" x2="21" y2="12" stroke="currentColor" stroke-width="2"/>
              <line x1="8" y1="18" x2="21" y2="18" stroke="currentColor" stroke-width="2"/>
              <line x1="3" y1="6" x2="3.01" y2="6" stroke="currentColor" stroke-width="2"/>
              <line x1="3" y1="12" x2="3.01" y2="12" stroke="currentColor" stroke-width="2"/>
              <line x1="3" y1="18" x2="3.01" y2="18" stroke="currentColor" stroke-width="2"/>
            </svg>
            <svg v-else viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <line x1="18" y1="20" x2="18" y2="10" stroke="currentColor" stroke-width="2"/>
              <line x1="12" y1="20" x2="12" y2="4" stroke="currentColor" stroke-width="2"/>
              <line x1="6" y1="20" x2="6" y2="14" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
        </div>
      </div>
      <div class="header-decoration"></div>
    </div>
    
    <div class="panel-content">
      <!-- 图表视图 -->
      <div v-if="viewType === 'chart'" class="chart-view">
        <div ref="chartContainer" class="chart-container"></div>
      </div>
      
      <!-- 列表视图 -->
      <div v-else class="list-view">
        <div class="ranking-list">
          <div 
            class="ranking-item" 
            v-for="(dept, index) in departmentData" 
            :key="index"
            :style="{ '--delay': index * 0.1 + 's' }"
          >
            <div class="rank-number" :class="getRankClass(index)">
              <span v-if="index < 3" class="medal">{{ getMedal(index) }}</span>
              <span v-else>{{ index + 1 }}</span>
            </div>
            
            <div class="dept-info">
              <div class="dept-name">{{ dept.name }}</div>
              <div class="dept-category">{{ dept.category }}</div>
            </div>
            
            <div class="dept-stats">
              <div class="asset-value" :style="{ color: dept.color }">
                {{ formatValue(dept.value) }}万元
              </div>
              <div class="asset-count">{{ dept.count }}项资产</div>
            </div>
            
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                :style="{ 
                  width: dept.percentage + '%',
                  backgroundColor: dept.color,
                  boxShadow: `0 0 10px ${dept.color}40`
                }"
              ></div>
            </div>
            
            <div class="rank-change" :class="dept.changeType">
              <svg v-if="dept.changeType === 'up'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M7 14L12 9L17 14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <svg v-else-if="dept.changeType === 'down'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M7 10L12 15L17 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <svg v-else viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" stroke-width="2"/>
              </svg>
              <span>{{ dept.change }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 统计摘要 -->
      <div class="summary-stats">
        <div class="summary-item">
          <div class="summary-label">参与部门</div>
          <div class="summary-value">{{ departmentData.length }}</div>
        </div>
        <div class="summary-item">
          <div class="summary-label">总资产值</div>
          <div class="summary-value">{{ totalValue.toLocaleString() }}万</div>
        </div>
        <div class="summary-item">
          <div class="summary-label">平均值</div>
          <div class="summary-value">{{ avgValue.toLocaleString() }}万</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, computed } from 'vue'
import * as echarts from 'echarts'

const chartContainer = ref(null)
const viewType = ref('list') // 'chart' 或 'list'
let chartInstance = null

// 部门数据
const departmentData = ref([
  { 
    name: '校园建设部', 
    category: '基础设施',
    value: 1613897, 
    count: 2847,
    color: '#ff6b6b',
    percentage: 100,
    change: '+12%',
    changeType: 'up'
  },
  { 
    name: '中国海大', 
    category: '教育机构',
    value: 634479, 
    count: 1523,
    color: '#ffb800',
    percentage: 39,
    change: '+8%',
    changeType: 'up'
  },
  { 
    name: '基础教育部', 
    category: '教育管理',
    value: 123137, 
    count: 856,
    color: '#00d4ff',
    percentage: 8,
    change: '-3%',
    changeType: 'down'
  },
  { 
    name: '总务处', 
    category: '后勤保障',
    value: 92237, 
    count: 634,
    color: '#00ff88',
    percentage: 6,
    change: '+5%',
    changeType: 'up'
  },
  { 
    name: '医疗中心', 
    category: '医疗服务',
    value: 47437, 
    count: 423,
    color: '#8b5cf6',
    percentage: 3,
    change: '0%',
    changeType: 'stable'
  }
])

// 计算总值和平均值
const totalValue = computed(() => {
  return departmentData.value.reduce((sum, dept) => sum + dept.value, 0)
})

const avgValue = computed(() => {
  return Math.round(totalValue.value / departmentData.value.length)
})

// 格式化数值
const formatValue = (value) => {
  if (value >= 10000) {
    return (value / 10000).toFixed(1)
  }
  return value.toLocaleString()
}

// 获取排名样式
const getRankClass = (index) => {
  if (index === 0) return 'rank-first'
  if (index === 1) return 'rank-second'
  if (index === 2) return 'rank-third'
  return 'rank-normal'
}

// 获取奖牌
const getMedal = (index) => {
  const medals = ['🥇', '🥈', '🥉']
  return medals[index] || ''
}

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return
  
  if (chartInstance) {
    chartInstance.dispose()
  }
  
  chartInstance = echarts.init(chartContainer.value)
  
  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' },
      backgroundColor: 'rgba(0, 20, 40, 0.9)',
      borderColor: '#00d4ff',
      borderWidth: 1,
      textStyle: { 
        color: '#fff',
        fontSize: 12
      },
      formatter: function(params) {
        const data = params[0]
        return `${data.name}<br/>
                资产价值: ${formatValue(data.value)}万元<br/>
                资产数量: ${departmentData.value[data.dataIndex].count}项`
      }
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '10%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      axisLine: { 
        lineStyle: { color: 'rgba(255, 255, 255, 0.2)' }
      },
      axisLabel: { 
        color: '#fff', 
        fontSize: 10,
        formatter: function(value) {
          return formatValue(value) + '万'
        }
      },
      splitLine: { 
        lineStyle: { 
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      }
    },
    yAxis: {
      type: 'category',
      data: departmentData.value.map(dept => dept.name),
      axisLine: { 
        lineStyle: { color: 'rgba(255, 255, 255, 0.2)' }
      },
      axisLabel: { 
        color: '#fff', 
        fontSize: 10
      },
      splitLine: { show: false }
    },
    series: [
      {
        type: 'bar',
        data: departmentData.value.map((dept, index) => ({
          value: dept.value,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: dept.color + '80' },
              { offset: 1, color: dept.color }
            ]),
            borderRadius: [0, 8, 8, 0]
          }
        })),
        barWidth: '60%',
        label: {
          show: true,
          position: 'right',
          color: '#fff',
          fontSize: 10,
          formatter: function(params) {
            return formatValue(params.value) + '万'
          }
        },
        animationDelay: function (idx) {
          return idx * 100
        }
      }
    ]
  }
  
  chartInstance.setOption(option)
}

// 切换视图
const toggleView = () => {
  viewType.value = viewType.value === 'chart' ? 'list' : 'chart'
  if (viewType.value === 'chart') {
    nextTick(() => {
      initChart()
    })
  }
}

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

onMounted(async () => {
  await nextTick()
  if (viewType.value === 'chart') {
    initChart()
  }
  
  window.addEventListener('dashboard-resize', handleResize)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('dashboard-resize', handleResize)
})
</script>

<style lang="scss" scoped>
.ranking-panel {
  background: linear-gradient(135deg, 
    rgba(139, 92, 246, 0.05) 0%, 
    rgba(0, 212, 255, 0.03) 100%
  );
  border: 1px solid rgba(139, 92, 246, 0.2);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  
  &:hover {
    border-color: rgba(139, 92, 246, 0.4);
    box-shadow: 0 12px 40px rgba(139, 92, 246, 0.2);
  }
  
  .panel-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid rgba(139, 92, 246, 0.1);
    position: relative;
    
    .header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .title-wrapper {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .title-icon {
          font-size: 20px;
          filter: drop-shadow(0 0 8px rgba(139, 92, 246, 0.6));
        }
        
        .title {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #fff;
          text-shadow: 0 0 10px rgba(139, 92, 246, 0.3);
        }
      }
      
      .header-actions {
        .view-toggle {
          width: 32px;
          height: 32px;
          border-radius: 8px;
          background: rgba(139, 92, 246, 0.1);
          border: 1px solid rgba(139, 92, 246, 0.2);
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.3s ease;
          
          svg {
            width: 16px;
            height: 16px;
            color: #8b5cf6;
          }
          
          &:hover {
            background: rgba(139, 92, 246, 0.2);
            transform: scale(1.1);
          }
        }
      }
    }
    
    .header-decoration {
      position: absolute;
      bottom: 0;
      left: 24px;
      width: 60px;
      height: 2px;
      background: linear-gradient(90deg, #8b5cf6, #00d4ff);
      border-radius: 1px;
    }
  }
  
  .panel-content {
    padding: 20px 24px 24px;
    
    .chart-view {
      .chart-container {
        width: 100%;
        height: 240px;
        margin-bottom: 20px;
      }
    }
    
    .list-view {
      .ranking-list {
        margin-bottom: 20px;
        
        .ranking-item {
          display: flex;
          align-items: center;
          padding: 12px;
          margin-bottom: 12px;
          background: rgba(139, 92, 246, 0.03);
          border: 1px solid rgba(139, 92, 246, 0.1);
          border-radius: 12px;
          transition: all 0.3s ease;
          animation: slideInRight 0.6s ease-out var(--delay);
          
          &:last-child {
            margin-bottom: 0;
          }
          
          &:hover {
            background: rgba(139, 92, 246, 0.05);
            border-color: rgba(139, 92, 246, 0.2);
            transform: translateY(-2px);
          }
          
          .rank-number {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            margin-right: 12px;
            
            &.rank-first {
              background: linear-gradient(135deg, #ffd700, #ffed4e);
              color: #000;
              box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
            }
            
            &.rank-second {
              background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
              color: #000;
              box-shadow: 0 4px 15px rgba(192, 192, 192, 0.4);
            }
            
            &.rank-third {
              background: linear-gradient(135deg, #cd7f32, #daa520);
              color: #fff;
              box-shadow: 0 4px 15px rgba(205, 127, 50, 0.4);
            }
            
            &.rank-normal {
              background: rgba(139, 92, 246, 0.2);
              color: #8b5cf6;
              border: 1px solid rgba(139, 92, 246, 0.3);
              font-size: 14px;
            }
            
            .medal {
              font-size: 16px;
            }
          }
          
          .dept-info {
            flex: 1;
            margin-right: 12px;
            
            .dept-name {
              font-size: 14px;
              color: #fff;
              font-weight: 500;
              margin-bottom: 2px;
            }
            
            .dept-category {
              font-size: 11px;
              color: rgba(255, 255, 255, 0.6);
            }
          }
          
          .dept-stats {
            text-align: right;
            margin-right: 12px;
            
            .asset-value {
              font-size: 14px;
              font-weight: 700;
              margin-bottom: 2px;
              text-shadow: 0 0 8px currentColor;
            }
            
            .asset-count {
              font-size: 11px;
              color: rgba(255, 255, 255, 0.6);
            }
          }
          
          .progress-bar {
            width: 60px;
            height: 4px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
            margin-right: 12px;
            overflow: hidden;
            
            .progress-fill {
              height: 100%;
              border-radius: 2px;
              transition: width 1s ease;
            }
          }
          
          .rank-change {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 11px;
            font-weight: 500;
            
            svg {
              width: 12px;
              height: 12px;
            }
            
            &.up {
              color: #00ff88;
            }
            
            &.down {
              color: #ff6b6b;
            }
            
            &.stable {
              color: rgba(255, 255, 255, 0.6);
            }
          }
        }
      }
    }
    
    .summary-stats {
      display: flex;
      justify-content: space-around;
      padding: 16px;
      background: rgba(139, 92, 246, 0.05);
      border-radius: 12px;
      border: 1px solid rgba(139, 92, 246, 0.1);
      
      .summary-item {
        text-align: center;
        
        .summary-label {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.7);
          margin-bottom: 4px;
        }
        
        .summary-value {
          font-size: 16px;
          font-weight: 700;
          color: #8b5cf6;
          text-shadow: 0 0 10px rgba(139, 92, 246, 0.3);
        }
      }
    }
  }
}

// 动画
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
