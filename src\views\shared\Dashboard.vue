<template>
  <div class="shared-dashboard">
    <!-- 页面标题 -->
    <div class="dashboard-header">
      <h1 class="dashboard-title">{{ isMobile ? '工作台' : '数据仪表板' }}</h1>
      <p class="dashboard-subtitle">{{ getCurrentTime() }}</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <AdaptiveCard
        v-for="stat in statsData"
        :key="stat.id"
        :title="stat.title"
        :clickable="true"
        @click="handleStatClick(stat)"
      >
        <div class="stat-content">
          <div class="stat-value">{{ stat.value }}</div>
          <div class="stat-change" :class="stat.trend">
            <span>{{ stat.change }}</span>
            <van-icon v-if="isMobile" :name="stat.trend === 'up' ? 'arrow-up' : 'arrow-down'" />
            <el-icon v-else>
              <component :is="stat.trend === 'up' ? 'ArrowUp' : 'ArrowDown'" />
            </el-icon>
          </div>
        </div>
      </AdaptiveCard>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions">
      <h3>快速操作</h3>
      <div class="action-grid">
        <AdaptiveButton
          v-for="action in quickActions"
          :key="action.id"
          :type="action.type"
          :block="isMobile"
          @click="handleActionClick(action)"
        >
          <van-icon v-if="isMobile" :name="action.icon" />
          <el-icon v-else>
            <component :is="action.icon" />
          </el-icon>
          {{ action.label }}
        </AdaptiveButton>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="data-section">
      <h3>最近活动</h3>
      <AdaptiveTable
        :data="tableData"
        :columns="tableColumns"
        :show-search="isMobile"
        @row-click="handleRowClick"
        @search="handleSearch"
      >
        <template #status="{ row }">
          <el-tag v-if="!isMobile" :type="getStatusType(row.status)">
            {{ row.status }}
          </el-tag>
          <van-tag v-else :type="getStatusType(row.status)">
            {{ row.status }}
          </van-tag>
        </template>
        
        <template #mobileCell="{ row }">
          <div class="mobile-row">
            <div class="mobile-row-title">{{ row.name }}</div>
            <div class="mobile-row-meta">
              <span>{{ row.department }}</span>
              <van-tag :type="getStatusType(row.status)">{{ row.status }}</van-tag>
            </div>
            <div class="mobile-row-time">{{ row.createTime }}</div>
          </div>
        </template>
      </AdaptiveTable>
    </div>

    <!-- 详情模态框 -->
    <AdaptiveModal
      v-model="showDetailModal"
      :title="modalTitle"
      :width="isMobile ? '100%' : '600px'"
    >
      <div class="detail-content">
        <p>这是一个自适应的模态框示例</p>
        <p>在PC端显示为对话框，在移动端显示为底部弹出层</p>
        <p v-if="selectedItem">选中项目: {{ selectedItem.name }}</p>
      </div>
      
      <template #footer>
        <div class="modal-footer">
          <AdaptiveButton @click="showDetailModal = false">
            取消
          </AdaptiveButton>
          <AdaptiveButton type="primary" @click="handleConfirm">
            确认
          </AdaptiveButton>
        </div>
      </template>
    </AdaptiveModal>
  </div>
</template>

<script setup lang="ts">
import { useDevice } from '@/composables/useDevice';
import AdaptiveCard from '@/components/adaptive/AdaptiveCard.vue';
import AdaptiveButton from '@/components/adaptive/AdaptiveButton.vue';
import AdaptiveTable from '@/components/adaptive/AdaptiveTable.vue';
import AdaptiveModal from '@/components/adaptive/AdaptiveModal.vue';

defineOptions({
  name: "SharedDashboard",
});

const { isMobile } = useDevice();

// 响应式数据
const showDetailModal = ref(false);
const selectedItem = ref(null);
const modalTitle = ref('详情');

// 统计数据
const statsData = ref([
  {
    id: 1,
    title: '总用户数',
    value: '12,345',
    change: '+12%',
    trend: 'up'
  },
  {
    id: 2,
    title: '活跃用户',
    value: '8,901',
    change: '+5%',
    trend: 'up'
  },
  {
    id: 3,
    title: '订单数量',
    value: '2,345',
    change: '-3%',
    trend: 'down'
  },
  {
    id: 4,
    title: '收入',
    value: '¥123,456',
    change: '+8%',
    trend: 'up'
  }
]);

// 快速操作
const quickActions = ref([
  {
    id: 1,
    label: '新建用户',
    icon: 'plus',
    type: 'primary'
  },
  {
    id: 2,
    label: '数据导出',
    icon: 'download',
    type: 'success'
  },
  {
    id: 3,
    label: '系统设置',
    icon: 'setting',
    type: 'info'
  },
  {
    id: 4,
    label: '帮助中心',
    icon: 'question',
    type: 'warning'
  }
]);

// 表格数据
const tableData = ref([
  {
    id: 1,
    name: '张三',
    department: '技术部',
    status: '在线',
    createTime: '2024-01-15 10:30'
  },
  {
    id: 2,
    name: '李四',
    department: '市场部',
    status: '离线',
    createTime: '2024-01-15 09:15'
  },
  {
    id: 3,
    name: '王五',
    department: '销售部',
    status: '忙碌',
    createTime: '2024-01-15 08:45'
  }
]);

// 表格列配置
const tableColumns = ref([
  {
    prop: 'name',
    label: '姓名',
    mobileDisplay: 'title'
  },
  {
    prop: 'department',
    label: '部门',
    mobileDisplay: 'label'
  },
  {
    prop: 'status',
    label: '状态',
    slot: 'status',
    mobileDisplay: 'value'
  },
  {
    prop: 'createTime',
    label: '创建时间',
    mobileDisplay: 'hidden'
  }
]);

// 方法
const getCurrentTime = () => {
  return new Date().toLocaleString('zh-CN');
};

const getStatusType = (status: string) => {
  const typeMap = {
    '在线': 'success',
    '离线': 'info',
    '忙碌': 'warning'
  };
  return typeMap[status] || 'default';
};

const handleStatClick = (stat: any) => {
  console.log('点击统计卡片:', stat);
  selectedItem.value = stat;
  modalTitle.value = stat.title;
  showDetailModal.value = true;
};

const handleActionClick = (action: any) => {
  console.log('点击快速操作:', action);
  if (isMobile.value) {
    // 移动端可以使用 Toast 提示
    console.log(`执行操作: ${action.label}`);
  } else {
    // PC端使用 Message 提示
    ElMessage.success(`执行操作: ${action.label}`);
  }
};

const handleRowClick = (row: any) => {
  console.log('点击表格行:', row);
  selectedItem.value = row;
  modalTitle.value = `用户详情 - ${row.name}`;
  showDetailModal.value = true;
};

const handleSearch = (value: string) => {
  console.log('搜索:', value);
  // 这里可以实现搜索逻辑
};

const handleConfirm = () => {
  console.log('确认操作');
  showDetailModal.value = false;
};
</script>

<style lang="scss" scoped>
.shared-dashboard {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .dashboard-header {
    margin-bottom: 24px;
    text-align: center;

    .dashboard-title {
      font-size: 28px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin-bottom: 8px;
    }

    .dashboard-subtitle {
      color: var(--el-text-color-regular);
      font-size: 14px;
    }
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    margin-bottom: 32px;

    .stat-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .stat-value {
        font-size: 24px;
        font-weight: 600;
        color: var(--el-color-primary);
      }

      .stat-change {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 14px;

        &.up {
          color: var(--el-color-success);
        }

        &.down {
          color: var(--el-color-danger);
        }
      }
    }
  }

  .quick-actions {
    margin-bottom: 32px;

    h3 {
      margin-bottom: 16px;
      color: var(--el-text-color-primary);
    }

    .action-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 12px;
    }
  }

  .data-section {
    h3 {
      margin-bottom: 16px;
      color: var(--el-text-color-primary);
    }
  }

  .mobile-row {
    .mobile-row-title {
      font-weight: 600;
      margin-bottom: 4px;
    }

    .mobile-row-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 4px;
      font-size: 14px;
      color: var(--el-text-color-regular);
    }

    .mobile-row-time {
      font-size: 12px;
      color: var(--el-text-color-secondary);
    }
  }

  .detail-content {
    padding: 16px 0;
    line-height: 1.6;
  }

  .modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

// 移动端适配
@media screen and (max-width: 991px) {
  .shared-dashboard {
    padding: 16px;

    .dashboard-header {
      .dashboard-title {
        font-size: 24px;
      }
    }

    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;
    }

    .action-grid {
      grid-template-columns: 1fr;
      gap: 8px;
    }

    .modal-footer {
      flex-direction: column-reverse;
      gap: 8px;
    }
  }
}
</style>
