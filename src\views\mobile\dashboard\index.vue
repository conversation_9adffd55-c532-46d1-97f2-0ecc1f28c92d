<template>
  <div class="mobile-dashboard">
    <MainSlide />
    <MainGrid />
    <!-- <van-divider content-position="left">我的资产</van-divider> -->
    <!-- <div class="cat_list_div">
      <van-list v-model:loading="assetscat.loading" :finished="assetscat.finished">
        <van-cell
          v-for="(item, index) in assetscat.list"
          :key="index"
          :to="{
            name: 'wodezichanliebia<PERSON>',
            query: {
              //全部的情况字符串为空，跳转不了
              catId: item.xcode || '  ',
            },
          }"
        >
          <template #title>
            <span>{{ item.name }}</span>
          </template>
          <template #label>
            <div>
              共计
              <span class="red">{{ item.num }}</span>
              件，原值
              <span class="red">{{ item.zje }}</span>
              元
            </div>
          </template>
        </van-cell>
      </van-list>
    </div> -->
  </div>
</template>

<script setup lang="ts">
import MainSlide from "@/views/mobile/dashboard/components/slide.vue";
import MainGrid from "@/views/mobile/dashboard/components/grid.vue";
</script>

<style lang="scss" scoped>
.mobile-dashboard {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: 100vh;
}
</style>
