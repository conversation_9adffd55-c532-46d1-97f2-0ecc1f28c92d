<template>
  <div class="asset-overview">
    <!-- 中央圆形统计 -->
    <div class="center-circle">
      <div class="circle-container">
        <div class="circle-progress" :style="{ '--progress': totalProgress }">
          <div class="circle-inner">
            <div class="total-number">{{ formatNumber(totalAssets) }}</div>
            <div class="total-label">资产总数量</div>
            <div class="total-unit">万元</div>
          </div>
        </div>
        
        <!-- 环形装饰 -->
        <div class="circle-decorations">
          <div class="decoration-ring ring-1"></div>
          <div class="decoration-ring ring-2"></div>
          <div class="decoration-ring ring-3"></div>
        </div>
      </div>
    </div>
    
    <!-- 四个统计卡片 -->
    <div class="stats-grid">
      <div 
        class="stat-card" 
        v-for="(stat, index) in assetStats" 
        :key="index"
        :style="{ '--delay': index * 0.1 + 's' }"
      >
        <div class="card-background"></div>
        <div class="card-content">
          <div class="stat-header">
            <div class="stat-icon" :style="{ '--icon-color': stat.iconColor }">
              <component :is="stat.icon" />
            </div>
            <div class="stat-trend" :class="stat.trend">
              <svg v-if="stat.trend === 'up'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M7 14L12 9L17 14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <svg v-else viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M7 10L12 15L17 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <span>{{ stat.trendValue }}</span>
            </div>
          </div>
          
          <div class="stat-body">
            <div class="stat-value">{{ formatNumber(stat.value) }}</div>
            <div class="stat-label">{{ stat.label }}</div>
            <div class="stat-unit">{{ stat.unit }}</div>
          </div>
          
          <div class="stat-footer">
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                :style="{ 
                  width: stat.progress + '%',
                  backgroundColor: stat.iconColor
                }"
              ></div>
            </div>
            <div class="stat-subtitle">{{ stat.subtitle }}</div>
          </div>
        </div>
        
        <!-- 卡片装饰 -->
        <div class="card-decoration">
          <div class="decoration-dot"></div>
          <div class="decoration-line"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

// 图标组件
const DeviceIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="2" y="3" width="20" height="14" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
      <line x1="8" y1="21" x2="16" y2="21" stroke="currentColor" stroke-width="2"/>
      <line x1="12" y1="17" x2="12" y2="21" stroke="currentColor" stroke-width="2"/>
    </svg>
  `
}

const MoneyIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <line x1="12" y1="1" x2="12" y2="23" stroke="currentColor" stroke-width="2"/>
      <path d="M17 5H9.5A3.5 3.5 0 0 0 9.5 12H14.5A3.5 3.5 0 0 1 14.5 19H6" stroke="currentColor" stroke-width="2"/>
    </svg>
  `
}

const EquipmentIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
      <path d="M19.4 15A1.65 1.65 0 0 0 21 13.35V10.65A1.65 1.65 0 0 0 19.4 9L17.7 7.3A1.65 1.65 0 0 0 15.35 7.3L13.65 9A1.65 1.65 0 0 0 13.65 10.65V13.35A1.65 1.65 0 0 0 15.35 15L17.7 16.7A1.65 1.65 0 0 0 19.4 15Z" stroke="currentColor" stroke-width="2"/>
    </svg>
  `
}

const AssetIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M3 3V21H21V3H3Z" stroke="currentColor" stroke-width="2"/>
      <path d="M9 9H15V15H9V9Z" stroke="currentColor" stroke-width="2"/>
    </svg>
  `
}

// 数据
const totalAssets = ref(30883.31)
const totalProgress = ref(75)

const assetStats = ref([
  {
    label: '设备资产数量',
    value: 11810,
    unit: '台',
    icon: DeviceIcon,
    iconColor: '#00d4ff',
    trend: 'up',
    trendValue: '+5%',
    progress: 85,
    subtitle: '较上月增长'
  },
  {
    label: '设备资产金额',
    value: 30831,
    unit: '万',
    icon: MoneyIcon,
    iconColor: '#00ff88',
    trend: 'down',
    trendValue: '-2%',
    progress: 72,
    subtitle: '较上月下降'
  },
  {
    label: '固定资产数量',
    value: 3780,
    unit: '台',
    icon: EquipmentIcon,
    iconColor: '#ffb800',
    trend: 'up',
    trendValue: '+8%',
    progress: 90,
    subtitle: '较上月增长'
  },
  {
    label: '无形资产数量',
    value: 2543,
    unit: '项',
    icon: AssetIcon,
    iconColor: '#ff6b6b',
    trend: 'up',
    trendValue: '+3%',
    progress: 68,
    subtitle: '较上月增长'
  }
])

// 格式化数字
const formatNumber = (num) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(2)
  }
  return num.toLocaleString()
}
</script>

<style lang="scss" scoped>
.asset-overview {
  display: flex;
  gap: 20px;
  height: 280px;
  
  .center-circle {
    flex: 0 0 280px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .circle-container {
      position: relative;
      width: 240px;
      height: 240px;
      
      .circle-progress {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: conic-gradient(
          from 0deg,
          #00d4ff 0deg,
          #00ff88 calc(var(--progress) * 3.6deg),
          rgba(255, 255, 255, 0.1) calc(var(--progress) * 3.6deg),
          rgba(255, 255, 255, 0.1) 360deg
        );
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        animation: rotate 20s linear infinite;
        
        &::before {
          content: '';
          position: absolute;
          width: 180px;
          height: 180px;
          background: linear-gradient(135deg, #0a0e27 0%, #1a1f3a 100%);
          border-radius: 50%;
          z-index: 1;
          box-shadow: inset 0 0 30px rgba(0, 0, 0, 0.5);
        }
        
        .circle-inner {
          position: relative;
          z-index: 2;
          text-align: center;
          color: #fff;
          
          .total-number {
            font-size: 36px;
            font-weight: 700;
            color: #00d4ff;
            margin-bottom: 8px;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
            animation: pulse 2s ease-in-out infinite;
          }
          
          .total-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 4px;
          }
          
          .total-unit {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
          }
        }
      }
      
      .circle-decorations {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        
        .decoration-ring {
          position: absolute;
          border-radius: 50%;
          border: 1px solid;
          animation: ringRotate linear infinite;
          
          &.ring-1 {
            width: 260px;
            height: 260px;
            top: -10px;
            left: -10px;
            border-color: rgba(0, 212, 255, 0.3);
            animation-duration: 30s;
          }
          
          &.ring-2 {
            width: 280px;
            height: 280px;
            top: -20px;
            left: -20px;
            border-color: rgba(0, 255, 136, 0.2);
            animation-duration: 40s;
            animation-direction: reverse;
          }
          
          &.ring-3 {
            width: 300px;
            height: 300px;
            top: -30px;
            left: -30px;
            border-color: rgba(255, 184, 0, 0.1);
            animation-duration: 50s;
          }
        }
      }
    }
  }
  
  .stats-grid {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    
    .stat-card {
      position: relative;
      background: linear-gradient(135deg, 
        rgba(0, 212, 255, 0.05) 0%, 
        rgba(0, 255, 136, 0.03) 100%
      );
      border: 1px solid rgba(0, 212, 255, 0.2);
      border-radius: 16px;
      backdrop-filter: blur(20px);
      overflow: hidden;
      transition: all 0.3s ease;
      animation: cardSlideIn 0.6s ease-out var(--delay);
      
      &:hover {
        border-color: rgba(0, 212, 255, 0.4);
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 212, 255, 0.2);
      }
      
      .card-background {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, 
          rgba(var(--icon-color), 0.05) 0%, 
          transparent 100%
        );
        opacity: 0;
        transition: opacity 0.3s ease;
      }
      
      &:hover .card-background {
        opacity: 1;
      }
      
      .card-content {
        position: relative;
        z-index: 2;
        padding: 20px;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        
        .stat-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 12px;
          
          .stat-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--icon-color), var(--icon-color)80);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 15px rgba(var(--icon-color), 0.3);
            
            svg {
              width: 20px;
              height: 20px;
              color: #fff;
            }
          }
          
          .stat-trend {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            font-weight: 500;
            
            svg {
              width: 14px;
              height: 14px;
            }
            
            &.up {
              color: #00ff88;
            }
            
            &.down {
              color: #ff6b6b;
            }
          }
        }
        
        .stat-body {
          margin-bottom: 16px;
          
          .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: #fff;
            margin-bottom: 4px;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
          }
          
          .stat-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 2px;
          }
          
          .stat-unit {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
          }
        }
        
        .stat-footer {
          .progress-bar {
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
            overflow: hidden;
            margin-bottom: 8px;
            
            .progress-fill {
              height: 100%;
              border-radius: 2px;
              transition: width 1s ease;
              box-shadow: 0 0 10px currentColor;
            }
          }
          
          .stat-subtitle {
            font-size: 11px;
            color: rgba(255, 255, 255, 0.5);
          }
        }
      }
      
      .card-decoration {
        position: absolute;
        top: 16px;
        right: 16px;
        
        .decoration-dot {
          width: 6px;
          height: 6px;
          background: var(--icon-color);
          border-radius: 50%;
          margin-bottom: 4px;
          animation: dotPulse 2s ease-in-out infinite;
        }
        
        .decoration-line {
          width: 20px;
          height: 1px;
          background: linear-gradient(90deg, var(--icon-color), transparent);
        }
      }
    }
  }
}

// 动画
@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes ringRotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
}

@keyframes cardSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes dotPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .asset-overview {
    flex-direction: column;
    height: auto;
    
    .center-circle {
      flex: none;
      height: 200px;
      
      .circle-container {
        width: 180px;
        height: 180px;
        
        .circle-progress {
          &::before {
            width: 140px;
            height: 140px;
          }
          
          .circle-inner {
            .total-number {
              font-size: 28px;
            }
          }
        }
      }
    }
    
    .stats-grid {
      grid-template-columns: repeat(4, 1fr);
    }
  }
}

@media (max-width: 768px) {
  .asset-overview {
    .stats-grid {
      grid-template-columns: 1fr 1fr;
    }
  }
}
</style>
