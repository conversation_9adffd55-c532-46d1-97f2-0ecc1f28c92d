<template>
  <div class="asset-overview">
    <!-- 中央圆形统计 -->
    <div class="center-circle">
      <div class="circle-bg">
        <img src="@/assets/images/dashboard/center_bg.png" alt="中央背景" />
      </div>
      <div class="circle-content">
        <div class="total-assets">
          <div class="total-number">{{ formatNumber(totalAssets) }}</div>
          <div class="total-label">资产总价值</div>
          <div class="total-unit">万元</div>
        </div>

        <!-- 环形进度 -->
        <div class="circle-progress">
          <svg class="progress-ring" width="300" height="300">
            <circle
              class="progress-ring-bg"
              cx="150"
              cy="150"
              r="120"
              fill="transparent"
              stroke="rgba(0, 212, 255, 0.2)"
              stroke-width="4"
            />
            <circle
              class="progress-ring-fill"
              cx="150"
              cy="150"
              r="120"
              fill="transparent"
              stroke="url(#gradient)"
              stroke-width="4"
              :stroke-dasharray="circumference"
              :stroke-dashoffset="strokeDashoffset"
              transform="rotate(-90 150 150)"
            />
            <defs>
              <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" style="stop-color: #00d4ff; stop-opacity: 1" />
                <stop offset="50%" style="stop-color: #00ff88; stop-opacity: 1" />
                <stop offset="100%" style="stop-color: #ffb800; stop-opacity: 1" />
              </linearGradient>
            </defs>
          </svg>
        </div>
      </div>
    </div>

    <!-- 四个统计卡片 -->
    <div class="stats-grid">
      <div
        class="stat-card"
        v-for="(stat, index) in assetStats"
        :key="index"
        :style="{ '--delay': index * 0.1 + 's' }"
      >
        <div class="card-bg">
          <img :src="stat.bgImage" alt="卡片背景" />
        </div>
        <div class="card-content">
          <div class="stat-icon">
            <img :src="stat.iconImage" alt="图标" />
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ formatNumber(stat.value) }}</div>
            <div class="stat-label">{{ stat.label }}</div>
            <div class="stat-unit">{{ stat.unit }}</div>
          </div>
          <div class="stat-trend" :class="stat.trend">
            <span>{{ stat.trendValue }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";

// 数据
const totalAssets = ref(30883.31);
const totalProgress = ref(75);

// 计算环形进度条
const radius = 120;
const circumference = computed(() => 2 * Math.PI * radius);
const strokeDashoffset = computed(() => {
  return circumference.value - (totalProgress.value / 100) * circumference.value;
});

const assetStats = ref([
  {
    label: "设备资产数量",
    value: 11810,
    unit: "台",
    bgImage: "@/assets/images/dashboard/dataScreen-main-lb.png",
    iconImage: "@/assets/images/dashboard/icon-device.png",
    trend: "up",
    trendValue: "+5%",
  },
  {
    label: "设备资产金额",
    value: 30831,
    unit: "万",
    bgImage: "@/assets/images/dashboard/dataScreen-main-rb.png",
    iconImage: "@/assets/images/dashboard/icon-money.png",
    trend: "down",
    trendValue: "-2%",
  },
  {
    label: "固定资产数量",
    value: 3780,
    unit: "台",
    bgImage: "@/assets/images/dashboard/dataScreen-main-lt.png",
    iconImage: "@/assets/images/dashboard/icon-equipment.png",
    trend: "up",
    trendValue: "+8%",
  },
  {
    label: "无形资产数量",
    value: 2543,
    unit: "项",
    bgImage: "@/assets/images/dashboard/dataScreen-main-rt.png",
    iconImage: "@/assets/images/dashboard/icon-asset.png",
    trend: "up",
    trendValue: "+3%",
  },
]);

// 格式化数字
const formatNumber = (num) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(2);
  }
  return num.toLocaleString();
};
</script>

<style lang="scss" scoped>
.asset-overview {
  display: flex;
  gap: 3%;
  height: 45%;

  .center-circle {
    flex: 0 0 40%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;

    .circle-bg {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 1;

      img {
        width: 100%;
        height: auto;
        max-width: 25vw;
      }
    }

    .circle-content {
      position: relative;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;

      .total-assets {
        position: absolute;
        text-align: center;
        color: #fff;
        z-index: 3;

        .total-number {
          font-size: 2.5vw;
          font-weight: 700;
          color: #00d4ff;
          font-family: 'electronicFont', 'Courier New', monospace;
          text-shadow: 0 0 20px rgba(0, 212, 255, 0.8);
          margin-bottom: 0.5vh;
          animation: numberGlow 2s ease-in-out infinite;
        }

        .total-label {
          font-size: 1vw;
          color: rgba(255, 255, 255, 0.9);
          margin-bottom: 0.2vh;
        }

        .total-unit {
          font-size: 0.8vw;
          color: rgba(255, 255, 255, 0.7);
        }
      }

      .circle-progress {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);

        .progress-ring {
          animation: rotate 30s linear infinite;
        }

        .progress-ring-fill {
          transition: stroke-dashoffset 2s ease-in-out;
          filter: drop-shadow(0 0 10px rgba(0, 212, 255, 0.6));
        }
      }
    }

    }
  }

  .stats-grid {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2%;

    .stat-card {
      position: relative;
      overflow: hidden;
      transition: all 0.3s ease;
      animation: cardSlideIn 0.6s ease-out var(--delay);

      &:hover {
        transform: scale(1.05);
        filter: brightness(1.2);
      }

      .card-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .card-content {
        position: relative;
        z-index: 2;
        padding: 1.5vw;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        text-align: center;

        .stat-icon {
          margin: 0 auto 1vh;
          width: 3vw;
          height: 3vw;

          img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            filter: drop-shadow(0 0 10px rgba(0, 212, 255, 0.6));
          }
        }

        .stat-info {
          .stat-value {
            font-size: 1.8vw;
            font-weight: 700;
            color: #00d4ff;
            font-family: 'electronicFont', 'Courier New', monospace;
            text-shadow: 0 0 15px rgba(0, 212, 255, 0.8);
            margin-bottom: 0.5vh;
            animation: numberGlow 2s ease-in-out infinite;
          }

          .stat-label {
            font-size: 0.9vw;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 0.2vh;
          }

          .stat-unit {
            font-size: 0.7vw;
            color: rgba(255, 255, 255, 0.7);
          }
        }

        .stat-trend {
          font-size: 0.8vw;
          font-weight: 600;
          margin-top: 0.5vh;

          &.up {
            color: #00ff88;
            text-shadow: 0 0 10px rgba(0, 255, 136, 0.6);
          }

          &.down {
            color: #ff6b6b;
            text-shadow: 0 0 10px rgba(255, 107, 107, 0.6);
          }
        }
      }
    }
  }
}

// 动画
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes numberGlow {
  0%, 100% {
    text-shadow: 0 0 15px rgba(0, 212, 255, 0.8);
  }
  50% {
    text-shadow: 0 0 25px rgba(0, 212, 255, 1);
  }
}

@keyframes cardSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}



// 响应式设计
@media (max-width: 1200px) {
  .asset-overview {
    flex-direction: column;
    height: auto;
    gap: 2vh;

    .center-circle {
      flex: none;
      height: 30vh;

      .circle-content {
        .total-assets {
          .total-number {
            font-size: 3vw;
          }

          .total-label {
            font-size: 1.2vw;
          }

          .total-unit {
            font-size: 1vw;
          }
        }
      }
    }

    .stats-grid {
      grid-template-columns: repeat(4, 1fr);

      .stat-card {
        .card-content {
          .stat-icon {
            width: 4vw;
            height: 4vw;
          }

          .stat-info {
            .stat-value {
              font-size: 2.2vw;
            }

            .stat-label {
              font-size: 1.1vw;
            }

            .stat-unit {
              font-size: 0.9vw;
            }
          }

          .stat-trend {
            font-size: 1vw;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .asset-overview {
    .stats-grid {
      grid-template-columns: 1fr 1fr;

      .stat-card {
        .card-content {
          .stat-icon {
            width: 6vw;
            height: 6vw;
          }

          .stat-info {
            .stat-value {
              font-size: 3vw;
            }

            .stat-label {
              font-size: 1.5vw;
            }

            .stat-unit {
              font-size: 1.2vw;
            }
          }

          .stat-trend {
            font-size: 1.3vw;
          }
        }
      }
    }
  }
}
</style>
