<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <van-grid :border="false" icon-size="50px" :clickable="true" column-num="4">
    <van-grid-item
      v-for="tab in zcItem"
      :key="tab.key"
      :icon="tab.icon"
      :text="tab.title"
      :to="tab.key"
      :badge="tab.badge"
    />
  </van-grid>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from "vue";
import { GLOBAL_CONSTANT } from "@/views/mobile/constants";
import { watch } from "vue";

const zcItem = reactive(GLOBAL_CONSTANT.MAIN_TABS.ZC_TABS);
const item = ref(zcItem.find((item) => item.key === "/remind"));

// onMounted(() => {
//   GetZcRemind();
// });

// watch(reminds, (newValue) => {
//   if (item.value != null) {
//     item.value.badge = newValue.count;
//   }
// });
</script>

<style scoped>
.my-swipe .van-swipe-item {
  color: #fff;
  font-size: 20px;
  height: 150px;
  text-align: center;
  background-color: #39a9ed;
}

:deep(.van-grid-item__text) {
  font-size: 14px;
}
</style>
