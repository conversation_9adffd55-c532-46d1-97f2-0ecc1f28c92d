<!DOCTYPE html>
<html>
<head>
  <title>Ease visualizer | anime.js</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <meta property="og:title" content="anime.js">
  <meta property="og:url" content="https://animejs.com">
  <meta property="og:description" content="Javascript Animation Engine">
  <meta property="og:image" content="https://animejs.com/documentation/assets/img/icons/og.png">
  <meta name="twitter:card" content="summary">
  <meta name="twitter:title" content="anime.js">
  <meta name="twitter:site" content="@juliangarnier">
  <meta name="twitter:description" content="Javascript Animation Engine">
  <meta name="twitter:image" content="https://animejs.com/documentation/assets/img/icons/twitter.png">
  <link rel="apple-touch-icon-precomposed" href="../assets/img/social-media-image.png">
  <link rel="icon" type="image/png" href="../assets/img/favicon.png">
  <link href="../assets/css/animejs.css" rel="stylesheet">
  <link href="../assets/css/documentation.css" rel="stylesheet">
  <script src="../../lib/anime.min.js"></script>
  <style>
    .easings {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-direction: column;
      position: absolute;
      width: 100%;
      height: 100%;
    }
    .output {
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      width: 100%;
      height: 400px;
    }
    .options {
      display: flex;
      flex-wrap: wrap;
      flex-direction: row;
      width: 100%;
      min-height: 300px;
      border-top: 1px solid #09090B;
    }
    .grid {
      border-top: 1px solid rgba(255,255,255,0.2);
      border-right: 1px solid rgba(255,255,255,0.2);
      background: linear-gradient(0deg, rgba(255,255,255,0.2) 1px, rgba(0,0,0,0) 1px),
                  linear-gradient(90deg, rgba(255,255,255,0.2) 1px, rgba(0,0,0,0) 1px),
                  linear-gradient(0deg, rgba(255,255,255,0.1) 1px, rgba(0,0,0,0) 1px),
                  linear-gradient(90deg, rgba(255,255,255,0.1) 1px, rgba(0,0,0,0) 1px);
      background-position: 0px -1px;
      background-size: 100px 100px, 100px 100px, 20px 20px, 20px 20px;
    }
    .visualizer-wrapper {
      position: relative;
      width: 100%;
      height: 100%;
    }
    .visualizer {
      position: relative;
      width: 100%;
    }
    .value {
      position: relative;
      width: 20px;
      height: 100%;
      margin-left: 20px;
    }
    @media screen and (min-width: 980px) {
      .easings {
        flex-direction: row;
      }
      .output {
        width: 50%;
        height: 100%;
      }
      .options {
        width: 50%;
        height: 100%;
      }
      .visualizer-wrapper {
        width: 400px;
        height: 400px;
      }
      .value {
        width: 40px;
        height: 400px;
        margin-left: 40px;
      }
    }
    .axis {
      position: absolute;
      background: #5A87FF;
    }
    .axis.x {
      width: 1px;
      height: 100%;
      transform-origin: 0 0;
    }
    .axis.y {
      bottom: 0;
      width: 100%;
      height: 1px;
      transform-origin: 100% 0;
    }
    .graph {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    }
    .cursor {
      position: absolute;
      z-index: 2;
      width: 40px;
      height: 40px;
      margin-top: -20px;
      background: #FF1461;
      border-radius: 50%;
      transform: translateY(100px);
    }
    .curve {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      overflow: visible;
      position: relative;
      z-index: 1;
      width: 100%;
      height: 100%;
    }
    .curve-dot {
      position: relative;
      width: 6px;
      height: 6px;
      margin-bottom: -3px;
      margin-left: -3px;
      border-radius: 50%;
      flex-shrink: 0;
      background-color: #18FF92;
    }
    .curve-dot:first-child {
      margin-left: -3px;
    }
    .curve-dot:last-child {
      margin-right: -4px;
    }
    button {
      opacity: 1;
      position: relative;
      color: currentColor;
      font-size: 16px;
      width: calc(50% + 1px);
      border: 1px solid #09090B;
      background: transparent;
      margin: -1px 0 0 -1px;
      letter-spacing: 1px;
    }
    label {
      display: flex;
      align-items: center;
      justify-content: center;
      width: calc(100% + 1px);
      margin: -1px 0 0 -1px;
      border-top: 0px;
      border-left: 1px solid #09090B;
      border-right: 1px solid #09090B;
      color: rgba(255,255,255,.3);
    }
    button.active {
      color: #18FF92;
      z-index: 1;
      opacity: 1;
      border: 1px solid currentColor;
      background: #222027;
    }
    button:focus {
      outline: none;
    }
    button:hover {
      background: #222027;
    }
  </style>
</head>
<body>
  <div class="easings">
    <div class="output">
      <div class="visualizer-wrapper grid">
        <div class="axis x"></div>
        <div class="axis y"></div>
        <div class="visualizer">
          <div class="curve">
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
            <div class="curve-dot"></div>
          </div>
        </div>
      </div>
      <div class="value grid">
        <div class="graph">
          <div class="cursor"></div>
          <div class="cursor"></div>
          <div class="cursor"></div>
          <div class="cursor"></div>
          <div class="cursor"></div>
          <div class="cursor"></div>
          <div class="cursor"></div>
          <div class="cursor"></div>
          <div class="cursor"></div>
          <div class="cursor"></div>
          <div class="cursor"></div>
          <div class="cursor"></div>
          <div class="cursor"></div>
          <div class="cursor"></div>
          <div class="cursor"></div>
          <div class="cursor"></div>
          <div class="cursor"></div>
          <div class="cursor"></div>
        </div>
      </div>
    </div>
    <div class="options">
      <label>Ease In</label>
      <button class="active" value="easeInQuad" >Quad</button>
      <button value="easeInCubic" >Cubic</button>
      <button value="easeInQuart" >Quart</button>
      <button value="easeInQuint" >Quint</button>
      <button value="easeInSine" >Sine</button>
      <button value="easeInExpo" >Expo</button>
      <button value="easeInCirc" >Circ</button>
      <button value="easeInBack" >Back</button>
      <button value="easeInBounce" >Bounce</button>
      <button value="easeInElastic" >Elastic</button>
      <label>Ease Out</label>
      <button value="easeOutQuad" >Quad</button>
      <button value="easeOutCubic" >Cubic</button>
      <button value="easeOutQuart" >Quart</button>
      <button value="easeOutQuint" >Quint</button>
      <button value="easeOutSine" >Sine</button>
      <button value="easeOutExpo" >Expo</button>
      <button value="easeOutCirc" >Circ</button>
      <button value="easeOutBack" >Back</button>
      <button value="easeOutBounce" >Bounce</button>
      <button value="easeOutElastic" >Elastic</button>
      <label>Ease In Out</label>
      <button value="easeInOutQuad" >Quad</button>
      <button value="easeInOutCubic" >Cubic</button>
      <button value="easeInOutQuart" >Quart</button>
      <button value="easeInOutQuint" >Quint</button>
      <button value="easeInOutSine" >Sine</button>
      <button value="easeInOutExpo" >Expo</button>
      <button value="easeInOutCirc" >Circ</button>
      <button value="easeInOutBack" >Back</button>
      <button value="easeInOutBounce" >Bounce</button>
      <button value="easeInOutElastic" >Elastic</button>
    </div>
  </div>
  <script>

  var pathEl = document.querySelector('.curve');
  var presetsEls = document.querySelectorAll('.options button');
  var duration = 1000;

  function animateProgress(easingName) {

    anime.remove(['.axis.x', '.axis.y', '.cursor']);

    var tl = anime.timeline({
      duration: duration,
      easing: 'linear',
      complete: function() {
        animateProgress(easingName);
      }
    })
    .add({
      targets: '.axis.x',
      translateX: [0, 399],
      translateZ: [0, 0]
     }, 0)
    .add({
      targets: '.axis.y',
      translateY: [0, -399],
      translateZ: [0, 0],
      easing: easingName,
      endDelay: 1000
    }, 0)
    .add({
      targets: '.cursor',
      translateY: [400, 0],
      translateZ: [0, 0],
      easing: easingName,
      update: function(anim) {
        var timeSegment = (100 / (anim.animatables.length - 2));
        var index = Math.round((anim.progress +.5) / timeSegment);
        var animatable = anim.animatables[index];
        if (animatable) {
          var target = animatable.target;
          anime.remove(target);
          target.style.opacity = .20;
        }
      }
    }, 0);

  }

  function changeEase(event) {
    for (var i = 0; i < presetsEls.length; i++) {
      presetsEls[i].classList.remove('active');
    }
    var buttonEl = event.target;
    var easingName = buttonEl.value;
    buttonEl.classList.add('active');
    animateProgress(easingName);
    anime({
      targets: '.curve-dot',
      translateY: anime.stagger([0, -400], {easing: easingName}),
      easing: 'easeInOutQuad',
      duration: 400
    });
  }

  for (var i = 0; i < presetsEls.length; i++) {
    presetsEls[i].onclick = changeEase;
  }

  presetsEls[0].click();

  pathEl.setAttribute('d', getCoordinates(presetsEls[0].value));

  </script>
</body>
</html>
