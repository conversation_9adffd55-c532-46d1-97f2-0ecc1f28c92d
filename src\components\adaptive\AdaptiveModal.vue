<template>
  <div class="adaptive-modal">
    <!-- PC端对话框 -->
    <el-dialog
      v-if="!isMobile"
      v-model="visible"
      v-bind="modalProps"
      @close="handleClose"
      @closed="handleClosed"
    >
      <template v-if="$slots.header" #header>
        <slot name="header" />
      </template>
      
      <slot />
      
      <template v-if="$slots.footer" #footer>
        <slot name="footer" />
      </template>
    </el-dialog>

    <!-- 移动端弹出层 -->
    <van-popup
      v-else
      v-model:show="visible"
      v-bind="modalProps"
      @close="handleClose"
      @closed="handleClosed"
    >
      <!-- 移动端头部 -->
      <div v-if="title || $slots.header" class="mobile-modal-header">
        <slot name="header">
          <div class="mobile-modal-title">{{ title }}</div>
        </slot>
        <van-icon 
          name="cross" 
          class="mobile-modal-close"
          @click="handleClose"
        />
      </div>
      
      <!-- 移动端内容 -->
      <div class="mobile-modal-content">
        <slot />
      </div>
      
      <!-- 移动端底部 -->
      <div v-if="$slots.footer" class="mobile-modal-footer">
        <slot name="footer" />
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { useDevice } from '@/composables/useDevice';

interface AdaptiveModalProps {
  modelValue: boolean;
  title?: string;
  width?: string | number;
  height?: string | number;
  fullscreen?: boolean;
  closable?: boolean;
  maskClosable?: boolean;
  destroyOnClose?: boolean;
  mobileProps?: Record<string, any>;
  desktopProps?: Record<string, any>;
}

const props = withDefaults(defineProps<AdaptiveModalProps>(), {
  width: '50%',
  fullscreen: false,
  closable: true,
  maskClosable: true,
  destroyOnClose: false
});

const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  'close': [];
  'closed': [];
  'open': [];
  'opened': [];
}>();

const { isMobile } = useDevice();

// 响应式显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 模态框属性适配
const modalProps = computed(() => {
  const baseProps = {
    title: props.title,
    closable: props.closable,
    'destroy-on-close': props.destroyOnClose
  };

  // 平台特定属性
  const platformProps = isMobile.value ? props.mobileProps : props.desktopProps;

  if (isMobile.value) {
    // Vant Popup 属性
    return {
      ...baseProps,
      ...platformProps,
      // 移动端特有属性
      position: 'bottom',
      round: true,
      'close-on-click-overlay': props.maskClosable,
      style: {
        height: props.height || '80vh',
        maxHeight: '90vh'
      }
    };
  } else {
    // Element Plus Dialog 属性
    return {
      ...baseProps,
      ...platformProps,
      // PC端特有属性
      width: props.width,
      fullscreen: props.fullscreen,
      'close-on-click-modal': props.maskClosable,
      'append-to-body': true
    };
  }
});

// 事件处理
const handleClose = () => {
  visible.value = false;
  emit('close');
};

const handleClosed = () => {
  emit('closed');
};

// 监听打开事件
watch(visible, (newVal, oldVal) => {
  if (newVal && !oldVal) {
    emit('open');
    nextTick(() => {
      emit('opened');
    });
  }
});
</script>

<style lang="scss" scoped>
.adaptive-modal {
  // PC端对话框样式
  .adaptive-layout--desktop & {
    :deep(.el-dialog) {
      border-radius: 12px;
      overflow: hidden;
      
      .el-dialog__header {
        padding: 20px 24px 16px;
        border-bottom: 1px solid var(--el-border-color-light);
      }
      
      .el-dialog__body {
        padding: 24px;
      }
      
      .el-dialog__footer {
        padding: 16px 24px 20px;
        border-top: 1px solid var(--el-border-color-light);
      }
    }
  }
  
  // 移动端弹出层样式
  .adaptive-layout--mobile & {
    .mobile-modal-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 20px;
      border-bottom: 1px solid #ebedf0;
      background: white;
      position: sticky;
      top: 0;
      z-index: 1;
      
      .mobile-modal-title {
        font-size: 18px;
        font-weight: 600;
        color: #323233;
      }
      
      .mobile-modal-close {
        font-size: 20px;
        color: #969799;
        cursor: pointer;
        
        &:active {
          color: #646566;
        }
      }
    }
    
    .mobile-modal-content {
      padding: 20px;
      max-height: calc(80vh - 120px);
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
    }
    
    .mobile-modal-footer {
      padding: 16px 20px;
      border-top: 1px solid #ebedf0;
      background: white;
      position: sticky;
      bottom: 0;
      z-index: 1;
    }
    
    :deep(.van-popup) {
      border-radius: 16px 16px 0 0;
    }
  }
}

// 全屏模态框样式
.adaptive-modal--fullscreen {
  .adaptive-layout--mobile & {
    .mobile-modal-content {
      max-height: calc(100vh - 120px);
    }
    
    :deep(.van-popup) {
      border-radius: 0;
    }
  }
}
</style>
