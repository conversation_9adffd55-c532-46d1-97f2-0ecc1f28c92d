<!DOCTYPE html>
<html>
<head>
  <title>Complex properties | anime.js</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <meta property="og:title" content="anime.js">
  <meta property="og:url" content="https://animejs.com">
  <meta property="og:description" content="Javascript Animation Engine">
  <meta property="og:image" content="https://animejs.com/documentation/assets/img/icons/og.png">
  <meta name="twitter:card" content="summary">
  <meta name="twitter:title" content="anime.js">
  <meta name="twitter:site" content="@juliangarnier">
  <meta name="twitter:description" content="Javascript Animation Engine">
  <meta name="twitter:image" content="https://animejs.com/documentation/assets/img/icons/twitter.png">
  <link rel="apple-touch-icon-precomposed" href="../assets/img/social-media-image.png">
  <link href="../assets/css/animejs.css" rel="stylesheet">
  <link href="../assets/css/documentation.css" rel="stylesheet">
  <!-- <script src="../../lib/anime.min.js"></script> -->
  <!-- <script src="../assets/js/anime/anime.1.3.js"></script> -->
  <!-- <script src="../assets/js/anime/anime.2.0.1.js"></script> -->
  <style>
    body {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
    }
    .item {
      width: 100px;
      height: 100px;
      margin-top: 20px;
      border-radius: 50%;
      background-color: currentColor;
      margin: 10px;
    }
    .box-shadow {
      background-color: transparent;
    }
    .clip-path {
      border-radius: 0;
    }
    .auto-property {
      background-image: radial-gradient(50% 100%, #FFF7A0 50%, #FF7A6D 100%);
      background-size: auto 100px;
    }
    .exponent-values {
      width: 300px;
      background-color: transparent;
      text-align: center;
    }
  </style>
</head>
<body>
  <div class="box-shadow item color-red"></div>
  <div class="unit-less item color-orange"></div>
  <div class="specified-unit item color-lightorange"></div>
  <div class="filter item color-yellow"></div>
  <div class="rgba item color-citrus"></div>
  <div class="calc item color-green"></div>
  <div class="zero-value item color-darkgreen"></div>
  <div class="round-value item color-turquoise"></div>
  <div class="clip-path item color-skyblue"></div>
  <svg class="item" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
    <g id="Page-1" fill="none" fill-rule="evenodd">
      <g id="svg" fill="#A7FF87">
        <rect id="Rectangle" width="50" height="50" x="25" y="25"/>
      </g>
    </g>
  </svg>
  <input class="item" type="range" value="0" min="0" max="100">
  <div class="basic-css color-lavender item"></div>
  <div class="auto-property color-purple item"></div>
  <input class="exponent-values color-pink item" value="-1.1234567890123456789e+30px"></input>
  <script type="module">
    
    import anime from "../../src/index.js";

    var boxShadow = anime({
      targets: '.box-shadow',
      boxShadow: [
        {value: ['10px -10px 10px 0 currentColor', '10px 10px 20px 0 currentColor']},
        {value: '-10px 10px 30px 0 currentColor'},
        {value: '-10px -10px 20px 0 currentColor'},
        {value: '10px -10px 10px 0 currentColor'}
      ],
      loop: true,
      easing: 'linear'
    });
    var unitLess = anime({
      targets: '.unit-less',
      borderRadius: 16,
      loop: true,
      direction: 'alternate',
      easing: 'linear'
    });
    var specifiedUnit = anime({
      targets: '.specified-unit',
      borderRadius: '1em',
      loop: true,
      direction: 'alternate',
      easing: 'linear'
    });
    var filter = anime({
      targets: '.filter',
      filter: ["blur(20px)", "blur(0px)"],
      loop: true,
      direction: 'alternate',
      easing: 'linear'
    });
    var rgba = anime({
      targets: '.rgba',
      backgroundColor: ['rgba(255,100,50,1)', 'rgba(255,100,50,.0)'],
      loop: true,
      direction: 'alternate',
      easing: 'linear'
    });
    var calc = anime({
      targets: '.calc',
      height: ['calc(25% - 100px)', 'calc(25% - 0px)'],
      loop: true,
      direction: 'alternate',
      easing: 'linear'
    });
    var zeroValue = anime({
      targets: '.zero-value',
      scale: 0,
      loop: true,
      direction: 'alternate',
      easing: 'linear'
    });
    var roundValue = anime({
      targets: '.round-value',
      translateY: 100,
      round: .1,
      loop: true,
      direction: 'alternate',
      easing: 'linear'
    });
    var clipPath = anime({
      targets: '.clip-path',
      clipPath: ['circle(0px at center)', 'circle(100px at center)'],
      loop: true,
      direction: 'alternate',
      easing: 'linear'
    });
    var svgTransform = anime({
      targets: 'rect',
      transform: 'translate(10 10) scale(2)',
      loop: true,
      direction: 'alternate',
      easing: 'linear'
    });
    var rangeValue = anime({
      targets: 'input[type="range"]',
      value: 100,
      loop: true,
      direction: 'alternate',
      easing: 'linear'
    });
    var basicCSS = anime({
      targets: '.basic-css',
      width: 50,
      height: 50,
      loop: true,
      direction: 'alternate',
      easing: 'linear'
    });
    var autoProperty = anime({
      targets: '.auto-property',
      backgroundSize: ['auto 10px', 'auto 400px'],
      margin: '20% 40px 10px 30px',
      loop: true,
      direction: 'alternate',
      easing: 'linear'
    });
    var exponentValues = anime({
      targets: '.exponent-values',
      value: '+1.0123456789012346e+21px',
      loop: true,
      direction: 'alternate',
      easing: 'linear'
    });
  </script>
</body>
</html>
