<template>
  <div class="simple-test">
    <h2>简单自适应测试</h2>
    
    <div class="device-info">
      <p>当前设备: {{ isMobile ? '移动端' : 'PC端' }}</p>
      <p>屏幕宽度: {{ screenWidth }}px</p>
    </div>

    <div class="button-test">
      <h3>按钮测试</h3>
      
      <!-- PC端按钮 -->
      <el-button v-if="!isMobile" type="primary" @click="showMessage('PC端按钮')">
        PC端按钮
      </el-button>
      
      <!-- 移动端按钮 -->
      <van-button v-else type="primary" @click="showMessage('移动端按钮')">
        移动端按钮
      </van-button>
    </div>

    <div class="layout-test">
      <h3>布局测试</h3>
      
      <!-- PC端表格 -->
      <el-table v-if="!isMobile" :data="tableData" style="width: 100%">
        <el-table-column prop="name" label="姓名" />
        <el-table-column prop="age" label="年龄" />
        <el-table-column prop="address" label="地址" />
      </el-table>
      
      <!-- 移动端列表 -->
      <van-list v-else>
        <van-cell 
          v-for="item in tableData" 
          :key="item.id"
          :title="item.name"
          :value="item.age"
          :label="item.address"
        />
      </van-list>
    </div>

    <div class="device-switch">
      <h3>设备切换测试</h3>
      <button @click="toggleDevice">
        切换到{{ isMobile ? 'PC端' : '移动端' }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import { useDevice } from '@/composables/useDevice';
import { useAppStore } from '@/store';
import { DeviceEnum } from '@/enums/DeviceEnum';

defineOptions({
  name: "SimpleTest",
});

const { isMobile } = useDevice();
const appStore = useAppStore();

// 响应式屏幕宽度
const screenWidth = ref(window.innerWidth);

// 测试数据
const tableData = ref([
  { id: 1, name: '张三', age: 25, address: '北京市朝阳区' },
  { id: 2, name: '李四', age: 30, address: '上海市浦东新区' },
  { id: 3, name: '王五', age: 28, address: '广州市天河区' }
]);

// 监听窗口大小变化
const updateScreenWidth = () => {
  screenWidth.value = window.innerWidth;
};

onMounted(() => {
  window.addEventListener('resize', updateScreenWidth);
});

onUnmounted(() => {
  window.removeEventListener('resize', updateScreenWidth);
});

// 显示消息
const showMessage = (message: string) => {
  if (isMobile.value) {
    console.log(`移动端消息: ${message}`);
    // 这里可以使用 Vant 的 Toast，暂时用 console.log
  } else {
    ElMessage.success(`PC端消息: ${message}`);
  }
};

// 切换设备
const toggleDevice = () => {
  const currentDevice = appStore.device;
  const newDevice = currentDevice === DeviceEnum.MOBILE ? DeviceEnum.DESKTOP : DeviceEnum.MOBILE;
  
  console.log(`切换设备: ${currentDevice} → ${newDevice}`);
  appStore.toggleDevice(newDevice);
  
  showMessage(`设备已切换到: ${newDevice}`);
};
</script>

<style lang="scss" scoped>
.simple-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;

  h2, h3 {
    color: var(--el-text-color-primary);
    margin-bottom: 15px;
  }

  .device-info {
    background: var(--el-fill-color-light);
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;

    p {
      margin: 5px 0;
      font-weight: 500;
    }
  }

  .button-test,
  .layout-test,
  .device-switch {
    margin-bottom: 30px;
    padding: 15px;
    border: 1px solid var(--el-border-color);
    border-radius: 8px;
  }

  .device-switch {
    text-align: center;

    button {
      padding: 10px 20px;
      background: var(--el-color-primary);
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;

      &:hover {
        background: var(--el-color-primary-light-3);
      }
    }
  }
}

// 移动端适配
@media screen and (max-width: 991px) {
  .simple-test {
    padding: 15px;

    .button-test,
    .layout-test,
    .device-switch {
      padding: 12px;
    }
  }
}
</style>
