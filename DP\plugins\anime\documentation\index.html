<!DOCTYPE html>
<html>
<head>
  <title>Documentation | anime.js</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">

  <meta property="og:title" content="anime.js">
  <meta property="og:url" content="https://animejs.com">
  <meta property="og:description" content="Javascript animation engine">
  <meta property="og:image" content="https://animejs.com/documentation/assets/img/social-media-image.png">

  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:site" content="@juliangarnier">
  <meta name="twitter:creator" content="@juliangarnier">
  <meta name="twitter:title" content="anime.js">
  <meta name="twitter:description" content="Javascript animation engine">
  <meta name="twitter:image" content="https://animejs.com/documentation/assets/img/social-media-image.png">

  <link rel="apple-touch-icon-precomposed" href="assets/img/apple-touch-icon.png">
  <link rel="icon" type="image/png" href="assets/img/favicon.png" >

  <link href="//cloud.typenetwork.com/projects/2160/fontface.css/" rel="stylesheet" type="text/css">

  <link href="assets/css/animejs.css" rel="stylesheet">
  <link href="assets/css/anime-theme.css" rel="stylesheet">
  <link href="assets/css/documentation.css" rel="stylesheet">

  <script src="../lib/anime.min.js"></script>
</head>
<body>
  <div class="content">
    <header class="header">
    <a class="logo" href="https://github.com/juliangarnier/anime/">
      <img class="anime-mini-logo" src="assets/img/anime-mini-logo.svg" />
      <span class="version-number"></span>
    </a>
    </header>
    <section class="pane sidebar">
      <nav class="navigation"></nav>
      <div class="sidebar-info">
        <a class="github-button" href="https://github.com/juliangarnier/anime/archive/master.zip" data-icon="octicon-cloud-download" aria-label="Download juliangarnier/anime on GitHub">Download</a>
        <a class="github-button" href="https://github.com/juliangarnier/anime" data-show-count="true" aria-label="Star juliangarnier/anime on GitHub">Star</a>
      </div>
    </section>
    <section class="pane demos">

<!-- TARGETS -->
<article id="targets" class="color-targets">
  <header>
    <h2 class="demos-title">Targets</h2>
  </header>

<div id="cssSelector" class="demo">
  <h3 class="demo-title">CSS Selector</h3>
  <div class="demo-description">
    <p>Can be any CSS selector.</p>
    <p class="bubble warning">Pseudo elements can't be targeted using JavaScript.</p>
    <table>
      <thead>
        <td>Type</td>
        <td>Default</td>
        <td>Example</td>
      </thead>
      <tr>
        <td>String</td>
        <td><code>null</code></td>
        <td><code>targets: '.item'</code></td>
      </tr>
    </table>
  </div>
  <div class="demo-content css-selector-demo">
    <div class="line">
      <div class="square shadow"></div>
      <div class="square el"></div>
    </div>
  </div>
  <script>var cssSelector = function() {
/*DEMO*/
anime({
  targets: '.css-selector-demo .el',
  translateX: 250
});
/*DEMO*/
}
  </script>

</div>

<div id="domNode" class="demo">
  <h3 class="demo-title">DOM Node / NodeList</h3>
  <div class="demo-description">
    <p>
      Can be any DOM Node or NodeList.
    </p>
    <table>
      <thead>
        <td>Type</td>
        <td>Default</td>
        <td>Example</td>
      </thead>
      <tr>
        <td>DOM Node</td>
        <td><code>null</code></td>
        <td><code>targets: el.querySelector('.item')</code></td>
      </tr>
      <tr>
        <td>NodeList</td>
        <td><code>null</code></td>
        <td><code>targets: el.querySelectorAll('.item')</code></td>
      </tr>
    </table>
  </div>
  <div class="demo-content dom-node-demo">
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
  </div>
  <script>var domNode = function() {
/*DEMO*/
var elements = document.querySelectorAll('.dom-node-demo .el');

anime({
  targets: elements,
  translateX: 270
});
/*DEMO*/
}
  </script>
</div>

<div id="JSobject" class="demo">
  <h3 class="demo-title">JavaScript Object</h3>
  <div class="demo-description">
    <p>
      A JavaScript Object with at least one property containing a numerical value.
    </p>
    <table>
      <thead>
        <td>Type</td>
        <td>Default</td>
        <td>Example</td>
      </thead>
      <tr>
        <td>Object</td>
        <td><code>null</code></td>
        <td><code>targets: myObjectProp</code></td>
      </tr>
    </table>
  </div>
  <div class="demo-content">
    <pre class="battery-log">{"charged":"0%","cycles":120}</pre>
  </div>
  <script>var JSobject = function() {
/*DEMO*/
var logEl = document.querySelector('.battery-log');

var battery = {
  charged: '0%',
  cycles: 120
}

anime({
  targets: battery,
  charged: '100%',
  cycles: 130,
  round: 1,
  easing: 'linear',
  update: function() {
    logEl.innerHTML = JSON.stringify(battery);
  }
});
/*DEMO*/
}
  </script>
<script>
</script>
</div>

<div id="array" class="demo">
  <h3 class="demo-title">Array</h3>
  <div class="demo-description">
    <p>
      An array containing multiple targets.
    </p>
    <p class="bubble info">
      Accepts mixed types. E.g. <code>['.el', domNode, jsObject]</code>
    </p>
    <table>
      <thead>
        <td>Type</td>
        <td>Default</td>
        <td>Example</td>
      </thead>
      <tr>
        <td>Array</td>
        <td><code>null</code></td>
        <td><code>targets: ['.item', el.getElementById('#thing')]</code></td>
      </tr>
    </table>
  </div>
  <div class="demo-content mixed-array-demo">
    <div class="line">
      <div class="square shadow"></div>
      <div class="square el el-01"></div>
    </div>
    <div class="line">
      <div class="square shadow"></div>
      <div class="square el el-02"></div>
    </div>
    <div class="line">
      <div class="square shadow"></div>
      <div class="square el el-03"></div>
    </div>
  </div>
  <script>var array = function() {
/*DEMO*/
var el = document.querySelector('.mixed-array-demo .el-01');

anime({
  targets: [el, '.mixed-array-demo .el-02', '.mixed-array-demo .el-03'],
  translateX: 250
});
/*DEMO*/
}
  </script>

</div>

</article>
<!-- -->

<!-- ANIMATABLE PROPERTIES -->
<article id="properties" class="color-properties">
<header>
  <h2 class="demos-title">Properties</h2>
</header>

<div id="cssProperties" class="demo">
  <h3 class="demo-title">CSS Properties</h3>
  <div class="demo-description">
    <p>
      Any CSS properties can be animated.
    </p>
    <p class="bubble warning">
      Most CSS properties will cause layout changes or repaint, and will result in choppy animation.
      Prioritize opacity and CSS transforms as much as possible.
    </p>
    <p>More details about accepted values in the <a class="color-values" href="#unitlessValue">values</a> section.</p>
    <table>
      <thead>
        <td>Example</td>
        <td>value</td>
      </thead>
      <tr>
        <td><code>opacity</code></td>
        <td><code>.5</code></td>
      </tr>
      <tr>
        <td><code>left</code></td>
        <td><code>'100px'</code></td>
      </tr>
    </table>
  </div>
  <div class="demo-content css-prop-demo">
    <div class="large square shadow"></div>
    <div class="large square el"></div>
  </div>
  <script>var cssProperties = function() {
/*DEMO*/
anime({
  targets: '.css-prop-demo .el',
  left: '240px',
  backgroundColor: '#FFF',
  borderRadius: ['0%', '50%'],
  easing: 'easeInOutQuad'
});
/*DEMO*/
}
  </script>
</div>

<div id="CSStransforms" class="demo">
  <h3 class="demo-title">CSS Transforms</h3>
  <div class="demo-description">
    <p>
      Animate CSS transforms properties individually.
    </p>
    <p class="bubble info">
      It's possible to specify different timing for each transforms properties, more details in the <a class="color-prop-params" href="#specificPropParameters">specific property parameters</a> section.
    </p>
    <p>More details about accepted values in the <a class="color-values" href="#unitlessValue">values</a> section.</p>
    <table>
      <thead>
        <td>Valid properties</td>
        <td>Default unit</td>
      </thead>
      <tr><td><code>'translateX'</code></td><td><code>'px'</code></td></tr>
      <tr><td><code>'translateY'</code></td><td><code>'px'</code></td></tr>
      <tr><td><code>'translateZ'</code></td><td><code>'px'</code></td></tr>
      <tr><td><code>'rotate'</code></td><td><code>'deg'</code></td></tr>
      <tr><td><code>'rotateX'</code></td><td><code>'deg'</code></td></tr>
      <tr><td><code>'rotateY'</code></td><td><code>'deg'</code></td></tr>
      <tr><td><code>'rotateZ'</code></td><td><code>'deg'</code></td></tr>
      <tr><td><code>'scale'</code></td><td><code>—</code></td></tr>
      <tr><td><code>'scaleX'</code></td><td><code>—</code></td></tr>
      <tr><td><code>'scaleY'</code></td><td><code>—</code></td></tr>
      <tr><td><code>'scaleZ'</code></td><td><code>—</code></td></tr>
      <tr><td><code>'skew'</code></td><td><code>'deg'</code></td></tr>
      <tr><td><code>'skewX'</code></td><td><code>'deg'</code></td></tr>
      <tr><td><code>'skewY'</code></td><td><code>'deg'</code></td></tr>
      <tr><td><code>'perspective'</code></td><td><code>'px'</code></td></tr>
    </table>
  </div>
  <div class="demo-content css-transforms-demo">
    <div class="square shadow"></div>
    <div class="square el"></div>
  </div>
  <script>var CSStransforms = function() {
/*DEMO*/
anime({
  targets: '.css-transforms-demo .el',
  translateX: 250,
  scale: 2,
  rotate: '1turn'
});
/*DEMO*/
}
  </script>
</div>

<div id="JSobjProp" class="demo">
  <h3 class="demo-title">Object properties</h3>
  <div class="demo-description">
    <p>
      Any Object property containing a numerical value can be animated.<br>
      More details about accepted values in the <a class="color-values" href="#unitlessValue">values</a> section.
    </p>
    <table>
      <thead>
        <td>Example</td>
        <td>value</td>
      </thead>
      <tr>
        <td><code>prop1</code></td>
        <td><code>50</code></td>
      </tr>
      <tr>
        <td><code>'prop2'</code></td>
        <td><code>'100%'</code></td>
      </tr>
    </table>
  </div>
  <div class="demo-content">
    <pre class="js-object-log">{"prop1":0,"prop2":"0%"}</pre>
  </div>
  <script>var JSobjProp = function() {
/*DEMO*/
var objPropLogEl = document.querySelector('.js-object-log');

var myObject = {
  prop1: 0,
  prop2: '0%'
}

anime({
  targets: myObject,
  prop1: 50,
  prop2: '100%',
  easing: 'linear',
  round: 1,
  update: function() {
    objPropLogEl.innerHTML = JSON.stringify(myObject);
  }
});
/*DEMO*/
}
  </script>
</div>

<div id="domAttr" class="demo">
  <h3 class="demo-title">DOM Attributes</h3>
  <div class="demo-description">
    <p>
      Any DOM Attributes containing a numerical value can be animated.<br>
      More details about accepted values in the <a class="color-values" href="#unitlessValue">values</a> section.
    </p>
    <table>
      <thead>
        <td>Example</td>
        <td>value</td>
      </thead>
      <tr>
        <td><code>value</code></td>
        <td><code>1000</code></td>
      </tr>
      <tr>
        <td><code>volume</code></td>
        <td><code>0</code></td>
      </tr>
      <tr>
        <td><code>data-custom</code></td>
        <td><code>'3 dogs'</code></td>
      </tr>
    </table>
  </div>
  <div class="demo-content dom-attribute-demo">
    <input class="el text-output" value="0"></input>
  </div>
  <script>var domAttr = function() {
/*DEMO*/
anime({
  targets: '.dom-attribute-demo input',
  value: [0, 1000],
  round: 1,
  easing: 'easeInOutExpo'
});
/*DEMO*/
}
  </script>
</div>

<div id="svgAttr" class="demo">
  <h3 class="demo-title">SVG Attributes</h3>
  <div class="demo-description">
    <p>
      Like any other <a class="color-properties" href="#domAttr">DOM attributes</a>, all SVG attributes containing at least one numerical value can be animated.<br>
      More details about accepted values in the <a class="color-values" href="#unitlessValue">values</a> section and SVG animation in the <a class="color-svg" href="#motionPath">SVG</a> section.
    </p>
    <table>
      <thead>
        <td>Example</td>
        <td>value</td>
      </thead>
      <tr>
        <td><code>points</code></td>
        <td><code>'64 68.64 8.574 100 63.446 67.68 64 4 64.554 67.68 119.426 100'</code></td>
      </tr>
      <tr>
        <td><code>scale</code></td>
        <td><code>1</code></td>
      </tr>
      <tr>
        <td><code>baseFrequency</code></td>
        <td><code>0</code></td>
      </tr>
    </table>
  </div>
  <div class="demo-content align-center svg-attributes-demo">
<svg width="128" height="128" viewBox="0 0 128 128">
  <filter id="displacementFilter">
    <feTurbulence type="turbulence" baseFrequency=".05" numOctaves="2" result="turbulence"/>
    <feDisplacementMap in2="turbulence" in="SourceGraphic" scale="15" xChannelSelector="R" yChannelSelector="G"/>
  </filter>
  <polygon points="64 68.64 8.574 100 63.446 67.68 64 4 64.554 67.68 119.426 100" style="filter: url(#displacementFilter)" fill="currentColor"/>
</svg>
  </div>
  <script>var svgAttr = function() {
  // Needed to reset demo
  var polyEl = document.querySelector('.svg-attributes-demo polygon');
  var feTurbulenceEl = document.querySelector('feTurbulence');
  var feDisplacementMap = document.querySelector('feDisplacementMap');
  polyEl.setAttribute('points', '64 68.64 8.574 100 63.446 67.68 64 4 64.554 67.68 119.426 100');
  feTurbulenceEl.setAttribute('baseFrequency', '.05');
  feDisplacementMap.setAttribute('scale', '15');
/*DEMO*/
anime({
  targets: ['.svg-attributes-demo polygon', 'feTurbulence', 'feDisplacementMap'],
  points: '64 128 8.574 96 8.574 32 64 0 119.426 32 119.426 96',
  baseFrequency: 0,
  scale: 1,
  loop: true,
  direction: 'alternate',
  easing: 'easeInOutExpo'
});
/*DEMO*/
}
  </script>

</div>

</article>
<!-- -->

<!-- PROPERTY PARAMETERS -->
<article id="propertyParameters" class="color-prop-params">
  <header>
    <h2 class="demos-title">Property parameters</h2>
  </header>

<div id="duration" class="demo">
  <h3 class="demo-title">Duration</h3>
  <div class="demo-description">
    <p>
      Defines the duration in milliseconds of the animation.
    </p>
    <table>
      <thead>
        <td>Type</td>
        <td>Default</td>
        <td>Example</td>
      </thead>
      <tr>
        <td>Number</td>
        <td><code>1000</code></td>
        <td><code>3000</code></td>
      </tr>
      <tr>
        <td>anime.stagger</td>
        <td>See <a class="color-staggering" href="#staggeringBasics">staggering</a> section</td>
        <td><code>anime.stagger(150)</code></td>
      </tr>
      <tr>
        <td>Function</td>
        <td>See <a class="color-prop-params" href="#functionBasedParameters">function based parameters</a> section</td>
        <td><code>(el, i) => i * 150</code></td>
      </tr>
    </table>
  </div>
  <div class="demo-content duration-demo">
    <div class="label">3000 ms</div>
    <div class="square shadow"></div>
    <div class="square el"></div>
  </div>
  <script>var duration = function() {
/*DEMO*/
anime({
  targets: '.duration-demo .el',
  translateX: 250,
  duration: 3000
});
/*DEMO*/
}
  </script>
</div>

<div id="delay" class="demo">
  <h3 class="demo-title">Delay</h3>
  <div class="demo-description">
    <p>
      Defines the delay in milliseconds of the animation.
    </p>
    <table>
      <thead>
        <td>Type</td>
        <td>Default</td>
        <td>Example</td>
      </thead>
      <tr>
        <td>Number</td>
        <td><code>0</code></td>
        <td><code>1000</code></td>
      </tr>
      <tr>
        <td>anime.stagger</td>
        <td>See <a class="color-staggering" href="#staggeringBasics">staggering</a> section</td>
        <td><code>anime.stagger(150)</code></td>
      </tr>
      <tr>
        <td>Function</td>
        <td>See <a class="color-prop-params" href="#functionBasedParameters">function based parameters</a> section</td>
        <td><code>(el, i) => i * 150</code></td>
      </tr>
    </table>
  </div>
  <div class="demo-content delay-demo">
    <div class="label">1000 ms</div>
    <div class="square shadow"></div>
    <div class="square el"></div>
  </div>
  <script>var delay = function() {
/*DEMO*/
anime({
  targets: '.delay-demo .el',
  translateX: 250,
  delay: 1000
});
/*DEMO*/
}
  </script>
</div>

<div id="endDelay" class="demo">
  <h3 class="demo-title">end delay</h3>
  <div class="demo-description">
    <p>
      Adds some extra time in milliseconds at the end of the animation.
    </p>
    <table>
      <thead>
        <td>Type</td>
        <td>Default</td>
        <td>Example</td>
      </thead>
      <tr>
        <td>Number</td>
        <td><code>0</code></td>
        <td><code>1000</code></td>
      </tr>
      <tr>
        <td>anime.stagger</td>
        <td>See <a class="color-staggering" href="#staggeringBasics">staggering</a> section</td>
        <td><code>anime.stagger(150)</code></td>
      </tr>
      <tr>
        <td>Function</td>
        <td>See <a class="color-prop-params" href="#functionBasedParameters">function based parameters</a> section</td>
        <td><code>(el, i) => i * 150</code></td>
      </tr>
    </table>
  </div>
  <div class="demo-content end-delay-demo">
    <div class="label">1000 ms</div>
    <div class="square shadow"></div>
    <div class="square el"></div>
  </div>
  <script>var endDelay = function() {
/*DEMO*/
anime({
  targets: '.end-delay-demo .el',
  translateX: 250,
  endDelay: 1000,
  direction: 'alternate'
});
/*DEMO*/
}
  </script>

</div>

<div id="easing" class="demo">
  <h3 class="demo-title">Easing</h3>
  <div class="demo-description">
    <p>
      Defines the timing function of the animation.
    </p>
    <p class="bubble info">
      Check out the <a class="color-easings" href="#linearEasing">easings</a> section for a complete list of available easing and parameters.
    </p>
    <table>
      <thead>
        <td>Type</td>
        <td>Default</td>
        <td>Example</td>
      </thead>
      <tr>
        <td>String</td>
        <td><code>'easeOutElastic(1, .5)'</code></td>
        <td><code>easing: 'easeInOutQuad'</code></td>
      </tr>
    </table>
  </div>
  <div class="demo-content easing-demo">
    <div class="label">easeInOutExpo</div>
    <div class="square shadow"></div>
    <div class="square el"></div>
  </div>
  <script>var easing = function() {
/*DEMO*/
anime({
  targets: '.easing-demo .el',
  translateX: 250,
  easing: 'easeInOutExpo'
});
/*DEMO*/
}
  </script>

</div>

<div id="round" class="demo">
  <h3 class="demo-title">Round</h3>
  <div class="demo-description">
    <p>
      Rounds up the value to x decimals.
    </p>
    <table>
      <thead>
        <td>Type</td>
        <td>Default</td>
        <td>Example</td>
      </thead>
      <tr>
        <td>Number</td>
        <td><code>0</code></td>
        <td><code>round: 10</code></td>
      </tr>
    </table>
  </div>
  <div class="demo-content">
    <pre class="round-log">0</pre>
  </div>
  <script>var round = function() {
/*DEMO*/
var roundLogEl = document.querySelector('.round-log');

anime({
  targets: roundLogEl,
  innerHTML: [0, 10000],
  easing: 'linear',
  round: 10 // Will round the animated value to 1 decimal
});
/*DEMO*/
}
  </script>
</div>

<div id="specificPropParameters" class="demo">
  <h3 class="demo-title">Specific property parameters</h3>
  <div class="demo-description">
    <p>
      Defines specific parameters to each property of the animation using an Object as value.<br>
      Other properties that aren't specified in the Object are inheritted from the main animation.
    </p>
    <table>
      <thead>
        <td>Type</td>
        <td>Example</td>
      </thead>
      <tr>
        <td>Object</td>
<td><code>rotate: {
  value: 360,
  duration: 1800,
  easing: 'easeInOutSine'
}</code></td>
      </tr>
    </table>
  </div>
  <div class="demo-content specific-prop-params-demo">
    <div class="square shadow"></div>
    <div class="square el"></div>
  </div>
  <script>var specificPropParameters = function() {
/*DEMO*/
anime({
  targets: '.specific-prop-params-demo .el',
  translateX: {
    value: 250,
    duration: 800
  },
  rotate: {
    value: 360,
    duration: 1800,
    easing: 'easeInOutSine'
  },
  scale: {
    value: 2,
    duration: 1600,
    delay: 800,
    easing: 'easeInOutQuart'
  },
  delay: 250 // All properties except 'scale' inherit 250ms delay
});
/*DEMO*/
}
  </script>

</div>

<div id="functionBasedParameters" class="demo">
  <h3 class="demo-title">Function based parameters</h3>
  <div class="demo-description">
    <p>
      Get different values for every target and property of the animation.
    </p>
    <p>
      The function accepts 3 arguments:
    </p>
    <table>
      <thead>
        <td>Arguments</td>
        <td>Infos</td>
      </thead>
      <tr>
        <td><code>target</code></td>
        <td>The curent animated targeted element</td>
      </tr>
      <tr>
        <td><code>index</code></td>
        <td>The index of the animated targeted element</td>
      </tr>
      <tr>
        <td><code>targetsLength</code></td>
        <td>The total number of animated targets</td>
      </tr>
    </table>
    <p class="bubble info">
      See <a class="color-staggering" href="#staggeringBasics">staggering</a> section for easier values manipulation.
    </p>
  </div>
  <div class="demo-content function-based-params-demo">
    <div class="line">
      <div class="small label">delay = 0 * 100</div>
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="small label">delay = 1 * 100</div>
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="small label">delay = 2 * 100</div>
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
  </div>
  <script>var functionBasedParameters = function() {
/*DEMO*/
anime({
  targets: '.function-based-params-demo .el',
  translateX: 270,
  direction: 'alternate',
  loop: true,
  delay: function(el, i, l) {
    return i * 100;
  },
  endDelay: function(el, i, l) {
    return (l - i) * 100;
  }
});
/*DEMO*/
}
  </script>

</div>

</article>
<!-- -->

<!-- ANIMATION PARAMETERS -->
<article id="animationParameters" class="color-anim-params">
  <header>
    <h2 class="demos-title">Animation parameters</h2>
  </header>

<div id="direction" class="demo">
  <h3 class="demo-title">Direction</h3>
  <div class="demo-description">
    <p>
      Defines the direction of the animation.
    </p>
    <table>
      <thead>
        <td>Accepts</td>
        <td>Infos</td>
      </thead>
      <tr>
        <td><code>'normal'</code></td>
        <td>Animation progress goes from 0 to 100%</td>
      </tr>
      <tr>
        <td><code>'reverse'</code></td>
        <td>Animation progress goes from 100% to 0%</td>
      </tr>
      <tr>
        <td><code>'alternate'</code></td>
        <td>Animation progress goes from 0% to 100% then goes back to 0%</td>
      </tr>
    </table>
  </div>
  <div class="demo-content">
    <div class="line">
      <div class="label">normal</div>
      <div class="square shadow"></div>
      <div class="square el dir-normal"></div>
    </div>
    <div class="line">
      <div class="label">reverse</div>
      <div class="square shadow"></div>
      <div class="square el dir-reverse"></div>
    </div>
    <div class="line">
      <div class="label">alternate</div>
      <div class="square shadow"></div>
      <div class="square el dir-alternate"></div>
    </div>
  </div>
  <script>var direction = function() {
/*DEMO*/
anime({
  targets: '.dir-normal',
  translateX: 250,
  easing: 'easeInOutSine'
});

anime({
  targets: '.dir-reverse',
  translateX: 250,
  direction: 'reverse',
  easing: 'easeInOutSine'
});

anime({
  targets: '.dir-alternate',
  translateX: 250,
  direction: 'alternate',
  easing: 'easeInOutSine'
});
/*DEMO*/
}
  </script>

</div>

<div id="loop" class="demo">
  <h3 class="demo-title">Loop</h3>
  <div class="demo-description">
    <p>
      Defines the number of iterations of your animation.
    </p>
    <table>
      <thead>
        <td>Accepts</td>
        <td>Infos</td>
      </thead>
      <tr>
        <td><code>Number</code></td>
        <td>The number of iterations</td>
      </tr>
      <tr>
        <td><code>true</code></td>
        <td>Loop indefinitely</td>
      </tr>
    </table>
  </div>
  <div class="demo-content">
    <div class="line">
      <div class="small label">normal 3 times</div>
      <div class="small square shadow"></div>
      <div class="small square el loop"></div>
    </div>
    <div class="line">
      <div class="small label">reverse 3 times</div>
      <div class="small square shadow"></div>
      <div class="small square el loop-reverse"></div>
    </div>
    <div class="line">
      <div class="small label">alternate 3 times</div>
      <div class="small square shadow"></div>
      <div class="small square el loop-alternate"></div>
    </div>
    <div class="line">
      <div class="small label">normal inifinite</div>
      <div class="small square shadow"></div>
      <div class="small square el loop-infinity"></div>
    </div>
    <div class="line">
      <div class="small label">inifinite reverse</div>
      <div class="small square shadow"></div>
      <div class="small square el loop-reverse-infinity"></div>
    </div>
    <div class="line">
      <div class="small label">inifinite alternate</div>
      <div class="small square shadow"></div>
      <div class="small square el loop-alternate-infinity"></div>
    </div>
  </div>
  <script>var loop = function() {
/*DEMO*/
anime({
  targets: '.loop',
  translateX: 270,
  loop: 3,
  easing: 'easeInOutSine'
});

anime({
  targets: '.loop-infinity',
  translateX: 270,
  loop: true,
  easing: 'easeInOutSine'
});

anime({
  targets: '.loop-reverse',
  translateX: 270,
  loop: 3,
  direction: 'reverse',
  easing: 'easeInOutSine'
});

anime({
  targets: '.loop-reverse-infinity',
  translateX: 270,
  direction: 'reverse',
  loop: true,
  easing: 'easeInOutSine'
});

anime({
  targets: '.loop-alternate',
  translateX: 270,
  loop: 3,
  direction: 'alternate',
  easing: 'easeInOutSine'
});

anime({
  targets: '.loop-alternate-infinity',
  translateX: 270,
  direction: 'alternate',
  loop: true,
  easing: 'easeInOutSine'
});
/*DEMO*/
}
  </script>
</div>

<div id="autoplay" class="demo">
  <h3 class="demo-title">Autoplay</h3>
  <div class="demo-description">
    <p>
      Defines if the animation should automatically starts or not.
    </p>
    <table>
      <thead>
        <td>Accepts</td>
        <td>Infos</td>
      </thead>
      <tr>
        <td><code>true</code></td>
        <td>Automatically starts the animation</td>
      </tr>
      <tr>
        <td><code>false</code></td>
        <td>Animation is paused by default</td>
      </tr>
    </table>
  </div>
  <div class="demo-content">
    <div class="line">
      <div class="label">autoplay: true</div>
      <div class="square shadow"></div>
      <div class="square el autoplay-true"></div>
    </div>
    <div class="line">
      <div class="label">autoplay: false</div>
      <div class="square shadow"></div>
      <div class="square el autoplay-false"></div>
    </div>
  </div>
  <script>var autoplay = function() {
/*DEMO*/
anime({
  targets: '.autoplay-true',
  translateX: 250,
  autoplay: true,
  easing: 'easeInOutSine'
});

anime({
  targets: '.autoplay-false',
  translateX: 250,
  autoplay: false,
  easing: 'easeInOutSine'
});
/*DEMO*/
}
  </script>
</div>

</article>
<!-- -->

<!-- VALUES -->
<article id="values" class="color-values">
<header>
  <h2 class="demos-title">Values</h2>
</header>

<div id="unitlessValue" class="demo">
  <h3 class="demo-title">Unitless</h3>
  <div class="demo-description">
    <p>
      If the original value has a unit, it will be automatically added to the animated value.
    </p>
    <table>
      <thead>
        <td>Type</td>
        <td>Example</td>
      </thead>
      <tr>
        <td>Number</td>
        <td><code>translateX: 250</code></td>
      </tr>
    </table>
  </div>
  <div class="demo-content unitless-values-demo">
    <div class="circle shadow"></div>
    <div class="circle el"></div>
  </div>
  <script>var unitlessValue = function() {
/*DEMO*/
anime({
  targets: '.unitless-values-demo .el',
  translateX: 250, // -> '250px'
  rotate: 540 // -> '540deg'
});
/*DEMO*/
}
  </script>

</div>

<div id="specificUnitValue" class="demo">
  <h3 class="demo-title">Specific unit</h3>
  <div class="demo-description">
    <p>
      Forces the animation to use a certain unit and will automatically convert the initial target value.
    </p>
    <p class="bubble warning">
      Conversion accuracy can vary depending of the unit used.<br>
      You can also define the initial value of the animation directly using an array, see the <a href="#fromToValues" class="color-values">from to values</a> section.
    </p>
    <table>
      <thead>
        <td>Type</td>
        <td>Example</td>
      </thead>
      <tr>
        <td>String</td>
        <td><code>width: '100%'</code></td>
      </tr>
    </table>
  </div>
  <div class="demo-content specific-unit-values-demo">
    <div class="square shadow"></div>
    <div class="square el"></div>
  </div>
  <script>var specificUnitValue = function() {
/*DEMO*/
anime({
  targets: '.specific-unit-values-demo .el',
  width: '100%', // -> from '28px' to '100%',
  easing: 'easeInOutQuad',
  direction: 'alternate',
  loop: true
});
/*DEMO*/
}
  </script>

</div>

<div id="relativeValues" class="demo">
  <h3 class="demo-title">Relative</h3>
  <div class="demo-description">
    <p>
      Adds, substracts or multiplies the original value.
    </p>
    <table>
      <thead>
        <td>Accepts</td>
        <td>Effect</td>
        <td>Example</td>
      </thead>
      <tr>
        <td><code>'+='</code></td>
        <td>Add</td>
        <td><code>'+=100'</code></td>
      </tr>
      <tr>
        <td><code>'-='</code></td>
        <td>Substract</td>
        <td><code>'-=2turn'</code></td>
      </tr>
      <tr>
        <td><code>'*='</code></td>
        <td>Multiply</td>
        <td><code>'*=10'</code></td>
      </tr>
    </table>
  </div>
  <div class="demo-content">
    <div class="circle shadow"></div>
    <div class="circle el relative-values"></div>
  </div>
  <script>var relativeValues = function() {
/*DEMO*/
var relativeEl = document.querySelector('.el.relative-values');
relativeEl.style.transform = 'translateX(100px)';

anime({
  targets: '.el.relative-values',
  translateX: {
    value: '*=2.5', // 100px * 2.5 = '250px'
    duration: 1000
  },
  width: {
    value: '-=20px', // 28 - 20 = '8px'
    duration: 1800,
    easing: 'easeInOutSine'
  },
  rotate: {
    value: '+=2turn', // 0 + 2 = '2turn'
    duration: 1800,
    easing: 'easeInOutSine'
  },
  direction: 'alternate'
});
/*DEMO*/
}
  </script>

</div>

<div id="colors" class="demo">
  <h3 class="demo-title">Colors</h3>
  <div class="demo-description">
    <p>
      <i>anime.js</i> accepts and converts Hexadecimal, RGB, RGBA, HSL, and HSLA color values.
    </p>
    <p class="bubble warning">
      CSS color codes ( e.g. : <code>'red', 'yellow', 'aqua'</code> ) are not supported.
    </p>
    <table>
      <thead>
        <td>Accepts</td>
        <td>Example</td>
      </thead>
      <tr>
        <td>Hexadecimal</td>
        <td><code>'#FFF'</code> or <code>'#FFFFFF'</code></td>
      </tr>
      <tr>
        <td>RGB</td>
        <td><code>'rgb(255, 255, 255)'</code></td>
      </tr>
      <tr>
        <td>RGBA</td>
        <td><code>'rgba(255, 255, 255, .2)'</code></td>
      </tr>
      <tr>
        <td>HSL</td>
        <td><code>'hsl(0, 100%, 100%)'</code></td>
      </tr>
      <tr>
        <td>HSLA</td>
        <td><code>'hsla(0, 100%, 100%, .2)'</code></td>
      </tr>
    </table>
  </div>
  <div class="demo-content colors-demo">
    <div class="line">
      <div class="small label">HEX</div>
      <div class="small circle shadow"></div>
      <div class="small circle el color-hex"></div>
    </div>
    <div class="line">
      <div class="small label">RGB</div>
      <div class="small circle shadow"></div>
      <div class="small circle el color-rgb"></div>
    </div>
    <div class="line">
      <div class="small label">HSL</div>
      <div class="small circle shadow"></div>
      <div class="small circle el color-hsl"></div>
    </div>
    <div class="line">
      <div class="small label">RGBA</div>
      <div class="small circle shadow"></div>
      <div class="small circle el color-rgba"></div>
    </div>
    <div class="line">
      <div class="small label">HSLA</div>
      <div class="small circle shadow"></div>
      <div class="small circle el color-hsla"></div>
    </div>
  </div>
  <script>var colors = function() {
/*DEMO*/
var colorsExamples = anime.timeline({
  endDelay: 1000,
  easing: 'easeInOutQuad',
  direction: 'alternate',
  loop: true
})
.add({ targets: '.color-hex',  background: '#FFF' }, 0)
.add({ targets: '.color-rgb',  background: 'rgb(255,255,255)' }, 0)
.add({ targets: '.color-hsl',  background: 'hsl(0, 100%, 100%)' }, 0)
.add({ targets: '.color-rgba', background: 'rgba(255,255,255, .2)' }, 0)
.add({ targets: '.color-hsla', background: 'hsla(0, 100%, 100%, .2)' }, 0)
.add({ targets: '.colors-demo .el', translateX: 270 }, 0);
/*DEMO*/
}
  </script>

</div>

<div id="fromToValues" class="demo">
  <h3 class="demo-title">From To</h3>
  <div class="demo-description">
    <p>
      Forces the animation to start at a specified value.
    </p>
    <table>
      <thead>
        <td>Type</td>
        <td>Example</td>
      </thead>
      <tr>
        <td><code>Array</code></td>
        <td><code>['50%', '100%']</code></td>
      </tr>
    </table>
  </div>
  <div class="demo-content">
    <div class="circle shadow"></div>
    <div class="circle el from-to-values"></div>
  </div>
  <script>var fromToValues = function() {
/*DEMO*/
anime({
  targets: '.el.from-to-values',
  translateX: [100, 250], // from 100 to 250
  delay: 500,
  direction: 'alternate',
  loop: true
});
/*DEMO*/
}
  </script>

</div>

<div id="functionBasedPropVal" class="demo">
  <h3 class="demo-title">Function based values</h3>
  <div class="demo-description">
    <p>
      Get different values for every target and property of the animation.
    </p>
    <p>
      The function accepts 3 arguments:
    </p>
    <table>
      <thead>
        <td>Arguments</td>
        <td>Infos</td>
      </thead>
      <tr>
        <td><code>target</code></td>
        <td>The curently animated targeted element</td>
      </tr>
      <tr>
        <td><code>index</code></td>
        <td>The index of the animated targeted element</td>
      </tr>
      <tr>
        <td><code>targetsLength</code></td>
        <td>The total number of animated targets</td>
      </tr>
    </table>
  </div>
  <div class="demo-content function-based-values-demo">
    <div class="line">
      <div class="small circle shadow"></div>
      <div data-x="170" class="small circle el"></div>
    </div>
    <div class="line">
      <div class="small circle shadow"></div>
      <div data-x="80" class="small circle el"></div>
    </div>
    <div class="line">
      <div class="small circle shadow"></div>
      <div data-x="270" class="small circle el"></div>
    </div>
  </div>
  <script>var functionBasedPropVal = function() {
/*DEMO*/
anime({
  targets: '.function-based-values-demo .el',
  translateX: function(el) {
    return el.getAttribute('data-x');
  },
  translateY: function(el, i) {
    return 50 + (-50 * i);
  },
  scale: function(el, i, l) {
    return (l - i) + .25;
  },
  rotate: function() { return anime.random(-360, 360); },
  borderRadius: function() { return ['50%', anime.random(10, 35) + '%']; },
  duration: function() { return anime.random(1200, 1800); },
  delay: function() { return anime.random(0, 400); },
  direction: 'alternate',
  loop: true
});
/*DEMO*/
}
  </script>

</div>

</article>
<!-- -->

<!-- KEYFRAMES -->
<article id="keyframes" class="color-keyframes">
<header>
  <h2 class="demos-title">Keyframes</h2>
</header>

<div id="animationKeyframes" class="demo">
  <h3 class="demo-title">Animation keyframes</h3>
  <div class="demo-description">
    <p>
      Animation keyframes are defined using an Array, within the <code>keyframes</code> property.
    </p>
    <p class="bubble info">
      If there is no duration specified inside the keyframes, each keyframe duration will be equal to the animation's total duration divided by the number of keyframes.
    </p>
    <table>
      <thead>
        <td>Type</td>
        <td>Example</td>
      </thead>
      <tr>
        <td><code>Array</code></td>
        <td><code>[
  {value: 100, easing: 'easeOutExpo'},
  {value: 200, delay: 500},
  {value: 300, duration: 1000}
]</code></td>
      </tr>
    </table>
  </div>
  <div class="demo-content animation-keyframes-demo">
    <div class="circle shadow"></div>
    <div class="circle el keyframes"></div>
  </div>
  <script>var animationKeyframes = function() {
/*DEMO*/
anime({
  targets: '.animation-keyframes-demo .el',
  keyframes: [
    {translateY: -40},
    {translateX: 250},
    {translateY: 40},
    {translateX: 0},
    {translateY: 0}
  ],
  duration: 4000,
  easing: 'easeOutElastic(1, .8)',
  loop: true
});
/*DEMO*/
}
  </script>
</div>

<div id="propertyKeyframes" class="demo">
  <h3 class="demo-title">Property keyframes</h3>
  <div class="demo-description">
    <p>
      Similar to <a class="color-keyframes" href="#animationKeyframes">animation keyframes</a>, property keyframes are defined using an Array of property Object. Property keyframes allow overlapping animations since each property have its own keyframes array.
    </p>
    <p class="bubble info">
      If there is no duration specified inside the keyframes, each keyframe duration will be equal to the animation's total duration divided by the number of keyframes.
    </p>
    <table>
      <thead>
        <td>Type</td>
        <td>Example</td>
      </thead>
      <tr>
        <td><code>Array</code></td>
        <td><code>[
  {value: 100, easing: 'easeOutExpo'},
  {value: 200, delay: 500},
  {value: 300, duration: 1000}
]</code></td>
      </tr>
    </table>
  </div>
  <div class="demo-content property-keyframes-demo">
    <div class="circle shadow"></div>
    <div class="circle el keyframes"></div>
  </div>
  <script>var propertyKeyframes = function() {
/*DEMO*/
anime({
  targets: '.property-keyframes-demo .el',
  translateX: [
    { value: 250, duration: 1000, delay: 500 },
    { value: 0, duration: 1000, delay: 500 }
  ],
  translateY: [
    { value: -40, duration: 500 },
    { value: 40, duration: 500, delay: 1000 },
    { value: 0, duration: 500, delay: 1000 }
  ],
  scaleX: [
    { value: 4, duration: 100, delay: 500, easing: 'easeOutExpo' },
    { value: 1, duration: 900 },
    { value: 4, duration: 100, delay: 500, easing: 'easeOutExpo' },
    { value: 1, duration: 900 }
  ],
  scaleY: [
    { value: [1.75, 1], duration: 500 },
    { value: 2, duration: 50, delay: 1000, easing: 'easeOutExpo' },
    { value: 1, duration: 450 },
    { value: 1.75, duration: 50, delay: 1000, easing: 'easeOutExpo' },
    { value: 1, duration: 450 }
  ],
  easing: 'easeOutElastic(1, .8)',
  loop: true
});
/*DEMO*/
}
  </script>
</div>

</article>
<!-- -->

<!-- STAGGERING -->
<article id="staggering" class="color-staggering">
<header>
  <h2 class="demos-title">Staggering</h2>
</header>

<div id="staggeringBasics" class="demo">
  <h3 class="demo-title">Staggering basics</h3>
  <div class="demo-description">
    <p>
      Staggering allows you to animate multiple elements with <a href="https://en.wikipedia.org/wiki/Follow_through_and_overlapping_action">follow through and overlapping action</a>.
    </p>
    <pre><code>anime.stagger(value, options)</code></pre>
    <table>
      <thead>
        <td>Arguments</td>
        <td>Type</td>
        <td>Info</td>
        <td>Required</td>
      </thead>
      <tr>
        <td>Value</td>
        <td><code>Number</code>, <code>String</code>, <code>Array</code></td>
        <td>The manipulated value(s)</td>
        <td>Yes</td>
      </tr>
      <tr>
        <td>Options</td>
        <td><code>Object</code></td>
        <td>Stagger parameters</td>
        <td>No</td>
      </tr>
    </table>
  </div>
  <div class="demo-content basic-staggering-demo">
    <div class="line">
      <div class="label">delay = (100 * 0) ms</div>
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="label">delay = (100 * 1) ms</div>
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="label">delay = (100 * 2) ms</div>
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="label">delay = (100 * 3) ms</div>
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="label">delay = (100 * 4) ms</div>
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="label">delay = (100 * 5) ms</div>
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
  </div>
  <script>var staggeringBasics = function() {
/*DEMO*/
anime({
  targets: '.basic-staggering-demo .el',
  translateX: 270,
  delay: anime.stagger(100) // increase delay by 100ms for each elements.
});
/*DEMO*/
}
  </script>
</div>

<div id="staggeringStartValue" class="demo">
  <h3 class="demo-title">Start value</h3>
  <div class="demo-description">
    <p>
      Starts the staggering effect from a specific value.
    </p>
    <pre><code>anime.stagger(value, {start: startValue})</code></pre>
    <table>
      <thead>
        <td>Types</td>
        <td>Info</td>
      </thead>
      <tr>
        <td><code>Number</code>, <code>String</code>
        <td>Same as propety values, see <a href="#unitlessValue" class="color-values">values section</a></td>
      </tr>
    </table>
  </div>
  <div class="demo-content staggering-start-value-demo">
    <div class="line">
      <div class="label">delay = 500 + (100 * 0) ms</div>
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="label">delay = 500 + (100 * 1) ms</div>
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="label">delay = 500 + (100 * 2) ms</div>
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="label">delay = 500 + (100 * 3) ms</div>
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="label">delay = 500 + (100 * 4) ms</div>
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="label">delay = 500 + (100 * 5) ms</div>
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
  </div>
  <script>var staggeringStartValue = function() {
/*DEMO*/
anime({
  targets: '.staggering-start-value-demo .el',
  translateX: 270,
  delay: anime.stagger(100, {start: 500}) // delay starts at 500ms then increase by 100ms for each elements.
});
/*DEMO*/
}
  </script>
</div>

<div id="rangeValueStaggering" class="demo">
  <h3 class="demo-title">Range value</h3>
  <div class="demo-description">
    <p>
      Distributes evenly values between two numbers.
    </p>
    <pre><code>anime.stagger([startValue, endValue])</code></pre>
    <table>
      <thead>
        <td>Type</td>
        <td>Info</td>
      </thead>
      <tr>
        <td><code>'easingName'</code></td>
        <td>All valid easing names are accepted, see <a href="#easings" class="color-easings">easings section</a></td>
      </tr>
      <tr>
        <td><code>function(i)</code></td>
        <td>Custom easing function, see <a href="#customEasing" class="color-easings">custom easings section</a></td>
      </tr>
    </table>
  </div>
  <div class="demo-content range-value-staggering-demo">
    <div class="line">
      <div class="label">rotate = ((360 - (-360)) / 5) * 0</div>
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="label">rotate = ((360 - (-360)) / 5) * 1</div>
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="label">rotate = ((360 - (-360)) / 5) * 2</div>
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="label">rotate = ((360 - (-360)) / 5) * 3</div>
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="label">rotate = ((360 - (-360)) / 5) * 4</div>
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="label">rotate = ((360 - (-360)) / 5) * 5</div>
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
  </div>
  <script>var rangeValueStaggering = function() {
/*DEMO*/
anime({
  targets: '.range-value-staggering-demo .el',
  translateX: 270,
  rotate: anime.stagger([-360, 360]), // rotation will be distributed from -360deg to 360deg evenly between all elements
  easing: 'easeInOutQuad'
});
/*DEMO*/
}
  </script>
</div>

<div id="staggeringFrom" class="demo">
  <h3 class="demo-title">From value</h3>
  <div class="demo-description">
    <p>
      Starts the stagger effect from a specific position.
    </p>
    <pre><code>anime.stagger(value, {from: startingPosition})</code></pre>
    <table>
      <thead>
        <td>Options</td>
        <td>Type</td>
        <td>Info</td>
      </thead>
      <tr>
        <td><code>'first'</code> (default)</td>
        <td><code>'string'</code></td>
        <td>Start the effect from the first element</td>
      </tr>
      <tr>
        <td><code>'last'</code></td>
        <td><code>'string'</code></td>
        <td>Start the effect from the last element</td>
      </tr>
      <tr>
        <td><code>'center'</code></td>
        <td><code>'string'</code></td>
        <td>Start the effect from the center</td>
      </tr>
      <tr>
        <td><code>index</code></td>
        <td><code>number</code></td>
        <td>Start the effect from the specified index</td>
      </tr>
    </table>
  </div>
  <div class="demo-content staggering-from-demo">
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
  </div>
  <script>var staggeringFrom = function() {
/*DEMO*/
anime({
  targets: '.staggering-from-demo .el',
  translateX: 270,
  delay: anime.stagger(100, {from: 'center'})
});
/*DEMO*/
}
  </script>
</div>

<div id="staggeringDirection" class="demo">
  <h3 class="demo-title">Direction</h3>
  <div class="demo-description">
    <p>
      Changes the order in which the stagger operates.
    </p>
    <pre><code>anime.stagger(value, {direction: 'reverse'})</code></pre>
    <table>
      <thead>
        <td>Options</td>
        <td>Info</td>
      </thead>
      <tr>
        <td><code>'normal'</code> (default)</td>
        <td>Normal staggering, from the first element to the last</td>
      </tr>
      <tr>
        <td><code>'reverse'</code></td>
        <td>Reversed staggering, from the last element to the first</td>
      </tr>
    </table>
  </div>
  <div class="demo-content staggering-direction-demo">
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
  </div>
  <script>var staggeringDirection = function() {
/*DEMO*/
anime({
  targets: '.staggering-direction-demo .el',
  translateX: 270,
  delay: anime.stagger(100, {direction: 'reverse'})
});
/*DEMO*/
}
  </script>
</div>

<div id="staggeringEasing" class="demo">
  <h3 class="demo-title">Easing</h3>
  <div class="demo-description">
    <p>
      Stagger values using an easing function.
    </p>
    <pre><code>anime.stagger(value, {easing: 'easingName'})</code></pre>
    <table>
      <thead>
        <td>Type</td>
        <td>Info</td>
      </thead>
      <tr>
        <td><code>'string'</code></td>
        <td>All valid <a href="#easings" class="color-easings">easing names</a> are accepted</td>
      </tr>
      <tr>
        <td><code>function(i)</code></td>
        <td>Use your own <a href="#customEasing" class="color-easings">custom easings function</a></td>
      </tr>
    </table>
  </div>
  <div class="demo-content staggering-easing-demo">
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
  </div>
  <script>var staggeringEasing = function() {
/*DEMO*/
anime({
  targets: '.staggering-easing-demo .el',
  translateX: 270,
  delay: anime.stagger(300, {easing: 'easeOutQuad'})
});
/*DEMO*/
}
  </script>
</div>

<div id="gridStaggering" class="demo">
  <h3 class="demo-title">Grid</h3>
  <div class="demo-description">
    <p>
      Staggering values based a 2D array that allow "ripple" effects.
    </p>
    <pre><code>anime.stagger(value, {grid: [rows, columns]})</code></pre>
    <table>
      <thead>
        <td>Type</td>
        <td>Info</td>
      </thead>
      <tr>
        <td><code>array</code></td>
        <td>A 2 items array, the first value is the number of rows, the second the number of columns</td>
      </tr>
    </table>
  </div>
  <div class="demo-content staggering-grid-demo">
    <div class="grid">
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
    </div>
    <div class="grid shadow">
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
    </div>
  </div>
  <script>var gridStaggering = function() {
/*DEMO*/
anime({
  targets: '.staggering-grid-demo .el',
  scale: [
    {value: .1, easing: 'easeOutSine', duration: 500},
    {value: 1, easing: 'easeInOutQuad', duration: 1200}
  ],
  delay: anime.stagger(200, {grid: [14, 5], from: 'center'})
});
/*DEMO*/
}
  </script>
</div>

<div id="gridAxisStaggering" class="demo">
  <h3 class="demo-title">Axis</h3>
  <div class="demo-description">
    <p>
      Forces the direction of a <a href="#gridStaggering" class="color-staggering">grid starrering</a> effect.
    </p>
    <pre><code>anime.stagger(value, {grid: [rows, columns], axis: 'x'})</code></pre>
    <table>
      <thead>
        <td>Parameters</td>
        <td>Info</td>
      </thead>
      <tr>
        <td><code>'x'</code></td>
        <td>Follows the x axis</td>
      </tr>
      <tr>
        <td><code>'y'</code></td>
        <td>Follows the y axis</td>
      </tr>
    </table>
  </div>
  <div class="demo-content staggering-axis-grid-demo">
    <div class="grid">
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
      <div class="small square el"></div>
    </div>
    <div class="grid shadow">
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
      <div class="small square"></div>
    </div>
  </div>
  <script>var gridAxisStaggering = function() {
/*DEMO*/
anime({
  targets: '.staggering-axis-grid-demo .el',
  translateX: anime.stagger(10, {grid: [14, 5], from: 'center', axis: 'x'}),
  translateY: anime.stagger(10, {grid: [14, 5], from: 'center', axis: 'y'}),
  rotateZ: anime.stagger([0, 90], {grid: [14, 5], from: 'center', axis: 'x'}),
  delay: anime.stagger(200, {grid: [14, 5], from: 'center'}),
  easing: 'easeInOutQuad'
});
/*DEMO*/
}
  </script>
</div>

</article>
<!-- -->

<!-- TIMELINE -->
<article id="timeline" class="color-tl">
<header>
  <h2 class="demos-title">Timeline</h2>
</header>

<div id="timelineBasics" class="demo">
  <h3 class="demo-title">Timeline basics</h3>
  <div class="demo-description">
    <p>
      Timelines let you synchronise multiple animations together.<br>
      By default each animation added to the timeline starts after the previous animation ends.
    </p>
    <p>
      Creating a timeline:
    </p>
<pre><code>var myTimeline = anime.timeline(parameters);</code></pre>
    <table>
      <thead>
        <td>Argument</td>
        <td>Type</td>
        <td>Info</td>
        <td>Required</td>
      </thead>
      <tr>
        <td><code>parameters</code></td>
        <td><code>Object</code></td>
        <td>The default parameters of the timeline inherited by children</td>
        <td>No</td>
      </tr>
    </table>
    <p>
      Adding animations to a timeline:
    </p>
<pre><code>myTimeline.add(parameters, offset);</code></pre>
    <table>
      <thead>
        <td>Argument</td>
        <td>Types</td>
        <td>Info</td>
        <td>Required</td>
      </thead>
      <tr>
        <td><code>parameters</code></td>
        <td><code>Object</code></td>
        <td>The child animation parameters, override the timeline default parameters</td>
        <td>Yes</td>
      </tr>
      <tr>
        <td><code>time offset</code></td>
        <td><code>String</code> or <code>Number</code></td>
        <td>Check out the <a class="color-tl" href="#timelineOffsets">Timeline offsets</a> section</td>
        <td>No</td>
      </tr>
    </table>
  </div>
  <div class="demo-content basic-timeline-demo">
    <div class="line">
      <div class="square shadow"></div>
      <div class="square el"></div>
    </div>
    <div class="line">
      <div class="circle shadow"></div>
      <div class="circle el"></div>
    </div>
    <div class="line">
      <div class="triangle shadow"></div>
      <div class="triangle el"></div>
    </div>
  </div>
  <script>var timelineBasics = function() {
/*DEMO*/
// Create a timeline with default parameters
var tl = anime.timeline({
  easing: 'easeOutExpo',
  duration: 750
});

// Add children
tl
.add({
  targets: '.basic-timeline-demo .el.square',
  translateX: 250,
})
.add({
  targets: '.basic-timeline-demo .el.circle',
  translateX: 250,
})
.add({
  targets: '.basic-timeline-demo .el.triangle',
  translateX: 250,
});

/*DEMO*/
}
  </script>

</div>

<div id="timelineOffsets" class="demo">
  <h3 class="demo-title">Time Offsets</h3>
  <div class="demo-description">
    <p>
      Time offsets can be specified with a second optional parameter using the timeline <code>.add()</code> function. It defines when a animation starts in the timeline, if no offset is specifed, the animation will starts after the previous animation ends.<br>
      An offset can be relative to the last animation or absolute to the whole timeline.
    </p>
    <table>
      <thead>
        <td>Type</td>
        <td>Offset</td>
        <td>Example</td>
        <td>Infos</td>
      </thead>
      <tr>
        <td>String</td>
        <td><code>'+='</code></td>
        <td><code>'+=200'</code></td>
        <td>Starts 200ms after the previous animation ends</td>
      </tr>
      <tr>
        <td>String</td>
        <td><code>'-='</code></td>
        <td><code>'-=200'</code></td>
        <td>Starts 200ms before the previous animation ends</td>
      </tr>
      <tr>
        <td>Number</td>
        <td><code>Number</code></td>
        <td><code>100</code></td>
        <td>Starts at 100ms, reagardless of the animtion position in the timeline</td>
      </tr>
    </table>
  </div>
  <div class="demo-content offsets-demo">
    <div class="line">
      <div class="label">no offset</div>
      <div class="square shadow"></div>
      <div class="square el"></div>
    </div>
    <div class="line">
      <div class="label">relative offset ('-=600')</div>
      <div class="circle shadow"></div>
      <div class="circle el"></div>
    </div>
    <div class="line">
      <div class="label">absolute offset (400)</div>
      <div class="triangle shadow"></div>
      <div class="triangle el"></div>
    </div>
  </div>
  <script>var timelineOffsets = function() {
/*DEMO*/
// Create a timeline with default parameters
var tl = anime.timeline({
  easing: 'easeOutExpo',
  duration: 750
});

tl
.add({
  targets: '.offsets-demo .el.square',
  translateX: 250,
})
.add({
  targets: '.offsets-demo .el.circle',
  translateX: 250,
}, '-=600') // relative offset
.add({
  targets: '.offsets-demo .el.triangle',
  translateX: 250,
}, 400); // absolute offset

/*DEMO*/
}
  </script>
</div>

<div id="TLParamsInheritance" class="demo">
  <h3 class="demo-title">Parameters inheritance</h3>
  <div class="demo-description">
    <p>
      Parameters set in the parent timeline instance will be inherited by all the children.
    </p>
    <table>
      <thead>
        <td>Parameters that can be inherited</td>
      </thead>
      <tr>
        <td><code>targets</code></td>
      </tr>
      <tr>
        <td><code>duration</code></td>
      </tr>
      <tr>
        <td><code>delay</code></td>
      </tr>
      <tr>
        <td><code>endDelay</code></td>
      </tr>
      <tr>
        <td><code>round</code></td>
      </tr>
    </table>
  </div>
  <div class="demo-content params-inheritance-demo">
    <div class="line">
      <div class="square shadow"></div>
      <div class="square el"></div>
    </div>
    <div class="line">
      <div class="circle shadow"></div>
      <div class="circle el"></div>
    </div>
    <div class="line">
      <div class="triangle shadow"></div>
      <div class="triangle el"></div>
    </div>
  </div>
  <script>var TLParamsInheritance = function() {
/*DEMO*/
var tl = anime.timeline({
  targets: '.params-inheritance-demo .el',
  delay: function(el, i) { return i * 200 },
  duration: 500,
  easing: 'easeOutExpo',
  direction: 'alternate',
  loop: true
});

tl
.add({
  translateX: 250,
  // override the easing parameter
  easing: 'spring',
})
.add({
  opacity: .5,
  scale: 2
})
.add({
  // override the targets parameter
  targets: '.params-inheritance-demo .el.triangle',
  rotate: 180
})
.add({
  translateX: 0,
  scale: 1
});
/*DEMO*/
}
  </script>

</div>

</article>
<!-- -->

<!-- CONTROLS -->
<article id="controls" class="color-controls">
<header>
  <h2 class="demos-title">Controls</h2>
</header>

<div id="playPause" class="demo">
  <h3 class="demo-title">Play / Pause</h3>
  <div class="demo-description">
    <p>Plays a paused animation, or starts the animation if the <code>autoplay</code> parameters is set to <code>false</code>.</p>
    <pre><code>animation.play();</code></pre>
    <p>Pauses a running animation.</p>
    <pre><code>animation.pause();</code></pre>
  </div>
  <div class="demo-content play-pause-demo">
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line controls">
      <button class="play">Play</button>
      <button class="pause">Pause</button>
    </div>
  </div>
  <script>var playPause = function() {
/*DEMO*/
var animation = anime({
  targets: '.play-pause-demo .el',
  translateX: 270,
  delay: function(el, i) { return i * 100; },
  direction: 'alternate',
  loop: true,
  autoplay: false,
  easing: 'easeInOutSine'
});

document.querySelector('.play-pause-demo .play').onclick = animation.play;
document.querySelector('.play-pause-demo .pause').onclick = animation.pause;
/*DEMO*/
}

</script>
</div>

<div id="restartAnim" class="demo">
  <h3 class="demo-title">Restart</h3>
  <div class="demo-description">
    <p>Restarts an animation from its initial values.</p>
    <pre><code>animation.restart();</code></pre>
  </div>
  <div class="demo-content restart-demo">
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line controls">
      <button class="restart">Restart</button>
    </div>
  </div>
  <script>var restartAnim = function() {
/*DEMO*/
var animation = anime({
  targets: '.restart-demo .el',
  translateX: 250,
  delay: function(el, i) { return i * 100; },
  direction: 'alternate',
  loop: true,
  easing: 'easeInOutSine'
});

document.querySelector('.restart-demo .restart').onclick = animation.restart;
/*DEMO*/
}
  </script>

</div>

<div id="reverseAnim" class="demo">
  <h3 class="demo-title">Reverse</h3>
  <div class="demo-description">
    <p>Reverses the direction of an animation.</p>
    <pre><code>animation.reverse();</code></pre>
  </div>
  <div class="demo-content reverse-anim-demo">
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line controls">
      <button class="reverse">Reverse</button>
    </div>
  </div>

  <script>

  var reverseAnimDemo = anime({
    targets: '.reverse-anim-demo .el',
    translateX: 270,
    duration: 2000,
    delay: function(el, i) { return i * 200; }
  });

  document.querySelector('.reverse-anim-demo .reverse').onclick = function() {
    reverseAnimDemo.reverse();
  }

  var reverseAnim = function() {
/*DEMO*/
var animation = anime({
  targets: '.reverse-anim-demo .el',
  translateX: 270,
  loop: true,
  delay: function(el, i) { return i * 200; },
  easing: 'easeInOutSine'
});

document.querySelector('.reverse-anim-demo .reverse').onclick = animation.reverse;
/*DEMO*/
}
  </script>

</div>

<div id="seekAnim" class="demo">
  <h3 class="demo-title">Seek</h3>
  <div class="demo-description">
    <p>Jump to a specific time (in milliseconds).</p>
    <pre><code>animation.seek(timeStamp);</code></pre>
    <p>Can also be used to control an animation while scrolling.</p>
    <pre><code>animation.seek((scrollPercent / 100) * animation.duration);</code></pre>
  </div>
  <div class="demo-content seek-anim-demo">
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line controls">
      <input class="progress" step=".001" type="range" min="0" max="100" value="0"></input>
    </div>
  </div>
  <script>var seekAnim = function() {
/*DEMO*/
var animation = anime({
  targets: '.seek-anim-demo .el',
  translateX: 270,
  delay: function(el, i) { return i * 100; },
  elasticity: 200,
  easing: 'easeInOutSine',
  autoplay: false
});

var seekProgressEl = document.querySelector('.seek-anim-demo .progress');
seekProgressEl.oninput = function() {
  animation.seek(animation.duration * (seekProgressEl.value / 100));
};
/*DEMO*/
}
  </script>

</div>

<div id="TLcontrols" class="demo">
  <h3 class="demo-title">Timeline controls</h3>
  <div class="demo-description">
    <p>Timelines can be controled like any other <i>anime.js</i> instance.</p>
<pre><code>timeline.play();
timeline.pause();
timeline.restart();
timeline.seek(timeStamp);
</code></pre>
  </div>
  <div class="demo-content timeline-controls-demo">
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="small circle shadow"></div>
      <div class="small circle el"></div>
    </div>
    <div class="line">
      <div class="small triangle shadow"></div>
      <div class="small triangle el"></div>
    </div>
    <div class="line controls">
      <button class="play">Play</button>
      <button class="pause">Pause</button>
      <button class="restart">Restart</button>
      <input class="progress" step=".001" type="range" min="0" max="100" value="0"></input>
    </div>
  </div>
  <script>var TLcontrols = function() {
/*DEMO*/
var controlsProgressEl = document.querySelector('.timeline-controls-demo .progress');

var tl = anime.timeline({
  direction: 'alternate',
  loop: true,
  duration: 500,
  easing: 'easeInOutSine',
  update: function(anim) {
    controlsProgressEl.value = tl.progress;
  }
});

tl
.add({
  targets: '.timeline-controls-demo .square.el',
  translateX: 270,
})
.add({
  targets: '.timeline-controls-demo .circle.el',
  translateX: 270,
}, '-=100')
.add({
  targets: '.timeline-controls-demo .triangle.el',
  translateX: 270,
}, '-=100');

document.querySelector('.timeline-controls-demo .play').onclick = tl.play;
document.querySelector('.timeline-controls-demo .pause').onclick = tl.pause;
document.querySelector('.timeline-controls-demo .restart').onclick = tl.restart;

controlsProgressEl.addEventListener('input', function() {
  tl.seek(tl.duration * (controlsProgressEl.value / 100));
});
/*DEMO*/
}
  </script>

</div>

</article>
<!-- -->

<!-- CALLBACKS -->
<article id="callbacks" class="color-callbacks">
<header>
  <h2 class="demos-title">Callbacks & Promises</h2>
</header>

<div id="update" class="demo">
  <h3 class="demo-title">Update</h3>
  <div class="demo-description">
    <p>
      Callback triggered on every frame as soon as the animation starts playing.
    </p>
    <table>
      <thead>
        <td>Type</td>
        <td>Parameters</td>
        <td>Info</td>
      </thead>
      <tr>
        <td>Function</td>
        <td>animation</td>
        <td>Return the current animation Object</td>
      </tr>
    </table>
  </div>
  <div class="demo-content update-demo">
    <div class="logs">
      <input class="log progress-log"></input>
      <input class="log update-log"></input>
    </div>
    <div class="circle shadow"></div>
    <div class="circle el"></div>
  </div>
  <script>var update = function() {

var updateLogEl = document.querySelector('.update-demo .update-log');
var progressLogEl = document.querySelector('.update-demo .progress-log');

/*DEMO*/
var updates = 0;

anime({
  targets: '.update-demo .el',
  translateX: 270,
  delay: 1000,
  direction: 'alternate',
  loop: 3,
  easing: 'easeInOutCirc',
  update: function(anim) {
    updates++;
    progressLogEl.value = 'progress : '+Math.round(anim.progress)+'%';
    updateLogEl.value = 'updates : '+updates;
  }
});
/*DEMO*/
}
  </script>
</div>

<div id="beginComplete" class="demo">
  <h3 class="demo-title">Begin & Complete</h3>
  <div class="demo-description">
      <p><code>begin()</code> callback is triggered once, when the animation starts playing.</p>
      <p><code>complete()</code> callback is triggered once, when the animation is completed.</p>
      <p class="bubble info">Both <code>begin()</code> and <code>complete()</code> callbacks are called if the animation's <code>duration</code> is <code>0</code>.</p>
    <table>
      <thead>
        <td>Type</td>
        <td>Parameters</td>
        <td>Info</td>
      </thead>
      <tr>
        <td>Function</td>
        <td>animation</td>
        <td>Return the current animation Object</td>
      </tr>
    </table>
  </div>
  <div class="demo-content begin-complete-demo">
    <div class="logs">
      <input class="log progress-log"></input>
      <input class="log begin-log" value=""></input>
      <input class="log complete-log" value=""></input>
    </div>
    <div class="large circle shadow"></div>
    <div class="large circle el"></div>
  </div>
  <script>var beginComplete = function() {

var beginLogEl = document.querySelector('.begin-complete-demo .begin-log');
var completeLogEl = document.querySelector('.begin-complete-demo .complete-log');
var progressLogEl = document.querySelector('.begin-complete-demo .progress-log');

beginLogEl.value = '';
completeLogEl.value = '';

/*DEMO*/
anime({
  targets: '.begin-complete-demo .el',
  translateX: 240,
  delay: 1000,
  easing: 'easeInOutCirc',
  update: function(anim) {
    progressLogEl.value = 'progress : ' + Math.round(anim.progress) + '%';
    beginLogEl.value = 'began : ' + anim.began;
    completeLogEl.value = 'completed : ' + anim.completed;
  },
  begin: function(anim) {
    beginLogEl.value = 'began : ' + anim.began;
  },
  complete: function(anim) {
    completeLogEl.value = 'completed : ' + anim.completed;
  }
});
/*DEMO*/
}
  </script>
</div>

<div id="loopBeginLoopComplete" class="demo">
  <h3 class="demo-title">loopBegin & loopComplete</h3>
  <div class="demo-description">
    <p><code>loopBegin()</code> callback is triggered once everytime a loop begin.</p>
    <p><code>loopComplete()</code> callback is triggered once everytime a loop is completed.</p>
    <table>
      <thead>
        <td>Type</td>
        <td>Parameters</td>
        <td>Info</td>
      </thead>
      <tr>
        <td>Function</td>
        <td>animation</td>
        <td>Return the current animation Object</td>
      </tr>
    </table>
  </div>
  <div class="demo-content loopBegin-loopComplete-demo">
    <div class="logs">
      <input class="log loopBegin-log" value=""></input>
      <input class="log loopComplete-log" value=""></input>
    </div>
    <div class="circle shadow"></div>
    <div class="circle el"></div>
  </div>
  <script>var loopBeginLoopComplete = function() {

var beginLogEl = document.querySelector('.loopBegin-log');
var completeLogEl = document.querySelector('.loopComplete-log');

beginLogEl.value = 'loop began 0 times';
completeLogEl.value = 'loop complete 0 times';

/*DEMO*/
var loopBegan = 0;
var loopCompleted = 0;

anime({
  targets: '.loopBegin-loopComplete-demo .el',
  translateX: 240,
  loop: true,
  direction: 'alternate',
  easing: 'easeInOutCirc',
  loopBegin: function(anim) {
    loopBegan++;
    beginLogEl.value = 'loop began : ' + loopBegan;
  },
  loopComplete: function(anim) {
    loopCompleted++;
    completeLogEl.value = 'loop completed : ' + loopCompleted;
  }
});
/*DEMO*/
}
  </script>
</div>

<div id="change" class="demo">
  <h3 class="demo-title">Change</h3>
  <div class="demo-description">
    <p>
      Callback triggered on every frames in between the animation's <a href="#delay" class="color-prop-params">delay</a> and <a href="#endDelay" class="color-prop-params">endDelay</a>.
    </p>
    <table>
      <thead>
        <td>Type</td>
        <td>Parameters</td>
        <td>Info</td>
      </thead>
      <tr>
        <td>Function</td>
        <td>animation</td>
        <td>Return the current animation Object</td>
      </tr>
    </table>
  </div>
  <div class="demo-content change-demo">
    <div class="logs">
      <input class="log progress-log"></input>
      <input class="log change-log"></input>
    </div>
    <div class="circle shadow"></div>
    <div class="circle el"></div>
  </div>
  <script>var change = function() {

var progressLogEl = document.querySelector('.change-demo .progress-log');
var changeLogEl = document.querySelector('.change-demo .change-log');
changeLogEl.value = 'changes : 0';

/*DEMO*/
var changes = 0;

anime({
  targets: '.change-demo .el',
  translateX: 270,
  delay: 1000,
  endDelay: 1000,
  direction: 'alternate',
  loop: true,
  easing: 'easeInOutCirc',
  update: function(anim) {
    progressLogEl.value = 'progress : '+Math.round(anim.progress)+'%';
  },
  change: function() {
    changes++;
    changeLogEl.value = 'changes : ' + changes;
  }
});
/*DEMO*/
}
  </script>
</div>

<div id="changeBeginChangeComplete" class="demo">
  <h3 class="demo-title">changeBegin & changeComplete</h3>
  <div class="demo-description">
    <p><code>changeBegin()</code> callback is triggered everytime the animation starts changing.</p>
    <p><code>changeComplete()</code> callback is triggered everytime the animation stops changing.</p>
    <p class="bubble warning">Animation direction will affect the order in which <code>changeBegin()</code> and <code>changeComplete()</code> are triggerd.</p>
    <table>
      <thead>
        <td>Type</td>
        <td>Parameters</td>
        <td>Info</td>
      </thead>
      <tr>
        <td>Function</td>
        <td>animation</td>
        <td>Return the current animation Object</td>
      </tr>
    </table>
  </div>
  <div class="demo-content changeBegin-chnageComplete-demo">
    <div class="logs">
      <input class="log progress-log" value=""></input>
      <input class="log changeBegin-log" value=""></input>
      <input class="log changeComplete-log" value=""></input>
    </div>
    <div class="large circle shadow"></div>
    <div class="large circle el"></div>
  </div>
  <script>var changeBeginChangeComplete = function() {

var progressLogEl = document.querySelector('.changeBegin-chnageComplete-demo .progress-log');
var beginLogEl = document.querySelector('.changeBegin-log');
var completeLogEl = document.querySelector('.changeComplete-log');

beginLogEl.value = 'change began : 0';
completeLogEl.value = 'change completed : 0';

/*DEMO*/
var changeBegan = 0;
var changeCompleted = 0;

anime({
  targets: '.changeBegin-chnageComplete-demo .el',
  translateX: 240,
  delay: 1000,
  endDelay: 1000,
  loop: true,
  direction: 'alternate',
  easing: 'easeInOutCirc',
  update: function(anim) {
    progressLogEl.value = 'progress : '+Math.round(anim.progress)+'%';
  },
  changeBegin: function(anim) {
    changeBegan++;
    beginLogEl.value = 'change began : ' + changeBegan;
  },
  changeComplete: function(anim) {
    changeCompleted++;
    completeLogEl.value = 'change completed : ' + changeCompleted;
  }
});
/*DEMO*/
}
  </script>
</div>

<div id="finishedPromise" class="demo">
  <h3 class="demo-title">Finished promise</h3>
  <div class="demo-description">
    <p>
      Every animation instances return a <code>finished</code> promise when the animation finised.
    </p>
<pre><code>animation.finished.then(function() {
  // Do things...
});</code></pre>
    <p class="bubble warning">
      Promises are not suported in IE < 11.
    </p>
  </div>
  <div class="demo-content promise-demo">
    <div class="logs">
      <input class="log progress-log"></input>
      <input class="log finished-log"></input>
    </div>
    <div class="circle shadow"></div>
    <div class="circle el"></div>
  </div>
  <script>var finishedPromise = function() {
anime.set('.promise-demo .el', {backgroundColor: ''});
anime.set('.promise-demo .finished-log', {value: ''});
/*DEMO*/
var progressLogEl = document.querySelector('.promise-demo .progress-log');
var promiseEl = document.querySelector('.promise-demo .el');
var finishedLogEl = document.querySelector('.promise-demo .finished-log');
var demoPromiseResetTimeout;

function logFinished() {
  anime.set(finishedLogEl, {value: 'Promise resolved'});
  anime.set(promiseEl, {backgroundColor: '#18FF92'});
}

var animation = anime.timeline({
  targets: promiseEl,
  delay: 400,
  duration: 500,
  endDelay: 400,
  easing: 'easeInOutSine',
  update: function(anim) {
    progressLogEl.value = 'progress : '+Math.round(anim.progress)+'%';
  }
}).add({
  translateX: 250
}).add({
  scale: 2
}).add({
  translateX: 0
});

animation.finished.then(logFinished);

/*DEMO*/
}
  </script>

</div>

</article>
<!-- -->

<!-- SVG -->
<article id="svg" class="color-svg">
<header>
  <h2 class="demos-title">SVG</h2>
</header>

<div id="motionPath" class="demo">
  <h3 class="demo-title">Motion path</h3>
  <div class="demo-description">
    <p>
      Animates an element relative to the <code>x</code>, <code>y</code> and <code>angle</code> values of an SVG path element.
    </p>
    <pre><code>var myPath = anime.path('svg path');</code></pre>
    <p>
      The path function returns a new Function that returns the specified property.
    </p>
    <p class="bubble info">
      Motion path animations are responsive since v3
    </p>
    <table>
      <thead>
        <td>Parameters</td>
        <td>Example</td>
        <td>Info</td>
      </thead>
      <tr>
        <td><code>'x'</code></td>
        <td><code>myPath('x')</code></td>
        <td>Return the current x value in <code>'px'</code> of the SVG path</td>
      </tr>
      <tr>
        <td><code>'y'</code></td>
        <td><code>myPath('y')</code></td>
        <td>Return the current y value in <code>'px'</code> of the SVG path</td>
      </tr>
      <tr>
        <td><code>'angle'</code></td>
        <td><code>myPath('angle')</code></td>
        <td>Return the current angle value in <code>'degrees'</code> of the SVG path</td>
      </tr>
    </table>
  </div>
  <div class="demo-content motion-path-demo">
    <div class="motion-path">
      <div class="small square shadow follow-path"></div>
      <div class="small square el follow-path"></div>
      <svg width="256" height="112" viewBox="0 0 256 112">
        <path fill="none" stroke="currentColor" stroke-width="1" d="M8,56 C8,33.90861 25.90861,16 48,16 C70.09139,16 88,33.90861 88,56 C88,78.09139 105.90861,92 128,92 C150.09139,92 160,72 160,56 C160,40 148,24 128,24 C108,24 96,40 96,56 C96,72 105.90861,92 128,92 C154,93 168,78 168,56 C168,33.90861 185.90861,16 208,16 C230.09139,16 248,33.90861 248,56 C248,78.09139 230.09139,96 208,96 L48,96 C25.90861,96 8,78.09139 8,56 Z"/>
      </svg>
    </div>
  </div>
  <script>var motionPath = function() {
/*DEMO*/
var path = anime.path('.motion-path-demo path');

anime({
  targets: '.motion-path-demo .el',
  translateX: path('x'),
  translateY: path('y'),
  rotate: path('angle'),
  easing: 'linear',
  duration: 2000,
  loop: true
});
/*DEMO*/
}
  </script>

</div>

<div id="morphing" class="demo">
  <h3 class="demo-title">Morphing</h3>
  <div class="demo-description">
    <p>
      Creates transition between two svg shapes.
    </p>
    <p class="bubble warning">
      Shapes must have the same number of points!
    </p><br>
    <p class="bubble info">
      More info on SVG shape morphing <a href="https://hackernoon.com/metamorphosis-morphing-svgs-378cf4f3aa58">here</a>.
    </p>
  </div>
  <div class="demo-content align-center morphing-demo">
    <svg width="140" height="140" viewBox="0 0 140 140">
      <g fill="none" fill-rule="evenodd">
        <g fill="currentColor" fill-opacity=".15" transform="translate(0 6)">
          <polygon points="70 0 136.574 48.369 111.145 126.631 28.855 126.631 3.426 48.369"/>
          <polygon points="70 18 119.455 53.931 100.565 112.069 39.435 112.069 20.545 53.931"/>
          <polygon points="70 34.86 101.727 57.911 89.609 95.209 50.391 95.209 38.273 57.911"/>
          <polygon points="70 50.898 84.864 61.697 79.186 79.171 60.814 79.171 55.136 61.697"/>
        </g>
        <polygon class="polymorph" stroke-width="1" stroke="currentColor" points="70 24 119.574 60.369 100.145 117.631 50.855 101.631 3.426 54.369"/>
      </g>
    </svg>
  </div>
  <script>var morphing = function() {
/*DEMO*/
anime({
  targets: '.morphing-demo .polymorph',
  points: [
    { value: [
      '70 24 119.574 60.369 100.145 117.631 50.855 101.631 3.426 54.369',
      '70 41 118.574 59.369 111.145 132.631 60.855 84.631 20.426 60.369']
    },
    { value: '70 6 119.574 60.369 100.145 117.631 39.855 117.631 55.426 68.369' },
    { value: '70 57 136.574 54.369 89.145 100.631 28.855 132.631 38.426 64.369' },
    { value: '70 24 119.574 60.369 100.145 117.631 50.855 101.631 3.426 54.369' }
  ],
  easing: 'easeOutQuad',
  duration: 2000,
  loop: true
});
/*DEMO*/
}
  </script>

</div>

<div id="lineDrawing" class="demo">
  <h3 class="demo-title">Line drawing</h3>
  <div class="demo-description">
    <p>
      Creates path drawing animation using the <code>'stroke-dashoffset'</code> property.<br>
      Set the path <code>'dash-offset'</code> value with <code>anime.setDashoffset()</code> in a <a href="#fromToValues" class="color-values">from to</a> formated value.
    </p>
    <pre><code>strokeDashoffset: [anime.setDashoffset, 0]</code></pre>
    <p class="bubble info">
      More info on line drawing animation <a href="https://css-tricks.com/svg-line-animation-works/">here</a>.
    </p>
  </div>
  <div class="demo-content align-center line-drawing-demo">
    <svg viewBox="0 0 280 100">
      <g fill="none" fill-rule="evenodd" stroke="currentColor" stroke-width="1" class="lines">
        <path class="el" d="M58 80V50.12C57.7 41.6 51.14 35 43 35a15 15 0 0 0 0 30h7.5v15H43a30 30 0 1 1 0-60c16.42 0 29.5 13.23 30 29.89V80H58z"/>
        <path class="el" d="M73 80V20H58v60h15z"/>
        <path class="el" d="M58 80V49.77C58.5 33.23 71.58 20 88 20a30 30 0 0 1 30 30v30h-15V50a15 15 0 0 0-15-15c-8.14 0-14.7 6.6-15 15.12V80H58zm75 0V20h-15v60h15z"/>
        <path class="el" d="M118 80V49.77C118.5 33.23 131.58 20 148 20a30 30 0 0 1 30 30v30h-15V50a15 15 0 0 0-15-15c-8.14 0-14.7 6.6-15 15.12V80h-15zm-7.5-60a7.5 7.5 0 1 1-7.48 8v-1c.25-3.9 3.5-7 7.48-7z"/>
        <path class="el" d="M133 65a15 15 0 0 1-15-15v-7.5h-15V50a30 30 0 0 0 30 30V65zm30 15V49.77C163.5 33.23 176.58 20 193 20a30 30 0 0 1 30 30v30h-15V50a15 15 0 0 0-15-15c-8.14 0-14.7 6.6-15 15.12V80h-15z"/>
        <path class="el" d="M238 65a15 15 0 0 1 0-30c8.1 0 14.63 6.53 15 15h-15v15h30V49.89C267.5 33.23 254.42 20 238 20a30 30 0 0 0 0 60V65z"/>
        <path class="el" d="M260.48 65a7.5 7.5 0 1 1-7.48 8v-1c.26-3.9 3.5-7 7.48-7z"/>
      </g>
    </svg>
  </div>
  <script>var lineDrawing = function() {
/*DEMO*/
anime({
  targets: '.line-drawing-demo .lines path',
  strokeDashoffset: [anime.setDashoffset, 0],
  easing: 'easeInOutSine',
  duration: 1500,
  delay: function(el, i) { return i * 250 },
  direction: 'alternate',
  loop: true
});
/*DEMO*/
}
  </script>

</div>

</article>
<!-- -->

<!-- EASINGS -->
<article id="easings" class="color-easings">
  <header>
    <h2 class="demos-title">Easings</h2>
  </header>

<div id="linearEasing" class="demo">
  <h3 class="demo-title">Linear</h3>
  <div class="demo-description">
    <p>
      Does not apply any easing timing to your animation.<br>
      Usefull for opacity and colors transitions.
    </p>
    <pre><code>easing: 'linear'</code></pre>
  </div>
  <div class="demo-content linear-easing-demo">
    <div class="line">
      <div class="square shadow"></div>
      <div class="square el"></div>
    </div>
  </div>
  <script>var linearEasing = function() {
/*DEMO*/
anime({
  targets: '.linear-easing-demo .el',
  translateX: 250,
  direction: 'alternate',
  loop: true,
  easing: 'linear'
});
/*DEMO*/
}
  </script>

</div>

<div id="pennerFunctions" class="demo">
  <h3 class="demo-title">Penner's functions</h3>
  <div class="demo-description">
    <p>
      Built-in <a href="http://robertpenner.com/easing/">Robert Penner's easing functions</a>.
    </p>
    <p class="bubble info">
      See easings in action on <a href="https://easings.net">easings.net</a>.
    </p>
    <pre><code>easing: 'easeInOutSine'</code></pre>
    <p>
      Availabe easings :
    </p>
    <table>
      <thead>
        <td>in</td>
        <td>out</td>
        <td>in-out</td>
      </thead>
      <tr>
        <td><code>'easeInQuad'</code></td>
        <td><code>'easeOutQuad'</code></td>
        <td><code>'easeInOutQuad'</code></td>
      </tr>
      <tr>
        <td><code>'easeInCubic'</code></td>
        <td><code>'easeOutCubic'</code></td>
        <td><code>'easeInOutCubic'</code></td>
      </tr>
      <tr>
        <td><code>'easeInQuart'</code></td>
        <td><code>'easeOutQuart'</code></td>
        <td><code>'easeInOutQuart'</code></td>
      </tr>
      <tr>
        <td><code>'easeInQuint'</code></td>
        <td><code>'easeOutQuint'</code></td>
        <td><code>'easeInOutQuint'</code></td>
      </tr>
      <tr>
        <td><code>'easeInSine'</code></td>
        <td><code>'easeOutSine'</code></td>
        <td><code>'easeInOutSine'</code></td>
      </tr>
      <tr>
        <td><code>'easeInExpo'</code></td>
        <td><code>'easeOutExpo'</code></td>
        <td><code>'easeInOutExpo'</code></td>
      </tr>
      <tr>
        <td><code>'easeInCirc'</code></td>
        <td><code>'easeOutCirc'</code></td>
        <td><code>'easeInOutCirc'</code></td>
      </tr>
      <tr>
        <td><code>'easeInBack'</code></td>
        <td><code>'easeOutBack'</code></td>
        <td><code>'easeInOutBack'</code></td>
      <tr>
      <tr>
        <td><code>'easeInBounce'</code></td>
        <td><code>'easeOutBounce'</code></td>
        <td><code>'easeInOutBounce'</code></td>
      <tr>
    </table>
  </div>
  <div class="demo-content penner-equations-demo">

  </div>
  <script>var pennerFunctions = function() {
/*DEMO*/
var demoContentEl = document.querySelector('.penner-equations-demo');
var fragment = document.createDocumentFragment();
var easingNames = [
  'easeInQuad',
  'easeInCubic',
  'easeInQuart',
  'easeInQuint',
  'easeInSine',
  'easeInExpo',
  'easeInCirc',
  'easeInBack',
  'easeInBounce',
  'easeInOutQuad',
  'easeInOutCubic',
  'easeInOutQuart',
  'easeInOutQuint',
  'easeInOutSine',
  'easeInOutExpo',
  'easeInOutCirc',
  'easeInOutBack',
  'easeInOutBounce',
  'easeOutQuad',
  'easeOutCubic',
  'easeOutQuart',
  'easeOutQuint',
  'easeOutSine',
  'easeOutExpo',
  'easeOutCirc',
  'easeOutBack',
  'easeOutBounce'
]

function createEasingDemo(easing) {
  var demoEl = document.createElement('div');
  demoEl.classList.add('stretched','square','el', 'easing-'+easing);
  anime({
    targets: demoEl,
    translateX: 250,
    direction: 'alternate',
    loop: true,
    delay: 200,
    endDelay: 200,
    duration: 2000,
    easing: easing
  });
  fragment.appendChild(demoEl);
}

easingNames.forEach(function(easeName) {
  createEasingDemo(easeName);
});

demoContentEl.innerHTML = '';
demoContentEl.appendChild(fragment);
/*DEMO*/
}
  </script>

</div>

<div id="cubicBezier" class="demo">
  <h3 class="demo-title">Cubic Bézier Curve</h3>
  <div class="demo-description">
    <p>
      Use your own custom cubic Bézier curves <code>cubicBezier(x1, y1, x2, y2)</code>.
    </p>
    <pre><code>easing: 'cubicBezier(.5, .05, .1, .3)'</code></pre>
    <p class="bubble warning">
      You can use Bézier curves generator like <a href="https://matthewlein.com/tools/ceaser">Ceaser to generate your curves coordinates</a>.
    </p>
  </div>
  <div class="demo-content cubic-bezier-demo">
    <div class="line">
      <div class="square shadow"></div>
      <div class="square el"></div>
    </div>
  </div>
  <script>var cubicBezier = function() {
/*DEMO*/
anime({
  targets: '.cubic-bezier-demo .el',
  translateX: 250,
  direction: 'alternate',
  loop: true,
  easing: 'cubicBezier(.5, .05, .1, .3)'
})
/*DEMO*/
}
  </script>

</div>

<div id="springPhysicsEasing" class="demo">
  <h3 class="demo-title">Spring</h3>
  <div class="demo-description">
    <p>
      Spring physics based easing.
    </p>
    <pre><code>easing: 'spring(mass, stiffness, damping, velocity)'</code></pre>
    <p class="bubble warning">
      The duration of a spring animation is defined by the spring parameters.<br>
      The animation <code>duration</code> parameter will not be taken into account.
    </p>
    <table>
      <thead>
        <td>Parameter</td>
        <td>Default</td>
        <td>Min</td>
        <td>Max</td>
      </thead>
      <tr>
        <td>Mass</td>
        <td><code>1</code></td>
        <td><code>0</code></td>
        <td><code>100</code></td>
      </tr>
      <tr>
        <td>Stiffness</td>
        <td><code>100</code></td>
        <td><code>0</code></td>
        <td><code>100</code></td>
      </tr>
      <tr>
        <td>Damping</td>
        <td><code>10</code></td>
        <td><code>0</code></td>
        <td><code>100</code></td>
      </tr>
      <tr>
        <td>Velocity</td>
        <td><code>0</code></td>
        <td><code>0</code></td>
        <td><code>100</code></td>
      </tr>
    </table>
  </div>
  <div class="demo-content spring-physics-demo">
    <div class="line">
      <div class="square shadow"></div>
      <div class="square el"></div>
    </div>
  </div>
  <script>var springPhysicsEasing = function() {
/*DEMO*/
anime({
  targets: '.spring-physics-demo .el',
  translateX: 250,
  direction: 'alternate',
  loop: true,
  easing: 'spring(1, 80, 10, 0)'
})
/*DEMO*/
}
  </script>

</div>

<div id="elasticEasing" class="demo">
  <h3 class="demo-title">Elastic</h3>
  <div class="demo-description">
    <p>Elastic easing.</p>
    <pre><code>easing: 'easeOutElastic(amplitude, period)'</code></pre>
    <table>
      <thead>
        <td>in</td>
        <td>out</td>
        <td>in-out</td>
      </thead>
      <tr>
        <td><code>'inElastic'</code></td>
        <td><code>'outElastic'</code></td>
        <td><code>'inOutElastic'</code></td>
      </tr>
    </table>
    <table>
      <thead>
        <td>Parameter</td>
        <td>Default</td>
        <td>Min</td>
        <td>Max</td>
        <td>Info</td>
      </thead>
      <tr>
        <td>Amplitude</td>
        <td><code>1</code></td>
        <td><code>1</code></td>
        <td><code>10</code></td>
        <td>Controls the overshoot of the curve</td>
      </tr>
      <tr>
        <td>Period</td>
        <td><code>.5</code></td>
        <td><code>0.1</code></td>
        <td><code>2</code></td>
        <td>Controls how many times the curve goes back and forth</td>
      </tr>
    </table>
  </div>
  <div class="demo-content elastic-easing-demo">
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el" data-ease="easeInElastic(1, .6)"></div>
    </div>
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el" data-ease="easeOutElastic(1, .6)"></div>
    </div>
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el" data-ease="easeInOutElastic(1, .6)"></div>
    </div>
  </div>
  <script>var elasticEasing = function() {
/*DEMO*/
anime({
  targets: '.elastic-easing-demo .el',
  translateX: 270,
  easing: function(el) {
    return el.getAttribute('data-ease');
  },
  duration: 1000
})
/*DEMO*/
}
  </script>

</div>

<div id="stepEasing" class="demo">
  <h3 class="demo-title">Steps</h3>
  <div class="demo-description">
    <p>
      Defines the number of jumps an animation takes to arrive at its end value.
    </p>
    <pre><code>easing: 'steps(numberOfSteps)'</code></pre>
    <table>
      <thead>
        <td>Parameter</td>
        <td>Default</td>
        <td>Min</td>
        <td>Max</td>
      </thead>
      <tr>
        <td>Number of steps</td>
        <td><code>10</code></td>
        <td><code>1</code></td>
        <td><code>∞</code></td>
      </tr>
    </table>
  </div>
  <div class="demo-content step-easing-demo">
    <div class="line">
      <div class="square shadow"></div>
      <div class="square el"></div>
    </div>
  </div>
  <script>var stepEasing = function() {
/*DEMO*/
anime({
  targets: '.step-easing-demo .el',
  translateX: 250,
  direction: 'alternate',
  loop: true,
  easing: 'steps(5)'
})
/*DEMO*/
}
  </script>

</div>

<div id="customEasing" class="demo">
  <h3 class="demo-title">Custom easing function</h3>
  <div class="demo-description">
    <p>
      A custom easing function must be returned by <a class="color-values" href="#functionBasedPropVal">function based value</a>.
    </p>
    <pre><code>easing: function() { return function(time) { return time * i} }</code></pre>
    <table>
      <thead>
        <td>Parameter</td>
        <td>Info</td>
      </thead>
      <tr>
        <td>Time</td>
        <td>Return the current time of the animation</td>
      </tr>
    </table>
  </div>
  <div class="demo-content custom-easing-demo">
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
  </div>
  <script>var customEasing = function() {
/*DEMO*/
anime({
  targets: '.custom-easing-demo .el',
  translateX: 270,
  direction: 'alternate',
  loop: true,
  duration: 2000,
  easing: function(el, i, total) {
    return function(t) {
      return Math.pow(Math.sin(t * (i + 1)), total);
    }
  }
});
/*DEMO*/
}
  </script>

</div>

</article>
<!-- -->

<!-- HELPERS -->
<article id="easings" class="color-helpers">
  <header>
    <h2 class="demos-title">Helpers</h2>
  </header>

<div id="remove" class="demo">
  <h3 class="demo-title">remove</h3>
  <div class="demo-description">
    <p>
      Removes targets from a running animation or timeline.<br>
      The <code>targets</code> parameters accepts the same values as the <a class="color-targets" href="#cssSelector">targets</a> property.
    </p>
    <pre><code>anime.remove(targets)</code></pre>
  </div>
  <div class="demo-content remove-demo">
    <div class="line">
      <div class="small circle shadow"></div>
      <div class="small circle el"></div>
    </div>
    <div class="line">
      <div class="small circle shadow"></div>
      <div class="small circle el"></div>
    </div>
    <div class="line">
      <div class="small circle shadow"></div>
      <div class="small circle el"></div>
    </div>
    <div class="line controls">
      <button class="remove-el-button">Remove second target</button>
    </div>
  </div>
  <script>var remove = function() {
/*DEMO*/
anime({
  targets: '.remove-demo .el',
  translateX: 270,
  direction: 'alternate',
  loop: true,
  easing: 'easeInOutQuad'
});

document.querySelector('.remove-el-button').addEventListener('click', function() {
  anime.remove('.remove-demo .line:nth-child(2) .el');
});
/*DEMO*/
}
  </script>

</div>

<div id="get" class="demo">
  <h3 class="demo-title">get</h3>
  <div class="demo-description">
    <p>Returns the original value of an element.</p>
    <pre><code>anime.get(target, propertyName, unit);</code></pre>
    <p>
      Since <i>anime.js</i> uses <a href="https://developer.mozilla.org/fr/docs/Web/API/Window/getComputedStyle">getComputedStyle</a> to access original CSS, the values are almost always returned in <code>'px'</code>, the third (optional) argument converts the value in the desired unit.
    </p>
    <pre><code>anime.get(domNode, 'width', 'rem');</code></pre>
    <table>
      <thead>
        <td>Parameter</td>
        <td>Type</td>
        <td>Info</td>
      </thead>
      <tr>
        <td>target</td>
        <td><code>'string'</code>, <code>var</code></td>
        <td>Any valid <a class="color-targets" href="#cssSelector">targets</a> can be used</td>
      </tr>
    </table>
    <p class="bubble warning">
      CSS transforms : Only inlined values can be accessed.
    </p>
  </div>
  <div class="demo-content get-value-demo">
    <div class="line">
      <div class="logs">
        <div class="log get-value-demo-log"></div>
      </div>
      <div class="large circle shadow"></div>
      <div class="large circle el"></div>
    </div>
  </div>
  <script>var get = function() {
/*DEMO*/
var logEl = document.querySelector('.get-value-demo-log');
var el = document.querySelector('.get-value-demo .el');

logEl.innerHTML = '';
logEl.innerHTML += '".el" width is :<br>';
logEl.innerHTML += '"' + anime.get(el, 'width', 'px') + '"';
logEl.innerHTML += ' or "' + anime.get(el, 'width', 'rem') + 'rem"'
/*DEMO*/
}
  </script>

</div>

<div id="set" class="demo">
  <h3 class="demo-title">set</h3>
  <div class="demo-description">
    <p>
      Immediately sets values to the specified targets.
    </p>
    <pre><code>anime.set(targets, {property: value});</code></pre>
    <table>
      <thead>
        <td>Parameters</td>
        <td>Types</td>
        <td>Info</td>
      </thead>
      <tr>
        <td>target(s)</td>
        <td><code>'string'</code>, <code>var</code></td>
        <td>Any valid <a class="color-targets" href="#cssSelector">targets</a> can be used</td>
      </tr>
      <tr>
        <td>values</td>
        <td><code>object</code></td>
        <td>Any valid <a class="color-properties" href="#cssProperties">properties</a> can be used</td>
      </tr>
    </table>
  </div>
  <div class="demo-content set-value-demo">
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
  </div>
  <script>var set = function() {
/*DEMO*/
anime.set('.set-value-demo .el', {
  translateX: function() { return anime.random(50, 250); },
  rotate: function() { return anime.random(0, 360); },
});
/*DEMO*/
}
  </script>

</div>

<div id="random" class="demo">
  <h3 class="demo-title">random</h3>
  <div class="demo-description">
    <p>
      Returns a random integer within a specific range.
    </p>
    <pre><code>anime.random(minValue, maxValue);</code></pre>
  </div>
  <div class="demo-content random-demo">
    <div class="line">
      <div class="small circle shadow"></div>
      <div class="small circle el"></div>
    </div>
    <div class="line">
      <div class="small circle shadow"></div>
      <div class="small circle el"></div>
    </div>
    <div class="line">
      <div class="small circle shadow"></div>
      <div class="small circle el"></div>
    </div>
  </div>
  <script>var random = function() {
/*DEMO*/
function randomValues() {
  anime({
    targets: '.random-demo .el',
    translateX: function() {
      return anime.random(0, 270);
    },
    easing: 'easeInOutQuad',
    duration: 750,
    complete: randomValues
  });
}

randomValues();
/*DEMO*/
}
  </script>

</div>

<div id="tick" class="demo">
  <h3 class="demo-title">tick</h3>
  <div class="demo-description">
    <p>
      Plays an animation using an external <code>requestAnimationFrame</code> loop.
    </p>
    <pre><code>animation.tick(time)</code></pre>
    <p class="bubble warning">
      Don't forget to set the <code>autoplay</code> parameter to <code>false</code> to prevent the <i>anime.js</i> built-in RAF loop to start.
    </p>
  </div>
  <div class="demo-content tick-demo">
    <div class="line">
      <div class="circle shadow"></div>
      <div class="circle el"></div>
    </div>
  </div>
  <script>
var customRAF = null;
var tick = function() {
if (customRAF) cancelAnimationFrame(customRAF);
/*DEMO*/
var animation = anime({
  targets: '.tick-demo .el',
  translateX: 270,
  direction: 'alternate',
  loop: true,
  easing: 'easeInOutQuad',
  autoplay: false
});

function loop(t) {
  animation.tick(t);
  customRAF = requestAnimationFrame(loop);
}

requestAnimationFrame(loop);
/*DEMO*/
}
  </script>

</div>

<div id="running" class="demo">
  <h3 class="demo-title">running</h3>
  <div class="demo-description">
    <p>
      Returns an Array of all active <i>anime.js</i> instances currently running.
    </p>
    <pre><code>anime.running</code></pre>
  </div>
  <div class="demo-content running-demo">
    <div class="line">
      <div class="small square shadow"></div>
      <div class="small square el"></div>
    </div>
    <div class="line">
      <div class="small circle shadow"></div>
      <div class="small circle el"></div>
    </div>
    <div class="line">
      <div class="small triangle shadow"></div>
      <div class="small triangle el"></div>
    </div>
    <div class="line">
      <pre class="bottom log running-log"></pre>
    </div>
  </div>
  <script>var running = function() {
/*DEMO*/
var runninLogEl = document.querySelector('.running-log');

anime({
  targets: '.running-demo .square.el',
  translateX: 270,
  direction: 'alternate',
  loop: true,
  easing: 'linear'
});

anime({
  targets: '.running-demo .circle.el',
  translateX: 270,
  direction: 'alternate',
  loop: true,
  easing: 'easeInOutCirc'
});

anime({
  targets: '.running-demo .triangle.el',
  translateX: 270,
  direction: 'alternate',
  easing: 'easeInOutQuad',
  loop: true,
  update: function() {
    runninLogEl.innerHTML = 'there are currently ' + anime.running.length + ' instances running';
  }
});

/*DEMO*/
}
  </script>
</div>

</article>
<!-- -->

    </section>
    <section class="pane demo-info">
      <header class="description-header">
        <h2><span></span></h2>
      </header>
      <div class="info-output"></div>
    </section>
  </div>
  <script async defer src="https://buttons.github.io/buttons.js"></script>
  <script>
    (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
    (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
    m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
    })(window,document,'script','https://www.google-analytics.com/analytics.js','ga');
    ga('create', 'UA-79927007-1', 'auto');
    ga('send', 'pageview');
  </script>
  <script src="assets/js/vendors/highlight.pack.js"></script>
  <script src="assets/js/vendors/beautify-html.js"></script>
  <script src="assets/js/documentation.js"></script>
</body>
</html>
