<template>
  <div class="loading-container">
    <div class="loading-bg"></div>
    <div class="loading-content">
      <div class="loading-logo">
        <img src="@/assets/images/dashboard/logo.png" alt="Logo" />
      </div>
      <div class="loading-title">资产智能驾驶舱</div>
      <div class="loading-subtitle">Asset Management Dashboard</div>

      <div class="loading-progress">
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: progress + '%' }"></div>
        </div>
        <div class="progress-text">{{ progress }}%</div>
      </div>

      <div class="loading-tips">
        <div
          class="tip-item"
          v-for="(tip, index) in tips"
          :key="index"
          :class="{ active: currentTip === index }"
        >
          {{ tip }}
        </div>
      </div>
    </div>

    <!-- 装饰元素 -->
    <div class="loading-decorations">
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
      <div class="decoration-circle circle-3"></div>
      <div class="decoration-line line-1"></div>
      <div class="decoration-line line-2"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";

const progress = ref(0);
const currentTip = ref(0);

const tips = [
  "正在加载资产数据...",
  "正在初始化图表组件...",
  "正在连接数据源...",
  "正在渲染界面...",
  "加载完成！",
];

let progressInterval = null;
let tipInterval = null;

onMounted(() => {
  // 进度条动画
  progressInterval = setInterval(() => {
    if (progress.value < 100) {
      progress.value += Math.random() * 15 + 5;
      if (progress.value > 100) progress.value = 100;
    }
  }, 100);

  // 提示文字切换
  tipInterval = setInterval(() => {
    currentTip.value = (currentTip.value + 1) % tips.length;
  }, 200);
});

onUnmounted(() => {
  if (progressInterval) clearInterval(progressInterval);
  if (tipInterval) clearInterval(tipInterval);
});
</script>

<style lang="scss" scoped>
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;

  .loading-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url("@/assets/images/dashboard/background1.jpg") no-repeat center center;
    background-size: cover;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.7);
    }
  }

  .loading-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;

    .loading-logo {
      margin-bottom: 30px;

      img {
        width: 120px;
        height: auto;
        filter: drop-shadow(0 0 20px rgba(0, 212, 255, 0.5));
        animation: logoFloat 3s ease-in-out infinite;
      }
    }

    .loading-title {
      font-size: 48px;
      font-weight: 700;
      margin-bottom: 10px;
      background: linear-gradient(90deg, #00d4ff 0%, #00ff88 50%, #ffb800 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
      animation: titleGlow 2s ease-in-out infinite;
    }

    .loading-subtitle {
      font-size: 18px;
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 50px;
      letter-spacing: 3px;
      text-transform: uppercase;
    }

    .loading-progress {
      width: 400px;
      margin: 0 auto 30px;

      .progress-bar {
        width: 100%;
        height: 4px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 2px;
        overflow: hidden;
        margin-bottom: 10px;

        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, #00d4ff, #00ff88);
          border-radius: 2px;
          transition: width 0.3s ease;
          box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
          animation: progressGlow 1.5s ease-in-out infinite;
        }
      }

      .progress-text {
        font-size: 16px;
        color: #00d4ff;
        font-family: "electronicFont", monospace;
        font-weight: 700;
      }
    }

    .loading-tips {
      height: 30px;

      .tip-item {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.6);
        opacity: 0;
        transition: opacity 0.3s ease;

        &.active {
          opacity: 1;
          animation: tipFadeIn 0.5s ease;
        }
      }
    }
  }

  .loading-decorations {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;

    .decoration-circle {
      position: absolute;
      border: 2px solid rgba(0, 212, 255, 0.3);
      border-radius: 50%;
      animation: circleRotate linear infinite;

      &.circle-1 {
        width: 200px;
        height: 200px;
        top: 20%;
        left: 10%;
        animation-duration: 20s;
      }

      &.circle-2 {
        width: 150px;
        height: 150px;
        top: 60%;
        right: 15%;
        animation-duration: 15s;
        animation-direction: reverse;
        border-color: rgba(0, 255, 136, 0.3);
      }

      &.circle-3 {
        width: 100px;
        height: 100px;
        bottom: 20%;
        left: 20%;
        animation-duration: 25s;
        border-color: rgba(255, 184, 0, 0.3);
      }
    }

    .decoration-line {
      position: absolute;
      background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.3), transparent);
      animation: lineMove 4s ease-in-out infinite;

      &.line-1 {
        width: 2px;
        height: 100%;
        left: 30%;
        animation-delay: 0s;
      }

      &.line-2 {
        width: 100%;
        height: 2px;
        top: 40%;
        animation-delay: 2s;
        background: linear-gradient(90deg, transparent, rgba(0, 255, 136, 0.3), transparent);
      }
    }
  }
}

// 动画效果
@keyframes logoFloat {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes titleGlow {
  0%,
  100% {
    text-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
  }
  50% {
    text-shadow: 0 0 50px rgba(0, 212, 255, 0.8);
  }
}

@keyframes progressGlow {
  0%,
  100% {
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.8);
  }
}

@keyframes tipFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes circleRotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes lineMove {
  0%,
  100% {
    opacity: 0;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .loading-container {
    .loading-content {
      .loading-title {
        font-size: 32px;
      }

      .loading-subtitle {
        font-size: 14px;
      }

      .loading-progress {
        width: 300px;
      }
    }
  }
}
</style>
