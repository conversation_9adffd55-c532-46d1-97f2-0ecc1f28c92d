<!DOCTYPE html>
<html>
<head>
  <title>stagger | anime.js</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <meta property="og:title" content="anime.js">
  <meta property="og:url" content="https://animejs.com">
  <meta property="og:description" content="Javascript Animation Engine">
  <meta property="og:image" content="https://animejs.com/documentation/assets/img/icons/og.png">
  <meta name="twitter:card" content="summary">
  <meta name="twitter:title" content="anime.js">
  <meta name="twitter:site" content="@juliangarnier">
  <meta name="twitter:description" content="Javascript Animation Engine">
  <meta name="twitter:image" content="https://animejs.com/documentation/assets/img/icons/twitter.png">
  <link rel="apple-touch-icon-precomposed" href="../assets/img/social-media-image.png">
  <link rel="icon" type="image/png" href="../assets/img/favicon.png" >
  <link href="../assets/css/animejs.css" rel="stylesheet">
  <link href="../assets/css/documentation.css" rel="stylesheet">
  <style>

    body {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      align-items: center;
    }

    .easing-visualizer {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      width: 400px;
      height: 400px;
    }

    .easing-visualizer div {
      width: 4px;
      height: 4px;
      margin: 0 0 0 0;
      background-color: #FFF;
      transform-origin: 50% 100%;
      border-radius: 50%;
    }

  </style>
</head>
<body>

  <div class="easing-visualizer"></div>

</body>
<script type="module">

  import anime from '../../src/index.js';

  const easingVisualizerEl = document.querySelector('.easing-visualizer');
  const fragment = document.createDocumentFragment();
  const numberOfElements = 101;

  for (let i = 0; i < numberOfElements; i++) {
    fragment.appendChild(document.createElement('div'));
  }

  easingVisualizerEl.appendChild(fragment);

  var easings = [
    "easeInSine",
    "easeOutSine",
    "easeInOutSine",
    "easeInCirc",
    "easeOutCirc",
    "easeInOutCirc",
    "easeInBack",
    "easeOutBack",
    "easeInOutBack",
    "easeInBounce",
    "easeOutBounce",
    "easeInOutBounce",
    "easeInElastic",
    "easeOutElastic",
    "easeInOutElastic",
    "easeInQuad",
    "easeOutQuad",
    "easeInOutQuad",
    "easeInCubic",
    "easeOutCubic",
    "easeInOutCubic",
    "easeInQuart",
    "easeOutQuart",
    "easeInOutQuart",
    "easeInQuint",
    "easeOutQuint",
    "easeInOutQuint",
    "easeInExpo",
    "easeOutExpo",
    "easeInOutExpo",
    "linear"
  ]

  anime.set('.easing-visualizer div', {
    translateY: anime.stagger([0, -400], {easing: 'linear'})
  })

  anime({
    targets: '.easing-visualizer div',
    translateY: (() => {
      const keyframes = [];
      easings.forEach(function(ease) {
        keyframes.push({
          value: anime.stagger([0, -400], {easing: ease}),
          delay: anime.stagger(.125)
        });
      })
      return keyframes;
    })(),
    translateZ: 0,
    easing: 'easeInOutSine',
    duration: 9000,
    loop: true
  });

  console.log(anime.penner);

</script>
</html>
