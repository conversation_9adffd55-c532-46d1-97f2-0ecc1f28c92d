<template>
  <div class="bg-decoration">
    <!-- 网格背景 -->
    <div class="grid-bg"></div>
    
    <!-- 粒子背景 -->
    <div class="particle-bg" ref="particleContainer"></div>
    
    <!-- 光效装饰 -->
    <div class="light-effects">
      <div class="light-beam light-beam-1"></div>
      <div class="light-beam light-beam-2"></div>
      <div class="light-beam light-beam-3"></div>
    </div>
    
    <!-- 几何装饰 -->
    <div class="geometric-shapes">
      <div class="shape shape-1"></div>
      <div class="shape shape-2"></div>
      <div class="shape shape-3"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const particleContainer = ref(null)

// 初始化粒子效果
const initParticles = () => {
  const container = particleContainer.value
  if (!container) return
  
  // 创建粒子
  for (let i = 0; i < 80; i++) {
    const particle = document.createElement('div')
    particle.className = 'particle'
    particle.style.left = Math.random() * 100 + '%'
    particle.style.top = Math.random() * 100 + '%'
    particle.style.animationDelay = Math.random() * 20 + 's'
    particle.style.animationDuration = (Math.random() * 15 + 10) + 's'
    
    // 随机粒子大小和颜色
    const size = Math.random() * 3 + 1
    particle.style.width = size + 'px'
    particle.style.height = size + 'px'
    
    const colors = ['#00d4ff', '#00ff88', '#ffb800', '#ff6b6b', '#8b5cf6']
    particle.style.background = colors[Math.floor(Math.random() * colors.length)]
    
    container.appendChild(particle)
  }
}

onMounted(() => {
  initParticles()
})
</script>

<style lang="scss" scoped>
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
  overflow: hidden;
  
  .grid-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 120%;
    height: 120%;
    background-image: 
      linear-gradient(rgba(0, 212, 255, 0.08) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 212, 255, 0.08) 1px, transparent 1px);
    background-size: 60px 60px;
    animation: gridMove 30s linear infinite;
  }
  
  .particle-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    
    .particle {
      position: absolute;
      border-radius: 50%;
      animation: float linear infinite;
      opacity: 0.6;
      
      &::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        width: calc(100% + 4px);
        height: calc(100% + 4px);
        background: radial-gradient(circle, currentColor 0%, transparent 70%);
        border-radius: 50%;
        opacity: 0.5;
      }
    }
  }
  
  .light-effects {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    
    .light-beam {
      position: absolute;
      background: linear-gradient(45deg, transparent, rgba(0, 212, 255, 0.1), transparent);
      animation: lightMove 8s ease-in-out infinite;
      
      &.light-beam-1 {
        width: 2px;
        height: 100%;
        left: 20%;
        animation-delay: 0s;
      }
      
      &.light-beam-2 {
        width: 100%;
        height: 2px;
        top: 30%;
        animation-delay: 2s;
        background: linear-gradient(90deg, transparent, rgba(0, 255, 136, 0.1), transparent);
      }
      
      &.light-beam-3 {
        width: 2px;
        height: 100%;
        right: 25%;
        animation-delay: 4s;
        background: linear-gradient(45deg, transparent, rgba(255, 184, 0, 0.1), transparent);
      }
    }
  }
  
  .geometric-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    
    .shape {
      position: absolute;
      border: 1px solid rgba(0, 212, 255, 0.2);
      animation: shapeRotate 20s linear infinite;
      
      &.shape-1 {
        width: 100px;
        height: 100px;
        top: 10%;
        left: 10%;
        border-radius: 50%;
        border-color: rgba(0, 212, 255, 0.3);
      }
      
      &.shape-2 {
        width: 80px;
        height: 80px;
        top: 60%;
        right: 15%;
        transform: rotate(45deg);
        border-color: rgba(0, 255, 136, 0.3);
        animation-direction: reverse;
      }
      
      &.shape-3 {
        width: 60px;
        height: 60px;
        bottom: 20%;
        left: 30%;
        border-radius: 30%;
        border-color: rgba(255, 184, 0, 0.3);
        animation-duration: 15s;
      }
    }
  }
}

// 动画效果
@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(60px, 60px); }
}

@keyframes float {
  0% {
    transform: translateY(100vh) translateX(0) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) translateX(100px) rotate(360deg);
    opacity: 0;
  }
}

@keyframes lightMove {
  0%, 100% {
    opacity: 0;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes shapeRotate {
  0% {
    transform: rotate(0deg) scale(1);
    opacity: 0.3;
  }
  50% {
    opacity: 0.6;
    transform: rotate(180deg) scale(1.1);
  }
  100% {
    transform: rotate(360deg) scale(1);
    opacity: 0.3;
  }
}
</style>
