{"version": 3, "file": "Draggable.min.js", "sources": ["../src/utils/matrix.js", "../src/Draggable.js"], "sourcesContent": ["/*!\n * matrix 3.11.3\n * https://greensock.com\n *\n * Copyright 2008-2022, GreenSock. All rights reserved.\n * Subject to the terms at https://greensock.com/standard-license or for\n * Club GreenSock members, the agreement issued with that membership.\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nlet _doc, _win, _docElement, _body,\t_div<PERSON><PERSON>r, _svg<PERSON><PERSON>r, _identityMatrix, _gEl,\n\t_transformProp = \"transform\",\n\t_transformOriginProp = _transformProp + \"Origin\",\n\t_hasOffsetBug,\n\t_setDoc = element => {\n\t\tlet doc = element.ownerDocument || element;\n\t\tif (!(_transformProp in element.style) && \"msTransform\" in element.style) { //to improve compatibility with old Microsoft browsers\n\t\t\t_transformProp = \"msTransform\";\n\t\t\t_transformOriginProp = _transformProp + \"Origin\";\n\t\t}\n\t\twhile (doc.parentNode && (doc = doc.parentNode)) {\t}\n\t\t_win = window;\n\t\t_identityMatrix = new Matrix2D();\n\t\tif (doc) {\n\t\t\t_doc = doc;\n\t\t\t_docElement = doc.documentElement;\n\t\t\t_body = doc.body;\n\t\t\t_gEl = _doc.createElementNS(\"http://www.w3.org/2000/svg\", \"g\");\n\t\t\t// prevent any existing CSS from transforming it\n\t\t\t_gEl.style.transform = \"none\";\n\t\t\t// now test for the offset reporting bug. Use feature detection instead of browser sniffing to make things more bulletproof and future-proof. Hopefully Safari will fix their bug soon but it's 2020 and it's still not fixed.\n\t\t\tlet d1 = doc.createElement(\"div\"),\n\t\t\t\td2 = doc.createElement(\"div\");\n\t\t\t_body.appendChild(d1);\n\t\t\td1.appendChild(d2);\n\t\t\td1.style.position = \"static\";\n\t\t\td1.style[_transformProp] = \"translate3d(0,0,1px)\";\n\t\t\t_hasOffsetBug = (d2.offsetParent !== d1);\n\t\t\t_body.removeChild(d1);\n\t\t}\n\t\treturn doc;\n\t},\n\t_forceNonZeroScale = e => { // walks up the element's ancestors and finds any that had their scale set to 0 via GSAP, and changes them to 0.0001 to ensure that measurements work. Firefox has a bug that causes it to incorrectly report getBoundingClientRect() when scale is 0.\n\t\tlet a, cache;\n\t\twhile (e && e !== _body) {\n\t\t\tcache = e._gsap;\n\t\t\tcache && cache.uncache && cache.get(e, \"x\"); // force re-parsing of transforms if necessary\n\t\t\tif (cache && !cache.scaleX && !cache.scaleY && cache.renderTransform) {\n\t\t\t\tcache.scaleX = cache.scaleY = 1e-4;\n\t\t\t\tcache.renderTransform(1, cache);\n\t\t\t\ta ? a.push(cache) : (a = [cache]);\n\t\t\t}\n\t\t\te = e.parentNode;\n\t\t}\n\t\treturn a;\n\t},\n\t// possible future addition: pass an element to _forceDisplay() and it'll walk up all its ancestors and make sure anything with display: none is set to display: block, and if there's no parentNode, it'll add it to the body. It returns an Array that you can then feed to _revertDisplay() to have it revert all the changes it made.\n\t// _forceDisplay = e => {\n\t// \tlet a = [],\n\t// \t\tparent;\n\t// \twhile (e && e !== _body) {\n\t// \t\tparent = e.parentNode;\n\t// \t\t(_win.getComputedStyle(e).display === \"none\" || !parent) && a.push(e, e.style.display, parent) && (e.style.display = \"block\");\n\t// \t\tparent || _body.appendChild(e);\n\t// \t\te = parent;\n\t// \t}\n\t// \treturn a;\n\t// },\n\t// _revertDisplay = a => {\n\t// \tfor (let i = 0; i < a.length; i+=3) {\n\t// \t\ta[i+1] ? (a[i].style.display = a[i+1]) : a[i].style.removeProperty(\"display\");\n\t// \t\ta[i+2] || a[i].parentNode.removeChild(a[i]);\n\t// \t}\n\t// },\n\t_svgTemps = [], //we create 3 elements for SVG, and 3 for other DOM elements and cache them for performance reasons. They get nested in _divContainer and _svgContainer so that just one element is added to the DOM on each successive attempt. Again, performance is key.\n\t_divTemps = [],\n\t_getDocScrollTop = () => _win.pageYOffset  || _doc.scrollTop || _docElement.scrollTop || _body.scrollTop || 0,\n\t_getDocScrollLeft = () => _win.pageXOffset || _doc.scrollLeft || _docElement.scrollLeft || _body.scrollLeft || 0,\n\t_svgOwner = element => element.ownerSVGElement || ((element.tagName + \"\").toLowerCase() === \"svg\" ? element : null),\n\t_isFixed = element => {\n\t\tif (_win.getComputedStyle(element).position === \"fixed\") {\n\t\t\treturn true;\n\t\t}\n\t\telement = element.parentNode;\n\t\tif (element && element.nodeType === 1) { // avoid document fragments which will throw an error.\n\t\t\treturn _isFixed(element);\n\t\t}\n\t},\n\t_createSibling = (element, i) => {\n\t\tif (element.parentNode && (_doc || _setDoc(element))) {\n\t\t\tlet svg = _svgOwner(element),\n\t\t\t\tns = svg ? (svg.getAttribute(\"xmlns\") || \"http://www.w3.org/2000/svg\") : \"http://www.w3.org/1999/xhtml\",\n\t\t\t\ttype = svg ? (i ? \"rect\" : \"g\") : \"div\",\n\t\t\t\tx = i !== 2 ? 0 : 100,\n\t\t\t\ty = i === 3 ? 100 : 0,\n\t\t\t\tcss = \"position:absolute;display:block;pointer-events:none;margin:0;padding:0;\",\n\t\t\t\te = _doc.createElementNS ? _doc.createElementNS(ns.replace(/^https/, \"http\"), type) : _doc.createElement(type);\n\t\t\tif (i) {\n\t\t\t\tif (!svg) {\n\t\t\t\t\tif (!_divContainer) {\n\t\t\t\t\t\t_divContainer = _createSibling(element);\n\t\t\t\t\t\t_divContainer.style.cssText = css;\n\t\t\t\t\t}\n\t\t\t\t\te.style.cssText = css + \"width:0.1px;height:0.1px;top:\" + y + \"px;left:\" + x + \"px\";\n\t\t\t\t\t_divContainer.appendChild(e);\n\n\t\t\t\t} else {\n\t\t\t\t\t_svgContainer || (_svgContainer = _createSibling(element));\n\t\t\t\t\te.setAttribute(\"width\", 0.01);\n\t\t\t\t\te.setAttribute(\"height\", 0.01);\n\t\t\t\t\te.setAttribute(\"transform\", \"translate(\" + x + \",\" + y + \")\");\n\t\t\t\t\t_svgContainer.appendChild(e);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn e;\n\t\t}\n\t\tthrow \"Need document and parent.\";\n\t},\n\t_consolidate = m => { // replaces SVGTransformList.consolidate() because a bug in Firefox causes it to break pointer events. See https://greensock.com/forums/topic/23248-touch-is-not-working-on-draggable-in-firefox-windows-v324/?tab=comments#comment-109800\n\t\tlet c = new Matrix2D(),\n\t\t\ti = 0;\n\t\tfor (; i < m.numberOfItems; i++) {\n\t\t\tc.multiply(m.getItem(i).matrix);\n\t\t}\n\t\treturn c;\n\t},\n\t_getCTM = svg => {\n\t\tlet m = svg.getCTM(),\n\t\t\ttransform;\n\t\tif (!m) { // Firefox returns null for getCTM() on root <svg> elements, so this is a workaround using a <g> that we temporarily append.\n\t\t\ttransform = svg.style[_transformProp];\n\t\t\tsvg.style[_transformProp] = \"none\"; // a bug in Firefox causes css transforms to contaminate the getCTM()\n\t\t\tsvg.appendChild(_gEl);\n\t\t\tm = _gEl.getCTM();\n\t\t\tsvg.removeChild(_gEl);\n\t\t\ttransform ? (svg.style[_transformProp] = transform) : svg.style.removeProperty(_transformProp.replace(/([A-Z])/g, \"-$1\").toLowerCase());\n\t\t}\n\t\treturn m || _identityMatrix.clone(); // Firefox will still return null if the <svg> has a width/height of 0 in the browser.\n\t},\n\t_placeSiblings = (element, adjustGOffset) => {\n\t\tlet svg = _svgOwner(element),\n\t\t\tisRootSVG = element === svg,\n\t\t\tsiblings = svg ? _svgTemps : _divTemps,\n\t\t\tparent = element.parentNode,\n\t\t\tcontainer, m, b, x, y, cs;\n\t\tif (element === _win) {\n\t\t\treturn element;\n\t\t}\n\t\tsiblings.length || siblings.push(_createSibling(element, 1), _createSibling(element, 2), _createSibling(element, 3));\n\t\tcontainer = svg ? _svgContainer : _divContainer;\n\t\tif (svg) {\n\t\t\tif (isRootSVG) {\n\t\t\t\tb = _getCTM(element);\n\t\t\t\tx = -b.e / b.a;\n\t\t\t\ty = -b.f / b.d;\n\t\t\t\tm = _identityMatrix;\n\t\t\t} else if (element.getBBox) {\n\t\t\t\tb = element.getBBox();\n\t\t\t\tm = element.transform ? element.transform.baseVal : {}; // IE11 doesn't follow the spec.\n\t\t\t\tm = !m.numberOfItems ? _identityMatrix : m.numberOfItems > 1 ? _consolidate(m) : m.getItem(0).matrix; // don't call m.consolidate().matrix because a bug in Firefox makes pointer events not work when consolidate() is called on the same tick as getBoundingClientRect()! See https://greensock.com/forums/topic/23248-touch-is-not-working-on-draggable-in-firefox-windows-v324/?tab=comments#comment-109800\n\t\t\t\tx = m.a * b.x + m.c * b.y;\n\t\t\t\ty = m.b * b.x + m.d * b.y;\n\t\t\t} else { // may be a <mask> which has no getBBox() so just use defaults instead of throwing errors.\n\t\t\t\tm = new Matrix2D();\n\t\t\t\tx = y = 0;\n\t\t\t}\n\t\t\tif (adjustGOffset && element.tagName.toLowerCase() === \"g\") {\n\t\t\t\tx = y = 0;\n\t\t\t}\n\t\t\t(isRootSVG ? svg : parent).appendChild(container);\n\t\t\tcontainer.setAttribute(\"transform\", \"matrix(\" + m.a + \",\" + m.b + \",\" + m.c + \",\" + m.d + \",\" + (m.e + x) + \",\" + (m.f + y) + \")\");\n\t\t} else {\n\t\t\tx = y = 0;\n\t\t\tif (_hasOffsetBug) { // some browsers (like Safari) have a bug that causes them to misreport offset values. When an ancestor element has a transform applied, it's supposed to treat it as if it's position: relative (new context). Safari botches this, so we need to find the closest ancestor (between the element and its offsetParent) that has a transform applied and if one is found, grab its offsetTop/Left and subtract them to compensate.\n\t\t\t\tm = element.offsetParent;\n\t\t\t\tb = element;\n\t\t\t\twhile (b && (b = b.parentNode) && b !== m && b.parentNode) {\n\t\t\t\t\tif ((_win.getComputedStyle(b)[_transformProp] + \"\").length > 4) {\n\t\t\t\t\t\tx = b.offsetLeft;\n\t\t\t\t\t\ty = b.offsetTop;\n\t\t\t\t\t\tb = 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tcs = _win.getComputedStyle(element);\n\t\t\tif (cs.position !== \"absolute\" && cs.position !== \"fixed\") {\n\t\t\t\tm = element.offsetParent;\n\t\t\t\twhile (parent && parent !== m) { // if there's an ancestor element between the element and its offsetParent that's scrolled, we must factor that in.\n\t\t\t\t\tx += parent.scrollLeft || 0;\n\t\t\t\t\ty += parent.scrollTop || 0;\n\t\t\t\t\tparent = parent.parentNode;\n\t\t\t\t}\n\t\t\t}\n\t\t\tb = container.style;\n\t\t\tb.top = (element.offsetTop - y) + \"px\";\n\t\t\tb.left = (element.offsetLeft - x) + \"px\";\n\t\t\tb[_transformProp] = cs[_transformProp];\n\t\t\tb[_transformOriginProp] = cs[_transformOriginProp];\n\t\t\t// b.border = m.border;\n\t\t\t// b.borderLeftStyle = m.borderLeftStyle;\n\t\t\t// b.borderTopStyle = m.borderTopStyle;\n\t\t\t// b.borderLeftWidth = m.borderLeftWidth;\n\t\t\t// b.borderTopWidth = m.borderTopWidth;\n\t\t\tb.position = cs.position === \"fixed\" ? \"fixed\" : \"absolute\";\n\t\t\telement.parentNode.appendChild(container);\n\t\t}\n\t\treturn container;\n\t},\n\t_setMatrix = (m, a, b, c, d, e, f) => {\n\t\tm.a = a;\n\t\tm.b = b;\n\t\tm.c = c;\n\t\tm.d = d;\n\t\tm.e = e;\n\t\tm.f = f;\n\t\treturn m;\n\t};\n\nexport class Matrix2D {\n\tconstructor(a=1, b=0, c=0, d=1, e=0, f=0) {\n\t\t_setMatrix(this, a, b, c, d, e, f);\n\t}\n\n\tinverse() {\n\t\tlet {a, b, c, d, e, f} = this,\n\t\t\tdeterminant = (a * d - b * c) || 1e-10;\n\t\treturn _setMatrix(\n\t\t\tthis,\n\t\t\td / determinant,\n\t\t\t-b / determinant,\n\t\t\t-c / determinant,\n\t\t\ta / determinant,\n\t\t\t(c * f - d * e) / determinant,\n\t\t\t-(a * f - b * e) / determinant\n\t\t);\n\t}\n\n\tmultiply(matrix) {\n\t\tlet {a, b, c, d, e, f} = this,\n\t\t\ta2 = matrix.a,\n\t\t\tb2 = matrix.c,\n\t\t\tc2 = matrix.b,\n\t\t\td2 = matrix.d,\n\t\t\te2 = matrix.e,\n\t\t\tf2 = matrix.f;\n\t\treturn _setMatrix(this,\n\t\t\ta2 * a + c2 * c,\n\t\t\ta2 * b + c2 * d,\n\t\t\tb2 * a + d2 * c,\n\t\t\tb2 * b + d2 * d,\n\t\t\te + e2 * a + f2 * c,\n\t\t\tf + e2 * b + f2 * d);\n\t}\n\n\tclone() {\n\t\treturn new Matrix2D(this.a, this.b, this.c, this.d, this.e, this.f);\n\t}\n\n\tequals(matrix) {\n\t\tlet {a, b, c, d, e, f} = this;\n\t\treturn (a === matrix.a && b === matrix.b && c === matrix.c && d === matrix.d && e === matrix.e && f === matrix.f);\n\t}\n\n\tapply(point, decoratee={}) {\n\t\tlet {x, y} = point,\n\t\t\t{a, b, c, d, e, f} = this;\n\t\tdecoratee.x = (x * a + y * c + e) || 0;\n\t\tdecoratee.y = (x * b + y * d + f) || 0;\n\t\treturn decoratee;\n\t}\n\n}\n\n// Feed in an element and it'll return a 2D matrix (optionally inverted) so that you can translate between coordinate spaces.\n// Inverting lets you translate a global point into a local coordinate space. No inverting lets you go the other way.\n// We needed this to work around various browser bugs, like Firefox doesn't accurately report getScreenCTM() when there\n// are transforms applied to ancestor elements.\n// The matrix math to convert any x/y coordinate is as follows, which is wrapped in a convenient apply() method of Matrix2D above:\n//     tx = m.a * x + m.c * y + m.e\n//     ty = m.b * x + m.d * y + m.f\nexport function getGlobalMatrix(element, inverse, adjustGOffset, includeScrollInFixed) { // adjustGOffset is typically used only when grabbing an element's PARENT's global matrix, and it ignores the x/y offset of any SVG <g> elements because they behave in a special way.\n\tif (!element || !element.parentNode || (_doc || _setDoc(element)).documentElement === element) {\n\t\treturn new Matrix2D();\n\t}\n\tlet zeroScales = _forceNonZeroScale(element),\n\t\tsvg = _svgOwner(element),\n\t\ttemps = svg ? _svgTemps : _divTemps,\n\t\tcontainer = _placeSiblings(element, adjustGOffset),\n\t\tb1 = temps[0].getBoundingClientRect(),\n\t\tb2 = temps[1].getBoundingClientRect(),\n\t\tb3 = temps[2].getBoundingClientRect(),\n\t\tparent = container.parentNode,\n\t\tisFixed = !includeScrollInFixed && _isFixed(element),\n\t\tm = new Matrix2D(\n\t\t\t(b2.left - b1.left) / 100,\n\t\t\t(b2.top - b1.top) / 100,\n\t\t\t(b3.left - b1.left) / 100,\n\t\t\t(b3.top - b1.top) / 100,\n\t\t\tb1.left + (isFixed ? 0 : _getDocScrollLeft()),\n\t\t\tb1.top + (isFixed ? 0 : _getDocScrollTop())\n\t\t);\n\tparent.removeChild(container);\n\tif (zeroScales) {\n\t\tb1 = zeroScales.length;\n\t\twhile (b1--) {\n\t\t\tb2 = zeroScales[b1];\n\t\t\tb2.scaleX = b2.scaleY = 0;\n\t\t\tb2.renderTransform(1, b2);\n\t\t}\n\t}\n\treturn inverse ? m.inverse() : m;\n}\n\nexport { _getDocScrollTop, _getDocScrollLeft, _setDoc, _isFixed, _getCTM };\n\n// export function getMatrix(element) {\n// \t_doc || _setDoc(element);\n// \tlet m = (_win.getComputedStyle(element)[_transformProp] + \"\").substr(7).match(/[-.]*\\d+[.e\\-+]*\\d*[e\\-\\+]*\\d*/g),\n// \t\tis2D = m && m.length === 6;\n// \treturn !m || m.length < 6 ? new Matrix2D() : new Matrix2D(+m[0], +m[1], +m[is2D ? 2 : 4], +m[is2D ? 3 : 5], +m[is2D ? 4 : 12], +m[is2D ? 5 : 13]);\n// }", "/*!\n * Draggable 3.11.3\n * https://greensock.com\n *\n * @license Copyright 2008-2022, GreenSock. All rights reserved.\n * Subject to the terms at https://greensock.com/standard-license or for\n * Club GreenSock members, the agreement issued with that membership.\n * @author: <PERSON>, <EMAIL>\n */\n/* eslint-disable */\n\nimport { getGlobalMatrix, Matrix2D } from \"./utils/matrix.js\";\n\nlet gsap, _win, _doc, _docElement, _body, _tempDiv, _placeholderDiv, _coreInitted, _checkPrefix, _toArray, _supportsPassive, _isTouchDevice, _touchEventLookup, _isMultiTouching, _isAndroid, InertiaPlugin, _defaultCursor, _supportsPointer,\n\t_dragCount = 0,\n\t_windowExists = () => typeof(window) !== \"undefined\",\n\t_getGSAP = () => gsap || (_windowExists() && (gsap = window.gsap) && gsap.registerPlugin && gsap),\n\t_isFunction = value => typeof(value) === \"function\",\n\t_isObject = value => typeof(value) === \"object\",\n\t_isUndefined = value => typeof(value) === \"undefined\",\n\t_emptyFunc = () => false,\n\t_transformProp = \"transform\",\n\t_transformOriginProp = \"transformOrigin\",\n\t_round = value => Math.round(value * 10000) / 10000,\n\t_isArray = Array.isArray,\n\t_createElement = (type, ns) => {\n\t\tlet e = _doc.createElementNS ? _doc.createElementNS((ns || \"http://www.w3.org/1999/xhtml\").replace(/^https/, \"http\"), type) : _doc.createElement(type); //some servers swap in https for http in the namespace which can break things, making \"style\" inaccessible.\n\t\treturn e.style ? e : _doc.createElement(type); //some environments won't allow access to the element's style when created with a namespace in which case we default to the standard createElement() to work around the issue. Also note that when GSAP is embedded directly inside an SVG file, createElement() won't allow access to the style object in Firefox (see https://greensock.com/forums/topic/20215-problem-using-tweenmax-in-standalone-self-containing-svg-file-err-cannot-set-property-csstext-of-undefined/).\n\t},\n\t_RAD2DEG = 180 / Math.PI,\n\t_bigNum = 1e20,\n\t_identityMatrix = new Matrix2D(),\n\t_getTime = Date.now || (() => new Date().getTime()),\n\t_renderQueue = [],\n\t_lookup = {}, //when a Draggable is created, the target gets a unique _gsDragID property that allows gets associated with the Draggable instance for quick lookups in Draggable.get(). This avoids circular references that could cause gc problems.\n\t_lookupCount = 0,\n\t_clickableTagExp = /^(?:a|input|textarea|button|select)$/i,\n\t_lastDragTime = 0,\n\t_temp1 = {}, // a simple object we reuse and populate (usually x/y properties) to conserve memory and improve performance.\n\t_windowProxy = {}, //memory/performance optimization - we reuse this object during autoScroll to store window-related bounds/offsets.\n\t_copy = (obj, factor) => {\n\t\tlet copy = {}, p;\n\t\tfor (p in obj) {\n\t\t\tcopy[p] = factor ? obj[p] * factor : obj[p];\n\t\t}\n\t\treturn copy;\n\t},\n\t_extend = (obj, defaults) => {\n\t\tfor (let p in defaults) {\n\t\t\tif (!(p in obj)) {\n\t\t\t\tobj[p] = defaults[p];\n\t\t\t}\n\t\t}\n\t\treturn obj;\n\t},\n\t_setTouchActionForAllDescendants = (elements, value) => {\n\t\tlet i = elements.length,\n\t\t\tchildren;\n\t\twhile (i--) {\n\t\t\tvalue ? (elements[i].style.touchAction = value) : elements[i].style.removeProperty(\"touch-action\");\n\t\t\tchildren = elements[i].children;\n\t\t\tchildren && children.length && _setTouchActionForAllDescendants(children, value);\n\t\t}\n\t},\n\t_renderQueueTick = () => _renderQueue.forEach(func => func()),\n\t_addToRenderQueue = func => {\n\t\t_renderQueue.push(func);\n\t\tif (_renderQueue.length === 1) {\n\t\t\tgsap.ticker.add(_renderQueueTick);\n\t\t}\n\t},\n\t_renderQueueTimeout = () => !_renderQueue.length && gsap.ticker.remove(_renderQueueTick),\n\t_removeFromRenderQueue = func => {\n\t\tlet i = _renderQueue.length;\n\t\twhile (i--) {\n\t\t\tif (_renderQueue[i] === func) {\n\t\t\t\t_renderQueue.splice(i, 1);\n\t\t\t}\n\t\t}\n\t\tgsap.to(_renderQueueTimeout, {overwrite:true, delay:15, duration:0, onComplete:_renderQueueTimeout, data:\"_draggable\"}); //remove the \"tick\" listener only after the render queue is empty for 15 seconds (to improve performance). Adding/removing it constantly for every click/touch wouldn't deliver optimal speed, and we also don't want the ticker to keep calling the render method when things are idle for long periods of time (we want to improve battery life on mobile devices).\n\t},\n\t_setDefaults = (obj, defaults) => {\n\t\tfor (let p in defaults) {\n\t\t\tif (!(p in obj)) {\n\t\t\t\tobj[p] = defaults[p];\n\t\t\t}\n\t\t}\n\t\treturn obj;\n\t},\n\t_addListener = (element, type, func, capture) => {\n\t\tif (element.addEventListener) {\n\t\t\tlet touchType = _touchEventLookup[type];\n\t\t\tcapture = capture || (_supportsPassive ? {passive: false} : null);\n\t\t\telement.addEventListener(touchType || type, func, capture);\n\t\t\t(touchType && type !== touchType) && element.addEventListener(type, func, capture);//some browsers actually support both, so must we. But pointer events cover all.\n\t\t}\n\t},\n\t_removeListener = (element, type, func) => {\n\t\tif (element.removeEventListener) {\n\t\t\tlet touchType = _touchEventLookup[type];\n\t\t\telement.removeEventListener(touchType || type, func);\n\t\t\t(touchType && type !== touchType) && element.removeEventListener(type, func);\n\t\t}\n\t},\n\t_preventDefault = event => {\n\t\tevent.preventDefault && event.preventDefault();\n\t\tevent.preventManipulation && event.preventManipulation();  //for some Microsoft browsers\n\t},\n\t_hasTouchID = (list, ID) => {\n\t\tlet i = list.length;\n\t\twhile (i--) {\n\t\t\tif (list[i].identifier === ID) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\t},\n\t_onMultiTouchDocumentEnd = event => {\n\t\t_isMultiTouching = (event.touches && _dragCount < event.touches.length);\n\t\t_removeListener(event.target, \"touchend\", _onMultiTouchDocumentEnd);\n\t},\n\n\t_onMultiTouchDocument = event => {\n\t\t_isMultiTouching = (event.touches && _dragCount < event.touches.length);\n\t\t_addListener(event.target, \"touchend\", _onMultiTouchDocumentEnd);\n\t},\n\t_getDocScrollTop = doc => _win.pageYOffset  || doc.scrollTop || doc.documentElement.scrollTop || doc.body.scrollTop || 0,\n\t_getDocScrollLeft = doc => _win.pageXOffset || doc.scrollLeft || doc.documentElement.scrollLeft || doc.body.scrollLeft || 0,\n\t_addScrollListener = (e, callback) => {\n\t\t_addListener(e, \"scroll\", callback);\n\t\tif (!_isRoot(e.parentNode)) {\n\t\t\t_addScrollListener(e.parentNode, callback);\n\t\t}\n\t},\n\t_removeScrollListener = (e, callback) => {\n\t\t_removeListener(e, \"scroll\", callback);\n\t\tif (!_isRoot(e.parentNode)) {\n\t\t\t_removeScrollListener(e.parentNode, callback);\n\t\t}\n\t},\n\t_isRoot = e => !!(!e || e === _docElement || e.nodeType === 9 || e === _doc.body || e === _win || !e.nodeType || !e.parentNode),\n\t_getMaxScroll = (element, axis) => {\n\t\tlet dim = (axis === \"x\") ? \"Width\" : \"Height\",\n\t\t\tscroll = \"scroll\" + dim,\n\t\t\tclient = \"client\" + dim;\n\t\treturn Math.max(0, _isRoot(element) ? Math.max(_docElement[scroll], _body[scroll]) - (_win[\"inner\" + dim] || _docElement[client] || _body[client]) : element[scroll] - element[client]);\n\t},\n\t_recordMaxScrolls = (e, skipCurrent) => { //records _gsMaxScrollX and _gsMaxScrollY properties for the element and all ancestors up the chain so that we can cap it, otherwise dragging beyond the edges with autoScroll on can endlessly scroll.\n\t\tlet x = _getMaxScroll(e, \"x\"),\n\t\t\ty = _getMaxScroll(e, \"y\");\n\t\tif (_isRoot(e)) {\n\t\t\te = _windowProxy;\n\t\t} else {\n\t\t\t_recordMaxScrolls(e.parentNode, skipCurrent);\n\t\t}\n\t\te._gsMaxScrollX = x;\n\t\te._gsMaxScrollY = y;\n\t\tif (!skipCurrent) {\n\t\t\te._gsScrollX = e.scrollLeft || 0;\n\t\t\te._gsScrollY = e.scrollTop || 0;\n\t\t}\n\t},\n\t_setStyle = (element, property, value) => {\n\t\tlet style = element.style;\n\t\tif (!style) {\n\t\t\treturn;\n\t\t}\n\t\tif (_isUndefined(style[property])) {\n\t\t\tproperty = _checkPrefix(property, element) || property;\n\t\t}\n\t\tif (value == null) {\n\t\t\tstyle.removeProperty && style.removeProperty(property.replace(/([A-Z])/g, \"-$1\").toLowerCase());\n\t\t} else {\n\t\t\tstyle[property] = value;\n\t\t}\n\t},\n\t_getComputedStyle = element => _win.getComputedStyle((element instanceof Element) ? element : element.host || (element.parentNode || {}).host || element), //the \"host\" stuff helps to accommodate ShadowDom objects.\n\n\t_tempRect = {}, //reuse to reduce garbage collection tasks\n\t_parseRect = e => { //accepts a DOM element, a mouse event, or a rectangle object and returns the corresponding rectangle with left, right, width, height, top, and bottom properties\n\t\tif (e === _win) {\n\t\t\t_tempRect.left = _tempRect.top = 0;\n\t\t\t_tempRect.width = _tempRect.right = _docElement.clientWidth || e.innerWidth || _body.clientWidth || 0;\n\t\t\t_tempRect.height = _tempRect.bottom = ((e.innerHeight || 0) - 20 < _docElement.clientHeight) ? _docElement.clientHeight : e.innerHeight || _body.clientHeight || 0;\n\t\t\treturn _tempRect;\n\t\t}\n\t\tlet doc = e.ownerDocument || _doc,\n\t\t\tr = !_isUndefined(e.pageX) ? {left: e.pageX - _getDocScrollLeft(doc), top: e.pageY - _getDocScrollTop(doc), right: e.pageX - _getDocScrollLeft(doc) + 1, bottom: e.pageY - _getDocScrollTop(doc) + 1} : (!e.nodeType && !_isUndefined(e.left) && !_isUndefined(e.top)) ? e : _toArray(e)[0].getBoundingClientRect();\n\t\tif (_isUndefined(r.right) && !_isUndefined(r.width)) {\n\t\t\tr.right = r.left + r.width;\n\t\t\tr.bottom = r.top + r.height;\n\t\t} else if (_isUndefined(r.width)) { //some browsers don't include width and height properties. We can't just set them directly on r because some browsers throw errors, so create a new generic object.\n\t\t\tr = {width: r.right - r.left, height: r.bottom - r.top, right: r.right, left: r.left, bottom: r.bottom, top: r.top};\n\t\t}\n\t\treturn r;\n\t},\n\n\t_dispatchEvent = (target, type, callbackName) => {\n\t\tlet vars = target.vars,\n\t\t\tcallback = vars[callbackName],\n\t\t\tlisteners = target._listeners[type],\n\t\t\tresult;\n\t\tif (_isFunction(callback)) {\n\t\t\tresult = callback.apply(vars.callbackScope || target, vars[callbackName + \"Params\"] || [target.pointerEvent]);\n\t\t}\n\t\tif (listeners && target.dispatchEvent(type) === false) {\n\t\t\tresult = false;\n\t\t}\n\t\treturn result;\n\t},\n\t_getBounds = (target, context) => { //accepts any of the following: a DOM element, jQuery object, selector text, or an object defining bounds as {top, left, width, height} or {minX, maxX, minY, maxY}. Returns an object with left, top, width, and height properties.\n\t\tlet e = _toArray(target)[0],\n\t\t\ttop, left, offset;\n\t\tif (!e.nodeType && e !== _win) {\n\t\t\tif (!_isUndefined(target.left)) {\n\t\t\t\toffset = {x:0, y:0}; //_getOffsetTransformOrigin(context); //the bounds should be relative to the origin\n\t\t\t\treturn {left: target.left - offset.x, top: target.top - offset.y, width: target.width, height: target.height};\n\t\t\t}\n\t\t\tleft = target.min || target.minX || target.minRotation || 0;\n\t\t\ttop = target.min || target.minY || 0;\n\t\t\treturn {left:left, top:top, width:(target.max || target.maxX || target.maxRotation || 0) - left, height:(target.max || target.maxY || 0) - top};\n\t\t}\n\t\treturn _getElementBounds(e, context);\n\t},\n\t_point1 = {}, //we reuse to minimize garbage collection tasks.\n\t_getElementBounds = (element, context) => {\n\t\tcontext = _toArray(context)[0];\n\t\tlet isSVG = (element.getBBox && element.ownerSVGElement),\n\t\t\tdoc = element.ownerDocument || _doc,\n\t\t\tleft, right, top, bottom, matrix, p1, p2, p3, p4, bbox, width, height, cs;\n\t\tif (element === _win) {\n\t\t\ttop = _getDocScrollTop(doc);\n\t\t\tleft = _getDocScrollLeft(doc);\n\t\t\tright = left + (doc.documentElement.clientWidth || element.innerWidth || doc.body.clientWidth || 0);\n\t\t\tbottom = top + (((element.innerHeight || 0) - 20 < doc.documentElement.clientHeight) ? doc.documentElement.clientHeight : element.innerHeight || doc.body.clientHeight || 0); //some browsers (like Firefox) ignore absolutely positioned elements, and collapse the height of the documentElement, so it could be 8px, for example, if you have just an absolutely positioned div. In that case, we use the innerHeight to resolve this.\n\t\t} else if (context === _win || _isUndefined(context)) {\n\t\t\treturn element.getBoundingClientRect();\n\t\t} else {\n\t\t\tleft = top =  0;\n\t\t\tif (isSVG) {\n\t\t\t\tbbox = element.getBBox();\n\t\t\t\twidth = bbox.width;\n\t\t\t\theight = bbox.height;\n\t\t\t} else {\n\t\t\t\tif (element.viewBox && (bbox = element.viewBox.baseVal)) {\n\t\t\t\t\tleft = bbox.x || 0;\n\t\t\t\t\ttop = bbox.y || 0;\n\t\t\t\t\twidth = bbox.width;\n\t\t\t\t\theight = bbox.height;\n\t\t\t\t}\n\t\t\t\tif (!width) {\n\t\t\t\t\tcs = _getComputedStyle(element);\n\t\t\t\t\tbbox = cs.boxSizing === \"border-box\";\n\t\t\t\t\twidth = (parseFloat(cs.width) || element.clientWidth || 0) + (bbox ? 0 : parseFloat(cs.borderLeftWidth) + parseFloat(cs.borderRightWidth));\n\t\t\t\t\theight = (parseFloat(cs.height) || element.clientHeight || 0) + (bbox ? 0 : parseFloat(cs.borderTopWidth) + parseFloat(cs.borderBottomWidth));\n\t\t\t\t}\n\t\t\t}\n\t\t\tright = width;\n\t\t\tbottom = height;\n\t\t}\n\t\tif (element === context) {\n\t\t\treturn {left:left, top:top, width: right - left, height: bottom - top};\n\t\t}\n\t\tmatrix = getGlobalMatrix(context, true).multiply(getGlobalMatrix(element));\n\t\tp1 = matrix.apply({x:left, y:top});\n\t\tp2 = matrix.apply({x:right, y:top});\n\t\tp3 = matrix.apply({x:right, y:bottom});\n\t\tp4 = matrix.apply({x:left, y:bottom});\n\t\tleft = Math.min(p1.x, p2.x, p3.x, p4.x);\n\t\ttop = Math.min(p1.y, p2.y, p3.y, p4.y);\n\t\treturn {left: left, top: top, width: Math.max(p1.x, p2.x, p3.x, p4.x) - left, height: Math.max(p1.y, p2.y, p3.y, p4.y) - top};\n\t},\n\t_parseInertia = (draggable, snap, max, min, factor, forceZeroVelocity) => {\n\t\tlet vars = {},\n\t\t\ta, i, l;\n\t\tif (snap) {\n\t\t\tif (factor !== 1 && snap instanceof Array) { //some data must be altered to make sense, like if the user passes in an array of rotational values in degrees, we must convert it to radians. Or for scrollLeft and scrollTop, we invert the values.\n\t\t\t\tvars.end = a = [];\n\t\t\t\tl = snap.length;\n\t\t\t\tif (_isObject(snap[0])) { //if the array is populated with objects, like points ({x:100, y:200}), make copies before multiplying by the factor, otherwise we'll mess up the originals and the user may reuse it elsewhere.\n\t\t\t\t\tfor (i = 0; i < l; i++) {\n\t\t\t\t\t\ta[i] = _copy(snap[i], factor);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tfor (i = 0; i < l; i++) {\n\t\t\t\t\t\ta[i] = snap[i] * factor;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tmax += 1.1; //allow 1.1 pixels of wiggle room when snapping in order to work around some browser inconsistencies in the way bounds are reported which can make them roughly a pixel off. For example, if \"snap:[-$('#menu').width(), 0]\" was defined and #menu had a wrapper that was used as the bounds, some browsers would be one pixel off, making the minimum -752 for example when snap was [-753,0], thus instead of snapping to -753, it would snap to 0 since -753 was below the minimum.\n\t\t\t\tmin -= 1.1;\n\t\t\t} else if (_isFunction(snap)) {\n\t\t\t\tvars.end = value => {\n\t\t\t\t\tlet result = snap.call(draggable, value),\n\t\t\t\t\t\tcopy, p;\n\t\t\t\t\tif (factor !== 1) {\n\t\t\t\t\t\tif (_isObject(result)) {\n\t\t\t\t\t\t\tcopy = {};\n\t\t\t\t\t\t\tfor (p in result) {\n\t\t\t\t\t\t\t\tcopy[p] = result[p] * factor;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tresult = copy;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tresult *= factor;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\treturn result; //we need to ensure that we can scope the function call to the Draggable instance itself so that users can access important values like maxX, minX, maxY, minY, x, and y from within that function.\n\t\t\t\t};\n\t\t\t} else {\n\t\t\t\tvars.end = snap;\n\t\t\t}\n\t\t}\n\t\tif (max || max === 0) {\n\t\t\tvars.max = max;\n\t\t}\n\t\tif (min || min === 0) {\n\t\t\tvars.min = min;\n\t\t}\n\t\tif (forceZeroVelocity) {\n\t\t\tvars.velocity = 0;\n\t\t}\n\t\treturn vars;\n\t},\n\t_isClickable = element => { //sometimes it's convenient to mark an element as clickable by adding a data-clickable=\"true\" attribute (in which case we won't preventDefault() the mouse/touch event). This method checks if the element is an <a>, <input>, or <button> or has an onclick or has the data-clickable or contentEditable attribute set to true (or any of its parent elements).\n\t\tlet data;\n\t\treturn (!element || !element.getAttribute || element === _body) ? false : ((data = element.getAttribute(\"data-clickable\")) === \"true\" || (data !== \"false\" && (element.onclick || _clickableTagExp.test(element.nodeName + \"\") || element.getAttribute(\"contentEditable\") === \"true\"))) ? true : _isClickable(element.parentNode);\n\t},\n\t_setSelectable = (elements, selectable) => {\n\t\tlet i = elements.length,\n\t\t\te;\n\t\twhile (i--) {\n\t\t\te = elements[i];\n\t\t\te.ondragstart = e.onselectstart = selectable ? null : _emptyFunc;\n\t\t\tgsap.set(e, {lazy:true, userSelect: (selectable ? \"text\" : \"none\")});\n\t\t}\n\t},\n\t_isFixed = element => {\n\t\tif (_getComputedStyle(element).position === \"fixed\") {\n\t\t\treturn true;\n\t\t}\n\t\telement = element.parentNode;\n\t\tif (element && element.nodeType === 1) { // avoid document fragments which will throw an error.\n\t\t\treturn _isFixed(element);\n\t\t}\n\t},\n\t_supports3D, _addPaddingBR,\n\n\t//The ScrollProxy class wraps an element's contents into another div (we call it \"content\") that we either add padding when necessary or apply a translate3d() transform in order to overscroll (scroll past the boundaries). This allows us to simply set the scrollTop/scrollLeft (or top/left for easier reverse-axis orientation, which is what we do in Draggable) and it'll do all the work for us. For example, if we tried setting scrollTop to -100 on a normal DOM element, it wouldn't work - it'd look the same as setting it to 0, but if we set scrollTop of a ScrollProxy to -100, it'll give the correct appearance by either setting paddingTop of the wrapper to 100 or applying a 100-pixel translateY.\n\tScrollProxy = function(element, vars) {\n\t\telement = gsap.utils.toArray(element)[0];\n\t\tvars = vars || {};\n\t\tlet content = document.createElement(\"div\"),\n\t\t\tstyle = content.style,\n\t\t\tnode = element.firstChild,\n\t\t\toffsetTop = 0,\n\t\t\toffsetLeft = 0,\n\t\t\tprevTop = element.scrollTop,\n\t\t\tprevLeft = element.scrollLeft,\n\t\t\tscrollWidth = element.scrollWidth,\n\t\t\tscrollHeight = element.scrollHeight,\n\t\t\textraPadRight = 0,\n\t\t\tmaxLeft = 0,\n\t\t\tmaxTop = 0,\n\t\t\telementWidth, elementHeight, contentHeight, nextNode, transformStart, transformEnd;\n\t\tif (_supports3D && vars.force3D !== false) {\n\t\t\ttransformStart = \"translate3d(\";\n\t\t\ttransformEnd = \"px,0px)\";\n\t\t} else if (_transformProp) {\n\t\t\ttransformStart = \"translate(\";\n\t\t\ttransformEnd = \"px)\";\n\t\t}\n\t\tthis.scrollTop = function(value, force) {\n\t\t\tif (!arguments.length) {\n\t\t\t\treturn -this.top();\n\t\t\t}\n\t\t\tthis.top(-value, force);\n\t\t};\n\t\tthis.scrollLeft = function(value, force) {\n\t\t\tif (!arguments.length) {\n\t\t\t\treturn -this.left();\n\t\t\t}\n\t\t\tthis.left(-value, force);\n\t\t};\n\t\tthis.left = function(value, force) {\n\t\t\tif (!arguments.length) {\n\t\t\t\treturn -(element.scrollLeft + offsetLeft);\n\t\t\t}\n\t\t\tlet dif = element.scrollLeft - prevLeft,\n\t\t\t\toldOffset = offsetLeft;\n\t\t\tif ((dif > 2 || dif < -2) && !force) { //if the user interacts with the scrollbar (or something else scrolls it, like the mouse wheel), we should kill any tweens of the ScrollProxy.\n\t\t\t\tprevLeft = element.scrollLeft;\n\t\t\t\tgsap.killTweensOf(this, {left:1, scrollLeft:1});\n\t\t\t\tthis.left(-prevLeft);\n\t\t\t\tif (vars.onKill) {\n\t\t\t\t\tvars.onKill();\n\t\t\t\t}\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tvalue = -value; //invert because scrolling works in the opposite direction\n\t\t\tif (value < 0) {\n\t\t\t\toffsetLeft = (value - 0.5) | 0;\n\t\t\t\tvalue = 0;\n\t\t\t} else if (value > maxLeft) {\n\t\t\t\toffsetLeft = (value - maxLeft) | 0;\n\t\t\t\tvalue = maxLeft;\n\t\t\t} else {\n\t\t\t\toffsetLeft = 0;\n\t\t\t}\n\t\t\tif (offsetLeft || oldOffset) {\n\t\t\t\tif (!this._skip) {\n\t\t\t\t\tstyle[_transformProp] = transformStart + -offsetLeft + \"px,\" + -offsetTop + transformEnd;\n\t\t\t\t}\n\t\t\t\tif (offsetLeft + extraPadRight >= 0) {\n\t\t\t\t\tstyle.paddingRight =  offsetLeft + extraPadRight + \"px\";\n\t\t\t\t}\n\t\t\t}\n\t\t\telement.scrollLeft = value | 0;\n\t\t\tprevLeft = element.scrollLeft; //don't merge this with the line above because some browsers adjust the scrollLeft after it's set, so in order to be 100% accurate in tracking it, we need to ask the browser to report it.\n\t\t};\n\t\tthis.top = function(value, force) {\n\t\t\tif (!arguments.length) {\n\t\t\t\treturn -(element.scrollTop + offsetTop);\n\t\t\t}\n\t\t\tlet dif = element.scrollTop - prevTop,\n\t\t\t\toldOffset = offsetTop;\n\t\t\tif ((dif > 2 || dif < -2) && !force) { //if the user interacts with the scrollbar (or something else scrolls it, like the mouse wheel), we should kill any tweens of the ScrollProxy.\n\t\t\t\tprevTop = element.scrollTop;\n\t\t\t\tgsap.killTweensOf(this, {top:1, scrollTop:1});\n\t\t\t\tthis.top(-prevTop);\n\t\t\t\tif (vars.onKill) {\n\t\t\t\t\tvars.onKill();\n\t\t\t\t}\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tvalue = -value; //invert because scrolling works in the opposite direction\n\t\t\tif (value < 0) {\n\t\t\t\toffsetTop = (value - 0.5) | 0;\n\t\t\t\tvalue = 0;\n\t\t\t} else if (value > maxTop) {\n\t\t\t\toffsetTop = (value - maxTop) | 0;\n\t\t\t\tvalue = maxTop;\n\t\t\t} else {\n\t\t\t\toffsetTop = 0;\n\t\t\t}\n\t\t\tif (offsetTop || oldOffset) {\n\t\t\t\tif (!this._skip) {\n\t\t\t\t\tstyle[_transformProp] = transformStart + -offsetLeft + \"px,\" + -offsetTop + transformEnd;\n\t\t\t\t}\n\t\t\t}\n\t\t\telement.scrollTop = value | 0;\n\t\t\tprevTop = element.scrollTop;\n\t\t};\n\n\t\tthis.maxScrollTop = () => maxTop;\n\t\tthis.maxScrollLeft = () => maxLeft;\n\n\t\tthis.disable = function() {\n\t\t\tnode = content.firstChild;\n\t\t\twhile (node) {\n\t\t\t\tnextNode = node.nextSibling;\n\t\t\t\telement.appendChild(node);\n\t\t\t\tnode = nextNode;\n\t\t\t}\n\t\t\tif (element === content.parentNode) { //in case disable() is called when it's already disabled.\n\t\t\t\telement.removeChild(content);\n\t\t\t}\n\t\t};\n\t\tthis.enable = function() {\n\t\t\tnode = element.firstChild;\n\t\t\tif (node === content) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\twhile (node) {\n\t\t\t\tnextNode = node.nextSibling;\n\t\t\t\tcontent.appendChild(node);\n\t\t\t\tnode = nextNode;\n\t\t\t}\n\t\t\telement.appendChild(content);\n\t\t\tthis.calibrate();\n\t\t};\n\t\tthis.calibrate = function(force) {\n\t\t\tlet widthMatches = (element.clientWidth === elementWidth),\n\t\t\t\tcs, x, y;\n\t\t\tprevTop = element.scrollTop;\n\t\t\tprevLeft = element.scrollLeft;\n\t\t\tif (widthMatches && element.clientHeight === elementHeight && content.offsetHeight === contentHeight && scrollWidth === element.scrollWidth && scrollHeight === element.scrollHeight && !force) {\n\t\t\t\treturn; //no need to recalculate things if the width and height haven't changed.\n\t\t\t}\n\t\t\tif (offsetTop || offsetLeft) {\n\t\t\t\tx = this.left();\n\t\t\t\ty = this.top();\n\t\t\t\tthis.left(-element.scrollLeft);\n\t\t\t\tthis.top(-element.scrollTop);\n\t\t\t}\n\t\t\tcs = _getComputedStyle(element);\n\t\t\t//first, we need to remove any width constraints to see how the content naturally flows so that we can see if it's wider than the containing element. If so, we've got to record the amount of overage so that we can apply that as padding in order for browsers to correctly handle things. Then we switch back to a width of 100% (without that, some browsers don't flow the content correctly)\n\t\t\tif (!widthMatches || force) {\n\t\t\t\tstyle.display = \"block\";\n\t\t\t\tstyle.width = \"auto\";\n\t\t\t\tstyle.paddingRight = \"0px\";\n\t\t\t\textraPadRight = Math.max(0, element.scrollWidth - element.clientWidth);\n\t\t\t\t//if the content is wider than the container, we need to add the paddingLeft and paddingRight in order for things to behave correctly.\n\t\t\t\tif (extraPadRight) {\n\t\t\t\t\textraPadRight += parseFloat(cs.paddingLeft) + (_addPaddingBR ? parseFloat(cs.paddingRight) : 0);\n\t\t\t\t}\n\t\t\t}\n\t\t\tstyle.display = \"inline-block\";\n\t\t\tstyle.position = \"relative\";\n\t\t\tstyle.overflow = \"visible\";\n\t\t\tstyle.verticalAlign = \"top\";\n\t\t\tstyle.boxSizing = \"content-box\";\n\t\t\tstyle.width = \"100%\";\n\t\t\tstyle.paddingRight = extraPadRight + \"px\";\n\t\t\t//some browsers neglect to factor in the bottom padding when calculating the scrollHeight, so we need to add that padding to the content when that happens. Allow a 2px margin for error\n\t\t\tif (_addPaddingBR) {\n\t\t\t\tstyle.paddingBottom = cs.paddingBottom;\n\t\t\t}\n\t\t\telementWidth = element.clientWidth;\n\t\t\telementHeight = element.clientHeight;\n\t\t\tscrollWidth = element.scrollWidth;\n\t\t\tscrollHeight = element.scrollHeight;\n\t\t\tmaxLeft = element.scrollWidth - elementWidth;\n\t\t\tmaxTop = element.scrollHeight - elementHeight;\n\t\t\tcontentHeight = content.offsetHeight;\n\t\t\tstyle.display = \"block\";\n\t\t\tif (x || y) {\n\t\t\t\tthis.left(x);\n\t\t\t\tthis.top(y);\n\t\t\t}\n\t\t};\n\t\tthis.content = content;\n\t\tthis.element = element;\n\t\tthis._skip = false;\n\t\tthis.enable();\n\t},\n\t_initCore = required => {\n\t\tif (_windowExists() && document.body) {\n\t\t\tlet nav = window && window.navigator;\n\t\t\t_win = window;\n\t\t\t_doc = document;\n\t\t\t_docElement = _doc.documentElement;\n\t\t\t_body = _doc.body;\n\t\t\t_tempDiv = _createElement(\"div\");\n\t\t\t_supportsPointer = !!window.PointerEvent;\n\t\t\t_placeholderDiv = _createElement(\"div\");\n\t\t\t_placeholderDiv.style.cssText = \"visibility:hidden;height:1px;top:-1px;pointer-events:none;position:relative;clear:both;cursor:grab\";\n\t\t\t_defaultCursor = _placeholderDiv.style.cursor === \"grab\" ? \"grab\" : \"move\";\n\t\t\t_isAndroid = (nav && nav.userAgent.toLowerCase().indexOf(\"android\") !== -1); //Android handles touch events in an odd way and it's virtually impossible to \"feature test\" so we resort to UA sniffing\n\t\t\t_isTouchDevice = ((\"ontouchstart\" in _docElement) && (\"orientation\" in _win)) || (nav && (nav.MaxTouchPoints > 0 || nav.msMaxTouchPoints > 0));\n\t\t\t_addPaddingBR = (function() { //this function is in charge of analyzing browser behavior related to padding. It sets the _addPaddingBR to true if the browser doesn't normally factor in the bottom or right padding on the element inside the scrolling area, and it sets _addPaddingLeft to true if it's a browser that requires the extra offset (offsetLeft) to be added to the paddingRight (like Opera).\n\t\t\t\tlet div = _createElement(\"div\"),\n\t\t\t\t\tchild = _createElement(\"div\"),\n\t\t\t\t\tchildStyle = child.style,\n\t\t\t\t\tparent = _body,\n\t\t\t\t\tval;\n\t\t\t\tchildStyle.display = \"inline-block\";\n\t\t\t\tchildStyle.position = \"relative\";\n\t\t\t\tdiv.style.cssText = \"width:90px;height:40px;padding:10px;overflow:auto;visibility:hidden\";\n\t\t\t\tdiv.appendChild(child);\n\t\t\t\tparent.appendChild(div);\n\t\t\t\tval = (child.offsetHeight + 18 > div.scrollHeight); //div.scrollHeight should be child.offsetHeight + 20 because of the 10px of padding on each side, but some browsers ignore one side. We allow a 2px margin of error.\n\t\t\t\tparent.removeChild(div);\n\t\t\t\treturn val;\n\t\t\t}());\n\t\t\t_touchEventLookup = (function(types) { //we create an object that makes it easy to translate touch event types into their \"pointer\" counterparts if we're in a browser that uses those instead. Like IE10 uses \"MSPointerDown\" instead of \"touchstart\", for example.\n\t\t\t\tlet standard = types.split(\",\"),\n\t\t\t\t\tconverted = (\"onpointerdown\" in _tempDiv ? \"pointerdown,pointermove,pointerup,pointercancel\" : \"onmspointerdown\" in _tempDiv ? \"MSPointerDown,MSPointerMove,MSPointerUp,MSPointerCancel\" : types).split(\",\"),\n\t\t\t\t\tobj = {},\n\t\t\t\t\ti = 4;\n\t\t\t\twhile (--i > -1) {\n\t\t\t\t\tobj[standard[i]] = converted[i];\n\t\t\t\t\tobj[converted[i]] = standard[i];\n\t\t\t\t}\n\t\t\t\t//to avoid problems in iOS 9, test to see if the browser supports the \"passive\" option on addEventListener().\n\t\t\t\ttry {\n\t\t\t\t\t_docElement.addEventListener(\"test\", null, Object.defineProperty({}, \"passive\", {\n\t\t\t\t\t\tget: function () {\n\t\t\t\t\t\t\t_supportsPassive = 1;\n\t\t\t\t\t\t}\n\t\t\t\t\t}));\n\t\t\t\t} catch (e) {}\n\t\t\t\treturn obj;\n\t\t\t}(\"touchstart,touchmove,touchend,touchcancel\"));\n\t\t\t_addListener(_doc, \"touchcancel\", _emptyFunc); //some older Android devices intermittently stop dispatching \"touchmove\" events if we don't listen for \"touchcancel\" on the document. Very strange indeed.\n\t\t\t_addListener(_win, \"touchmove\", _emptyFunc); //works around Safari bugs that still allow the page to scroll even when we preventDefault() on the touchmove event.\n\t\t\t_body && _body.addEventListener(\"touchstart\", _emptyFunc); //works around Safari bug: https://greensock.com/forums/topic/21450-draggable-in-iframe-on-mobile-is-buggy/\n\t\t\t_addListener(_doc, \"contextmenu\", function() {\n\t\t\t\tfor (let p in _lookup) {\n\t\t\t\t\tif (_lookup[p].isPressed) {\n\t\t\t\t\t\t_lookup[p].endDrag();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t\tgsap = _coreInitted = _getGSAP();\n\t\t}\n\t\tif (gsap) {\n\t\t\tInertiaPlugin = gsap.plugins.inertia;\n\t\t\t_checkPrefix = gsap.utils.checkPrefix;\n\t\t\t_transformProp = _checkPrefix(_transformProp);\n\t\t\t_transformOriginProp = _checkPrefix(_transformOriginProp);\n\t\t\t_toArray = gsap.utils.toArray;\n\t\t\t_supports3D = !!_checkPrefix(\"perspective\");\n\t\t} else if (required) {\n\t\t\tconsole.warn(\"Please gsap.registerPlugin(Draggable)\");\n\t\t}\n\t};\n\n\n\n\n\n\nclass EventDispatcher {\n\n\tconstructor(target) {\n\t\tthis._listeners = {};\n\t\tthis.target = target || this;\n\t}\n\n\taddEventListener(type, callback) {\n\t\tlet list = this._listeners[type] || (this._listeners[type] = []);\n\t\tif (!~list.indexOf(callback)) {\n\t\t\tlist.push(callback);\n\t\t}\n\t}\n\n\tremoveEventListener(type, callback) {\n\t\tlet list = this._listeners[type],\n\t\t\ti = (list && list.indexOf(callback));\n\t\t(i >= 0) && list.splice(i, 1);\n\t}\n\n\tdispatchEvent(type) {\n\t\tlet result;\n\t\t(this._listeners[type] || []).forEach(callback => (callback.call(this, {type: type, target: this.target}) === false) && (result = false));\n\t\treturn result; //if any of the callbacks return false, pass that along.\n\t}\n}\n\n\n\n\n\n\n\n\n\nexport class Draggable extends EventDispatcher {\n\n\tconstructor(target, vars) {\n\t\tsuper();\n\t\t_coreInitted || _initCore(1);\n\t\ttarget = _toArray(target)[0]; //in case the target is a selector object or selector text\n\t\tif (!InertiaPlugin) {\n\t\t\tInertiaPlugin = gsap.plugins.inertia;\n\t\t}\n\t\tthis.vars = vars = _copy(vars || {});\n\t\tthis.target = target;\n\t\tthis.x = this.y = this.rotation = 0;\n\t\tthis.dragResistance = parseFloat(vars.dragResistance) || 0;\n\t\tthis.edgeResistance = isNaN(vars.edgeResistance) ? 1 : parseFloat(vars.edgeResistance) || 0;\n\t\tthis.lockAxis = vars.lockAxis;\n\t\tthis.autoScroll = vars.autoScroll || 0;\n\t\tthis.lockedAxis = null;\n\t\tthis.allowEventDefault = !!vars.allowEventDefault;\n\n\t\tgsap.getProperty(target, \"x\"); // to ensure that transforms are instantiated.\n\n\t\tlet type = (vars.type || \"x,y\").toLowerCase(),\n\t\t\txyMode = (~type.indexOf(\"x\") || ~type.indexOf(\"y\")),\n\t\t\trotationMode = (type.indexOf(\"rotation\") !== -1),\n\t\t\txProp = rotationMode ? \"rotation\" : xyMode ? \"x\" : \"left\",\n\t\t\tyProp = xyMode ? \"y\" : \"top\",\n\t\t\tallowX = !!(~type.indexOf(\"x\") || ~type.indexOf(\"left\") || type === \"scroll\"),\n\t\t\tallowY = !!(~type.indexOf(\"y\") || ~type.indexOf(\"top\") || type === \"scroll\"),\n\t\t\tminimumMovement = vars.minimumMovement || 2,\n\t\t\tself = this,\n\t\t\ttriggers = _toArray(vars.trigger || vars.handle || target),\n\t\t\tkillProps = {},\n\t\t\tdragEndTime = 0,\n\t\t\tcheckAutoScrollBounds = false,\n\t\t\tautoScrollMarginTop = vars.autoScrollMarginTop || 40,\n\t\t\tautoScrollMarginRight = vars.autoScrollMarginRight || 40,\n\t\t\tautoScrollMarginBottom = vars.autoScrollMarginBottom || 40,\n\t\t\tautoScrollMarginLeft = vars.autoScrollMarginLeft || 40,\n\t\t\tisClickable = vars.clickableTest || _isClickable,\n\t\t\tclickTime = 0,\n\t\t\tgsCache = target._gsap || gsap.core.getCache(target),\n\t\t\tisFixed = _isFixed(target),\n\t\t\tgetPropAsNum = (property, unit) => parseFloat(gsCache.get(target, property, unit)),\n\t\t\townerDoc = target.ownerDocument || _doc,\n\t\t\tenabled, scrollProxy, startPointerX, startPointerY, startElementX, startElementY, hasBounds, hasDragCallback, hasMoveCallback, maxX, minX, maxY, minY, touch, touchID, rotationOrigin, dirty, old, snapX, snapY, snapXY, isClicking, touchEventTarget, matrix, interrupted, allowNativeTouchScrolling, touchDragAxis, isDispatching, clickDispatch, trustedClickDispatch, isPreventingDefault, innerMatrix, dragged,\n\n\t\t\tonContextMenu = e => { //used to prevent long-touch from triggering a context menu.\n\t\t\t\t// (self.isPressed && e.which < 2) && self.endDrag() // previously ended drag when context menu was triggered, but instead we should just stop propagation and prevent the default event behavior.\n\t\t\t\t_preventDefault(e);\n\t\t\t\te.stopImmediatePropagation && e.stopImmediatePropagation();\n\t\t\t\treturn false;\n\t\t\t},\n\n\t\t\t//this method gets called on every tick of TweenLite.ticker which allows us to synchronize the renders to the core engine (which is typically synchronized with the display refresh via requestAnimationFrame). This is an optimization - it's better than applying the values inside the \"mousemove\" or \"touchmove\" event handler which may get called many times inbetween refreshes.\n\t\t\trender = suppressEvents => {\n\t\t\t\tif (self.autoScroll && self.isDragging && (checkAutoScrollBounds || dirty)) {\n\t\t\t\t\tlet e = target,\n\t\t\t\t\t\tautoScrollFactor = self.autoScroll * 15, //multiplying by 15 just gives us a better \"feel\" speed-wise.\n\t\t\t\t\t\tparent, isRoot, rect, pointerX, pointerY, changeX, changeY, gap;\n\t\t\t\t\tcheckAutoScrollBounds = false;\n\t\t\t\t\t_windowProxy.scrollTop = ((_win.pageYOffset != null) ? _win.pageYOffset : (ownerDoc.documentElement.scrollTop != null) ? ownerDoc.documentElement.scrollTop : ownerDoc.body.scrollTop);\n\t\t\t\t\t_windowProxy.scrollLeft = ((_win.pageXOffset != null) ? _win.pageXOffset : (ownerDoc.documentElement.scrollLeft != null) ? ownerDoc.documentElement.scrollLeft : ownerDoc.body.scrollLeft);\n\t\t\t\t\tpointerX = self.pointerX - _windowProxy.scrollLeft;\n\t\t\t\t\tpointerY = self.pointerY - _windowProxy.scrollTop;\n\t\t\t\t\twhile (e && !isRoot) { //walk up the chain and sense wherever the pointer is within 40px of an edge that's scrollable.\n\t\t\t\t\t\tisRoot = _isRoot(e.parentNode);\n\t\t\t\t\t\tparent = isRoot ? _windowProxy : e.parentNode;\n\t\t\t\t\t\trect = isRoot ? {bottom:Math.max(_docElement.clientHeight, _win.innerHeight || 0), right: Math.max(_docElement.clientWidth, _win.innerWidth || 0), left:0, top:0} : parent.getBoundingClientRect();\n\t\t\t\t\t\tchangeX = changeY = 0;\n\t\t\t\t\t\tif (allowY) {\n\t\t\t\t\t\t\tgap = parent._gsMaxScrollY - parent.scrollTop;\n\t\t\t\t\t\t\tif (gap < 0) {\n\t\t\t\t\t\t\t\tchangeY = gap;\n\t\t\t\t\t\t\t} else if (pointerY > rect.bottom - autoScrollMarginBottom && gap) {\n\t\t\t\t\t\t\t\tcheckAutoScrollBounds = true;\n\t\t\t\t\t\t\t\tchangeY = Math.min(gap, (autoScrollFactor * (1 - Math.max(0, (rect.bottom - pointerY)) / autoScrollMarginBottom)) | 0);\n\t\t\t\t\t\t\t} else if (pointerY < rect.top + autoScrollMarginTop && parent.scrollTop) {\n\t\t\t\t\t\t\t\tcheckAutoScrollBounds = true;\n\t\t\t\t\t\t\t\tchangeY = -Math.min(parent.scrollTop, (autoScrollFactor * (1 - Math.max(0, (pointerY - rect.top)) / autoScrollMarginTop)) | 0);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (changeY) {\n\t\t\t\t\t\t\t\tparent.scrollTop += changeY;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (allowX) {\n\t\t\t\t\t\t\tgap = parent._gsMaxScrollX - parent.scrollLeft;\n\t\t\t\t\t\t\tif (gap < 0) {\n\t\t\t\t\t\t\t\tchangeX = gap;\n\t\t\t\t\t\t\t} else if (pointerX > rect.right - autoScrollMarginRight && gap) {\n\t\t\t\t\t\t\t\tcheckAutoScrollBounds = true;\n\t\t\t\t\t\t\t\tchangeX = Math.min(gap, (autoScrollFactor * (1 - Math.max(0, (rect.right - pointerX)) / autoScrollMarginRight)) | 0);\n\t\t\t\t\t\t\t} else if (pointerX < rect.left + autoScrollMarginLeft && parent.scrollLeft) {\n\t\t\t\t\t\t\t\tcheckAutoScrollBounds = true;\n\t\t\t\t\t\t\t\tchangeX = -Math.min(parent.scrollLeft, (autoScrollFactor * (1 - Math.max(0, (pointerX - rect.left)) / autoScrollMarginLeft)) | 0);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (changeX) {\n\t\t\t\t\t\t\t\tparent.scrollLeft += changeX;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (isRoot && (changeX || changeY)) {\n\t\t\t\t\t\t\t_win.scrollTo(parent.scrollLeft, parent.scrollTop);\n\t\t\t\t\t\t\tsetPointerPosition(self.pointerX + changeX, self.pointerY + changeY);\n\t\t\t\t\t\t}\n\t\t\t\t\t\te = parent;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (dirty) {\n\t\t\t\t\tlet {x, y} = self;\n\t\t\t\t\tif (rotationMode) {\n\t\t\t\t\t\tself.deltaX = x - parseFloat(gsCache.rotation);\n\t\t\t\t\t\tself.rotation = x;\n\t\t\t\t\t\tgsCache.rotation = x + \"deg\";\n\t\t\t\t\t\tgsCache.renderTransform(1, gsCache);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tif (scrollProxy) {\n\t\t\t\t\t\t\tif (allowY) {\n\t\t\t\t\t\t\t\tself.deltaY = y - scrollProxy.top();\n\t\t\t\t\t\t\t\tscrollProxy.top(y);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (allowX) {\n\t\t\t\t\t\t\t\tself.deltaX = x - scrollProxy.left();\n\t\t\t\t\t\t\t\tscrollProxy.left(x);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else if (xyMode) {\n\t\t\t\t\t\t\tif (allowY) {\n\t\t\t\t\t\t\t\tself.deltaY = y - parseFloat(gsCache.y);\n\t\t\t\t\t\t\t\tgsCache.y = y + \"px\";\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (allowX) {\n\t\t\t\t\t\t\t\tself.deltaX = x - parseFloat(gsCache.x);\n\t\t\t\t\t\t\t\tgsCache.x = x + \"px\";\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tgsCache.renderTransform(1, gsCache);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tif (allowY) {\n\t\t\t\t\t\t\t\tself.deltaY = y - parseFloat(target.style.top || 0);\n\t\t\t\t\t\t\t\ttarget.style.top = y + \"px\";\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (allowX) {\n\t\t\t\t\t\t\t\tself.deltaX = x - parseFloat(target.style.left || 0);\n\t\t\t\t\t\t\t\ttarget.style.left = x + \"px\";\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif (hasDragCallback && !suppressEvents && !isDispatching) {\n\t\t\t\t\t\tisDispatching = true; //in case onDrag has an update() call (avoid endless loop)\n\t\t\t\t\t\tif (_dispatchEvent(self, \"drag\", \"onDrag\") === false) {\n\t\t\t\t\t\t\tif (allowX) {\n\t\t\t\t\t\t\t\tself.x -= self.deltaX;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (allowY) {\n\t\t\t\t\t\t\t\tself.y -= self.deltaY;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\trender(true);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tisDispatching = false;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tdirty = false;\n\t\t\t},\n\n\t\t\t//copies the x/y from the element (whether that be transforms, top/left, or ScrollProxy's top/left) to the Draggable's x and y (and rotation if necessary) properties so that they reflect reality and it also (optionally) applies any snapping necessary. This is used by the InertiaPlugin tween in an onUpdate to ensure things are synced and snapped.\n\t\t\tsyncXY = (skipOnUpdate, skipSnap) => {\n\t\t\t\tlet { x, y } = self,\n\t\t\t\t\tsnappedValue, cs;\n\t\t\t\tif (!target._gsap) { //just in case the _gsap cache got wiped, like if the user called clearProps on the transform or something (very rare).\n\t\t\t\t\tgsCache = gsap.core.getCache(target);\n\t\t\t\t}\n\t\t\t\tgsCache.uncache && gsap.getProperty(target, \"x\"); // trigger a re-cache\n\t\t\t\tif (xyMode) {\n\t\t\t\t\tself.x = parseFloat(gsCache.x);\n\t\t\t\t\tself.y = parseFloat(gsCache.y);\n\t\t\t\t} else if (rotationMode) {\n\t\t\t\t\tself.x = self.rotation = parseFloat(gsCache.rotation);\n\t\t\t\t} else if (scrollProxy) {\n\t\t\t\t\tself.y = scrollProxy.top();\n\t\t\t\t\tself.x = scrollProxy.left();\n\t\t\t\t} else {\n\t\t\t\t\tself.y = parseFloat(target.style.top || ((cs = _getComputedStyle(target)) && cs.top)) || 0;\n\t\t\t\t\tself.x = parseFloat(target.style.left || (cs || {}).left) || 0;\n\t\t\t\t}\n\t\t\t\tif ((snapX || snapY || snapXY) && !skipSnap && (self.isDragging || self.isThrowing)) {\n\t\t\t\t\tif (snapXY) {\n\t\t\t\t\t\t_temp1.x = self.x;\n\t\t\t\t\t\t_temp1.y = self.y;\n\t\t\t\t\t\tsnappedValue = snapXY(_temp1);\n\t\t\t\t\t\tif (snappedValue.x !== self.x) {\n\t\t\t\t\t\t\tself.x = snappedValue.x;\n\t\t\t\t\t\t\tdirty = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (snappedValue.y !== self.y) {\n\t\t\t\t\t\t\tself.y = snappedValue.y;\n\t\t\t\t\t\t\tdirty = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif (snapX) {\n\t\t\t\t\t\tsnappedValue = snapX(self.x);\n\t\t\t\t\t\tif (snappedValue !== self.x) {\n\t\t\t\t\t\t\tself.x = snappedValue;\n\t\t\t\t\t\t\tif (rotationMode) {\n\t\t\t\t\t\t\t\tself.rotation = snappedValue;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tdirty = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif (snapY) {\n\t\t\t\t\t\tsnappedValue = snapY(self.y);\n\t\t\t\t\t\tif (snappedValue !== self.y) {\n\t\t\t\t\t\t\tself.y = snappedValue;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tdirty = true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tdirty && render(true);\n\t\t\t\tif (!skipOnUpdate) {\n\t\t\t\t\tself.deltaX = self.x - x;\n\t\t\t\t\tself.deltaY = self.y - y;\n\t\t\t\t\t_dispatchEvent(self, \"throwupdate\", \"onThrowUpdate\");\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tbuildSnapFunc = (snap, min, max, factor) => {\n\t\t\t\tif (min == null) {\n\t\t\t\t\tmin = -_bigNum;\n\t\t\t\t}\n\t\t\t\tif (max == null) {\n\t\t\t\t\tmax = _bigNum;\n\t\t\t\t}\n\t\t\t\tif (_isFunction(snap)) {\n\t\t\t\t\treturn n => {\n\t\t\t\t\t\tlet edgeTolerance = !self.isPressed ? 1 : 1 - self.edgeResistance; //if we're tweening, disable the edgeTolerance because it's already factored into the tweening values (we don't want to apply it multiple times)\n\t\t\t\t\t\treturn snap.call(self, (n > max ? max + (n - max) * edgeTolerance : (n < min) ? min + (n - min) * edgeTolerance : n) * factor) * factor;\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\tif (_isArray(snap)) {\n\t\t\t\t\treturn n => {\n\t\t\t\t\t\tlet i = snap.length,\n\t\t\t\t\t\t\tclosest = 0,\n\t\t\t\t\t\t\tabsDif = _bigNum,\n\t\t\t\t\t\t\tval, dif;\n\t\t\t\t\t\twhile (--i > -1) {\n\t\t\t\t\t\t\tval = snap[i];\n\t\t\t\t\t\t\tdif = val - n;\n\t\t\t\t\t\t\tif (dif < 0) {\n\t\t\t\t\t\t\t\tdif = -dif;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (dif < absDif && val >= min && val <= max) {\n\t\t\t\t\t\t\t\tclosest = i;\n\t\t\t\t\t\t\t\tabsDif = dif;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn snap[closest];\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\treturn isNaN(snap) ? n => n : () => snap * factor;\n\t\t\t},\n\n\t\t\tbuildPointSnapFunc = (snap, minX, maxX, minY, maxY, radius, factor) => {\n\t\t\t\tradius = (radius && radius < _bigNum) ? radius * radius : _bigNum; //so we don't have to Math.sqrt() in the functions. Performance optimization.\n\t\t\t\tif (_isFunction(snap)) {\n\t\t\t\t\treturn point => {\n\t\t\t\t\t\tlet edgeTolerance = !self.isPressed ? 1 : 1 - self.edgeResistance,\n\t\t\t\t\t\t\tx = point.x,\n\t\t\t\t\t\t\ty = point.y,\n\t\t\t\t\t\t\tresult, dx, dy; //if we're tweening, disable the edgeTolerance because it's already factored into the tweening values (we don't want to apply it multiple times)\n\t\t\t\t\t\tpoint.x = x = (x > maxX ? maxX + (x - maxX) * edgeTolerance : (x < minX) ? minX + (x - minX) * edgeTolerance : x);\n\t\t\t\t\t\tpoint.y = y = (y > maxY ? maxY + (y - maxY) * edgeTolerance : (y < minY) ? minY + (y - minY) * edgeTolerance : y);\n\t\t\t\t\t\tresult = snap.call(self, point);\n\t\t\t\t\t\tif (result !== point) {\n\t\t\t\t\t\t\tpoint.x = result.x;\n\t\t\t\t\t\t\tpoint.y = result.y;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (factor !== 1) {\n\t\t\t\t\t\t\tpoint.x *= factor;\n\t\t\t\t\t\t\tpoint.y *= factor;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (radius < _bigNum) {\n\t\t\t\t\t\t\tdx = point.x - x;\n\t\t\t\t\t\t\tdy = point.y - y;\n\t\t\t\t\t\t\tif (dx * dx + dy * dy > radius) {\n\t\t\t\t\t\t\t\tpoint.x = x;\n\t\t\t\t\t\t\t\tpoint.y = y;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn point;\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\tif (_isArray(snap)) {\n\t\t\t\t\treturn p => {\n\t\t\t\t\t\tlet i = snap.length,\n\t\t\t\t\t\t\tclosest = 0,\n\t\t\t\t\t\t\tminDist = _bigNum,\n\t\t\t\t\t\t\tx, y, point, dist;\n\t\t\t\t\t\twhile (--i > -1) {\n\t\t\t\t\t\t\tpoint = snap[i];\n\t\t\t\t\t\t\tx = point.x - p.x;\n\t\t\t\t\t\t\ty = point.y - p.y;\n\t\t\t\t\t\t\tdist = x * x + y * y;\n\t\t\t\t\t\t\tif (dist < minDist) {\n\t\t\t\t\t\t\t\tclosest = i;\n\t\t\t\t\t\t\t\tminDist = dist;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn (minDist <= radius) ? snap[closest] : p;\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\treturn n => n;\n\t\t\t},\n\n\t\t\tcalculateBounds = () => {\n\t\t\t\tlet bounds, targetBounds, snap, snapIsRaw;\n\t\t\t\thasBounds = false;\n\t\t\t\tif (scrollProxy) {\n\t\t\t\t\tscrollProxy.calibrate();\n\t\t\t\t\tself.minX = minX = -scrollProxy.maxScrollLeft();\n\t\t\t\t\tself.minY = minY = -scrollProxy.maxScrollTop();\n\t\t\t\t\tself.maxX = maxX = self.maxY = maxY = 0;\n\t\t\t\t\thasBounds = true;\n\t\t\t\t} else if (!!vars.bounds) {\n\t\t\t\t\tbounds = _getBounds(vars.bounds, target.parentNode); //could be a selector/jQuery object or a DOM element or a generic object like {top:0, left:100, width:1000, height:800} or {minX:100, maxX:1100, minY:0, maxY:800}\n\t\t\t\t\tif (rotationMode) {\n\t\t\t\t\t\tself.minX = minX = bounds.left;\n\t\t\t\t\t\tself.maxX = maxX = bounds.left + bounds.width;\n\t\t\t\t\t\tself.minY = minY = self.maxY = maxY = 0;\n\t\t\t\t\t} else if (!_isUndefined(vars.bounds.maxX) || !_isUndefined(vars.bounds.maxY)) {\n\t\t\t\t\t\tbounds = vars.bounds;\n\t\t\t\t\t\tself.minX = minX = bounds.minX;\n\t\t\t\t\t\tself.minY = minY = bounds.minY;\n\t\t\t\t\t\tself.maxX = maxX = bounds.maxX;\n\t\t\t\t\t\tself.maxY = maxY = bounds.maxY;\n\t\t\t\t\t} else {\n\t\t\t\t\t\ttargetBounds = _getBounds(target, target.parentNode);\n\t\t\t\t\t\tself.minX = minX = Math.round(getPropAsNum(xProp, \"px\") + bounds.left - targetBounds.left);\n\t\t\t\t\t\tself.minY = minY = Math.round(getPropAsNum(yProp, \"px\") + bounds.top - targetBounds.top);\n\t\t\t\t\t\tself.maxX = maxX = Math.round(minX + (bounds.width - targetBounds.width));\n\t\t\t\t\t\tself.maxY = maxY = Math.round(minY + (bounds.height - targetBounds.height));\n\t\t\t\t\t}\n\t\t\t\t\tif (minX > maxX) {\n\t\t\t\t\t\tself.minX = maxX;\n\t\t\t\t\t\tself.maxX = maxX = minX;\n\t\t\t\t\t\tminX = self.minX;\n\t\t\t\t\t}\n\t\t\t\t\tif (minY > maxY) {\n\t\t\t\t\t\tself.minY = maxY;\n\t\t\t\t\t\tself.maxY = maxY = minY;\n\t\t\t\t\t\tminY = self.minY;\n\t\t\t\t\t}\n\t\t\t\t\tif (rotationMode) {\n\t\t\t\t\t\tself.minRotation = minX;\n\t\t\t\t\t\tself.maxRotation = maxX;\n\t\t\t\t\t}\n\t\t\t\t\thasBounds = true;\n\t\t\t\t}\n\t\t\t\tif (vars.liveSnap) {\n\t\t\t\t\tsnap = (vars.liveSnap === true) ? (vars.snap || {}) : vars.liveSnap;\n\t\t\t\t\tsnapIsRaw = (_isArray(snap) || _isFunction(snap));\n\t\t\t\t\tif (rotationMode) {\n\t\t\t\t\t\tsnapX = buildSnapFunc((snapIsRaw ? snap : snap.rotation), minX, maxX, 1);\n\t\t\t\t\t\tsnapY = null;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tif (snap.points) {\n\t\t\t\t\t\t\tsnapXY = buildPointSnapFunc((snapIsRaw ? snap : snap.points), minX, maxX, minY, maxY, snap.radius, scrollProxy ? -1 : 1);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tif (allowX) {\n\t\t\t\t\t\t\t\tsnapX = buildSnapFunc((snapIsRaw ? snap : snap.x || snap.left || snap.scrollLeft), minX, maxX, scrollProxy ? -1 : 1);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (allowY) {\n\t\t\t\t\t\t\t\tsnapY = buildSnapFunc((snapIsRaw ? snap : snap.y || snap.top || snap.scrollTop), minY, maxY, scrollProxy ? -1 : 1);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tonThrowComplete = () => {\n\t\t\t\tself.isThrowing = false;\n\t\t\t\t_dispatchEvent(self, \"throwcomplete\", \"onThrowComplete\");\n\t\t\t},\n\t\t\tonThrowInterrupt = () => {\n\t\t\t\tself.isThrowing = false;\n\t\t\t},\n\n\t\t\tanimate = (inertia, forceZeroVelocity) => {\n\t\t\t\tlet snap, snapIsRaw, tween, overshootTolerance;\n\t\t\t\tif (inertia && InertiaPlugin) {\n\t\t\t\t\tif (inertia === true) {\n\t\t\t\t\t\tsnap = vars.snap || vars.liveSnap || {};\n\t\t\t\t\t\tsnapIsRaw = (_isArray(snap) || _isFunction(snap));\n\t\t\t\t\t\tinertia = {resistance:(vars.throwResistance || vars.resistance || 1000) / (rotationMode ? 10 : 1)};\n\t\t\t\t\t\tif (rotationMode) {\n\t\t\t\t\t\t\tinertia.rotation = _parseInertia(self, snapIsRaw ? snap : snap.rotation, maxX, minX, 1, forceZeroVelocity);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tif (allowX) {\n\t\t\t\t\t\t\t\tinertia[xProp] = _parseInertia(self, snapIsRaw ? snap : snap.points || snap.x || snap.left, maxX, minX, scrollProxy ? -1 : 1, forceZeroVelocity || (self.lockedAxis === \"x\"));\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (allowY) {\n\t\t\t\t\t\t\t\tinertia[yProp] = _parseInertia(self, snapIsRaw ? snap : snap.points || snap.y || snap.top, maxY, minY, scrollProxy ? -1 : 1, forceZeroVelocity || (self.lockedAxis === \"y\"));\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (snap.points || (_isArray(snap) && _isObject(snap[0]))) {\n\t\t\t\t\t\t\t\tinertia.linkedProps = xProp + \",\" + yProp;\n\t\t\t\t\t\t\t\tinertia.radius = snap.radius; //note: we also disable liveSnapping while throwing if there's a \"radius\" defined, otherwise it looks weird to have the item thrown past a snapping point but live-snapping mid-tween. We do this by altering the onUpdateParams so that \"skipSnap\" parameter is true for syncXY.\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tself.isThrowing = true;\n\t\t\t\t\tovershootTolerance = (!isNaN(vars.overshootTolerance)) ? vars.overshootTolerance : (vars.edgeResistance === 1) ? 0 : (1 - self.edgeResistance) + 0.2;\n\t\t\t\t\tif (!inertia.duration) {\n\t\t\t\t\t\tinertia.duration = {max: Math.max(vars.minDuration || 0, (\"maxDuration\" in vars) ? vars.maxDuration : 2), min: (!isNaN(vars.minDuration) ? vars.minDuration : (overshootTolerance === 0 || (_isObject(inertia) && inertia.resistance > 1000)) ? 0 : 0.5), overshoot: overshootTolerance};\n\t\t\t\t\t}\n\t\t\t\t\tself.tween = tween = gsap.to(scrollProxy || target, {\n\t\t\t\t\t\tinertia: inertia,\n\t\t\t\t\t\tdata: \"_draggable\",\n\t\t\t\t\t\tonComplete: onThrowComplete,\n\t\t\t\t\t\tonInterrupt: onThrowInterrupt,\n\t\t\t\t\t\tonUpdate: (vars.fastMode ? _dispatchEvent : syncXY),\n\t\t\t\t\t\tonUpdateParams: (vars.fastMode ? [self, \"onthrowupdate\", \"onThrowUpdate\"] : (snap && snap.radius) ? [false, true] : [])\n\t\t\t\t\t});\n\t\t\t\t\tif (!vars.fastMode) {\n\t\t\t\t\t\tif (scrollProxy) {\n\t\t\t\t\t\t\tscrollProxy._skip = true; // Microsoft browsers have a bug that causes them to briefly render the position incorrectly (it flashes to the end state when we seek() the tween even though we jump right back to the current position, and this only seems to happen when we're affecting both top and left), so we set a _suspendTransforms flag to prevent it from actually applying the values in the ScrollProxy.\n\t\t\t\t\t\t}\n\t\t\t\t\t\ttween.render(1e9, true, true); // force to the end. Remember, the duration will likely change upon initting because that's when InertiaPlugin calculates it.\n\t\t\t\t\t\tsyncXY(true, true);\n\t\t\t\t\t\tself.endX = self.x;\n\t\t\t\t\t\tself.endY = self.y;\n\t\t\t\t\t\tif (rotationMode) {\n\t\t\t\t\t\t\tself.endRotation = self.x;\n\t\t\t\t\t\t}\n\t\t\t\t\t\ttween.play(0);\n\t\t\t\t\t\tsyncXY(true, true);\n\t\t\t\t\t\tif (scrollProxy) {\n\t\t\t\t\t\t\tscrollProxy._skip = false; //Microsoft browsers have a bug that causes them to briefly render the position incorrectly (it flashes to the end state when we seek() the tween even though we jump right back to the current position, and this only seems to happen when we're affecting both top and left), so we set a _suspendTransforms flag to prevent it from actually applying the values in the ScrollProxy.\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} else if (hasBounds) {\n\t\t\t\t\tself.applyBounds();\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tupdateMatrix = shiftStart => {\n\t\t\t\tlet start = matrix,\n\t\t\t\t\tp;\n\t\t\t\tmatrix = getGlobalMatrix(target.parentNode, true);\n\t\t\t\tif (shiftStart && self.isPressed && !matrix.equals(start || new Matrix2D())) { //if the matrix changes WHILE the element is pressed, we must adjust the startPointerX and startPointerY accordingly, so we invert the original matrix and figure out where the pointerX and pointerY were in the global space, then apply the new matrix to get the updated coordinates.\n\t\t\t\t\tp = start.inverse().apply({x:startPointerX, y:startPointerY});\n\t\t\t\t\tmatrix.apply(p, p);\n\t\t\t\t\tstartPointerX = p.x;\n\t\t\t\t\tstartPointerY = p.y;\n\t\t\t\t}\n\t\t\t\tif (matrix.equals(_identityMatrix)) { //if there are no transforms, we can optimize performance by not factoring in the matrix\n\t\t\t\t\tmatrix = null;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\trecordStartPositions = () => {\n\t\t\t\tlet edgeTolerance = 1 - self.edgeResistance,\n\t\t\t\t\toffsetX = isFixed ? _getDocScrollLeft(ownerDoc) : 0,\n\t\t\t\t\toffsetY = isFixed ? _getDocScrollTop(ownerDoc) : 0,\n\t\t\t\t\tparsedOrigin, x, y;\n\t\t\t\tif (xyMode) { // in case the user set it as a different unit, like animating the x to \"100%\". We must convert it back to px!\n\t\t\t\t\tgsCache.x = getPropAsNum(xProp, \"px\") + \"px\";\n\t\t\t\t\tgsCache.y = getPropAsNum(yProp, \"px\") + \"px\";\n\t\t\t\t\tgsCache.renderTransform();\n\t\t\t\t}\n\t\t\t\tupdateMatrix(false);\n\t\t\t\t_point1.x = self.pointerX - offsetX;\n\t\t\t\t_point1.y = self.pointerY - offsetY;\n\t\t\t\tmatrix && matrix.apply(_point1, _point1);\n\t\t\t\tstartPointerX = _point1.x; //translate to local coordinate system\n\t\t\t\tstartPointerY = _point1.y;\n\t\t\t\tif (dirty) {\n\t\t\t\t\tsetPointerPosition(self.pointerX, self.pointerY);\n\t\t\t\t\trender(true);\n\t\t\t\t}\n\t\t\t\tinnerMatrix = getGlobalMatrix(target);\n\t\t\t\tif (scrollProxy) {\n\t\t\t\t\tcalculateBounds();\n\t\t\t\t\tstartElementY = scrollProxy.top();\n\t\t\t\t\tstartElementX = scrollProxy.left();\n\t\t\t\t} else {\n\t\t\t\t\t//if the element is in the process of tweening, don't force snapping to occur because it could make it jump. Imagine the user throwing, then before it's done, clicking on the element in its inbetween state.\n\t\t\t\t\tif (isTweening()) {\n\t\t\t\t\t\tsyncXY(true, true);\n\t\t\t\t\t\tcalculateBounds();\n\t\t\t\t\t} else {\n\t\t\t\t\t\tself.applyBounds();\n\t\t\t\t\t}\n\t\t\t\t\tif (rotationMode) {\n\t\t\t\t\t\tparsedOrigin = target.ownerSVGElement ? [gsCache.xOrigin - target.getBBox().x, gsCache.yOrigin - target.getBBox().y] : (_getComputedStyle(target)[_transformOriginProp] || \"0 0\").split(\" \");\n\t\t\t\t\t\trotationOrigin = self.rotationOrigin = getGlobalMatrix(target).apply({x: parseFloat(parsedOrigin[0]) || 0, y: parseFloat(parsedOrigin[1]) || 0});\n\t\t\t\t\t\tsyncXY(true, true);\n\t\t\t\t\t\tx = self.pointerX - rotationOrigin.x - offsetX;\n\t\t\t\t\t\ty = rotationOrigin.y - self.pointerY + offsetY;\n\t\t\t\t\t\tstartElementX = self.x; //starting rotation (x always refers to rotation in type:\"rotation\", measured in degrees)\n\t\t\t\t\t\tstartElementY = self.y = Math.atan2(y, x) * _RAD2DEG;\n\t\t\t\t\t} else {\n\t\t\t\t\t\t//parent = !isFixed && target.parentNode;\n\t\t\t\t\t\t//startScrollTop = parent ? parent.scrollTop || 0 : 0;\n\t\t\t\t\t\t//startScrollLeft = parent ? parent.scrollLeft || 0 : 0;\n\t\t\t\t\t\tstartElementY = getPropAsNum(yProp, \"px\"); //record the starting top and left values so that we can just add the mouse's movement to them later.\n\t\t\t\t\t\tstartElementX = getPropAsNum(xProp, \"px\");\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (hasBounds && edgeTolerance) {\n\t\t\t\t\tif (startElementX > maxX) {\n\t\t\t\t\t\tstartElementX = maxX + (startElementX - maxX) / edgeTolerance;\n\t\t\t\t\t} else if (startElementX < minX) {\n\t\t\t\t\t\tstartElementX = minX - (minX - startElementX) / edgeTolerance;\n\t\t\t\t\t}\n\t\t\t\t\tif (!rotationMode) {\n\t\t\t\t\t\tif (startElementY > maxY) {\n\t\t\t\t\t\t\tstartElementY = maxY + (startElementY - maxY) / edgeTolerance;\n\t\t\t\t\t\t} else if (startElementY < minY) {\n\t\t\t\t\t\t\tstartElementY = minY - (minY - startElementY) / edgeTolerance;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tself.startX = startElementX = _round(startElementX);\n\t\t\t\tself.startY = startElementY = _round(startElementY);\n\t\t\t},\n\n\t\t\tisTweening = () => self.tween && self.tween.isActive(),\n\n\t\t\tremovePlaceholder = () => {\n\t\t\t\tif (_placeholderDiv.parentNode && !isTweening() && !self.isDragging) { //_placeholderDiv just props open auto-scrolling containers so they don't collapse as the user drags left/up. We remove it after dragging (and throwing, if necessary) finishes.\n\t\t\t\t\t_placeholderDiv.parentNode.removeChild(_placeholderDiv);\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t//called when the mouse is pressed (or touch starts)\n\t\t\tonPress = (e, force) => {\n\t\t\t\tlet i;\n\t\t\t\tif (!enabled || self.isPressed || !e || ((e.type === \"mousedown\" || e.type === \"pointerdown\") && !force && _getTime() - clickTime < 30 && _touchEventLookup[self.pointerEvent.type])) { //when we DON'T preventDefault() in order to accommodate touch-scrolling and the user just taps, many browsers also fire a mousedown/mouseup sequence AFTER the touchstart/touchend sequence, thus it'd result in two quick \"click\" events being dispatched. This line senses that condition and halts it on the subsequent mousedown.\n\t\t\t\t\tisPreventingDefault && e && enabled && _preventDefault(e); // in some browsers, we must listen for multiple event types like touchstart, pointerdown, mousedown. The first time this function is called, we record whether or not we _preventDefault() so that on duplicate calls, we can do the same if necessary.\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tinterrupted = isTweening();\n\t\t\t\tdragged = false; // we need to track whether or not it was dragged in this interaction so that if, for example, the user calls .endDrag() to FORCE it to stop and then they keep the mouse pressed down and eventually release, that would normally cause an onClick but we have to skip it in that case if there was dragging that occurred.\n\t\t\t\tself.pointerEvent = e;\n\t\t\t\tif (_touchEventLookup[e.type]) { //note: on iOS, BOTH touchmove and mousemove are dispatched, but the mousemove has pageY and pageX of 0 which would mess up the calculations and needlessly hurt performance.\n\t\t\t\t\ttouchEventTarget = ~e.type.indexOf(\"touch\") ? (e.currentTarget || e.target) : ownerDoc; //pointer-based touches (for Microsoft browsers) don't remain locked to the original target like other browsers, so we must use the document instead. The event type would be \"MSPointerDown\" or \"pointerdown\".\n\t\t\t\t\t_addListener(touchEventTarget, \"touchend\", onRelease);\n\t\t\t\t\t_addListener(touchEventTarget, \"touchmove\", onMove); // possible future change if PointerEvents are more standardized: https://developer.mozilla.org/en-US/docs/Web/API/Element/setPointerCapture\n\t\t\t\t\t_addListener(touchEventTarget, \"touchcancel\", onRelease);\n\t\t\t\t\t_addListener(ownerDoc, \"touchstart\", _onMultiTouchDocument);\n\t\t\t\t} else {\n\t\t\t\t\ttouchEventTarget = null;\n\t\t\t\t\t_addListener(ownerDoc, \"mousemove\", onMove); //attach these to the document instead of the box itself so that if the user's mouse moves too quickly (and off of the box), things still work.\n\t\t\t\t}\n\t\t\t\ttouchDragAxis = null;\n\t\t\t\tif (!_supportsPointer  || !touchEventTarget) {\n\t\t\t\t\t_addListener(ownerDoc, \"mouseup\", onRelease);\n\t\t\t\t\te && e.target && _addListener(e.target, \"mouseup\", onRelease); //we also have to listen directly on the element because some browsers don't bubble up the event to the _doc on elements with contentEditable=\"true\"\n\t\t\t\t}\n\t\t\t\tisClicking = (isClickable.call(self, e.target) && vars.dragClickables === false && !force);\n\t\t\t\tif (isClicking) {\n\t\t\t\t\t_addListener(e.target, \"change\", onRelease); //in some browsers, when you mousedown on a <select> element, no mouseup gets dispatched! So we listen for a \"change\" event instead.\n\t\t\t\t\t_dispatchEvent(self, \"pressInit\", \"onPressInit\");\n\t\t\t\t\t_dispatchEvent(self, \"press\", \"onPress\");\n\t\t\t\t\t_setSelectable(triggers, true); //accommodates things like inputs and elements with contentEditable=\"true\" (otherwise user couldn't drag to select text)\n\t\t\t\t\tisPreventingDefault = false;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tallowNativeTouchScrolling = (!touchEventTarget || allowX === allowY || self.vars.allowNativeTouchScrolling === false || (self.vars.allowContextMenu && e && (e.ctrlKey || e.which > 2))) ? false : allowX ? \"y\" : \"x\"; //note: in Chrome, right-clicking (for a context menu) fires onPress and it doesn't have the event.which set properly, so we must look for event.ctrlKey. If the user wants to allow context menus we should of course sense it here and not allow native touch scrolling.\n\t\t\t\tisPreventingDefault = !allowNativeTouchScrolling && !self.allowEventDefault;\n\t\t\t\tif (isPreventingDefault) {\n\t\t\t\t\t_preventDefault(e);\n\t\t\t\t\t_addListener(_win, \"touchforcechange\", _preventDefault); //works around safari bug: https://greensock.com/forums/topic/21450-draggable-in-iframe-on-mobile-is-buggy/\n\t\t\t\t}\n\t\t\t\tif (e.changedTouches) { //touch events store the data slightly differently\n\t\t\t\t\te = touch = e.changedTouches[0];\n\t\t\t\t\ttouchID = e.identifier;\n\t\t\t\t} else if (e.pointerId) {\n\t\t\t\t\ttouchID = e.pointerId; //for some Microsoft browsers\n\t\t\t\t} else {\n\t\t\t\t\ttouch = touchID = null;\n\t\t\t\t}\n\t\t\t\t_dragCount++;\n\t\t\t\t_addToRenderQueue(render); //causes the Draggable to render on each \"tick\" of gsap.ticker (performance optimization - updating values in a mousemove can cause them to happen too frequently, like multiple times between frame redraws which is wasteful, and it also prevents values from updating properly in IE8)\n\t\t\t\tstartPointerY = self.pointerY = e.pageY; //record the starting x and y so that we can calculate the movement from the original in _onMouseMove\n\t\t\t\tstartPointerX = self.pointerX = e.pageX;\n\t\t\t\t_dispatchEvent(self, \"pressInit\", \"onPressInit\");\n\t\t\t\tif (allowNativeTouchScrolling || self.autoScroll) {\n\t\t\t\t\t_recordMaxScrolls(target.parentNode);\n\t\t\t\t}\n\t\t\t\tif (target.parentNode && self.autoScroll && !scrollProxy && !rotationMode && target.parentNode._gsMaxScrollX && !_placeholderDiv.parentNode && !target.getBBox) { //add a placeholder div to prevent the parent container from collapsing when the user drags the element left.\n\t\t\t\t\t_placeholderDiv.style.width = target.parentNode.scrollWidth + \"px\";\n\t\t\t\t\ttarget.parentNode.appendChild(_placeholderDiv);\n\t\t\t\t}\n\t\t\t\trecordStartPositions();\n\t\t\t\tself.tween && self.tween.kill();\n\t\t\t\tself.isThrowing = false;\n\t\t\t\tgsap.killTweensOf(scrollProxy || target, killProps, true); //in case the user tries to drag it before the last tween is done.\n\t\t\t\tscrollProxy && gsap.killTweensOf(target, {scrollTo:1}, true); //just in case the original target's scroll position is being tweened somewhere else.\n\t\t\t\tself.tween = self.lockedAxis = null;\n\t\t\t\tif (vars.zIndexBoost || (!rotationMode && !scrollProxy && vars.zIndexBoost !== false)) {\n\t\t\t\t\ttarget.style.zIndex = Draggable.zIndex++;\n\t\t\t\t}\n\t\t\t\tself.isPressed = true;\n\t\t\t\thasDragCallback = !!(vars.onDrag || self._listeners.drag);\n\t\t\t\thasMoveCallback = !!(vars.onMove || self._listeners.move);\n\t\t\t\tif (vars.cursor !== false || vars.activeCursor) {\n\t\t\t\t\ti = triggers.length;\n\t\t\t\t\twhile (--i > -1) {\n\t\t\t\t\t\tgsap.set(triggers[i], {cursor: vars.activeCursor || vars.cursor || (_defaultCursor === \"grab\" ? \"grabbing\" : _defaultCursor)});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t_dispatchEvent(self, \"press\", \"onPress\");\n\t\t\t},\n\n\t\t\t//called every time the mouse/touch moves\n\t\t\tonMove = e => {\n\t\t\t\tlet originalEvent = e,\n\t\t\t\t\ttouches, pointerX, pointerY, i, dx, dy;\n\t\t\t\tif (!enabled || _isMultiTouching || !self.isPressed || !e) {\n\t\t\t\t\tisPreventingDefault && e && enabled && _preventDefault(e); // in some browsers, we must listen for multiple event types like touchmove, pointermove, mousemove. The first time this function is called, we record whether or not we _preventDefault() so that on duplicate calls, we can do the same if necessary.\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tself.pointerEvent = e;\n\t\t\t\ttouches = e.changedTouches;\n\t\t\t\tif (touches) { //touch events store the data slightly differently\n\t\t\t\t\te = touches[0];\n\t\t\t\t\tif (e !== touch && e.identifier !== touchID) { //Usually changedTouches[0] will be what we're looking for, but in case it's not, look through the rest of the array...(and Android browsers don't reuse the event like iOS)\n\t\t\t\t\t\ti = touches.length;\n\t\t\t\t\t\twhile (--i > -1 && (e = touches[i]).identifier !== touchID && e.target !== target) {} // Some Android devices dispatch a touchstart AND pointerdown initially, and then only pointermove thus the touchID may not match because it was grabbed from the touchstart event whereas the pointer event is the one that the browser dispatches for move, so if the event target matches this Draggable's target, let it through.\n\t\t\t\t\t\tif (i < 0) {\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} else if (e.pointerId && touchID && e.pointerId !== touchID) { //for some Microsoft browsers, we must attach the listener to the doc rather than the trigger so that when the finger moves outside the bounds of the trigger, things still work. So if the event we're receiving has a pointerId that doesn't match the touchID, ignore it (for multi-touch)\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif (touchEventTarget && allowNativeTouchScrolling && !touchDragAxis) { //Android browsers force us to decide on the first \"touchmove\" event if we should allow the default (scrolling) behavior or preventDefault(). Otherwise, a \"touchcancel\" will be fired and then no \"touchmove\" or \"touchend\" will fire during the scrolling (no good).\n\t\t\t\t\t_point1.x = e.pageX - (isFixed ? _getDocScrollLeft(ownerDoc) : 0);\n\t\t\t\t\t_point1.y = e.pageY - (isFixed ? _getDocScrollTop(ownerDoc) : 0);\n\t\t\t\t\tmatrix && matrix.apply(_point1, _point1);\n\t\t\t\t\tpointerX = _point1.x;\n\t\t\t\t\tpointerY = _point1.y;\n\t\t\t\t\tdx = Math.abs(pointerX - startPointerX);\n\t\t\t\t\tdy = Math.abs(pointerY - startPointerY);\n\t\t\t\t\tif ((dx !== dy && (dx > minimumMovement || dy > minimumMovement)) || (_isAndroid && allowNativeTouchScrolling === touchDragAxis)) {\n\t\t\t\t\t\ttouchDragAxis = (dx > dy && allowX) ? \"x\" : \"y\";\n\t\t\t\t\t\tif (allowNativeTouchScrolling && touchDragAxis !== allowNativeTouchScrolling) {\n\t\t\t\t\t\t\t_addListener(_win, \"touchforcechange\", _preventDefault); // prevents native touch scrolling from taking over if the user started dragging in the other direction in iOS Safari\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (self.vars.lockAxisOnTouchScroll !== false && allowX && allowY) {\n\t\t\t\t\t\t\tself.lockedAxis = (touchDragAxis === \"x\") ? \"y\" : \"x\";\n\t\t\t\t\t\t\t_isFunction(self.vars.onLockAxis) && self.vars.onLockAxis.call(self, originalEvent);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (_isAndroid && allowNativeTouchScrolling === touchDragAxis) {\n\t\t\t\t\t\t\tonRelease(originalEvent);\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (!self.allowEventDefault && (!allowNativeTouchScrolling || (touchDragAxis && allowNativeTouchScrolling !== touchDragAxis)) && originalEvent.cancelable !== false) {\n\t\t\t\t\t_preventDefault(originalEvent);\n\t\t\t\t\tisPreventingDefault = true;\n\t\t\t\t} else if (isPreventingDefault) {\n\t\t\t\t\tisPreventingDefault = false;\n\t\t\t\t}\n\n\t\t\t\tif (self.autoScroll) {\n\t\t\t\t\tcheckAutoScrollBounds = true;\n\t\t\t\t}\n\t\t\t\tsetPointerPosition(e.pageX, e.pageY, hasMoveCallback);\n\t\t\t},\n\n\t\t\tsetPointerPosition = (pointerX, pointerY, invokeOnMove) => {\n\t\t\t\tlet dragTolerance = 1 - self.dragResistance,\n\t\t\t\t\tedgeTolerance = 1 - self.edgeResistance,\n\t\t\t\t\tprevPointerX = self.pointerX,\n\t\t\t\t\tprevPointerY = self.pointerY,\n\t\t\t\t\tprevStartElementY = startElementY,\n\t\t\t\t\tprevX = self.x,\n\t\t\t\t\tprevY = self.y,\n\t\t\t\t\tprevEndX = self.endX,\n\t\t\t\t\tprevEndY = self.endY,\n\t\t\t\t\tprevEndRotation = self.endRotation,\n\t\t\t\t\tprevDirty = dirty,\n\t\t\t\t\txChange, yChange, x, y, dif, temp;\n\t\t\t\tself.pointerX = pointerX;\n\t\t\t\tself.pointerY = pointerY;\n\t\t\t\tif (isFixed) {\n\t\t\t\t\tpointerX -= _getDocScrollLeft(ownerDoc);\n\t\t\t\t\tpointerY -= _getDocScrollTop(ownerDoc);\n\t\t\t\t}\n\t\t\t\tif (rotationMode) {\n\t\t\t\t\ty = Math.atan2(rotationOrigin.y - pointerY, pointerX - rotationOrigin.x) * _RAD2DEG;\n\t\t\t\t\tdif = self.y - y;\n\t\t\t\t\tif (dif > 180) {\n\t\t\t\t\t\tstartElementY -= 360;\n\t\t\t\t\t\tself.y = y;\n\t\t\t\t\t} else if (dif < -180) {\n\t\t\t\t\t\tstartElementY += 360;\n\t\t\t\t\t\tself.y = y;\n\t\t\t\t\t}\n\t\t\t\t\tif (self.x !== startElementX || Math.abs(startElementY - y) > minimumMovement) {\n\t\t\t\t\t\tself.y = y;\n\t\t\t\t\t\tx = startElementX + (startElementY - y) * dragTolerance;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tx = startElementX;\n\t\t\t\t\t}\n\n\t\t\t\t} else {\n\t\t\t\t\tif (matrix) {\n\t\t\t\t\t\ttemp = pointerX * matrix.a + pointerY * matrix.c + matrix.e;\n\t\t\t\t\t\tpointerY = pointerX * matrix.b + pointerY * matrix.d + matrix.f;\n\t\t\t\t\t\tpointerX = temp;\n\t\t\t\t\t}\n\t\t\t\t\tyChange = (pointerY - startPointerY);\n\t\t\t\t\txChange = (pointerX - startPointerX);\n\t\t\t\t\tif (yChange < minimumMovement && yChange > -minimumMovement) {\n\t\t\t\t\t\tyChange = 0;\n\t\t\t\t\t}\n\t\t\t\t\tif (xChange < minimumMovement && xChange > -minimumMovement) {\n\t\t\t\t\t\txChange = 0;\n\t\t\t\t\t}\n\t\t\t\t\tif ((self.lockAxis || self.lockedAxis) && (xChange || yChange)) {\n\t\t\t\t\t\ttemp = self.lockedAxis;\n\t\t\t\t\t\tif (!temp) {\n\t\t\t\t\t\t\tself.lockedAxis = temp = (allowX && Math.abs(xChange) > Math.abs(yChange)) ? \"y\" : allowY ? \"x\" : null;\n\t\t\t\t\t\t\tif (temp && _isFunction(self.vars.onLockAxis)) {\n\t\t\t\t\t\t\t\tself.vars.onLockAxis.call(self, self.pointerEvent);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (temp === \"y\") {\n\t\t\t\t\t\t\tyChange = 0;\n\t\t\t\t\t\t} else if (temp === \"x\") {\n\t\t\t\t\t\t\txChange = 0;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tx = _round(startElementX + xChange * dragTolerance);\n\t\t\t\t\ty = _round(startElementY + yChange * dragTolerance);\n\t\t\t\t}\n\n\t\t\t\tif ((snapX || snapY || snapXY) && (self.x !== x || (self.y !== y && !rotationMode))) {\n\t\t\t\t\tif (snapXY) {\n\t\t\t\t\t\t_temp1.x = x;\n\t\t\t\t\t\t_temp1.y = y;\n\t\t\t\t\t\ttemp = snapXY(_temp1);\n\t\t\t\t\t\tx = _round(temp.x);\n\t\t\t\t\t\ty = _round(temp.y);\n\t\t\t\t\t}\n\t\t\t\t\tif (snapX) {\n\t\t\t\t\t\tx = _round(snapX(x));\n\t\t\t\t\t}\n\t\t\t\t\tif (snapY) {\n\t\t\t\t\t\ty = _round(snapY(y));\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (hasBounds) {\n\t\t\t\t\tif (x > maxX) {\n\t\t\t\t\t\tx = maxX + Math.round((x - maxX) * edgeTolerance);\n\t\t\t\t\t} else if (x < minX) {\n\t\t\t\t\t\tx = minX + Math.round((x - minX) * edgeTolerance);\n\t\t\t\t\t}\n\t\t\t\t\tif (!rotationMode) {\n\t\t\t\t\t\tif (y > maxY) {\n\t\t\t\t\t\t\ty = Math.round(maxY + (y - maxY) * edgeTolerance);\n\t\t\t\t\t\t} else if (y < minY) {\n\t\t\t\t\t\t\ty = Math.round(minY + (y - minY) * edgeTolerance);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (self.x !== x || (self.y !== y && !rotationMode)) {\n\t\t\t\t\tif (rotationMode) {\n\t\t\t\t\t\tself.endRotation = self.x = self.endX = x;\n\t\t\t\t\t\tdirty = true;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tif (allowY) {\n\t\t\t\t\t\t\tself.y = self.endY = y;\n\t\t\t\t\t\t\tdirty = true; //a flag that indicates we need to render the target next time the TweenLite.ticker dispatches a \"tick\" event (typically on a requestAnimationFrame) - this is a performance optimization (we shouldn't render on every move because sometimes many move events can get dispatched between screen refreshes, and that'd be wasteful to render every time)\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (allowX) {\n\t\t\t\t\t\t\tself.x = self.endX = x;\n\t\t\t\t\t\t\tdirty = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif (!invokeOnMove || _dispatchEvent(self, \"move\", \"onMove\") !== false) {\n\t\t\t\t\t\tif (!self.isDragging && self.isPressed) {\n\t\t\t\t\t\t\tself.isDragging = dragged = true;\n\t\t\t\t\t\t\t_dispatchEvent(self, \"dragstart\", \"onDragStart\");\n\t\t\t\t\t\t}\n\t\t\t\t\t} else { //revert because the onMove returned false!\n\t\t\t\t\t\tself.pointerX = prevPointerX;\n\t\t\t\t\t\tself.pointerY = prevPointerY;\n\t\t\t\t\t\tstartElementY = prevStartElementY;\n\t\t\t\t\t\tself.x = prevX;\n\t\t\t\t\t\tself.y = prevY;\n\t\t\t\t\t\tself.endX = prevEndX;\n\t\t\t\t\t\tself.endY = prevEndY;\n\t\t\t\t\t\tself.endRotation = prevEndRotation;\n\t\t\t\t\t\tdirty = prevDirty;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t//called when the mouse/touch is released\n\t\t\tonRelease = (e, force) => {\n\t\t\t\tif (!enabled || !self.isPressed || (e && touchID != null && !force && ((e.pointerId && e.pointerId !== touchID && e.target !== target) || (e.changedTouches && !_hasTouchID(e.changedTouches, touchID))))) {  //for some Microsoft browsers, we must attach the listener to the doc rather than the trigger so that when the finger moves outside the bounds of the trigger, things still work. So if the event we're receiving has a pointerId that doesn't match the touchID, ignore it (for multi-touch)\n\t\t\t\t\tisPreventingDefault && e && enabled && _preventDefault(e); // in some browsers, we must listen for multiple event types like touchend, pointerup, mouseup. The first time this function is called, we record whether or not we _preventDefault() so that on duplicate calls, we can do the same if necessary.\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tself.isPressed = false;\n\t\t\t\tlet originalEvent = e,\n\t\t\t\t\twasDragging = self.isDragging,\n\t\t\t\t\tisContextMenuRelease = (self.vars.allowContextMenu && e && (e.ctrlKey || e.which > 2)),\n\t\t\t\t\tplaceholderDelayedCall = gsap.delayedCall(0.001, removePlaceholder),\n\t\t\t\t\ttouches, i, syntheticEvent, eventTarget, syntheticClick;\n\t\t\t\tif (touchEventTarget) {\n\t\t\t\t\t_removeListener(touchEventTarget, \"touchend\", onRelease);\n\t\t\t\t\t_removeListener(touchEventTarget, \"touchmove\", onMove);\n\t\t\t\t\t_removeListener(touchEventTarget, \"touchcancel\", onRelease);\n\t\t\t\t\t_removeListener(ownerDoc, \"touchstart\", _onMultiTouchDocument);\n\t\t\t\t} else {\n\t\t\t\t\t_removeListener(ownerDoc, \"mousemove\", onMove);\n\t\t\t\t}\n\t\t\t\t_removeListener(_win, \"touchforcechange\", _preventDefault);\n\t\t\t\tif (!_supportsPointer || !touchEventTarget) {\n\t\t\t\t\t_removeListener(ownerDoc, \"mouseup\", onRelease);\n\t\t\t\t\te && e.target && _removeListener(e.target, \"mouseup\", onRelease);\n\t\t\t\t}\n\t\t\t\tdirty = false;\n\t\t\t\tif (wasDragging) {\n\t\t\t\t\tdragEndTime = _lastDragTime = _getTime();\n\t\t\t\t\tself.isDragging = false;\n\t\t\t\t}\n\t\t\t\t_removeFromRenderQueue(render);\n\t\t\t\tif (isClicking && !isContextMenuRelease) {\n\t\t\t\t\tif (e) {\n\t\t\t\t\t\t_removeListener(e.target, \"change\", onRelease);\n\t\t\t\t\t\tself.pointerEvent = originalEvent;\n\t\t\t\t\t}\n\t\t\t\t\t_setSelectable(triggers, false);\n\t\t\t\t\t_dispatchEvent(self, \"release\", \"onRelease\");\n\t\t\t\t\t_dispatchEvent(self, \"click\", \"onClick\");\n\t\t\t\t\tisClicking = false;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\ti = triggers.length;\n\t\t\t\twhile (--i > -1) {\n\t\t\t\t\t_setStyle(triggers[i], \"cursor\", vars.cursor || (vars.cursor !== false ? _defaultCursor : null));\n\t\t\t\t}\n\t\t\t\t_dragCount--;\n\t\t\t\tif (e) {\n\t\t\t\t\ttouches = e.changedTouches;\n\t\t\t\t\tif (touches) { //touch events store the data slightly differently\n\t\t\t\t\t\te = touches[0];\n\t\t\t\t\t\tif (e !== touch && e.identifier !== touchID) { //Usually changedTouches[0] will be what we're looking for, but in case it's not, look through the rest of the array...(and Android browsers don't reuse the event like iOS)\n\t\t\t\t\t\t\ti = touches.length;\n\t\t\t\t\t\t\twhile (--i > -1 && (e = touches[i]).identifier !== touchID && e.target !== target) {}\n\t\t\t\t\t\t\tif (i < 0 && !force) {\n\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tself.pointerEvent = originalEvent;\n\t\t\t\t\tself.pointerX = e.pageX;\n\t\t\t\t\tself.pointerY = e.pageY;\n\t\t\t\t}\n\t\t\t\tif (isContextMenuRelease && originalEvent) {\n\t\t\t\t\t_preventDefault(originalEvent);\n\t\t\t\t\tisPreventingDefault = true;\n\t\t\t\t\t_dispatchEvent(self, \"release\", \"onRelease\");\n\t\t\t\t} else if (originalEvent && !wasDragging) {\n\t\t\t\t\tisPreventingDefault = false;\n\t\t\t\t\tif (interrupted && (vars.snap || vars.bounds)) { //otherwise, if the user clicks on the object while it's animating to a snapped position, and then releases without moving 3 pixels, it will just stay there (it should animate/snap)\n\t\t\t\t\t\tanimate(vars.inertia || vars.throwProps);\n\t\t\t\t\t}\n\t\t\t\t\t_dispatchEvent(self, \"release\", \"onRelease\");\n\t\t\t\t\tif ((!_isAndroid || originalEvent.type !== \"touchmove\") && originalEvent.type.indexOf(\"cancel\") === -1) { //to accommodate native scrolling on Android devices, we have to immediately call onRelease() on the first touchmove event, but that shouldn't trigger a \"click\".\n\t\t\t\t\t\t_dispatchEvent(self, \"click\", \"onClick\");\n\t\t\t\t\t\tif (_getTime() - clickTime < 300) {\n\t\t\t\t\t\t\t_dispatchEvent(self, \"doubleclick\", \"onDoubleClick\");\n\t\t\t\t\t\t}\n\t\t\t\t\t\teventTarget = originalEvent.target || target; //old IE uses srcElement\n\t\t\t\t\t\tclickTime = _getTime();\n\t\t\t\t\t\tsyntheticClick = () => { // some browsers (like Firefox) won't trust script-generated clicks, so if the user tries to click on a video to play it, for example, it simply won't work. Since a regular \"click\" event will most likely be generated anyway (one that has its isTrusted flag set to true), we must slightly delay our script-generated click so that the \"real\"/trusted one is prioritized. Remember, when there are duplicate events in quick succession, we suppress all but the first one. Some browsers don't even trigger the \"real\" one at all, so our synthetic one is a safety valve that ensures that no matter what, a click event does get dispatched.\n\t\t\t\t\t\t\tif (clickTime !== clickDispatch && self.enabled() && !self.isPressed && !originalEvent.defaultPrevented) {\n\t\t\t\t\t\t\t\tif (eventTarget.click) { //some browsers (like mobile Safari) don't properly trigger the click event\n\t\t\t\t\t\t\t\t\teventTarget.click();\n\t\t\t\t\t\t\t\t} else if (ownerDoc.createEvent) {\n\t\t\t\t\t\t\t\t\tsyntheticEvent = ownerDoc.createEvent(\"MouseEvents\");\n\t\t\t\t\t\t\t\t\tsyntheticEvent.initMouseEvent(\"click\", true, true, _win, 1, self.pointerEvent.screenX, self.pointerEvent.screenY, self.pointerX, self.pointerY, false, false, false, false, 0, null);\n\t\t\t\t\t\t\t\t\teventTarget.dispatchEvent(syntheticEvent);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t};\n\t\t\t\t\t\tif (!_isAndroid && !originalEvent.defaultPrevented) { //iOS Safari requires the synthetic click to happen immediately or else it simply won't work, but Android doesn't play nice.\n\t\t\t\t\t\t\tgsap.delayedCall(0.05, syntheticClick); //in addition to the iOS bug workaround, there's a Firefox issue with clicking on things like a video to play, so we must fake a click event in a slightly delayed fashion. Previously, we listened for the \"click\" event with \"capture\" false which solved the video-click-to-play issue, but it would allow the \"click\" event to be dispatched twice like if you were using a jQuery.click() because that was handled in the capture phase, thus we had to switch to the capture phase to avoid the double-dispatching, but do the delayed synthetic click. Don't fire it too fast (like 0.00001) because we want to give the native event a chance to fire first as it's \"trusted\".\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tanimate(vars.inertia || vars.throwProps); //will skip if inertia/throwProps isn't defined or InertiaPlugin isn't loaded.\n\t\t\t\t\tif (!self.allowEventDefault && originalEvent && (vars.dragClickables !== false || !isClickable.call(self, originalEvent.target)) && wasDragging && (!allowNativeTouchScrolling || (touchDragAxis && allowNativeTouchScrolling === touchDragAxis)) && originalEvent.cancelable !== false) {\n\t\t\t\t\t\tisPreventingDefault = true;\n\t\t\t\t\t\t_preventDefault(originalEvent);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tisPreventingDefault = false;\n\t\t\t\t\t}\n\t\t\t\t\t_dispatchEvent(self, \"release\", \"onRelease\");\n\t\t\t\t}\n\t\t\t\tisTweening() && placeholderDelayedCall.duration( self.tween.duration() ); //sync the timing so that the placeholder DIV gets\n\t\t\t\twasDragging && _dispatchEvent(self, \"dragend\", \"onDragEnd\");\n\t\t\t\treturn true;\n\t\t\t},\n\n\t\t\tupdateScroll = e => {\n\t\t\t\tif (e && self.isDragging && !scrollProxy) {\n\t\t\t\t\tlet parent = e.target || target.parentNode,\n\t\t\t\t\t\tdeltaX = parent.scrollLeft - parent._gsScrollX,\n\t\t\t\t\t\tdeltaY = parent.scrollTop - parent._gsScrollY;\n\t\t\t\t\tif (deltaX || deltaY) {\n\t\t\t\t\t\tif (matrix) {\n\t\t\t\t\t\t\tstartPointerX -= deltaX * matrix.a + deltaY * matrix.c;\n\t\t\t\t\t\t\tstartPointerY -= deltaY * matrix.d + deltaX * matrix.b;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tstartPointerX -= deltaX;\n\t\t\t\t\t\t\tstartPointerY -= deltaY;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tparent._gsScrollX += deltaX;\n\t\t\t\t\t\tparent._gsScrollY += deltaY;\n\t\t\t\t\t\tsetPointerPosition(self.pointerX, self.pointerY);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tonClick = e => { //this was a huge pain in the neck to align all the various browsers and their behaviors. Chrome, Firefox, Safari, Opera, Android, and Microsoft Edge all handle events differently! Some will only trigger native behavior (like checkbox toggling) from trusted events. Others don't even support isTrusted, but require 2 events to flow through before triggering native behavior. Edge treats everything as trusted but also mandates that 2 flow through to trigger the correct native behavior.\n\t\t\t\tlet time = _getTime(),\n\t\t\t\t\trecentlyClicked = (time - clickTime < 100),\n\t\t\t\t\trecentlyDragged = (time - dragEndTime < 50),\n\t\t\t\t\talreadyDispatched = (recentlyClicked && clickDispatch === clickTime),\n\t\t\t\t\tdefaultPrevented = (self.pointerEvent && self.pointerEvent.defaultPrevented),\n\t\t\t\t\talreadyDispatchedTrusted = (recentlyClicked && trustedClickDispatch === clickTime),\n\t\t\t\t\ttrusted = e.isTrusted || (e.isTrusted == null && recentlyClicked && alreadyDispatched); //note: Safari doesn't support isTrusted, and it won't properly execute native behavior (like toggling checkboxes) on the first synthetic \"click\" event - we must wait for the 2nd and treat it as trusted (but stop propagation at that point). Confusing, I know. Don't you love cross-browser compatibility challenges?\n\t\t\t\tif ((alreadyDispatched || (recentlyDragged && self.vars.suppressClickOnDrag !== false) ) && e.stopImmediatePropagation) {\n\t\t\t\t\te.stopImmediatePropagation();\n\t\t\t\t}\n\t\t\t\tif (recentlyClicked && !(self.pointerEvent && self.pointerEvent.defaultPrevented) && (!alreadyDispatched || (trusted && !alreadyDispatchedTrusted))) { //let the first click pass through unhindered. Let the next one only if it's trusted, then no more (stop quick-succession ones)\n\t\t\t\t\tif (trusted && alreadyDispatched) {\n\t\t\t\t\t\ttrustedClickDispatch = clickTime;\n\t\t\t\t\t}\n\t\t\t\t\tclickDispatch = clickTime;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (self.isPressed || recentlyDragged || recentlyClicked) {\n\t\t\t\t\tif (!trusted || !e.detail || !recentlyClicked || defaultPrevented) {\n\t\t\t\t\t\t_preventDefault(e);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (!recentlyClicked && !recentlyDragged && !dragged) { // for script-triggered event dispatches, like element.click()\n\t\t\t\t\te && e.target && (self.pointerEvent = e);\n\t\t\t\t\t_dispatchEvent(self, \"click\", \"onClick\");\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tlocalizePoint = p => matrix ? {x:p.x * matrix.a + p.y * matrix.c + matrix.e, y:p.x * matrix.b + p.y * matrix.d + matrix.f} : {x:p.x, y:p.y};\n\n\t\told = Draggable.get(target);\n\t\told && old.kill(); // avoids duplicates (an element can only be controlled by one Draggable)\n\n\t\t//give the user access to start/stop dragging...\n\t\tthis.startDrag = (event, align) => {\n\t\t\tlet r1, r2, p1, p2;\n\t\t\tonPress(event || self.pointerEvent, true);\n\t\t\t//if the pointer isn't on top of the element, adjust things accordingly\n\t\t\tif (align && !self.hitTest(event || self.pointerEvent)) {\n\t\t\t\tr1 = _parseRect(event || self.pointerEvent);\n\t\t\t\tr2 = _parseRect(target);\n\t\t\t\tp1 = localizePoint({x:r1.left + r1.width / 2, y:r1.top + r1.height / 2});\n\t\t\t\tp2 = localizePoint({x:r2.left + r2.width / 2, y:r2.top + r2.height / 2});\n\t\t\t\tstartPointerX -= p1.x - p2.x;\n\t\t\t\tstartPointerY -= p1.y - p2.y;\n\t\t\t}\n\t\t\tif (!self.isDragging) {\n\t\t\t\tself.isDragging = dragged = true;\n\t\t\t\t_dispatchEvent(self, \"dragstart\", \"onDragStart\");\n\t\t\t}\n\t\t};\n\t\tthis.drag = onMove;\n\t\tthis.endDrag = e =>\tonRelease(e || self.pointerEvent, true);\n\t\tthis.timeSinceDrag = () => self.isDragging ? 0 : (_getTime() - dragEndTime) / 1000;\n\t\tthis.timeSinceClick = () => (_getTime() - clickTime) / 1000;\n\t\tthis.hitTest = (target, threshold) => Draggable.hitTest(self.target, target, threshold);\n\n\t\tthis.getDirection = (from, diagonalThreshold) => { //from can be \"start\" (default), \"velocity\", or an element\n\t\t\tlet mode = (from === \"velocity\" && InertiaPlugin) ? from : (_isObject(from) && !rotationMode) ? \"element\" : \"start\",\n\t\t\t\txChange, yChange, ratio, direction, r1, r2;\n\t\t\tif (mode === \"element\") {\n\t\t\t\tr1 = _parseRect(self.target);\n\t\t\t\tr2 = _parseRect(from);\n\t\t\t}\n\t\t\txChange = (mode === \"start\") ? self.x - startElementX : (mode === \"velocity\") ? InertiaPlugin.getVelocity(target, xProp) : (r1.left + r1.width / 2) - (r2.left + r2.width / 2);\n\t\t\tif (rotationMode) {\n\t\t\t\treturn xChange < 0 ? \"counter-clockwise\" : \"clockwise\";\n\t\t\t} else {\n\t\t\t\tdiagonalThreshold = diagonalThreshold || 2;\n\t\t\t\tyChange = (mode === \"start\") ? self.y - startElementY : (mode === \"velocity\") ? InertiaPlugin.getVelocity(target, yProp) : (r1.top + r1.height / 2) - (r2.top + r2.height / 2);\n\t\t\t\tratio = Math.abs(xChange / yChange);\n\t\t\t\tdirection = (ratio < 1 / diagonalThreshold) ? \"\" : (xChange < 0) ? \"left\" : \"right\";\n\t\t\t\tif (ratio < diagonalThreshold) {\n\t\t\t\t\tif (direction !== \"\") {\n\t\t\t\t\t\tdirection += \"-\";\n\t\t\t\t\t}\n\t\t\t\t\tdirection += (yChange < 0) ? \"up\" : \"down\";\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn direction;\n\t\t};\n\n\t\tthis.applyBounds = (newBounds, sticky) => {\n\t\t\tlet x, y, forceZeroVelocity, e, parent, isRoot;\n\t\t\tif (newBounds && vars.bounds !== newBounds) {\n\t\t\t\tvars.bounds = newBounds;\n\t\t\t\treturn self.update(true, sticky);\n\t\t\t}\n\t\t\tsyncXY(true);\n\t\t\tcalculateBounds();\n\t\t\tif (hasBounds && !isTweening()) {\n\t\t\t\tx = self.x;\n\t\t\t\ty = self.y;\n\t\t\t\tif (x > maxX) {\n\t\t\t\t\tx = maxX;\n\t\t\t\t} else if (x < minX) {\n\t\t\t\t\tx = minX;\n\t\t\t\t}\n\t\t\t\tif (y > maxY) {\n\t\t\t\t\ty = maxY;\n\t\t\t\t} else if (y < minY) {\n\t\t\t\t\ty = minY;\n\t\t\t\t}\n\t\t\t\tif (self.x !== x || self.y !== y) {\n\t\t\t\t\tforceZeroVelocity = true;\n\t\t\t\t\tself.x = self.endX = x;\n\t\t\t\t\tif (rotationMode) {\n\t\t\t\t\t\tself.endRotation = x;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tself.y = self.endY = y;\n\t\t\t\t\t}\n\t\t\t\t\tdirty = true;\n\t\t\t\t\trender(true);\n\t\t\t\t\tif (self.autoScroll && !self.isDragging) {\n\t\t\t\t\t\t_recordMaxScrolls(target.parentNode);\n\t\t\t\t\t\te = target;\n\t\t\t\t\t\t_windowProxy.scrollTop = ((_win.pageYOffset != null) ? _win.pageYOffset : (ownerDoc.documentElement.scrollTop != null) ? ownerDoc.documentElement.scrollTop : ownerDoc.body.scrollTop);\n\t\t\t\t\t\t_windowProxy.scrollLeft = ((_win.pageXOffset != null) ? _win.pageXOffset : (ownerDoc.documentElement.scrollLeft != null) ? ownerDoc.documentElement.scrollLeft : ownerDoc.body.scrollLeft);\n\t\t\t\t\t\twhile (e && !isRoot) { //walk up the chain and sense wherever the scrollTop/scrollLeft exceeds the maximum.\n\t\t\t\t\t\t\tisRoot = _isRoot(e.parentNode);\n\t\t\t\t\t\t\tparent = isRoot ? _windowProxy : e.parentNode;\n\t\t\t\t\t\t\tif (allowY && parent.scrollTop > parent._gsMaxScrollY) {\n\t\t\t\t\t\t\t\tparent.scrollTop = parent._gsMaxScrollY;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (allowX && parent.scrollLeft > parent._gsMaxScrollX) {\n\t\t\t\t\t\t\t\tparent.scrollLeft = parent._gsMaxScrollX;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\te = parent;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (self.isThrowing && (forceZeroVelocity || self.endX > maxX || self.endX < minX || self.endY > maxY || self.endY < minY)) {\n\t\t\t\t\tanimate(vars.inertia || vars.throwProps, forceZeroVelocity);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn self;\n\t\t};\n\n\t\tthis.update = (applyBounds, sticky, ignoreExternalChanges) => {\n\t\t\tif (sticky && self.isPressed) { // in case the element was repositioned in the document flow, thus its x/y may be identical but its position is actually quite different.\n\t\t\t\tlet m = getGlobalMatrix(target),\n\t\t\t\t\tp = innerMatrix.apply({x: self.x - startElementX, y: self.y - startElementY}),\n\t\t\t\t\tm2 = getGlobalMatrix(target.parentNode, true);\n\t\t\t\tm2.apply({x: m.e - p.x, y: m.f - p.y}, p);\n\t\t\t\tself.x -= p.x - m2.e;\n\t\t\t\tself.y -= p.y - m2.f;\n\t\t\t\trender(true);\n\t\t\t\trecordStartPositions();\n\t\t\t}\n\t\t\tlet { x, y } = self;\n\t\t\tupdateMatrix(!sticky);\n\t\t\tif (applyBounds) {\n\t\t\t\tself.applyBounds();\n\t\t\t} else {\n\t\t\t\tdirty && ignoreExternalChanges && render(true);\n\t\t\t\tsyncXY(true);\n\t\t\t}\n\t\t\tif (sticky) {\n\t\t\t\tsetPointerPosition(self.pointerX, self.pointerY);\n\t\t\t\tdirty && render(true);\n\t\t\t}\n\t\t\tif (self.isPressed && !sticky && ((allowX && Math.abs(x - self.x) > 0.01) || (allowY && (Math.abs(y - self.y) > 0.01 && !rotationMode)))) {\n\t\t\t\trecordStartPositions();\n\t\t\t}\n\t\t\tif (self.autoScroll) {\n\t\t\t\t_recordMaxScrolls(target.parentNode, self.isDragging);\n\t\t\t\tcheckAutoScrollBounds = self.isDragging;\n\t\t\t\trender(true);\n\t\t\t\t//in case reparenting occurred.\n\t\t\t\t_removeScrollListener(target, updateScroll);\n\t\t\t\t_addScrollListener(target, updateScroll);\n\t\t\t}\n\t\t\treturn self;\n\t\t};\n\n\t\tthis.enable = type => {\n\t\t\tlet setVars = {lazy: true},\n\t\t\t\tid, i, trigger;\n\t\t\tif (vars.cursor !== false) {\n\t\t\t\tsetVars.cursor = vars.cursor || _defaultCursor;\n\t\t\t}\n\t\t\tif (gsap.utils.checkPrefix(\"touchCallout\")) {\n\t\t\t\tsetVars.touchCallout = \"none\";\n\t\t\t}\n\t\t\tif (type !== \"soft\") {\n\t\t\t\t_setTouchActionForAllDescendants(triggers, (allowX === allowY) ? \"none\" : (vars.allowNativeTouchScrolling && (target.scrollHeight === target.clientHeight) === (target.scrollWidth === target.clientHeight)) || vars.allowEventDefault ? \"manipulation\" : allowX ? \"pan-y\" : \"pan-x\"); // Some browsers like Internet Explorer will fire a pointercancel event when the user attempts to drag when touchAction is \"manipulate\" because it's perceived as a pan. If the element has scrollable content in only one direction, we should use pan-x or pan-y accordingly so that the pointercancel doesn't prevent dragging.\n\t\t\t\ti = triggers.length;\n\t\t\t\twhile (--i > -1) {\n\t\t\t\t\ttrigger = triggers[i];\n\t\t\t\t\t_supportsPointer || _addListener(trigger, \"mousedown\", onPress);\n\t\t\t\t\t_addListener(trigger, \"touchstart\", onPress);\n\t\t\t\t\t_addListener(trigger, \"click\", onClick, true); // note: used to pass true for capture but it prevented click-to-play-video functionality in Firefox.\n\t\t\t\t\tgsap.set(trigger, setVars);\n\t\t\t\t\tif (trigger.getBBox && trigger.ownerSVGElement && allowX !== allowY) { // a bug in chrome doesn't respect touch-action on SVG elements - it only works if we set it on the parent SVG.\n\t\t\t\t\t\tgsap.set(trigger.ownerSVGElement, {touchAction: vars.allowNativeTouchScrolling || vars.allowEventDefault ? \"manipulation\" : allowX ? \"pan-y\" : \"pan-x\"});\n\t\t\t\t\t}\n\t\t\t\t\tvars.allowContextMenu || _addListener(trigger, \"contextmenu\", onContextMenu);\n\t\t\t\t}\n\t\t\t\t_setSelectable(triggers, false);\n\t\t\t}\n\t\t\t_addScrollListener(target, updateScroll);\n\t\t\tenabled = true;\n\t\t\tif (InertiaPlugin && type !== \"soft\") {\n\t\t\t\tInertiaPlugin.track(scrollProxy || target, (xyMode ? \"x,y\" : rotationMode ? \"rotation\" : \"top,left\"));\n\t\t\t}\n\t\t\ttarget._gsDragID = id = \"d\" + (_lookupCount++);\n\t\t\t_lookup[id] = self;\n\t\t\tif (scrollProxy) {\n\t\t\t\tscrollProxy.enable();\n\t\t\t\tscrollProxy.element._gsDragID = id;\n\t\t\t}\n\t\t\t(vars.bounds || rotationMode) && recordStartPositions();\n\t\t\tvars.bounds && self.applyBounds();\n\t\t\treturn self;\n\t\t};\n\n\t\tthis.disable = type => {\n\t\t\tlet dragging = self.isDragging,\n\t\t\t\ti = triggers.length,\n\t\t\t\ttrigger;\n\t\t\twhile (--i > -1) {\n\t\t\t\t_setStyle(triggers[i], \"cursor\", null);\n\t\t\t}\n\t\t\tif (type !== \"soft\") {\n\t\t\t\t_setTouchActionForAllDescendants(triggers, null);\n\t\t\t\ti = triggers.length;\n\t\t\t\twhile (--i > -1) {\n\t\t\t\t\ttrigger = triggers[i];\n\t\t\t\t\t_setStyle(trigger, \"touchCallout\", null);\n\t\t\t\t\t_removeListener(trigger, \"mousedown\", onPress);\n\t\t\t\t\t_removeListener(trigger, \"touchstart\", onPress);\n\t\t\t\t\t_removeListener(trigger, \"click\", onClick);\n\t\t\t\t\t_removeListener(trigger, \"contextmenu\", onContextMenu);\n\t\t\t\t}\n\t\t\t\t_setSelectable(triggers, true);\n\t\t\t\tif (touchEventTarget) {\n\t\t\t\t\t_removeListener(touchEventTarget, \"touchcancel\", onRelease);\n\t\t\t\t\t_removeListener(touchEventTarget, \"touchend\", onRelease);\n\t\t\t\t\t_removeListener(touchEventTarget, \"touchmove\", onMove);\n\t\t\t\t}\n\t\t\t\t_removeListener(ownerDoc, \"mouseup\", onRelease);\n\t\t\t\t_removeListener(ownerDoc, \"mousemove\", onMove);\n\t\t\t}\n\t\t\t_removeScrollListener(target, updateScroll);\n\t\t\tenabled = false;\n\t\t\tInertiaPlugin && type !== \"soft\" && InertiaPlugin.untrack(scrollProxy || target, (xyMode ? \"x,y\" : rotationMode ? \"rotation\" : \"top,left\"));\n\t\t\tscrollProxy && scrollProxy.disable();\n\t\t\t_removeFromRenderQueue(render);\n\t\t\tself.isDragging = self.isPressed = isClicking = false;\n\t\t\tdragging && _dispatchEvent(self, \"dragend\", \"onDragEnd\");\n\t\t\treturn self;\n\t\t};\n\n\t\tthis.enabled = function(value, type) {\n\t\t\treturn arguments.length ? (value ? self.enable(type) : self.disable(type)) : enabled;\n\t\t};\n\n\t\tthis.kill = function() {\n\t\t\tself.isThrowing = false;\n\t\t\tself.tween && self.tween.kill();\n\t\t\tself.disable();\n\t\t\tgsap.set(triggers, {clearProps:\"userSelect\"});\n\t\t\tdelete _lookup[target._gsDragID];\n\t\t\treturn self;\n\t\t};\n\n\t\tif (~type.indexOf(\"scroll\")) {\n\t\t\tscrollProxy = this.scrollProxy = new ScrollProxy(target, _extend({onKill:function() { //ScrollProxy's onKill() gets called if/when the ScrollProxy senses that the user interacted with the scroll position manually (like using the scrollbar). IE9 doesn't fire the \"mouseup\" properly when users drag the scrollbar of an element, so this works around that issue.\n\t\t\t\t\tself.isPressed && onRelease(null);\n\t\t\t}}, vars));\n\t\t\t//a bug in many Android devices' stock browser causes scrollTop to get forced back to 0 after it is altered via JS, so we set overflow to \"hidden\" on mobile/touch devices (they hide the scroll bar anyway). That works around the bug. (This bug is discussed at https://code.google.com/p/android/issues/detail?id=19625)\n\t\t\ttarget.style.overflowY = (allowY && !_isTouchDevice) ? \"auto\" : \"hidden\";\n\t\t\ttarget.style.overflowX = (allowX && !_isTouchDevice) ? \"auto\" : \"hidden\";\n\t\t\ttarget = scrollProxy.content;\n\t\t}\n\n\t\tif (rotationMode) {\n\t\t\tkillProps.rotation = 1;\n\t\t} else {\n\t\t\tif (allowX) {\n\t\t\t\tkillProps[xProp] = 1;\n\t\t\t}\n\t\t\tif (allowY) {\n\t\t\t\tkillProps[yProp] = 1;\n\t\t\t}\n\t\t}\n\n\t\tgsCache.force3D = (\"force3D\" in vars) ? vars.force3D : true; //otherwise, normal dragging would be in 2D and then as soon as it's released and there's an inertia tween, it'd jump to 3D which can create an initial jump due to the work the browser must to do layerize it.\n\n\t\tthis.enable();\n\t}\n\n\n\n\n\tstatic register(core) {\n\t\tgsap = core;\n\t\t_initCore();\n\t}\n\n\tstatic create(targets, vars) {\n\t\t_coreInitted || _initCore(true);\n\t\treturn _toArray(targets).map(target => new Draggable(target, vars));\n\t}\n\n\tstatic get(target) {\n\t\treturn _lookup[(_toArray(target)[0] || {})._gsDragID];\n\t}\n\n\tstatic timeSinceDrag() {\n\t\treturn (_getTime() - _lastDragTime) / 1000;\n\t}\n\n\tstatic hitTest(obj1, obj2, threshold) {\n\t\tif (obj1 === obj2) {\n\t\t\treturn false;\n\t\t}\n\t\tlet r1 = _parseRect(obj1),\n\t\t\tr2 = _parseRect(obj2),\n\t\t\t{ top, left, right, bottom, width, height } = r1,\n\t\t\tisOutside = (r2.left > right || r2.right < left || r2.top > bottom || r2.bottom < top),\n\t\t\toverlap, area, isRatio;\n\t\tif (isOutside || !threshold) {\n\t\t\treturn !isOutside;\n\t\t}\n\t\tisRatio = ((threshold + \"\").indexOf(\"%\") !== -1);\n\t\tthreshold = parseFloat(threshold) || 0;\n\t\toverlap = {left: Math.max(left, r2.left), top: Math.max(top, r2.top)};\n\t\toverlap.width = Math.min(right, r2.right) - overlap.left;\n\t\toverlap.height = Math.min(bottom, r2.bottom) - overlap.top;\n\t\tif (overlap.width < 0 || overlap.height < 0) {\n\t\t\treturn false;\n\t\t}\n\t\tif (isRatio) {\n\t\t\tthreshold *= 0.01;\n\t\t\tarea = overlap.width * overlap.height;\n\t\t\treturn (area >= width * height * threshold || area >= r2.width * r2.height * threshold);\n\t\t}\n\t\treturn (overlap.width > threshold && overlap.height > threshold);\n\t}\n\n}\n\n_setDefaults(Draggable.prototype, {pointerX:0, pointerY: 0, startX: 0, startY: 0, deltaX: 0, deltaY: 0, isDragging: false, isPressed: false});\n\nDraggable.zIndex = 1000;\nDraggable.version = \"3.11.3\";\n\n_getGSAP() && gsap.registerPlugin(Draggable);\n\nexport { Draggable as default };"], "names": ["_createSibling", "element", "i", "parentNode", "_doc", "_setDoc", "svg", "_svgOwner", "ns", "getAttribute", "type", "x", "y", "css", "e", "createElementNS", "replace", "createElement", "_svgContainer", "setAttribute", "append<PERSON><PERSON><PERSON>", "_divContainer", "style", "cssText", "_setMatrix", "m", "a", "b", "c", "d", "f", "_win", "_doc<PERSON>lement", "_body", "_identityMatrix", "_gEl", "_hasOffsetBug", "_transformProp", "_transformOriginProp", "doc", "ownerDocument", "window", "Matrix2D", "documentElement", "body", "transform", "d1", "d2", "position", "offsetParent", "<PERSON><PERSON><PERSON><PERSON>", "_forceNonZeroScale", "cache", "_gsap", "uncache", "get", "scaleX", "scaleY", "renderTransform", "push", "_svgTemps", "_divTemps", "_getDocScrollTop", "pageYOffset", "scrollTop", "_getDocScrollLeft", "pageXOffset", "scrollLeft", "ownerSVGElement", "tagName", "toLowerCase", "_isFixed", "getComputedStyle", "nodeType", "_placeSiblings", "adjustGOffset", "container", "cs", "isRootSVG", "siblings", "parent", "length", "_getCTM", "getCTM", "removeProperty", "clone", "getBBox", "baseVal", "numberOfItems", "_consolidate", "multiply", "getItem", "matrix", "offsetLeft", "offsetTop", "top", "left", "inverse", "this", "determinant", "a2", "b2", "c2", "e2", "f2", "equals", "apply", "point", "decoratee", "getGlobalMatrix", "includeScrollInFixed", "zeroScales", "temps", "b1", "getBoundingClientRect", "b3", "isFixed", "_windowExists", "_getGSAP", "gsap", "registerPlugin", "_isFunction", "value", "_isObject", "_isUndefined", "_emptyFunc", "_round", "Math", "round", "_createElement", "_copy", "obj", "factor", "p", "copy", "_setTouchActionForAllDescendants", "elements", "children", "touchAction", "_renderQueueTick", "_renderQueue", "for<PERSON>ach", "func", "_renderQueueTimeout", "ticker", "remove", "_removeFromRenderQueue", "splice", "to", "overwrite", "delay", "duration", "onComplete", "data", "_addListener", "capture", "addEventListener", "touchType", "_touchEventLookup", "_supportsPassive", "passive", "_removeListener", "removeEventListener", "_preventDefault", "event", "preventDefault", "preventManipulation", "_onMultiTouchDocumentEnd", "_isMultiTouching", "touches", "_dragCount", "target", "_onMultiTouchDocument", "_addScrollListener", "callback", "_isRoot", "_removeScrollListener", "_getMaxScroll", "axis", "dim", "scroll", "client", "max", "_recordMaxScrolls", "<PERSON><PERSON><PERSON><PERSON>", "_windowProxy", "_gsMaxScrollX", "_gsMaxScrollY", "_gsScrollX", "_gsScrollY", "_setStyle", "property", "_checkPrefix", "_getComputedStyle", "Element", "host", "_parseRect", "_tempRect", "width", "right", "clientWidth", "innerWidth", "height", "bottom", "innerHeight", "clientHeight", "r", "pageX", "_toArray", "pageY", "_dispatchEvent", "callback<PERSON><PERSON>", "result", "vars", "listeners", "_listeners", "callbackScope", "pointerEvent", "dispatchEvent", "_getBounds", "context", "offset", "_getElementBounds", "min", "minX", "minRotation", "minY", "maxX", "maxRotation", "maxY", "_parseInertia", "draggable", "snap", "forceZeroVelocity", "l", "Array", "end", "call", "velocity", "_isClickable", "onclick", "_clickableTagExp", "test", "nodeName", "_setSelectable", "selectable", "ondragstart", "onselectstart", "set", "lazy", "userSelect", "ScrollProxy", "utils", "toArray", "elementWidth", "elementHeight", "contentHeight", "nextNode", "transformStart", "transformEnd", "content", "document", "node", "<PERSON><PERSON><PERSON><PERSON>", "prevTop", "prevLeft", "scrollWidth", "scrollHeight", "extraPadRight", "maxLeft", "maxTop", "_supports3D", "force3D", "force", "arguments", "dif", "oldOffset", "killTweensOf", "onKill", "_skip", "paddingRight", "maxScrollTop", "maxScrollLeft", "disable", "nextS<PERSON>ling", "enable", "calibrate", "widthMatches", "offsetHeight", "display", "parseFloat", "paddingLeft", "_addPaddingBR", "overflow", "verticalAlign", "boxSizing", "paddingBottom", "_initCore", "required", "nav", "navigator", "_tempDiv", "_supportsPointer", "PointerEvent", "_placeholderDiv", "_defaultCursor", "cursor", "_isAndroid", "userAgent", "indexOf", "_isTouchDevice", "MaxTouchPoints", "msMaxTouchPoints", "div", "child", "childStyle", "val", "types", "standard", "split", "converted", "Object", "defineProperty", "_lookup", "isPressed", "endDrag", "_coreInitted", "InertiaPlugin", "plugins", "inertia", "checkPrefix", "console", "warn", "_isArray", "isArray", "_RAD2DEG", "PI", "_bigNum", "_getTime", "Date", "now", "getTime", "_lookupCount", "_lastDragTime", "_temp1", "_point1", "p1", "p2", "p3", "p4", "bbox", "isSVG", "viewBox", "borderLeftWidth", "borderRightWidth", "borderTopWidth", "borderBottomWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "list", "_this", "Draggable", "register", "core", "create", "targets", "map", "_gsDragID", "timeSinceDrag", "hitTest", "obj1", "obj2", "threshold", "overlap", "area", "isRatio", "r1", "r2", "isOutside", "_this2", "rotation", "dragResistance", "edgeResistance", "isNaN", "lockAxis", "autoScroll", "lockedAxis", "allowEventDefault", "getProperty", "getPropAsNum", "unit", "gsCache", "onContextMenu", "stopImmediatePropagation", "render", "suppressEvents", "self", "isDragging", "checkAutoScrollBounds", "dirty", "isRoot", "rect", "pointerX", "pointerY", "changeX", "changeY", "gap", "autoScrollFactor", "ownerDoc", "allowY", "autoScrollMarginBottom", "autoScrollMarginTop", "allowX", "autoScrollMarginRight", "autoScrollMarginLeft", "scrollTo", "setPointerPosition", "rotationMode", "deltaX", "scrollProxy", "deltaY", "xyMode", "hasDragCallback", "isDispatching", "syncXY", "skipOnUpdate", "skipSnap", "snappedValue", "getCache", "snapX", "snapY", "snapXY", "isThrowing", "buildSnapFunc", "n", "edgeTolerance", "closest", "absDif", "calculateBounds", "bounds", "targetBounds", "snapIsRaw", "hasBounds", "xProp", "yProp", "liveSnap", "points", "buildPointSnapFunc", "radius", "dx", "dy", "dist", "minDist", "onThrowComplete", "onThrowInterrupt", "animate", "tween", "overshootTolerance", "resistance", "throwResistance", "linkedProps", "minDuration", "maxDuration", "overshoot", "onInterrupt", "onUpdate", "fastMode", "onUpdateParams", "endX", "endY", "endRotation", "play", "applyBounds", "updateMatrix", "shiftStart", "start", "startPointerX", "startPointerY", "recordStartPositions", "parsed<PERSON><PERSON>in", "offsetX", "offsetY", "innerMatrix", "startElementY", "startElementX", "isTweening", "xOrigin", "y<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "atan2", "startX", "startY", "removePlaceholder", "onPress", "enabled", "clickTime", "isPreventingDefault", "interrupted", "dragged", "touchEventTarget", "currentTarget", "onRelease", "onMove", "touchDragAxis", "isClicking", "isClickable", "dragClickables", "triggers", "allowNativeTouchScrolling", "allowContextMenu", "ctrl<PERSON>ey", "which", "changedTouches", "touch", "touchID", "identifier", "pointerId", "_addToRenderQueue", "add", "kill", "killProps", "zIndexBoost", "zIndex", "onDrag", "drag", "hasMoveCallback", "move", "activeCursor", "updateScroll", "onClick", "time", "recentlyClicked", "recentlyDragged", "dragEndTime", "alreadyDispatched", "clickDispatch", "defaultPrevented", "alreadyDispatchedTrusted", "trustedClickDispatch", "trusted", "isTrusted", "suppressClickOnDrag", "detail", "localizePoint", "old", "minimumMovement", "trigger", "handle", "clickableTest", "isActive", "originalEvent", "abs", "lockAxisOnTouchScroll", "onLockAxis", "cancelable", "invokeOnMove", "xChange", "yChange", "temp", "dragTolerance", "prevPointerX", "prevPointerY", "prevStartElementY", "prevX", "prevY", "prevEndX", "prevEndY", "prevEndRotation", "prevDirty", "_hasTouchID", "ID", "syntheticEvent", "eventTarget", "syntheticClick", "wasDragging", "isContextMenuRelease", "placeholder<PERSON><PERSON><PERSON><PERSON><PERSON>", "delayedCall", "throwProps", "click", "createEvent", "initMouseEvent", "screenX", "screenY", "startDrag", "align", "timeSinceClick", "getDirection", "from", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ratio", "direction", "mode", "getVelocity", "newBounds", "sticky", "update", "ignoreExternalChanges", "m2", "id", "setVars", "touchCallout", "track", "dragging", "untrack", "clearProps", "_extend", "defaults", "overflowY", "overflowX", "_setDefaults", "prototype", "version"], "mappings": ";;;;;;;;;6MAyFkB,SAAjBA,EAAkBC,EAASC,MACtBD,EAAQE,aAAeC,GAAQC,EAAQJ,IAAW,KACjDK,EAAMC,EAAUN,GACnBO,EAAKF,EAAOA,EAAIG,aAAa,UAAY,6BAAgC,+BACzEC,EAAOJ,EAAOJ,EAAI,OAAS,IAAO,MAClCS,EAAU,IAANT,EAAU,EAAI,IAClBU,EAAU,IAANV,EAAU,IAAM,EACpBW,EAAM,0EACNC,EAAIV,EAAKW,gBAAkBX,EAAKW,gBAAgBP,EAAGQ,QAAQ,SAAU,QAASN,GAAQN,EAAKa,cAAcP,UACtGR,IACEI,GAScY,EAAlBA,GAAkClB,EAAeC,GACjDa,EAAEK,aAAa,QAAS,KACxBL,EAAEK,aAAa,SAAU,KACzBL,EAAEK,aAAa,YAAa,aAAeR,EAAI,IAAMC,EAAI,KACzDM,EAAcE,YAAYN,KAZrBO,KACJA,EAAgBrB,EAAeC,IACjBqB,MAAMC,QAAUV,GAE/BC,EAAEQ,MAAMC,QAAUV,EAAM,gCAAkCD,EAAI,WAAaD,EAAI,KAC/EU,EAAcD,YAAYN,KAUrBA,OAEF,4BA4FM,SAAbU,EAAcC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGf,EAAGgB,UAC/BL,EAAEC,EAAIA,EACND,EAAEE,EAAIA,EACNF,EAAEG,EAAIA,EACNH,EAAEI,EAAIA,EACNJ,EAAEX,EAAIA,EACNW,EAAEK,EAAIA,EACCL,EA7MT,IAAIrB,EAAM2B,EAAMC,EAAaC,EAAOZ,EAAeH,EAAegB,EAAiBC,EAGlFC,IAFAC,EAAiB,YACjBC,EAAuBD,EAAiB,SAExChC,EAAU,SAAVA,QAAUJ,OACLsC,EAAMtC,EAAQuC,eAAiBvC,IAC7BoC,KAAkBpC,EAAQqB,QAAU,gBAAiBrB,EAAQqB,QAElEgB,GADAD,EAAiB,eACuB,eAElCE,EAAIpC,aAAeoC,EAAMA,EAAIpC,iBACpC4B,EAAOU,OACPP,EAAkB,IAAIQ,GAClBH,EAAK,CAERP,GADA5B,EAAOmC,GACWI,gBAClBV,EAAQM,EAAIK,MACZT,EAAO/B,EAAKW,gBAAgB,6BAA8B,MAErDO,MAAMuB,UAAY,WAEnBC,EAAKP,EAAItB,cAAc,OAC1B8B,EAAKR,EAAItB,cAAc,OACxBgB,EAAMb,YAAY0B,GAClBA,EAAG1B,YAAY2B,GACfD,EAAGxB,MAAM0B,SAAW,SACpBF,EAAGxB,MAAMe,GAAkB,uBAC3BD,EAAiBW,EAAGE,eAAiBH,EACrCb,EAAMiB,YAAYJ,UAEZP,GAERY,EAAqB,SAArBA,mBAAqBrC,WAChBY,EAAG0B,EACAtC,GAAKA,IAAMmB,IACjBmB,EAAQtC,EAAEuC,QACDD,EAAME,SAAWF,EAAMG,IAAIzC,EAAG,KACnCsC,IAAUA,EAAMI,SAAWJ,EAAMK,QAAUL,EAAMM,kBACpDN,EAAMI,OAASJ,EAAMK,OAAS,KAC9BL,EAAMM,gBAAgB,EAAGN,GACzB1B,EAAIA,EAAEiC,KAAKP,GAAU1B,EAAI,CAAC0B,IAE3BtC,EAAIA,EAAEX,kBAEAuB,GAoBRkC,EAAY,GACZC,EAAY,GACZC,EAAmB,SAAnBA,0BAAyB/B,EAAKgC,aAAgB3D,EAAK4D,WAAahC,EAAYgC,WAAa/B,EAAM+B,WAAa,GAC5GC,EAAoB,SAApBA,2BAA0BlC,EAAKmC,aAAe9D,EAAK+D,YAAcnC,EAAYmC,YAAclC,EAAMkC,YAAc,GAC/G5D,EAAY,SAAZA,UAAYN,UAAWA,EAAQmE,kBAA6D,SAAxCnE,EAAQoE,QAAU,IAAIC,cAA0BrE,EAAU,OAC9GsE,EAAW,SAAXA,SAAWtE,SACsC,UAA5C8B,EAAKyC,iBAAiBvE,GAAS+C,YAGnC/C,EAAUA,EAAQE,aACkB,IAArBF,EAAQwE,SACfF,SAAStE,YAsDlByE,EAAiB,SAAjBA,eAAkBzE,EAAS0E,OAKzBC,EAAWnD,EAAGE,EAAGhB,EAAGC,EAAGiE,EAJpBvE,EAAMC,EAAUN,GACnB6E,EAAY7E,IAAYK,EACxByE,EAAWzE,EAAMsD,EAAYC,EAC7BmB,EAAS/E,EAAQE,cAEdF,IAAY8B,SACR9B,KAER8E,EAASE,QAAUF,EAASpB,KAAK3D,EAAeC,EAAS,GAAID,EAAeC,EAAS,GAAID,EAAeC,EAAS,IACjH2E,EAAYtE,EAAMY,EAAgBG,EAC9Bf,EACCwE,GAEHnE,IADAgB,EA1BO,SAAVuD,QAAU5E,OAERuC,EADGpB,EAAInB,EAAI6E,gBAEP1D,IACJoB,EAAYvC,EAAIgB,MAAMe,GACtB/B,EAAIgB,MAAMe,GAAkB,OAC5B/B,EAAIc,YAAYe,GAChBV,EAAIU,EAAKgD,SACT7E,EAAI4C,YAAYf,GAChBU,EAAavC,EAAIgB,MAAMe,GAAkBQ,EAAavC,EAAIgB,MAAM8D,eAAe/C,EAAerB,QAAQ,WAAY,OAAOsD,gBAEnH7C,GAAKS,EAAgBmD,QAetBH,CAAQjF,IACLa,EAAIa,EAAED,EACbd,GAAKe,EAAEG,EAAIH,EAAEE,EACbJ,EAAIS,GACMjC,EAAQqF,SAClB3D,EAAI1B,EAAQqF,UAGZ3E,GADAc,GADAA,EAAIxB,EAAQ4C,UAAY5C,EAAQ4C,UAAU0C,QAAU,IAC7CC,cAAoD,EAAlB/D,EAAE+D,cAzC/B,SAAfC,aAAehE,WACVG,EAAI,IAAIc,GACXxC,EAAI,EACEA,EAAIuB,EAAE+D,cAAetF,IAC3B0B,EAAE8D,SAASjE,EAAEkE,QAAQzF,GAAG0F,eAElBhE,EAmC0D6D,CAAahE,GAAKA,EAAEkE,QAAQ,GAAGC,OAAvE1D,GACjBR,EAAIC,EAAEhB,EAAIc,EAAEG,EAAID,EAAEf,EACxBA,EAAIa,EAAEE,EAAIA,EAAEhB,EAAIc,EAAEI,EAAIF,EAAEf,IAExBa,EAAI,IAAIiB,GACR/B,EAAIC,EAAI,GAEL+D,GAAmD,MAAlC1E,EAAQoE,QAAQC,gBACpC3D,EAAIC,EAAI,IAERkE,EAAYxE,EAAM0E,GAAQ5D,YAAYwD,GACvCA,EAAUzD,aAAa,YAAa,UAAYM,EAAEC,EAAI,IAAMD,EAAEE,EAAI,IAAMF,EAAEG,EAAI,IAAMH,EAAEI,EAAI,KAAOJ,EAAEX,EAAIH,GAAK,KAAOc,EAAEK,EAAIlB,GAAK,SACxH,IACND,EAAIC,EAAI,EACJwB,MACHX,EAAIxB,EAAQgD,aACZtB,EAAI1B,GACS0B,EAANA,GAAUA,EAAExB,aAAewB,IAAMF,GAAKE,EAAExB,YACe,GAAxD4B,EAAKyC,iBAAiB7C,GAAGU,GAAkB,IAAI4C,SACnDtE,EAAIgB,EAAEkE,WACNjF,EAAIe,EAAEmE,UACNnE,EAAI,MAKa,cADpBkD,EAAK9C,EAAKyC,iBAAiBvE,IACpB+C,UAA2C,UAAhB6B,EAAG7B,aACpCvB,EAAIxB,EAAQgD,aACL+B,GAAUA,IAAWvD,GAC3Bd,GAAKqE,EAAOb,YAAc,EAC1BvD,GAAKoE,EAAOhB,WAAa,EACzBgB,EAASA,EAAO7E,YAGlBwB,EAAIiD,EAAUtD,OACZyE,IAAO9F,EAAQ6F,UAAYlF,EAAK,KAClCe,EAAEqE,KAAQ/F,EAAQ4F,WAAalF,EAAK,KACpCgB,EAAEU,GAAkBwC,EAAGxC,GACvBV,EAAEW,GAAwBuC,EAAGvC,GAM7BX,EAAEqB,SAA2B,UAAhB6B,EAAG7B,SAAuB,QAAU,WACjD/C,EAAQE,WAAWiB,YAAYwD,UAEzBA,GAYIlC,2BAKZuD,QAAA,uBACMvE,EAAoBwE,KAApBxE,EAAGC,EAAiBuE,KAAjBvE,EAAGC,EAAcsE,KAAdtE,EAAGC,EAAWqE,KAAXrE,EAAGf,EAAQoF,KAARpF,EAAGgB,EAAKoE,KAALpE,EACnBqE,EAAezE,EAAIG,EAAIF,EAAIC,GAAM,aAC3BJ,EACN0E,KACArE,EAAIsE,GACHxE,EAAIwE,GACJvE,EAAIuE,EACLzE,EAAIyE,GACHvE,EAAIE,EAAID,EAAIf,GAAKqF,IAChBzE,EAAII,EAAIH,EAAIb,GAAKqF,MAIrBT,SAAA,kBAASE,OACHlE,EAAoBwE,KAApBxE,EAAGC,EAAiBuE,KAAjBvE,EAAGC,EAAcsE,KAAdtE,EAAGC,EAAWqE,KAAXrE,EAAGf,EAAQoF,KAARpF,EAAGgB,EAAKoE,KAALpE,EACnBsE,EAAKR,EAAOlE,EACZ2E,EAAKT,EAAOhE,EACZ0E,EAAKV,EAAOjE,EACZoB,EAAK6C,EAAO/D,EACZ0E,EAAKX,EAAO9E,EACZ0F,EAAKZ,EAAO9D,SACNN,EAAW0E,KACjBE,EAAK1E,EAAI4E,EAAK1E,EACdwE,EAAKzE,EAAI2E,EAAKzE,EACdwE,EAAK3E,EAAIqB,EAAKnB,EACdyE,EAAK1E,EAAIoB,EAAKlB,EACdf,EAAIyF,EAAK7E,EAAI8E,EAAK5E,EAClBE,EAAIyE,EAAK5E,EAAI6E,EAAK3E,MAGpBwD,MAAA,wBACQ,IAAI3C,SAASwD,KAAKxE,EAAGwE,KAAKvE,EAAGuE,KAAKtE,EAAGsE,KAAKrE,EAAGqE,KAAKpF,EAAGoF,KAAKpE,MAGlE2E,OAAA,gBAAOb,OACDlE,EAAoBwE,KAApBxE,EAAGC,EAAiBuE,KAAjBvE,EAAGC,EAAcsE,KAAdtE,EAAGC,EAAWqE,KAAXrE,EAAGf,EAAQoF,KAARpF,EAAGgB,EAAKoE,KAALpE,SACZJ,IAAMkE,EAAOlE,GAAKC,IAAMiE,EAAOjE,GAAKC,IAAMgE,EAAOhE,GAAKC,IAAM+D,EAAO/D,GAAKf,IAAM8E,EAAO9E,GAAKgB,IAAM8D,EAAO9D,KAGhH4E,MAAA,eAAMC,EAAOC,YAAAA,IAAAA,EAAU,QACjBjG,EAAQgG,EAARhG,EAAGC,EAAK+F,EAAL/F,EACNc,EAAoBwE,KAApBxE,EAAGC,EAAiBuE,KAAjBvE,EAAGC,EAAcsE,KAAdtE,EAAGC,EAAWqE,KAAXrE,EAAGf,EAAQoF,KAARpF,EAAGgB,EAAKoE,KAALpE,SACjB8E,EAAUjG,EAAKA,EAAIe,EAAId,EAAIgB,EAAId,GAAM,EACrC8F,EAAUhG,EAAKD,EAAIgB,EAAIf,EAAIiB,EAAIC,GAAM,EAC9B8E,+BAjDIlF,EAAKC,EAAKC,EAAKC,EAAKf,EAAKgB,YAAzBJ,IAAAA,EAAE,YAAGC,IAAAA,EAAE,YAAGC,IAAAA,EAAE,YAAGC,IAAAA,EAAE,YAAGf,IAAAA,EAAE,YAAGgB,IAAAA,EAAE,GACtCN,EAAW0E,KAAMxE,EAAGC,EAAGC,EAAGC,EAAGf,EAAGgB,GA4D3B,SAAS+E,gBAAgB5G,EAASgG,EAAStB,EAAemC,OAC3D7G,IAAYA,EAAQE,aAAeC,GAAQC,EAAQJ,IAAU0C,kBAAoB1C,SAC9E,IAAIyC,OAERqE,EAAa5D,EAAmBlD,GAEnC+G,EADMzG,EAAUN,GACF2D,EAAYC,EAC1Be,EAAYF,EAAezE,EAAS0E,GACpCsC,EAAKD,EAAM,GAAGE,wBACdb,EAAKW,EAAM,GAAGE,wBACdC,EAAKH,EAAM,GAAGE,wBACdlC,EAASJ,EAAUzE,WACnBiH,GAAWN,GAAwBvC,EAAStE,GAC5CwB,EAAI,IAAIiB,IACN2D,EAAGL,KAAOiB,EAAGjB,MAAQ,KACrBK,EAAGN,IAAMkB,EAAGlB,KAAO,KACnBoB,EAAGnB,KAAOiB,EAAGjB,MAAQ,KACrBmB,EAAGpB,IAAMkB,EAAGlB,KAAO,IACpBkB,EAAGjB,MAAQoB,EAAU,EAAInD,KACzBgD,EAAGlB,KAAOqB,EAAU,EAAItD,SAE1BkB,EAAO9B,YAAY0B,GACfmC,MACHE,EAAKF,EAAW9B,OACTgC,MACNZ,EAAKU,EAAWE,IACbzD,OAAS6C,EAAG5C,OAAS,EACxB4C,EAAG3C,gBAAgB,EAAG2C,UAGjBJ,EAAUxE,EAAEwE,UAAYxE,ECxSf,SAAhB4F,UAAyC,oBAAZ5E,OAClB,SAAX6E,WAAiBC,IAASF,MAAoBE,GAAO9E,OAAO8E,OAASA,GAAKC,gBAAkBD,GAC9E,SAAdE,EAAcC,SAA2B,mBAAXA,EAClB,SAAZC,EAAYD,SAA2B,iBAAXA,EACb,SAAfE,EAAeF,eAA2B,IAAXA,EAClB,SAAbG,WAAmB,EAGV,SAATC,GAASJ,UAASK,KAAKC,MAAc,IAARN,GAAiB,IAE7B,SAAjBO,GAAkBvH,EAAMF,OACnBM,EAAIV,GAAKW,gBAAkBX,GAAKW,iBAAiBP,GAAM,gCAAgCQ,QAAQ,SAAU,QAASN,GAAQN,GAAKa,cAAcP,UAC1II,EAAEQ,MAAQR,EAAIV,GAAKa,cAAcP,GAajC,SAARwH,GAASC,EAAKC,OACEC,EAAXC,EAAO,OACND,KAAKF,EACTG,EAAKD,GAAKD,EAASD,EAAIE,GAAKD,EAASD,EAAIE,UAEnCC,EAU2B,SAAnCC,GAAoCC,EAAUd,WAE5Ce,EADGvI,EAAIsI,EAASvD,OAEV/E,KACNwH,EAASc,EAAStI,GAAGoB,MAAMoH,YAAchB,EAASc,EAAStI,GAAGoB,MAAM8D,eAAe,iBACnFqD,EAAWD,EAAStI,GAAGuI,WACXA,EAASxD,QAAUsD,GAAiCE,EAAUf,GAGzD,SAAnBiB,YAAyBC,GAAaC,QAAQ,SAAAC,UAAQA,MAOhC,SAAtBC,YAA6BH,GAAa3D,QAAUsC,GAAKyB,OAAOC,OAAON,IAC9C,SAAzBO,GAAyBJ,WACpB5I,EAAI0I,GAAa3D,OACd/E,KACF0I,GAAa1I,KAAO4I,GACvBF,GAAaO,OAAOjJ,EAAG,GAGzBqH,GAAK6B,GAAGL,GAAqB,CAACM,WAAU,EAAMC,MAAM,GAAIC,SAAS,EAAGC,WAAWT,GAAqBU,KAAK,eAU3F,SAAfC,GAAgBzJ,EAASS,EAAMoI,EAAMa,MAChC1J,EAAQ2J,iBAAkB,KACzBC,EAAYC,GAAkBpJ,GAClCiJ,EAAUA,IAAYI,EAAmB,CAACC,SAAS,GAAS,MAC5D/J,EAAQ2J,iBAAiBC,GAAanJ,EAAMoI,EAAMa,GACjDE,GAAanJ,IAASmJ,GAAc5J,EAAQ2J,iBAAiBlJ,EAAMoI,EAAMa,IAG1D,SAAlBM,GAAmBhK,EAASS,EAAMoI,MAC7B7I,EAAQiK,oBAAqB,KAC5BL,EAAYC,GAAkBpJ,GAClCT,EAAQiK,oBAAoBL,GAAanJ,EAAMoI,GAC9Ce,GAAanJ,IAASmJ,GAAc5J,EAAQiK,oBAAoBxJ,EAAMoI,IAGvD,SAAlBqB,GAAkBC,GACjBA,EAAMC,gBAAkBD,EAAMC,iBAC9BD,EAAME,qBAAuBF,EAAME,sBAUT,SAA3BC,GAA2BH,GAC1BI,GAAoBJ,EAAMK,SAAWC,GAAaN,EAAMK,QAAQxF,OAChEgF,GAAgBG,EAAMO,OAAQ,WAAYJ,IAGnB,SAAxBK,GAAwBR,GACvBI,GAAoBJ,EAAMK,SAAWC,GAAaN,EAAMK,QAAQxF,OAChEyE,GAAaU,EAAMO,OAAQ,WAAYJ,IAErB,SAAnBzG,GAAmBvB,UAAOR,GAAKgC,aAAgBxB,EAAIyB,WAAazB,EAAII,gBAAgBqB,WAAazB,EAAIK,KAAKoB,WAAa,EACnG,SAApBC,GAAoB1B,UAAOR,GAAKmC,aAAe3B,EAAI4B,YAAc5B,EAAII,gBAAgBwB,YAAc5B,EAAIK,KAAKuB,YAAc,EACrG,SAArB0G,GAAsB/J,EAAGgK,GACxBpB,GAAa5I,EAAG,SAAUgK,GACrBC,GAAQjK,EAAEX,aACd0K,GAAmB/J,EAAEX,WAAY2K,GAGX,SAAxBE,GAAyBlK,EAAGgK,GAC3Bb,GAAgBnJ,EAAG,SAAUgK,GACxBC,GAAQjK,EAAEX,aACd6K,GAAsBlK,EAAEX,WAAY2K,GAItB,SAAhBG,GAAiBhL,EAASiL,OACrBC,EAAgB,MAATD,EAAgB,QAAU,SACpCE,EAAS,SAAWD,EACpBE,EAAS,SAAWF,SACdpD,KAAKuD,IAAI,EAAGP,GAAQ9K,GAAW8H,KAAKuD,IAAItJ,GAAYoJ,GAASnJ,EAAMmJ,KAAYrJ,GAAK,QAAUoJ,IAAQnJ,GAAYqJ,IAAWpJ,EAAMoJ,IAAWpL,EAAQmL,GAAUnL,EAAQoL,IAE5J,SAApBE,GAAqBzK,EAAG0K,OACnB7K,EAAIsK,GAAcnK,EAAG,KACxBF,EAAIqK,GAAcnK,EAAG,KAClBiK,GAAQjK,GACXA,EAAI2K,GAEJF,GAAkBzK,EAAEX,WAAYqL,GAEjC1K,EAAE4K,cAAgB/K,EAClBG,EAAE6K,cAAgB/K,EACb4K,IACJ1K,EAAE8K,WAAa9K,EAAEqD,YAAc,EAC/BrD,EAAE+K,WAAa/K,EAAEkD,WAAa,GAGpB,SAAZ8H,GAAa7L,EAAS8L,EAAUrE,OAC3BpG,EAAQrB,EAAQqB,MACfA,IAGDsG,EAAatG,EAAMyK,MACtBA,EAAWC,EAAaD,EAAU9L,IAAY8L,GAElC,MAATrE,EACHpG,EAAM8D,gBAAkB9D,EAAM8D,eAAe2G,EAAS/K,QAAQ,WAAY,OAAOsD,eAEjFhD,EAAMyK,GAAYrE,GAGA,SAApBuE,GAAoBhM,UAAW8B,GAAKyC,iBAAkBvE,aAAmBiM,QAAWjM,EAAUA,EAAQkM,OAASlM,EAAQE,YAAc,IAAIgM,MAAQlM,GAGpI,SAAbmM,GAAatL,MACRA,IAAMiB,UACTsK,EAAUrG,KAAOqG,EAAUtG,IAAM,EACjCsG,EAAUC,MAAQD,EAAUE,MAAQvK,GAAYwK,aAAe1L,EAAE2L,YAAcxK,EAAMuK,aAAe,EACpGH,EAAUK,OAASL,EAAUM,QAAW7L,EAAE8L,aAAe,GAAK,GAAK5K,GAAY6K,aAAgB7K,GAAY6K,aAAe/L,EAAE8L,aAAe3K,EAAM4K,cAAgB,EAC1JR,MAEJ9J,EAAMzB,EAAE0B,eAAiBpC,GAC5B0M,EAAKlF,EAAa9G,EAAEiM,OAAsLjM,EAAE2D,UAAamD,EAAa9G,EAAEkF,OAAU4B,EAAa9G,EAAEiF,KAAYiH,GAASlM,GAAG,GAAGoG,wBAAnBpG,EAA5O,CAACkF,KAAMlF,EAAEiM,MAAQ9I,GAAkB1B,GAAMwD,IAAKjF,EAAEmM,MAAQnJ,GAAiBvB,GAAMgK,MAAOzL,EAAEiM,MAAQ9I,GAAkB1B,GAAO,EAAGoK,OAAQ7L,EAAEmM,MAAQnJ,GAAiBvB,GAAO,UAChMqF,EAAakF,EAAEP,SAAW3E,EAAakF,EAAER,QAC5CQ,EAAEP,MAAQO,EAAE9G,KAAO8G,EAAER,MACrBQ,EAAEH,OAASG,EAAE/G,IAAM+G,EAAEJ,QACX9E,EAAakF,EAAER,SACzBQ,EAAI,CAACR,MAAOQ,EAAEP,MAAQO,EAAE9G,KAAM0G,OAAQI,EAAEH,OAASG,EAAE/G,IAAKwG,MAAOO,EAAEP,MAAOvG,KAAM8G,EAAE9G,KAAM2G,OAAQG,EAAEH,OAAQ5G,IAAK+G,EAAE/G,MAEzG+G,EAGS,SAAjBI,GAAkBvC,EAAQjK,EAAMyM,OAI9BC,EAHGC,EAAO1C,EAAO0C,KACjBvC,EAAWuC,EAAKF,GAChBG,EAAY3C,EAAO4C,WAAW7M,UAE3B+G,EAAYqD,KACfsC,EAAStC,EAASpE,MAAM2G,EAAKG,eAAiB7C,EAAQ0C,EAAKF,EAAe,WAAa,CAACxC,EAAO8C,gBAE5FH,IAA4C,IAA/B3C,EAAO+C,cAAchN,KACrC0M,GAAS,GAEHA,EAEK,SAAbO,GAAchD,EAAQiD,OAEpB7H,EAAKC,EAAM6H,EADR/M,EAAIkM,GAASrC,GAAQ,UAEpB7J,EAAE2D,UAAY3D,IAAMiB,GASlB+L,EAAkBhN,EAAG8M,GARtBhG,EAAa+C,EAAO3E,MAMlB,CAACA,KAFRA,EAAO2E,EAAOoD,KAAOpD,EAAOqD,MAAQrD,EAAOsD,aAAe,EAEvClI,IADnBA,EAAM4E,EAAOoD,KAAOpD,EAAOuD,MAAQ,EACP5B,OAAO3B,EAAOW,KAAOX,EAAOwD,MAAQxD,EAAOyD,aAAe,GAAKpI,EAAM0G,QAAQ/B,EAAOW,KAAOX,EAAO0D,MAAQ,GAAKtI,IAL1I8H,EAAS,CAAClN,EAAE,EAAGC,EAAE,GACV,CAACoF,KAAM2E,EAAO3E,KAAO6H,EAAOlN,EAAGoF,IAAK4E,EAAO5E,IAAM8H,EAAOjN,EAAG0L,MAAO3B,EAAO2B,MAAOI,OAAQ/B,EAAO+B,SAwDzF,SAAhB4B,GAAiBC,EAAWC,EAAMlD,EAAKyC,EAAK3F,EAAQqG,OAElD/M,EAAGxB,EAAGwO,EADHrB,EAAO,MAEPmB,KACY,IAAXpG,GAAgBoG,aAAgBG,MAAO,IAC1CtB,EAAKuB,IAAMlN,EAAI,GACfgN,EAAIF,EAAKvJ,OACL0C,EAAU6G,EAAK,QACbtO,EAAI,EAAGA,EAAIwO,EAAGxO,IAClBwB,EAAExB,GAAKgI,GAAMsG,EAAKtO,GAAIkI,YAGlBlI,EAAI,EAAGA,EAAIwO,EAAGxO,IAClBwB,EAAExB,GAAKsO,EAAKtO,GAAKkI,EAGnBkD,GAAO,IACPyC,GAAO,SACGtG,EAAY+G,GACtBnB,EAAKuB,IAAM,SAAAlH,OAETY,EAAMD,EADH+E,EAASoB,EAAKK,KAAKN,EAAW7G,MAEnB,IAAXU,KACCT,EAAUyF,GAAS,KAEjB/E,KADLC,EAAO,GACG8E,EACT9E,EAAKD,GAAK+E,EAAO/E,GAAKD,EAEvBgF,EAAS9E,OAET8E,GAAUhF,SAGLgF,GAGRC,EAAKuB,IAAMJ,SAGTlD,GAAe,IAARA,IACV+B,EAAK/B,IAAMA,IAERyC,GAAe,IAARA,IACVV,EAAKU,IAAMA,GAERU,IACHpB,EAAKyB,SAAW,GAEVzB,EAEO,SAAf0B,GAAe9O,OACVwJ,WACKxJ,IAAYA,EAAQQ,cAAgBR,IAAYgC,OAAsE,UAAnDwH,EAAOxJ,EAAQQ,aAAa,qBAA2C,UAATgJ,IAAqBxJ,EAAQ+O,UAAWC,EAAiBC,KAAKjP,EAAQkP,SAAW,KAAmD,SAA5ClP,EAAQQ,aAAa,sBAA0CsO,GAAa9O,EAAQE,aAEtS,SAAjBiP,GAAkB5G,EAAU6G,WAE1BvO,EADGZ,EAAIsI,EAASvD,OAEV/E,MACNY,EAAI0H,EAAStI,IACXoP,YAAcxO,EAAEyO,cAAgBF,EAAa,KAAOxH,EACtDN,GAAKiI,IAAI1O,EAAG,CAAC2O,MAAK,EAAMC,WAAaL,EAAa,OAAS,SAe/C,SAAdM,GAAuB1P,EAASoN,GAC/BpN,EAAUsH,GAAKqI,MAAMC,QAAQ5P,GAAS,GACtCoN,EAAOA,GAAQ,OAadyC,EAAcC,EAAeC,EAAeC,EAAUC,EAAgBC,EAZnEC,EAAUC,SAASpP,cAAc,OACpCK,EAAQ8O,EAAQ9O,MAChBgP,EAAOrQ,EAAQsQ,WACfzK,EAAY,EACZD,EAAa,EACb2K,EAAUvQ,EAAQ+D,UAClByM,EAAWxQ,EAAQkE,WACnBuM,EAAczQ,EAAQyQ,YACtBC,EAAe1Q,EAAQ0Q,aACvBC,EAAgB,EAChBC,EAAU,EACVC,EAAS,EAENC,IAAgC,IAAjB1D,EAAK2D,SACvBd,EAAiB,eACjBC,EAAe,WACL9N,IACV6N,EAAiB,aACjBC,EAAe,YAEXnM,UAAY,SAAS0D,EAAOuJ,OAC3BC,UAAUjM,cACNiB,KAAKH,WAETA,KAAK2B,EAAOuJ,SAEb9M,WAAa,SAASuD,EAAOuJ,OAC5BC,UAAUjM,cACNiB,KAAKF,YAETA,MAAM0B,EAAOuJ,SAEdjL,KAAO,SAAS0B,EAAOuJ,OACtBC,UAAUjM,eACLhF,EAAQkE,WAAa0B,OAE3BsL,EAAMlR,EAAQkE,WAAasM,EAC9BW,EAAYvL,MACF,EAANsL,GAAWA,GAAO,KAAOF,SAC7BR,EAAWxQ,EAAQkE,WACnBoD,GAAK8J,aAAanL,KAAM,CAACF,KAAK,EAAG7B,WAAW,SACvC6B,MAAMyK,QACPpD,EAAKiE,QACRjE,EAAKiE,WAIP5J,GAASA,GACG,GACX7B,EAAc6B,EAAQ,GAAO,EAC7BA,EAAQ,GACUmJ,EAARnJ,GACV7B,EAAc6B,EAAQmJ,EAAW,EACjCnJ,EAAQmJ,GAERhL,EAAa,GAEVA,GAAcuL,KACZlL,KAAKqL,QACTjQ,EAAMe,GAAkB6N,GAAkBrK,EAAa,OAASC,EAAYqK,GAE3C,GAA9BtK,EAAa+K,IAChBtP,EAAMkQ,aAAgB3L,EAAa+K,EAAgB,OAGrD3Q,EAAQkE,WAAqB,EAARuD,EACrB+I,EAAWxQ,EAAQkE,iBAEf4B,IAAM,SAAS2B,EAAOuJ,OACrBC,UAAUjM,eACLhF,EAAQ+D,UAAY8B,OAE1BqL,EAAMlR,EAAQ+D,UAAYwM,EAC7BY,EAAYtL,MACF,EAANqL,GAAWA,GAAO,KAAOF,SAC7BT,EAAUvQ,EAAQ+D,UAClBuD,GAAK8J,aAAanL,KAAM,CAACH,IAAI,EAAG/B,UAAU,SACrC+B,KAAKyK,QACNnD,EAAKiE,QACRjE,EAAKiE,WAIP5J,GAASA,GACG,GACX5B,EAAa4B,EAAQ,GAAO,EAC5BA,EAAQ,GACUoJ,EAARpJ,GACV5B,EAAa4B,EAAQoJ,EAAU,EAC/BpJ,EAAQoJ,GAERhL,EAAY,GAETA,GAAasL,KACXlL,KAAKqL,QACTjQ,EAAMe,GAAkB6N,GAAkBrK,EAAa,OAASC,EAAYqK,IAG9ElQ,EAAQ+D,UAAoB,EAAR0D,EACpB8I,EAAUvQ,EAAQ+D,gBAGdyN,aAAe,kBAAMX,QACrBY,cAAgB,kBAAMb,QAEtBc,QAAU,eACdrB,EAAOF,EAAQG,WACRD,GACNL,EAAWK,EAAKsB,YAChB3R,EAAQmB,YAAYkP,GACpBA,EAAOL,EAEJhQ,IAAYmQ,EAAQjQ,YACvBF,EAAQiD,YAAYkN,SAGjByB,OAAS,eACbvB,EAAOrQ,EAAQsQ,cACFH,QAGNE,GACNL,EAAWK,EAAKsB,YAChBxB,EAAQhP,YAAYkP,GACpBA,EAAOL,EAERhQ,EAAQmB,YAAYgP,QACf0B,mBAEDA,UAAY,SAASb,OAExBpM,EAAIlE,EAAGC,EADJmR,EAAgB9R,EAAQuM,cAAgBsD,EAE5CU,EAAUvQ,EAAQ+D,UAClByM,EAAWxQ,EAAQkE,WACf4N,GAAgB9R,EAAQ4M,eAAiBkD,GAAiBK,EAAQ4B,eAAiBhC,GAAiBU,IAAgBzQ,EAAQyQ,aAAeC,IAAiB1Q,EAAQ0Q,eAAiBM,KAGrLnL,GAAaD,KAChBlF,EAAIuF,KAAKF,OACTpF,EAAIsF,KAAKH,WACJC,MAAM/F,EAAQkE,iBACd4B,KAAK9F,EAAQ+D,YAEnBa,EAAKoH,GAAkBhM,GAElB8R,IAAgBd,IACpB3P,EAAM2Q,QAAU,QAChB3Q,EAAMgL,MAAQ,OACdhL,EAAMkQ,aAAe,OACrBZ,EAAgB7I,KAAKuD,IAAI,EAAGrL,EAAQyQ,YAAczQ,EAAQuM,gBAGzDoE,GAAiBsB,WAAWrN,EAAGsN,cAAgBC,EAAgBF,WAAWrN,EAAG2M,cAAgB,KAG/FlQ,EAAM2Q,QAAU,eAChB3Q,EAAM0B,SAAW,WACjB1B,EAAM+Q,SAAW,UACjB/Q,EAAMgR,cAAgB,MACtBhR,EAAMiR,UAAY,cAClBjR,EAAMgL,MAAQ,OACdhL,EAAMkQ,aAAeZ,EAAgB,KAEjCwB,IACH9Q,EAAMkR,cAAgB3N,EAAG2N,eAE1B1C,EAAe7P,EAAQuM,YACvBuD,EAAgB9P,EAAQ4M,aACxB6D,EAAczQ,EAAQyQ,YACtBC,EAAe1Q,EAAQ0Q,aACvBE,EAAU5Q,EAAQyQ,YAAcZ,EAChCgB,EAAS7Q,EAAQ0Q,aAAeZ,EAChCC,EAAgBI,EAAQ4B,aACxB1Q,EAAM2Q,QAAU,SACZtR,GAAKC,UACHoF,KAAKrF,QACLoF,IAAInF,WAGNwP,QAAUA,OACVnQ,QAAUA,OACVsR,OAAQ,OACRM,SAEM,SAAZY,GAAYC,MACPrL,KAAmBgJ,SAASzN,KAAM,KACjC+P,EAAMlQ,QAAUA,OAAOmQ,UAC3B7Q,GAAOU,OACPrC,GAAOiQ,SACPrO,GAAc5B,GAAKuC,gBACnBV,EAAQ7B,GAAKwC,KACbiQ,EAAW5K,GAAe,OAC1B6K,KAAqBrQ,OAAOsQ,cAC5BC,GAAkB/K,GAAe,QACjB3G,MAAMC,QAAU,qGAChC0R,GAAkD,SAAjCD,GAAgB1R,MAAM4R,OAAoB,OAAS,OACpEC,GAAcR,IAA2D,IAApDA,EAAIS,UAAU9O,cAAc+O,QAAQ,WACzDC,GAAmB,iBAAkBtR,IAAiB,gBAAiBD,IAAW4Q,IAA6B,EAArBA,EAAIY,gBAA6C,EAAvBZ,EAAIa,kBAEnHC,EAAMxL,GAAe,OACxByL,EAAQzL,GAAe,OACvB0L,EAAaD,EAAMpS,MACnB0D,EAAS/C,EAEV0R,EAAW1B,QAAU,eACrB0B,EAAW3Q,SAAW,WACtByQ,EAAInS,MAAMC,QAAU,sEACpBkS,EAAIrS,YAAYsS,GAChB1O,EAAO5D,YAAYqS,GACnBG,EAAOF,EAAM1B,aAAe,GAAKyB,EAAI9C,aACrC3L,EAAO9B,YAAYuQ,GAZpBrB,EAaQwB,EAER9J,GAAqB,SAAS+J,WACzBC,EAAWD,EAAME,MAAM,KAC1BC,IAAa,kBAAmBnB,EAAW,kDAAoD,oBAAqBA,EAAW,0DAA4DgB,GAAOE,MAAM,MACxM5L,EAAM,GACNjI,EAAI,GACS,IAALA,GACRiI,EAAI2L,EAAS5T,IAAM8T,EAAU9T,GAC7BiI,EAAI6L,EAAU9T,IAAM4T,EAAS5T,OAI7B8B,GAAY4H,iBAAiB,OAAQ,KAAMqK,OAAOC,eAAe,GAAI,UAAW,CAC/E3Q,IAAK,eACJwG,EAAmB,MAGpB,MAAOjJ,WACFqH,EAjBa,CAkBnB,6CACFuB,GAAatJ,GAAM,cAAeyH,GAClC6B,GAAa3H,GAAM,YAAa8F,GAChC5F,GAASA,EAAM2H,iBAAiB,aAAc/B,GAC9C6B,GAAatJ,GAAM,cAAe,eAC5B,IAAIiI,KAAK8L,GACTA,GAAQ9L,GAAG+L,WACdD,GAAQ9L,GAAGgM,YAId9M,GAAO+M,GAAehN,IA5CL,IAKfsM,EAJGH,EACHC,EACAC,EACA3O,EA0CCuC,IACHgN,GAAgBhN,GAAKiN,QAAQC,QAC7BzI,EAAezE,GAAKqI,MAAM8E,YAC1BrS,EAAiB2J,EAAa3J,GAC9BC,GAAuB0J,EAAa1J,IACpC0K,GAAWzF,GAAKqI,MAAMC,QACtBkB,IAAgB/E,EAAa,gBACnB0G,GACViC,QAAQC,KAAK,yCA5kBhB,IAAIrN,GAAMxF,GAAM3B,GAAM4B,GAAaC,EAAO4Q,EAAUG,GAAiBsB,GAActI,EAAcgB,GAAUjD,EAAkBuJ,GAAgBxJ,GAAmBU,GAAkB2I,GAAYoB,GAAetB,GAAgBH,GA0U5N/B,EAAaqB,IAzUb1H,GAAa,EAObrI,EAAiB,YACjBC,GAAuB,kBAEvBuS,GAAWlG,MAAMmG,QAKjBC,GAAW,IAAMhN,KAAKiN,GACtBC,GAAU,KACV/S,EAAkB,IAAIQ,GACtBwS,GAAWC,KAAKC,KAAQ,kBAAM,IAAID,MAAOE,WACzCzM,GAAe,GACfuL,GAAU,GACVmB,GAAe,EACfrG,EAAmB,wCACnBsG,GAAgB,EAChBC,GAAS,GACT/J,GAAe,GAoGfV,GAAU,SAAVA,QAAUjK,WAASA,GAAKA,IAAMkB,IAA8B,IAAflB,EAAE2D,UAAkB3D,IAAMV,GAAKwC,MAAQ9B,IAAMiB,IAASjB,EAAE2D,UAAa3D,EAAEX,aAsCpHkM,EAAY,GA8CZoJ,GAAU,GACV3H,EAAoB,SAApBA,kBAAqB7N,EAAS2N,GAC7BA,EAAUZ,GAASY,GAAS,OAG3B5H,EAAMuG,EAAOxG,EAAK4G,EAAQ/G,EAAQ8P,EAAIC,EAAIC,EAAIC,EAAIC,EAAMxJ,EAAOI,EAAQ7H,EAFpEkR,EAAS9V,EAAQqF,SAAWrF,EAAQmE,gBACvC7B,EAAMtC,EAAQuC,eAAiBpC,MAE5BH,IAAY8B,GACfgE,EAAMjC,GAAiBvB,GAEvBgK,GADAvG,EAAO/B,GAAkB1B,KACTA,EAAII,gBAAgB6J,aAAevM,EAAQwM,YAAclK,EAAIK,KAAK4J,aAAe,GACjGG,EAAS5G,IAAS9F,EAAQ2M,aAAe,GAAK,GAAKrK,EAAII,gBAAgBkK,aAAgBtK,EAAII,gBAAgBkK,aAAe5M,EAAQ2M,aAAerK,EAAIK,KAAKiK,cAAgB,OACpK,CAAA,GAAIe,IAAY7L,IAAQ6F,EAAagG,UACpC3N,EAAQiH,wBAEflB,EAAOD,EAAO,EACVgQ,GAEHzJ,GADAwJ,EAAO7V,EAAQqF,WACFgH,MACbI,EAASoJ,EAAKpJ,SAEVzM,EAAQ+V,UAAYF,EAAO7V,EAAQ+V,QAAQzQ,WAC9CS,EAAO8P,EAAKnV,GAAK,EACjBoF,EAAM+P,EAAKlV,GAAK,EAChB0L,EAAQwJ,EAAKxJ,MACbI,EAASoJ,EAAKpJ,QAEVJ,IAEJwJ,EAAwB,gBADxBjR,EAAKoH,GAAkBhM,IACbsS,UACVjG,GAAS4F,WAAWrN,EAAGyH,QAAUrM,EAAQuM,aAAe,IAAMsJ,EAAO,EAAI5D,WAAWrN,EAAGoR,iBAAmB/D,WAAWrN,EAAGqR,mBACxHxJ,GAAUwF,WAAWrN,EAAG6H,SAAWzM,EAAQ4M,cAAgB,IAAMiJ,EAAO,EAAI5D,WAAWrN,EAAGsR,gBAAkBjE,WAAWrN,EAAGuR,sBAG5H7J,EAAQD,EACRK,EAASD,SAENzM,IAAY2N,EACR,CAAC5H,KAAKA,EAAMD,IAAIA,EAAKuG,MAAOC,EAAQvG,EAAM0G,OAAQC,EAAS5G,IAGnE2P,GADA9P,EAASiB,gBAAgB+G,GAAS,GAAMlI,SAASmB,gBAAgB5G,KACrDyG,MAAM,CAAC/F,EAAEqF,EAAMpF,EAAEmF,IAC7B4P,EAAK/P,EAAOc,MAAM,CAAC/F,EAAE4L,EAAO3L,EAAEmF,IAC9B6P,EAAKhQ,EAAOc,MAAM,CAAC/F,EAAE4L,EAAO3L,EAAE+L,IAC9BkJ,EAAKjQ,EAAOc,MAAM,CAAC/F,EAAEqF,EAAMpF,EAAE+L,IAGtB,CAAC3G,KAFRA,EAAO+B,KAAKgG,IAAI2H,EAAG/U,EAAGgV,EAAGhV,EAAGiV,EAAGjV,EAAGkV,EAAGlV,GAEjBoF,IADpBA,EAAMgC,KAAKgG,IAAI2H,EAAG9U,EAAG+U,EAAG/U,EAAGgV,EAAGhV,EAAGiV,EAAGjV,GACN0L,MAAOvE,KAAKuD,IAAIoK,EAAG/U,EAAGgV,EAAGhV,EAAGiV,EAAGjV,EAAGkV,EAAGlV,GAAKqF,EAAM0G,OAAQ3E,KAAKuD,IAAIoK,EAAG9U,EAAG+U,EAAG/U,EAAGgV,EAAGhV,EAAGiV,EAAGjV,GAAKmF,KAqVrHsQ,iCAOLzM,iBAAA,0BAAiBlJ,EAAMoK,OAClBwL,EAAOpQ,KAAKqH,WAAW7M,KAAUwF,KAAKqH,WAAW7M,GAAQ,KACvD4V,EAAKjD,QAAQvI,IAClBwL,EAAK3S,KAAKmH,MAIZZ,oBAAA,6BAAoBxJ,EAAMoK,OACrBwL,EAAOpQ,KAAKqH,WAAW7M,GAC1BR,EAAKoW,GAAQA,EAAKjD,QAAQvI,GACrB,GAAL5K,GAAWoW,EAAKnN,OAAOjJ,EAAG,MAG5BwN,cAAA,uBAAchN,OACT0M,gBACHlH,KAAKqH,WAAW7M,IAAS,IAAImI,QAAQ,SAAAiC,UAAwE,IAA3DA,EAAS+D,KAAK0H,EAAM,CAAC7V,KAAMA,EAAMiK,OAAQ4L,EAAK5L,WAAwByC,GAAS,KAC3HA,6CArBIzC,QACN4C,WAAa,QACb5C,OAASA,GAAUzE,YA+BbsQ,6HAAkBH,aA2sCvBI,SAAP,kBAAgBC,GACfnP,GAAOmP,EACPjE,gBAGMkE,OAAP,gBAAcC,EAASvJ,UACtBiH,IAAgB7B,IAAU,GACnBzF,GAAS4J,GAASC,IAAI,SAAAlM,UAAU,IAAI6L,UAAU7L,EAAQ0C,gBAGvD9J,IAAP,aAAWoH,UACHwJ,IAASnH,GAASrC,GAAQ,IAAM,IAAImM,sBAGrCC,cAAP,gCACS7B,KAAaK,IAAiB,eAGhCyB,QAAP,iBAAeC,EAAMC,EAAMC,MACtBF,IAASC,SACL,MAMPE,EAASC,EAAMC,EAJZC,EAAKnL,GAAW6K,GACnBO,EAAKpL,GAAW8K,GACdnR,EAA4CwR,EAA5CxR,IAAKC,EAAuCuR,EAAvCvR,KAAMuG,EAAiCgL,EAAjChL,MAAOI,EAA0B4K,EAA1B5K,OAAQL,EAAkBiL,EAAlBjL,MAAOI,EAAW6K,EAAX7K,OACnC+K,EAAaD,EAAGxR,KAAOuG,GAASiL,EAAGjL,MAAQvG,GAAQwR,EAAGzR,IAAM4G,GAAU6K,EAAG7K,OAAS5G,SAE/E0R,IAAcN,GACTM,GAETH,GAA8C,KAAlCH,EAAY,IAAI9D,QAAQ,KACpC8D,EAAYjF,WAAWiF,IAAc,GACrCC,EAAU,CAACpR,KAAM+B,KAAKuD,IAAItF,EAAMwR,EAAGxR,MAAOD,IAAKgC,KAAKuD,IAAIvF,EAAKyR,EAAGzR,OACxDuG,MAAQvE,KAAKgG,IAAIxB,EAAOiL,EAAGjL,OAAS6K,EAAQpR,KACpDoR,EAAQ1K,OAAS3E,KAAKgG,IAAIpB,EAAQ6K,EAAG7K,QAAUyK,EAAQrR,MACnDqR,EAAQ9K,MAAQ,GAAK8K,EAAQ1K,OAAS,KAGtC4K,EAGahL,EAAQI,GAFxByK,GAAa,OACbE,EAAOD,EAAQ9K,MAAQ8K,EAAQ1K,SACe2K,GAAQG,EAAGlL,MAAQkL,EAAG9K,OAASyK,EAEtEC,EAAQ9K,MAAQ6K,GAAaC,EAAQ1K,OAASyK,mCApvC3CxM,EAAQ0C,+BAEnBiH,IAAgB7B,GAAU,GAC1B9H,EAASqC,GAASrC,GAAQ,GAEzB4J,GADIA,IACYhN,GAAKiN,QAAQC,UAEzBpH,KAAOA,EAAOnF,GAAMmF,GAAQ,MAC5B1C,OAASA,IACThK,EAAI+W,EAAK9W,EAAI8W,EAAKC,SAAW,IAC7BC,eAAiB1F,WAAW7E,EAAKuK,iBAAmB,IACpDC,eAAiBC,MAAMzK,EAAKwK,gBAAkB,EAAI3F,WAAW7E,EAAKwK,iBAAmB,IACrFE,SAAW1K,EAAK0K,WAChBC,WAAa3K,EAAK2K,YAAc,IAChCC,WAAa,OACbC,oBAAsB7K,EAAK6K,kBAEhC3Q,GAAK4Q,YAAYxN,EAAQ,KAuBT,SAAfyN,GAAgBrM,EAAUsM,UAASnG,WAAWoG,GAAQ/U,IAAIoH,EAAQoB,EAAUsM,IAI5D,SAAhBE,GAAgBzX,UAEfqJ,GAAgBrJ,GAChBA,EAAE0X,0BAA4B1X,EAAE0X,4BACzB,EAIC,SAATC,GAASC,MACJC,EAAKX,YAAcW,EAAKC,aAAeC,IAAyBC,GAAQ,KAG1E9T,EAAQ+T,EAAQC,EAAMC,EAAUC,EAAUC,EAASC,EAASC,EAFzDvY,EAAI6J,EACP2O,EAAqC,GAAlBX,EAAKX,eAEzBa,IAAwB,EACxBpN,GAAazH,UAAkC,MAApBjC,GAAKgC,YAAuBhC,GAAKgC,YAAqD,MAAtCwV,GAAS5W,gBAAgBqB,UAAqBuV,GAAS5W,gBAAgBqB,UAAYuV,GAAS3W,KAAKoB,UAC5KyH,GAAatH,WAAmC,MAApBpC,GAAKmC,YAAuBnC,GAAKmC,YAAsD,MAAvCqV,GAAS5W,gBAAgBwB,WAAsBoV,GAAS5W,gBAAgBwB,WAAaoV,GAAS3W,KAAKuB,WAC/K8U,EAAWN,EAAKM,SAAWxN,GAAatH,WACxC+U,EAAWP,EAAKO,SAAWzN,GAAazH,UACjClD,IAAMiY,GAEZ/T,GADA+T,EAAShO,GAAQjK,EAAEX,aACDsL,GAAe3K,EAAEX,WACnC6Y,EAAOD,EAAS,CAACpM,OAAO5E,KAAKuD,IAAItJ,GAAY6K,aAAc9K,GAAK6K,aAAe,GAAIL,MAAOxE,KAAKuD,IAAItJ,GAAYwK,YAAazK,GAAK0K,YAAc,GAAIzG,KAAK,EAAGD,IAAI,GAAKf,EAAOkC,wBAC3KiS,EAAUC,EAAU,EAChBI,KACHH,EAAMrU,EAAO2G,cAAgB3G,EAAOhB,WAC1B,EACToV,EAAUC,EACAH,EAAWF,EAAKrM,OAAS8M,IAA0BJ,GAC7DR,IAAwB,EACxBO,EAAUrR,KAAKgG,IAAIsL,EAAMC,GAAoB,EAAIvR,KAAKuD,IAAI,EAAI0N,EAAKrM,OAASuM,GAAaO,IAA2B,IAC1GP,EAAWF,EAAKjT,IAAM2T,IAAuB1U,EAAOhB,YAC9D6U,IAAwB,EACxBO,GAAWrR,KAAKgG,IAAI/I,EAAOhB,UAAYsV,GAAoB,EAAIvR,KAAKuD,IAAI,EAAI4N,EAAWF,EAAKjT,KAAQ2T,IAAwB,IAEzHN,IACHpU,EAAOhB,WAAaoV,IAGlBO,KACHN,EAAMrU,EAAO0G,cAAgB1G,EAAOb,YAC1B,EACTgV,EAAUE,EACAJ,EAAWD,EAAKzM,MAAQqN,IAAyBP,GAC3DR,IAAwB,EACxBM,EAAUpR,KAAKgG,IAAIsL,EAAMC,GAAoB,EAAIvR,KAAKuD,IAAI,EAAI0N,EAAKzM,MAAQ0M,GAAaW,IAA0B,IACxGX,EAAWD,EAAKhT,KAAO6T,IAAwB7U,EAAOb,aAChE0U,IAAwB,EACxBM,GAAWpR,KAAKgG,IAAI/I,EAAOb,WAAamV,GAAoB,EAAIvR,KAAKuD,IAAI,EAAI2N,EAAWD,EAAKhT,MAAS6T,IAAyB,IAE5HV,IACHnU,EAAOb,YAAcgV,IAInBJ,IAAWI,GAAWC,KACzBrX,GAAK+X,SAAS9U,EAAOb,WAAYa,EAAOhB,WACxC+V,GAAmBpB,EAAKM,SAAWE,EAASR,EAAKO,SAAWE,IAE7DtY,EAAIkE,KAGF8T,EAAO,KACLnY,EAAQgY,EAARhY,EAAGC,EAAK+X,EAAL/X,EACJoZ,GACHrB,EAAKsB,OAAStZ,EAAIuR,WAAWoG,GAAQX,UACrCgB,EAAKhB,SAAWhX,EAChB2X,GAAQX,SAAWhX,EAAI,MACvB2X,GAAQ5U,gBAAgB,EAAG4U,KAEvB4B,GACCV,IACHb,EAAKwB,OAASvZ,EAAIsZ,EAAYnU,MAC9BmU,EAAYnU,IAAInF,IAEb+Y,IACHhB,EAAKsB,OAAStZ,EAAIuZ,EAAYlU,OAC9BkU,EAAYlU,KAAKrF,KAERyZ,GACNZ,IACHb,EAAKwB,OAASvZ,EAAIsR,WAAWoG,GAAQ1X,GACrC0X,GAAQ1X,EAAIA,EAAI,MAEb+Y,IACHhB,EAAKsB,OAAStZ,EAAIuR,WAAWoG,GAAQ3X,GACrC2X,GAAQ3X,EAAIA,EAAI,MAEjB2X,GAAQ5U,gBAAgB,EAAG4U,MAEvBkB,IACHb,EAAKwB,OAASvZ,EAAIsR,WAAWvH,EAAOrJ,MAAMyE,KAAO,GACjD4E,EAAOrJ,MAAMyE,IAAMnF,EAAI,MAEpB+Y,IACHhB,EAAKsB,OAAStZ,EAAIuR,WAAWvH,EAAOrJ,MAAM0E,MAAQ,GAClD2E,EAAOrJ,MAAM0E,KAAOrF,EAAI,QAIvB0Z,GAAoB3B,GAAmB4B,MAC1CA,GAAgB,KACZpN,GAAeyL,EAAM,OAAQ,YAC5BgB,IACHhB,EAAKhY,GAAKgY,EAAKsB,QAEZT,IACHb,EAAK/X,GAAK+X,EAAKwB,QAEhB1B,IAAO,IAER6B,GAAgB,GAGlBxB,GAAQ,EAIA,SAATyB,GAAUC,EAAcC,OAEtBC,EAAc7V,EADTlE,EAASgY,EAAThY,EAAGC,EAAM+X,EAAN/X,EAEJ+J,EAAOtH,QACXiV,GAAU/Q,GAAKmP,KAAKiE,SAAShQ,IAE9B2N,GAAQhV,SAAWiE,GAAK4Q,YAAYxN,EAAQ,KACxCyP,GACHzB,EAAKhY,EAAIuR,WAAWoG,GAAQ3X,GAC5BgY,EAAK/X,EAAIsR,WAAWoG,GAAQ1X,IAClBoZ,EACVrB,EAAKhY,EAAIgY,EAAKhB,SAAWzF,WAAWoG,GAAQX,UAClCuC,GACVvB,EAAK/X,EAAIsZ,EAAYnU,MACrB4S,EAAKhY,EAAIuZ,EAAYlU,SAErB2S,EAAK/X,EAAIsR,WAAWvH,EAAOrJ,MAAMyE,MAASlB,EAAKoH,GAAkBtB,KAAY9F,EAAGkB,MAAS,EACzF4S,EAAKhY,EAAIuR,WAAWvH,EAAOrJ,MAAM0E,OAASnB,GAAM,IAAImB,OAAS,IAEzD4U,GAASC,GAASC,KAAYL,IAAa9B,EAAKC,YAAcD,EAAKoC,cACnED,IACHtF,GAAO7U,EAAIgY,EAAKhY,EAChB6U,GAAO5U,EAAI+X,EAAK/X,GAChB8Z,EAAeI,EAAOtF,KACL7U,IAAMgY,EAAKhY,IAC3BgY,EAAKhY,EAAI+Z,EAAa/Z,EACtBmY,GAAQ,GAEL4B,EAAa9Z,IAAM+X,EAAK/X,IAC3B+X,EAAK/X,EAAI8Z,EAAa9Z,EACtBkY,GAAQ,IAGN8B,IACHF,EAAeE,EAAMjC,EAAKhY,MACLgY,EAAKhY,IACzBgY,EAAKhY,EAAI+Z,EACLV,IACHrB,EAAKhB,SAAW+C,GAEjB5B,GAAQ,GAGN+B,KACHH,EAAeG,EAAMlC,EAAK/X,MACL+X,EAAK/X,IACzB+X,EAAK/X,EAAI8Z,GAEV5B,GAAQ,IAGVA,GAASL,IAAO,GACX+B,IACJ7B,EAAKsB,OAAStB,EAAKhY,EAAIA,EACvBgY,EAAKwB,OAASxB,EAAK/X,EAAIA,EACvBsM,GAAeyL,EAAM,cAAe,kBAItB,SAAhBqC,GAAiBxM,EAAMT,EAAKzC,EAAKlD,UACrB,MAAP2F,IACHA,GAAOkH,IAEG,MAAP3J,IACHA,EAAM2J,IAEHxN,EAAY+G,GACR,SAAAyM,OACFC,EAAiBvC,EAAKvE,UAAgB,EAAIuE,EAAKd,eAAb,SAC/BrJ,EAAKK,KAAK8J,GAAWrN,EAAJ2P,EAAU3P,GAAO2P,EAAI3P,GAAO4P,EAAiBD,EAAIlN,EAAOA,GAAOkN,EAAIlN,GAAOmN,EAAgBD,GAAK7S,GAAUA,GAG/HyM,GAASrG,GACL,SAAAyM,WAILrH,EAAKzC,EAHFjR,EAAIsO,EAAKvJ,OACZkW,EAAU,EACVC,EAASnG,IAEI,IAAL/U,IAERiR,GADAyC,EAAMpF,EAAKtO,IACC+a,GACF,IACT9J,GAAOA,GAEJA,EAAMiK,GAAiBrN,GAAP6F,GAAcA,GAAOtI,IACxC6P,EAAUjb,EACVkb,EAASjK,UAGJ3C,EAAK2M,IAGPrD,MAAMtJ,GAAQ,SAAAyM,UAAKA,GAAI,kBAAMzM,EAAOpG,GAuD1B,SAAlBiT,SACKC,EAAQC,EAAc/M,EAAMgN,EAChCC,GAAY,EACRvB,GACHA,EAAYpI,YACZ6G,EAAK3K,KAAOA,GAAQkM,EAAYxI,gBAChCiH,EAAKzK,KAAOA,GAAQgM,EAAYzI,eAChCkH,EAAKxK,KAAOA,EAAOwK,EAAKtK,KAAOA,EAAO,EACtCoN,GAAY,GACApO,EAAKiO,SACjBA,EAAS3N,GAAWN,EAAKiO,OAAQ3Q,EAAOxK,YACpC6Z,GACHrB,EAAK3K,KAAOA,EAAOsN,EAAOtV,KAC1B2S,EAAKxK,KAAOA,EAAOmN,EAAOtV,KAAOsV,EAAOhP,MACxCqM,EAAKzK,KAAOA,EAAOyK,EAAKtK,KAAOA,EAAO,GAC3BzG,EAAayF,EAAKiO,OAAOnN,OAAUvG,EAAayF,EAAKiO,OAAOjN,OAOvEkN,EAAe5N,GAAWhD,EAAQA,EAAOxK,YACzCwY,EAAK3K,KAAOA,EAAOjG,KAAKC,MAAMoQ,GAAasD,EAAO,MAAQJ,EAAOtV,KAAOuV,EAAavV,MACrF2S,EAAKzK,KAAOA,EAAOnG,KAAKC,MAAMoQ,GAAauD,EAAO,MAAQL,EAAOvV,IAAMwV,EAAaxV,KACpF4S,EAAKxK,KAAOA,EAAOpG,KAAKC,MAAMgG,GAAQsN,EAAOhP,MAAQiP,EAAajP,QAClEqM,EAAKtK,KAAOA,EAAOtG,KAAKC,MAAMkG,GAAQoN,EAAO5O,OAAS6O,EAAa7O,WAVnE4O,EAASjO,EAAKiO,OACd3C,EAAK3K,KAAOA,EAAOsN,EAAOtN,KAC1B2K,EAAKzK,KAAOA,EAAOoN,EAAOpN,KAC1ByK,EAAKxK,KAAOA,EAAOmN,EAAOnN,KAC1BwK,EAAKtK,KAAOA,EAAOiN,EAAOjN,MAQhBF,EAAPH,IACH2K,EAAK3K,KAAOG,EACZwK,EAAKxK,KAAOA,EAAOH,EACnBA,EAAO2K,EAAK3K,MAEFK,EAAPH,IACHyK,EAAKzK,KAAOG,EACZsK,EAAKtK,KAAOA,EAAOH,EACnBA,EAAOyK,EAAKzK,MAET8L,IACHrB,EAAK1K,YAAcD,EACnB2K,EAAKvK,YAAcD,GAEpBsN,GAAY,GAETpO,EAAKuO,WACRpN,GAA0B,IAAlBnB,EAAKuO,SAAsBvO,EAAKmB,MAAQ,GAAMnB,EAAKuO,SAC3DJ,EAAa3G,GAASrG,IAAS/G,EAAY+G,GACvCwL,GACHY,EAAQI,GAAeQ,EAAYhN,EAAOA,EAAKmJ,SAAW3J,EAAMG,EAAM,GACtE0M,EAAQ,MAEJrM,EAAKqN,OACRf,EAxGiB,SAArBgB,mBAAsBtN,EAAMR,EAAMG,EAAMD,EAAMG,EAAM0N,EAAQ3T,UAC3D2T,EAAUA,GAAUA,EAAS9G,GAAW8G,EAASA,EAAS9G,GACtDxN,EAAY+G,GACR,SAAA7H,OAILyG,EAAQ4O,EAAIC,EAHTf,EAAiBvC,EAAKvE,UAAgB,EAAIuE,EAAKd,eAAb,EACrClX,EAAIgG,EAAMhG,EACVC,EAAI+F,EAAM/F,SAEX+F,EAAMhG,EAAIA,EAASwN,EAAJxN,EAAWwN,GAAQxN,EAAIwN,GAAQ+M,EAAiBva,EAAIqN,EAAQA,GAAQrN,EAAIqN,GAAQkN,EAAgBva,EAC/GgG,EAAM/F,EAAIA,EAASyN,EAAJzN,EAAWyN,GAAQzN,EAAIyN,GAAQ6M,EAAiBta,EAAIsN,EAAQA,GAAQtN,EAAIsN,GAAQgN,EAAgBta,GAC/GwM,EAASoB,EAAKK,KAAK8J,EAAMhS,MACVA,IACdA,EAAMhG,EAAIyM,EAAOzM,EACjBgG,EAAM/F,EAAIwM,EAAOxM,GAEH,IAAXwH,IACHzB,EAAMhG,GAAKyH,EACXzB,EAAM/F,GAAKwH,GAER2T,EAAS9G,KACZ+G,EAAKrV,EAAMhG,EAAIA,EACfsb,EAAKtV,EAAM/F,EAAIA,EACSmb,EAApBC,EAAKA,EAAKC,EAAKA,IAClBtV,EAAMhG,EAAIA,EACVgG,EAAM/F,EAAIA,IAGL+F,GAGLkO,GAASrG,GACL,SAAAnG,WAIL1H,EAAGC,EAAG+F,EAAOuV,EAHVhc,EAAIsO,EAAKvJ,OACZkW,EAAU,EACVgB,EAAUlH,IAEG,IAAL/U,IAIRgc,GAFAvb,GADAgG,EAAQ6H,EAAKtO,IACHS,EAAI0H,EAAE1H,GAELA,GADXC,EAAI+F,EAAM/F,EAAIyH,EAAEzH,GACGA,GACRub,IACVhB,EAAUjb,EACVic,EAAUD,UAGJC,GAAWJ,EAAUvN,EAAK2M,GAAW9S,GAGxC,SAAA4S,UAAKA,GAuDAa,CAAoBN,EAAYhN,EAAOA,EAAKqN,OAAS7N,EAAMG,EAAMD,EAAMG,EAAMG,EAAKuN,OAAQ7B,GAAe,EAAI,IAElHP,IACHiB,EAAQI,GAAeQ,EAAYhN,EAAOA,EAAK7N,GAAK6N,EAAKxI,MAAQwI,EAAKrK,WAAa6J,EAAMG,EAAM+L,GAAe,EAAI,IAE/GV,IACHqB,EAAQG,GAAeQ,EAAYhN,EAAOA,EAAK5N,GAAK4N,EAAKzI,KAAOyI,EAAKxK,UAAYkK,EAAMG,EAAM6L,GAAe,EAAI,MAOnG,SAAlBkC,KACCzD,EAAKoC,YAAa,EAClB7N,GAAeyL,EAAM,gBAAiB,mBAEpB,SAAnB0D,KACC1D,EAAKoC,YAAa,EAGT,SAAVuB,GAAW7H,EAAShG,OACfD,EAAMgN,EAAWe,EAAOC,EACxB/H,GAAWF,KACE,IAAZE,IACHjG,EAAOnB,EAAKmB,MAAQnB,EAAKuO,UAAY,GACrCJ,EAAa3G,GAASrG,IAAS/G,EAAY+G,GAC3CiG,EAAU,CAACgI,YAAYpP,EAAKqP,iBAAmBrP,EAAKoP,YAAc,MAASzC,EAAe,GAAK,IAC3FA,EACHvF,EAAQkD,SAAWrJ,GAAcqK,EAAM6C,EAAYhN,EAAOA,EAAKmJ,SAAUxJ,EAAMH,EAAM,EAAGS,IAEpFkL,IACHlF,EAAQiH,GAASpN,GAAcqK,EAAM6C,EAAYhN,EAAOA,EAAKqN,QAAUrN,EAAK7N,GAAK6N,EAAKxI,KAAMmI,EAAMH,EAAMkM,GAAe,EAAI,EAAGzL,GAA0C,MAApBkK,EAAKV,aAEtJuB,IACH/E,EAAQkH,GAASrN,GAAcqK,EAAM6C,EAAYhN,EAAOA,EAAKqN,QAAUrN,EAAK5N,GAAK4N,EAAKzI,IAAKsI,EAAMH,EAAMgM,GAAe,EAAI,EAAGzL,GAA0C,MAApBkK,EAAKV,cAErJzJ,EAAKqN,QAAWhH,GAASrG,IAAS7G,EAAU6G,EAAK,OACpDiG,EAAQkI,YAAcjB,EAAQ,IAAMC,EACpClH,EAAQsH,OAASvN,EAAKuN,UAIzBpD,EAAKoC,YAAa,EAClByB,EAAuB1E,MAAMzK,EAAKmP,oBAA0E,IAAxBnP,EAAKwK,eAAwB,EAAK,EAAIc,EAAKd,eAAkB,GAAxFxK,EAAKmP,mBACzD/H,EAAQlL,WACZkL,EAAQlL,SAAW,CAAC+B,IAAKvD,KAAKuD,IAAI+B,EAAKuP,aAAe,EAAI,gBAAiBvP,EAAQA,EAAKwP,YAAc,GAAI9O,IAAO+J,MAAMzK,EAAKuP,aAA0D,IAAvBJ,GAA6B7U,EAAU8M,IAAiC,IAArBA,EAAQgI,WAAsB,EAAI,GAAzGpP,EAAKuP,YAA0GE,UAAWN,IAEtQ7D,EAAK4D,MAAQA,EAAQhV,GAAK6B,GAAG8Q,GAAevP,EAAQ,CACnD8J,QAASA,EACThL,KAAM,aACND,WAAY4S,GACZW,YAAaV,GACbW,SAAW3P,EAAK4P,SAAW/P,GAAiBqN,GAC5C2C,eAAiB7P,EAAK4P,SAAW,CAACtE,EAAM,gBAAiB,iBAAoBnK,GAAQA,EAAKuN,OAAU,EAAC,GAAO,GAAQ,KAEhH1O,EAAK4P,WACL/C,IACHA,EAAY3I,OAAQ,GAErBgL,EAAM9D,OAAO,KAAK,GAAM,GACxB8B,IAAO,GAAM,GACb5B,EAAKwE,KAAOxE,EAAKhY,EACjBgY,EAAKyE,KAAOzE,EAAK/X,EACboZ,IACHrB,EAAK0E,YAAc1E,EAAKhY,GAEzB4b,EAAMe,KAAK,GACX/C,IAAO,GAAM,GACTL,IACHA,EAAY3I,OAAQ,KAGZkK,GACV9C,EAAK4E,cAIQ,SAAfC,GAAeC,OAEbpV,EADGqV,EAAQ9X,EAEZA,EAASiB,gBAAgB8D,EAAOxK,YAAY,GACxCsd,GAAc9E,EAAKvE,YAAcxO,EAAOa,OAAOiX,GAAS,IAAIhb,MAC/D2F,EAAIqV,EAAMzX,UAAUS,MAAM,CAAC/F,EAAEgd,EAAe/c,EAAEgd,IAC9ChY,EAAOc,MAAM2B,EAAGA,GAChBsV,EAAgBtV,EAAE1H,EAClBid,EAAgBvV,EAAEzH,GAEfgF,EAAOa,OAAOvE,KACjB0D,EAAS,MAIY,SAAvBiY,SAIEC,EAAcnd,EAAGC,EAHdsa,EAAgB,EAAIvC,EAAKd,eAC5BkG,EAAU3W,GAAUnD,GAAkBsV,IAAY,EAClDyE,EAAU5W,GAAUtD,GAAiByV,IAAY,EAE9Ca,IACH9B,GAAQ3X,EAAIyX,GAAasD,EAAO,MAAQ,KACxCpD,GAAQ1X,EAAIwX,GAAauD,EAAO,MAAQ,KACxCrD,GAAQ5U,mBAET8Z,IAAa,GACb/H,GAAQ9U,EAAIgY,EAAKM,SAAW8E,EAC5BtI,GAAQ7U,EAAI+X,EAAKO,SAAW8E,EAC5BpY,GAAUA,EAAOc,MAAM+O,GAASA,IAChCkI,EAAgBlI,GAAQ9U,EACxBid,EAAgBnI,GAAQ7U,EACpBkY,IACHiB,GAAmBpB,EAAKM,SAAUN,EAAKO,UACvCT,IAAO,IAERwF,EAAcpX,gBAAgB8D,GAC1BuP,GACHmB,KACA6C,EAAgBhE,EAAYnU,MAC5BoY,EAAgBjE,EAAYlU,SAGxBoY,MACH7D,IAAO,GAAM,GACbc,MAEA1C,EAAK4E,cAEFvD,GACH8D,EAAenT,EAAOvG,gBAAkB,CAACkU,GAAQ+F,QAAU1T,EAAOrF,UAAU3E,EAAG2X,GAAQgG,QAAU3T,EAAOrF,UAAU1E,IAAMqL,GAAkBtB,GAAQrI,KAAyB,OAAOyR,MAAM,KACxLwK,EAAiB5F,EAAK4F,eAAiB1X,gBAAgB8D,GAAQjE,MAAM,CAAC/F,EAAGuR,WAAW4L,EAAa,KAAO,EAAGld,EAAGsR,WAAW4L,EAAa,KAAO,IAC7IvD,IAAO,GAAM,GACb5Z,EAAIgY,EAAKM,SAAWsF,EAAe5d,EAAIod,EACvCnd,EAAI2d,EAAe3d,EAAI+X,EAAKO,SAAW8E,EACvCG,EAAgBxF,EAAKhY,EACrBud,EAAgBvF,EAAK/X,EAAImH,KAAKyW,MAAM5d,EAAGD,GAAKoU,KAK5CmJ,EAAgB9F,GAAauD,EAAO,MACpCwC,EAAgB/F,GAAasD,EAAO,QAIlCD,GAAaP,IACI/M,EAAhBgQ,EACHA,EAAgBhQ,GAAQgQ,EAAgBhQ,GAAQ+M,EACtCiD,EAAgBnQ,IAC1BmQ,EAAgBnQ,GAAQA,EAAOmQ,GAAiBjD,GAE5ClB,IACgB3L,EAAhB6P,EACHA,EAAgB7P,GAAQ6P,EAAgB7P,GAAQ6M,EACtCgD,EAAgBhQ,IAC1BgQ,EAAgBhQ,GAAQA,EAAOgQ,GAAiBhD,KAInDvC,EAAK8F,OAASN,EAAgBrW,GAAOqW,GACrCxF,EAAK+F,OAASR,EAAgBpW,GAAOoW,GAKlB,SAApBS,MACK3L,GAAgB7S,YAAeie,MAAiBzF,EAAKC,YACxD5F,GAAgB7S,WAAW+C,YAAY8P,IAK/B,SAAV4L,GAAW9d,EAAGmQ,OACT/Q,MACC2e,GAAWlG,EAAKvE,YAActT,KAAkB,cAAXA,EAAEJ,MAAmC,gBAAXI,EAAEJ,MAA4BuQ,IAASiE,KAAa4J,GAAY,IAAMhV,GAAkB6O,EAAKlL,aAAa/M,MAC7Kqe,GAAuBje,GAAK+d,GAAW1U,GAAgBrJ,WAGxDke,EAAcZ,KACda,GAAU,EACVtG,EAAKlL,aAAe3M,EAChBgJ,GAAkBhJ,EAAEJ,OACvBwe,GAAoBpe,EAAEJ,KAAK2S,QAAQ,SAAYvS,EAAEqe,eAAiBre,EAAE6J,OAAU4O,GAC9E7P,GAAawV,EAAkB,WAAYE,IAC3C1V,GAAawV,EAAkB,YAAaG,IAC5C3V,GAAawV,EAAkB,cAAeE,IAC9C1V,GAAa6P,GAAU,aAAc3O,MAErCsU,EAAmB,KACnBxV,GAAa6P,GAAU,YAAa8F,KAErCC,EAAgB,KACXxM,IAAsBoM,IAC1BxV,GAAa6P,GAAU,UAAW6F,IAClCte,GAAKA,EAAE6J,QAAUjB,GAAa5I,EAAE6J,OAAQ,UAAWyU,KAEpDG,EAAcC,GAAY3Q,KAAK8J,EAAM7X,EAAE6J,UAAmC,IAAxB0C,EAAKoS,iBAA6BxO,SAEnFvH,GAAa5I,EAAE6J,OAAQ,SAAUyU,IACjClS,GAAeyL,EAAM,YAAa,eAClCzL,GAAeyL,EAAM,QAAS,WAC9BvJ,GAAesQ,GAAU,QACzBX,GAAsB,MAGvBY,KAA8BT,GAAoBvF,GAAWH,IAAkD,IAAxCb,EAAKtL,KAAKsS,2BAAwChH,EAAKtL,KAAKuS,kBAAoB9e,IAAMA,EAAE+e,SAAqB,EAAV/e,EAAEgf,UAAuBnG,EAAS,IAAM,MAClNoF,GAAuBY,IAA8BhH,EAAKT,qBAEzD/N,GAAgBrJ,GAChB4I,GAAa3H,GAAM,mBAAoBoI,KAEpCrJ,EAAEif,gBACLjf,EAAIkf,EAAQlf,EAAEif,eAAe,GAC7BE,EAAUnf,EAAEof,YACFpf,EAAEqf,UACZF,EAAUnf,EAAEqf,UAEZH,EAAQC,EAAU,KAEnBvV,KAvoCiB,SAApB0V,kBAAoBtX,GACnBF,GAAajF,KAAKmF,GACU,IAAxBF,GAAa3D,QAChBsC,GAAKyB,OAAOqX,IAAI1X,IAqoCfyX,CAAkB3H,IAClBmF,EAAgBjF,EAAKO,SAAWpY,EAAEmM,MAClC0Q,EAAgBhF,EAAKM,SAAWnY,EAAEiM,MAClCG,GAAeyL,EAAM,YAAa,gBAC9BgH,GAA6BhH,EAAKX,aACrCzM,GAAkBZ,EAAOxK,aAEtBwK,EAAOxK,aAAcwY,EAAKX,YAAekC,GAAgBF,IAAgBrP,EAAOxK,WAAWuL,eAAkBsH,GAAgB7S,YAAewK,EAAOrF,UACtJ0N,GAAgB1R,MAAMgL,MAAQ3B,EAAOxK,WAAWuQ,YAAc,KAC9D/F,EAAOxK,WAAWiB,YAAY4R,KAE/B6K,KACAlF,EAAK4D,OAAS5D,EAAK4D,MAAM+D,OACzB3H,EAAKoC,YAAa,EAClBxT,GAAK8J,aAAa6I,GAAevP,EAAQ4V,GAAW,GACpDrG,GAAe3S,GAAK8J,aAAa1G,EAAQ,CAACmP,SAAS,IAAI,GACvDnB,EAAK4D,MAAQ5D,EAAKV,WAAa,MAC3B5K,EAAKmT,cAAiBxG,GAAiBE,IAAoC,IAArB7M,EAAKmT,eAC9D7V,EAAOrJ,MAAMmf,OAASjK,UAAUiK,UAEjC9H,EAAKvE,WAAY,EACjBiG,KAAqBhN,EAAKqT,SAAU/H,EAAKpL,WAAWoT,MACpDC,KAAqBvT,EAAKgS,SAAU1G,EAAKpL,WAAWsT,OAChC,IAAhBxT,EAAK6F,QAAoB7F,EAAKyT,iBACjC5gB,EAAIwf,EAASza,QACC,IAAL/E,GACRqH,GAAKiI,IAAIkQ,EAASxf,GAAI,CAACgT,OAAQ7F,EAAKyT,cAAgBzT,EAAK6F,SAA8B,SAAnBD,GAA4B,WAAaA,MAG/G/F,GAAeyL,EAAM,QAAS,YA8ShB,SAAfoI,GAAejgB,MACVA,GAAK6X,EAAKC,aAAesB,EAAa,KACrClV,EAASlE,EAAE6J,QAAUA,EAAOxK,WAC/B8Z,EAASjV,EAAOb,WAAaa,EAAO4G,WACpCuO,EAASnV,EAAOhB,UAAYgB,EAAO6G,YAChCoO,GAAUE,KACTvU,GACH+X,GAAiB1D,EAASrU,EAAOlE,EAAIyY,EAASvU,EAAOhE,EACrDgc,GAAiBzD,EAASvU,EAAO/D,EAAIoY,EAASrU,EAAOjE,IAErDgc,GAAiB1D,EACjB2D,GAAiBzD,GAElBnV,EAAO4G,YAAcqO,EACrBjV,EAAO6G,YAAcsO,EACrBJ,GAAmBpB,EAAKM,SAAUN,EAAKO,YAKhC,SAAV8H,GAAUlgB,OACLmgB,EAAO/L,KACVgM,EAAmBD,EAAOnC,GAAY,IACtCqC,EAAmBF,EAAOG,GAAc,GACxCC,EAAqBH,GAAmBI,IAAkBxC,GAC1DyC,EAAoB5I,EAAKlL,cAAgBkL,EAAKlL,aAAa8T,iBAC3DC,EAA4BN,GAAmBO,IAAyB3C,GACxE4C,EAAU5gB,EAAE6gB,WAA6B,MAAf7gB,EAAE6gB,WAAqBT,GAAmBG,MAChEA,GAAsBF,IAAqD,IAAlCxI,EAAKtL,KAAKuU,sBAAoC9gB,EAAE0X,0BAC7F1X,EAAE0X,2BAEC0I,KAAqBvI,EAAKlL,eAAgBkL,EAAKlL,aAAa8T,qBAAuBF,GAAsBK,IAAYF,UACpHE,GAAWL,IACdI,EAAuB3C,SAExBwC,EAAgBxC,KAGbnG,EAAKvE,WAAa+M,GAAmBD,KACnCQ,GAAY5gB,EAAE+gB,QAAWX,IAAmBK,GAChDpX,GAAgBrJ,IAGbogB,GAAoBC,GAAoBlC,IAC5Cne,GAAKA,EAAE6J,SAAWgO,EAAKlL,aAAe3M,GACtCoM,GAAeyL,EAAM,QAAS,YAIhB,SAAhBmJ,GAAgBzZ,UAAKzC,EAAS,CAACjF,EAAE0H,EAAE1H,EAAIiF,EAAOlE,EAAI2G,EAAEzH,EAAIgF,EAAOhE,EAAIgE,EAAO9E,EAAGF,EAAEyH,EAAE1H,EAAIiF,EAAOjE,EAAI0G,EAAEzH,EAAIgF,EAAO/D,EAAI+D,EAAO9D,GAAK,CAACnB,EAAE0H,EAAE1H,EAAGC,EAAEyH,EAAEzH,OAp5BzIie,EAAS3E,EAAayD,EAAeC,EAAeO,EAAeD,EAAezC,EAAWpB,EAAiBuG,EAAiBzS,EAAMH,EAAMK,EAAMH,EAAM8R,EAAOC,EAAS1B,EAAgBzF,EAAOiJ,EAAKnH,EAAOC,EAAOC,EAAQyE,EAAYL,EAAkBtZ,EAAQoZ,EAAaW,EAA2BL,EAAehF,EAAegH,EAAeG,EAAsB1C,EAAqBd,EAAagB,EAvBzYve,GAAQ2M,EAAK3M,MAAQ,OAAO4D,cAC/B8V,GAAW1Z,EAAK2S,QAAQ,OAAS3S,EAAK2S,QAAQ,KAC9C2G,GAA8C,IAA9BtZ,EAAK2S,QAAQ,YAC7BqI,EAAQ1B,EAAe,WAAaI,EAAS,IAAM,OACnDuB,EAAQvB,EAAS,IAAM,MACvBT,MAAajZ,EAAK2S,QAAQ,QAAS3S,EAAK2S,QAAQ,SAAoB,WAAT3S,GAC3D8Y,MAAa9Y,EAAK2S,QAAQ,QAAS3S,EAAK2S,QAAQ,QAAmB,WAAT3S,GAC1DshB,EAAkB3U,EAAK2U,iBAAmB,EAC1CrJ,sJACA+G,EAAW1S,GAASK,EAAK4U,SAAW5U,EAAK6U,QAAUvX,GACnD4V,EAAY,GACZa,GAAc,EACdvI,IAAwB,EACxBa,GAAsBrM,EAAKqM,qBAAuB,GAClDE,GAAwBvM,EAAKuM,uBAAyB,GACtDH,GAAyBpM,EAAKoM,wBAA0B,GACxDI,GAAuBxM,EAAKwM,sBAAwB,GACpD2F,GAAcnS,EAAK8U,eAAiBpT,GACpC+P,GAAY,EACZxG,GAAU3N,EAAOtH,OAASkE,GAAKmP,KAAKiE,SAAShQ,GAC7CvD,GAhWS,SAAX7C,SAAWtE,SACkC,UAAxCgM,GAAkBhM,GAAS+C,YAG/B/C,EAAUA,EAAQE,aACkB,IAArBF,EAAQwE,SACfF,SAAStE,WA0VNsE,CAASoG,GAEnB4O,GAAW5O,EAAOnI,eAAiBpC,GAgenCge,GAAa,SAAbA,oBAAmBzF,EAAK4D,OAAS5D,EAAK4D,MAAM6F,YA0F5C/C,GAAS,SAATA,OAASve,OAEP2J,EAASwO,EAAUC,EAAUhZ,EAAG8b,EAAIC,EADjCoG,EAAgBvhB,KAEf+d,IAAWrU,IAAqBmO,EAAKvE,WAActT,MAKxD2J,GADAkO,EAAKlL,aAAe3M,GACRif,oBAEXjf,EAAI2J,EAAQ,MACFuV,GAASlf,EAAEof,aAAeD,EAAS,KAC5C/f,EAAIuK,EAAQxF,QACE,IAAL/E,IAAWY,EAAI2J,EAAQvK,IAAIggB,aAAeD,GAAWnf,EAAE6J,SAAWA,OACvEzK,EAAI,eAIH,GAAIY,EAAEqf,WAAaF,GAAWnf,EAAEqf,YAAcF,SAIjDf,GAAoBS,IAA8BL,IACrD7J,GAAQ9U,EAAIG,EAAEiM,OAAS3F,GAAUnD,GAAkBsV,IAAY,GAC/D9D,GAAQ7U,EAAIE,EAAEmM,OAAS7F,GAAUtD,GAAiByV,IAAY,GAC9D3T,GAAUA,EAAOc,MAAM+O,GAASA,IAChCwD,EAAWxD,GAAQ9U,EACnBuY,EAAWzD,GAAQ7U,IACnBob,EAAKjU,KAAKua,IAAIrJ,EAAW0E,OACzB1B,EAAKlU,KAAKua,IAAIpJ,EAAW0E,MACDoE,EAALhG,GAA6BgG,EAAL/F,IAA2B9I,IAAcwM,IAA8BL,KACjHA,EAAsBrD,EAALD,GAAWrC,EAAU,IAAM,IACxCgG,GAA6BL,IAAkBK,GAClDjW,GAAa3H,GAAM,mBAAoBoI,KAEA,IAApCwO,EAAKtL,KAAKkV,uBAAmC5I,GAAUH,IAC1Db,EAAKV,WAAgC,MAAlBqH,EAAyB,IAAM,IAClD7X,EAAYkR,EAAKtL,KAAKmV,aAAe7J,EAAKtL,KAAKmV,WAAW3T,KAAK8J,EAAM0J,IAElElP,IAAcwM,IAA8BL,IAC/CF,GAAUiD,IASZtD,EAJIpG,EAAKT,mBAAuByH,KAA8BL,GAAiBK,IAA8BL,KAAgD,IAA7B+C,EAAcI,WAGpI1D,IACY,GAHtB5U,GAAgBkY,IACM,GAKnB1J,EAAKX,aACRa,IAAwB,GAEzBkB,GAAmBjZ,EAAEiM,MAAOjM,EAAEmM,MAAO2T,SAnDpC7B,GAAuBje,GAAK+d,GAAW1U,GAAgBrJ,IAsDzDiZ,GAAqB,SAArBA,mBAAsBd,EAAUC,EAAUwJ,OAYxCC,EAASC,EAASjiB,EAAGC,EAAGuQ,EAAK0R,EAX1BC,EAAgB,EAAInK,EAAKf,eAC5BsD,EAAgB,EAAIvC,EAAKd,eACzBkL,EAAepK,EAAKM,SACpB+J,EAAerK,EAAKO,SACpB+J,EAAoB/E,EACpBgF,EAAQvK,EAAKhY,EACbwiB,EAAQxK,EAAK/X,EACbwiB,EAAWzK,EAAKwE,KAChBkG,EAAW1K,EAAKyE,KAChBkG,EAAkB3K,EAAK0E,YACvBkG,EAAYzK,EAEbH,EAAKM,SAAWA,EAChBN,EAAKO,SAAWA,EACZ9R,KACH6R,GAAYhV,GAAkBsV,IAC9BL,GAAYpV,GAAiByV,KAE1BS,GACHpZ,EAAImH,KAAKyW,MAAMD,EAAe3d,EAAIsY,EAAUD,EAAWsF,EAAe5d,GAAKoU,GAEjE,KADV5D,EAAMwH,EAAK/X,EAAIA,IAEdsd,GAAiB,IACjBvF,EAAK/X,EAAIA,GACCuQ,GAAO,MACjB+M,GAAiB,IACjBvF,EAAK/X,EAAIA,GAITD,EAFGgY,EAAKhY,IAAMwd,GAAiBpW,KAAKua,IAAIpE,EAAgBtd,GAAKohB,GAC7DrJ,EAAK/X,EAAIA,EACLud,GAAiBD,EAAgBtd,GAAKkiB,GAEtC3E,IAIDvY,IACHid,EAAO5J,EAAWrT,EAAOlE,EAAIwX,EAAWtT,EAAOhE,EAAIgE,EAAO9E,EAC1DoY,EAAWD,EAAWrT,EAAOjE,EAAIuX,EAAWtT,EAAO/D,EAAI+D,EAAO9D,EAC9DmX,EAAW4J,IAEZD,EAAW1J,EAAW0E,GAERoE,IAA8BA,EAAXY,IAChCA,EAAU,IAFXD,EAAW1J,EAAW0E,GAIRqE,IAA8BA,EAAXW,IAChCA,EAAU,IAENhK,EAAKZ,UAAYY,EAAKV,cAAgB0K,GAAWC,MACrDC,EAAOlK,EAAKV,cAEXU,EAAKV,WAAa4K,EAAQlJ,GAAU5R,KAAKua,IAAIK,GAAW5a,KAAKua,IAAIM,GAAY,IAAMpJ,EAAS,IAAM,KAC9FqJ,GAAQpb,EAAYkR,EAAKtL,KAAKmV,aACjC7J,EAAKtL,KAAKmV,WAAW3T,KAAK8J,EAAMA,EAAKlL,eAG1B,MAAToV,EACHD,EAAU,EACS,MAATC,IACVF,EAAU,IAGZhiB,EAAImH,GAAOqW,EAAgBwE,EAAUG,GACrCliB,EAAIkH,GAAOoW,EAAgB0E,EAAUE,KAGjClI,GAASC,GAASC,KAAYnC,EAAKhY,IAAMA,GAAMgY,EAAK/X,IAAMA,IAAMoZ,KAChEc,IACHtF,GAAO7U,EAAIA,EACX6U,GAAO5U,EAAIA,EACXiiB,EAAO/H,EAAOtF,IACd7U,EAAImH,GAAO+a,EAAKliB,GAChBC,EAAIkH,GAAO+a,EAAKjiB,IAEbga,IACHja,EAAImH,GAAO8S,EAAMja,KAEdka,IACHja,EAAIkH,GAAO+S,EAAMja,MAGf6a,IACKtN,EAAJxN,EACHA,EAAIwN,EAAOpG,KAAKC,OAAOrH,EAAIwN,GAAQ+M,GACzBva,EAAIqN,IACdrN,EAAIqN,EAAOjG,KAAKC,OAAOrH,EAAIqN,GAAQkN,IAE/BlB,IACI3L,EAAJzN,EACHA,EAAImH,KAAKC,MAAMqG,GAAQzN,EAAIyN,GAAQ6M,GACzBta,EAAIsN,IACdtN,EAAImH,KAAKC,MAAMkG,GAAQtN,EAAIsN,GAAQgN,MAIlCvC,EAAKhY,IAAMA,IAAMgY,EAAK/X,IAAMA,GAAMoZ,KACjCA,GACHrB,EAAK0E,YAAc1E,EAAKhY,EAAIgY,EAAKwE,KAAOxc,EACxCmY,GAAQ,IAEJU,IACHb,EAAK/X,EAAI+X,EAAKyE,KAAOxc,EACrBkY,GAAQ,GAELa,IACHhB,EAAKhY,EAAIgY,EAAKwE,KAAOxc,EACrBmY,GAAQ,IAGL4J,IAA2D,IAA3CxV,GAAeyL,EAAM,OAAQ,WAMjDA,EAAKM,SAAW8J,EAChBpK,EAAKO,SAAW8J,EAChB9E,EAAgB+E,EAChBtK,EAAKhY,EAAIuiB,EACTvK,EAAK/X,EAAIuiB,EACTxK,EAAKwE,KAAOiG,EACZzK,EAAKyE,KAAOiG,EACZ1K,EAAK0E,YAAciG,EACnBxK,EAAQyK,IAbH5K,EAAKC,YAAcD,EAAKvE,YAC5BuE,EAAKC,WAAaqG,GAAU,EAC5B/R,GAAeyL,EAAM,YAAa,kBAiBtCyG,GAAY,SAAZA,UAAate,EAAGmQ,MACV4N,GAAYlG,EAAKvE,aAActT,GAAgB,MAAXmf,GAAoBhP,KAAWnQ,EAAEqf,WAAarf,EAAEqf,YAAcF,GAAWnf,EAAE6J,SAAWA,GAAY7J,EAAEif,iBA5zClI,SAAdyD,YAAelN,EAAMmN,WAChBvjB,EAAIoW,EAAKrR,OACN/E,QACFoW,EAAKpW,GAAGggB,aAAeuD,SACnB,EAwzCyJD,CAAY1iB,EAAEif,eAAgBE,MAI9LtH,EAAKvE,WAAY,MAKhB3J,EAASvK,EAAGwjB,EAAgBC,EAAaC,EAJtCvB,EAAgBvhB,EACnB+iB,EAAclL,EAAKC,WACnBkL,EAAwBnL,EAAKtL,KAAKuS,kBAAoB9e,IAAMA,EAAE+e,SAAqB,EAAV/e,EAAEgf,OAC3EiE,EAAyBxc,GAAKyc,YAAY,KAAOrF,OAE9CO,GACHjV,GAAgBiV,EAAkB,WAAYE,WAC9CnV,GAAgBiV,EAAkB,YAAaG,IAC/CpV,GAAgBiV,EAAkB,cAAeE,WACjDnV,GAAgBsP,GAAU,aAAc3O,KAExCX,GAAgBsP,GAAU,YAAa8F,IAExCpV,GAAgBlI,GAAM,mBAAoBoI,IACrC2I,IAAqBoM,IACzBjV,GAAgBsP,GAAU,UAAW6F,WACrCte,GAAKA,EAAE6J,QAAUV,GAAgBnJ,EAAE6J,OAAQ,UAAWyU,YAEvDtG,GAAQ,EACJ+K,IACHzC,GAAc7L,GAAgBL,KAC9ByD,EAAKC,YAAa,GAEnB1P,GAAuBuP,IACnB8G,IAAeuE,SACdhjB,IACHmJ,GAAgBnJ,EAAE6J,OAAQ,SAAUyU,WACpCzG,EAAKlL,aAAe4U,GAErBjT,GAAesQ,GAAU,GACzBxS,GAAeyL,EAAM,UAAW,aAChCzL,GAAeyL,EAAM,QAAS,gBAC9B4G,GAAa,OAGdrf,EAAIwf,EAASza,QACC,IAAL/E,GACR4L,GAAU4T,EAASxf,GAAI,SAAUmN,EAAK6F,UAA2B,IAAhB7F,EAAK6F,OAAmBD,GAAiB,UAE3FvI,KACI5J,EAAG,KACN2J,EAAU3J,EAAEif,kBAEXjf,EAAI2J,EAAQ,MACFuV,GAASlf,EAAEof,aAAeD,EAAS,KAC5C/f,EAAIuK,EAAQxF,QACE,IAAL/E,IAAWY,EAAI2J,EAAQvK,IAAIggB,aAAeD,GAAWnf,EAAE6J,SAAWA,OACvEzK,EAAI,IAAM+Q,SAKhB0H,EAAKlL,aAAe4U,EACpB1J,EAAKM,SAAWnY,EAAEiM,MAClB4L,EAAKO,SAAWpY,EAAEmM,aAEf6W,GAAwBzB,GAC3BlY,GAAgBkY,GAChBtD,GAAsB,EACtB7R,GAAeyL,EAAM,UAAW,cACtB0J,IAAkBwB,GAC5B9E,GAAsB,EAClBC,IAAgB3R,EAAKmB,MAAQnB,EAAKiO,SACrCgB,GAAQjP,EAAKoH,SAAWpH,EAAK4W,YAE9B/W,GAAeyL,EAAM,UAAW,aAC1BxF,IAAqC,cAAvBkP,EAAc3hB,OAAmE,IAA1C2hB,EAAc3hB,KAAK2S,QAAQ,YACrFnG,GAAeyL,EAAM,QAAS,WAC1BzD,KAAa4J,GAAY,KAC5B5R,GAAeyL,EAAM,cAAe,iBAErCgL,EAActB,EAAc1X,QAAUA,EACtCmU,GAAY5J,KACZ0O,EAAiB,0BACZ9E,KAAcwC,IAAiB3I,EAAKkG,WAAclG,EAAKvE,WAAciO,EAAcd,mBAClFoC,EAAYO,MACfP,EAAYO,QACF3K,GAAS4K,eACnBT,EAAiBnK,GAAS4K,YAAY,gBACvBC,eAAe,SAAS,GAAM,EAAMriB,GAAM,EAAG4W,EAAKlL,aAAa4W,QAAS1L,EAAKlL,aAAa6W,QAAS3L,EAAKM,SAAUN,EAAKO,UAAU,GAAO,GAAO,GAAO,EAAO,EAAG,MAC/KyK,EAAYjW,cAAcgW,MAIxBvQ,IAAekP,EAAcd,kBACjCha,GAAKyc,YAAY,IAAMJ,MAIzBtH,GAAQjP,EAAKoH,SAAWpH,EAAK4W,YACxBtL,EAAKT,oBAAqBmK,IAA0C,IAAxBhV,EAAKoS,gBAA6BD,GAAY3Q,KAAK8J,EAAM0J,EAAc1X,UAAYkZ,GAAiBlE,KAA8BL,GAAiBK,IAA8BL,KAAgD,IAA7B+C,EAAcI,WAIlQ1D,GAAsB,GAHtBA,GAAsB,EACtB5U,GAAgBkY,IAIjBnV,GAAeyL,EAAM,UAAW,cAEjCyF,MAAgB2F,EAAuBxa,SAAUoP,EAAK4D,MAAMhT,YAC5Dsa,GAAe3W,GAAeyL,EAAM,UAAW,cACxC,EAxGNoG,GAAuBje,GAAK+d,GAAW1U,GAAgBrJ,WA8J1DihB,EAAMvL,UAAUjT,IAAIoH,KACboX,EAAIzB,SAGNiE,UAAY,SAACna,EAAOoa,OACpBjN,EAAIC,EAAI9B,EAAIC,EAChBiJ,GAAQxU,GAASuO,EAAKlL,cAAc,GAEhC+W,IAAU7L,EAAK3B,QAAQ5M,GAASuO,EAAKlL,gBACxC8J,EAAKnL,GAAWhC,GAASuO,EAAKlL,cAC9B+J,EAAKpL,GAAWzB,GAChB+K,EAAKoM,GAAc,CAACnhB,EAAE4W,EAAGvR,KAAOuR,EAAGjL,MAAQ,EAAG1L,EAAE2W,EAAGxR,IAAMwR,EAAG7K,OAAS,IACrEiJ,EAAKmM,GAAc,CAACnhB,EAAE6W,EAAGxR,KAAOwR,EAAGlL,MAAQ,EAAG1L,EAAE4W,EAAGzR,IAAMyR,EAAG9K,OAAS,IACrEiR,GAAiBjI,EAAG/U,EAAIgV,EAAGhV,EAC3Bid,GAAiBlI,EAAG9U,EAAI+U,EAAG/U,GAEvB+X,EAAKC,aACTD,EAAKC,WAAaqG,GAAU,EAC5B/R,GAAeyL,EAAM,YAAa,mBAG/BgI,KAAOtB,KACPhL,QAAU,SAAAvT,UAAKse,GAAUte,GAAK6X,EAAKlL,cAAc,MACjDsJ,cAAgB,kBAAM4B,EAAKC,WAAa,GAAK1D,KAAakM,IAAe,OACzEqD,eAAiB,kBAAOvP,KAAa4J,IAAa,OAClD9H,QAAU,SAACrM,EAAQwM,UAAcX,UAAUQ,QAAQ2B,EAAKhO,OAAQA,EAAQwM,MAExEuN,aAAe,SAACC,EAAMC,OAEzBjC,EAASC,EAASiC,EAAOC,EAAWvN,EAAIC,EADrCuN,EAAiB,aAATJ,GAAuBpQ,GAAiBoQ,EAAQhd,EAAUgd,KAAU3K,EAAgB,UAAY,cAE/F,YAAT+K,IACHxN,EAAKnL,GAAWuM,EAAKhO,QACrB6M,EAAKpL,GAAWuY,IAEjBhC,EAAoB,UAAToC,EAAoBpM,EAAKhY,EAAIwd,EAA0B,aAAT4G,EAAuBxQ,GAAcyQ,YAAYra,EAAQ+Q,GAAUnE,EAAGvR,KAAOuR,EAAGjL,MAAQ,GAAMkL,EAAGxR,KAAOwR,EAAGlL,MAAQ,GACxK0N,EACI2I,EAAU,EAAI,oBAAsB,aAE3CiC,EAAoBA,GAAqB,EACzChC,EAAoB,UAATmC,EAAoBpM,EAAK/X,EAAIsd,EAA0B,aAAT6G,EAAuBxQ,GAAcyQ,YAAYra,EAAQgR,GAAUpE,EAAGxR,IAAMwR,EAAG7K,OAAS,GAAM8K,EAAGzR,IAAMyR,EAAG9K,OAAS,GAE5KoY,GADAD,EAAQ9c,KAAKua,IAAIK,EAAUC,IACN,EAAIgC,EAAqB,GAAMjC,EAAU,EAAK,OAAS,QACxEkC,EAAQD,IACO,KAAdE,IACHA,GAAa,KAEdA,GAAclC,EAAU,EAAK,KAAO,QAG/BkC,MAGHvH,YAAc,SAAC0H,EAAWC,OAC1BvkB,EAAGC,EAAG6N,EAAmB3N,EAAGkE,EAAQ+T,KACpCkM,GAAa5X,EAAKiO,SAAW2J,SAChC5X,EAAKiO,OAAS2J,EACPtM,EAAKwM,QAAO,EAAMD,MAE1B3K,IAAO,GACPc,KACII,IAAc2C,KAAc,IAC/Bzd,EAAIgY,EAAKhY,EACTC,EAAI+X,EAAK/X,EACDuN,EAAJxN,EACHA,EAAIwN,EACMxN,EAAIqN,IACdrN,EAAIqN,GAEGK,EAAJzN,EACHA,EAAIyN,EACMzN,EAAIsN,IACdtN,EAAIsN,IAEDyK,EAAKhY,IAAMA,GAAKgY,EAAK/X,IAAMA,KAC9B6N,GAAoB,EACpBkK,EAAKhY,EAAIgY,EAAKwE,KAAOxc,EACjBqZ,EACHrB,EAAK0E,YAAc1c,EAEnBgY,EAAK/X,EAAI+X,EAAKyE,KAAOxc,EAGtB6X,GADAK,GAAQ,GAEJH,EAAKX,aAAeW,EAAKC,gBAC5BrN,GAAkBZ,EAAOxK,YACzBW,EAAI6J,EACJc,GAAazH,UAAkC,MAApBjC,GAAKgC,YAAuBhC,GAAKgC,YAAqD,MAAtCwV,GAAS5W,gBAAgBqB,UAAqBuV,GAAS5W,gBAAgBqB,UAAYuV,GAAS3W,KAAKoB,UAC5KyH,GAAatH,WAAmC,MAApBpC,GAAKmC,YAAuBnC,GAAKmC,YAAsD,MAAvCqV,GAAS5W,gBAAgBwB,WAAsBoV,GAAS5W,gBAAgBwB,WAAaoV,GAAS3W,KAAKuB,WACxKrD,IAAMiY,GAEZ/T,GADA+T,EAAShO,GAAQjK,EAAEX,aACDsL,GAAe3K,EAAEX,WAC/BqZ,GAAUxU,EAAOhB,UAAYgB,EAAO2G,gBACvC3G,EAAOhB,UAAYgB,EAAO2G,eAEvBgO,GAAU3U,EAAOb,WAAaa,EAAO0G,gBACxC1G,EAAOb,WAAaa,EAAO0G,eAE5B5K,EAAIkE,EAIH2T,EAAKoC,aAAetM,GAAqBkK,EAAKwE,KAAOhP,GAAQwK,EAAKwE,KAAOnP,GAAQ2K,EAAKyE,KAAO/O,GAAQsK,EAAKyE,KAAOlP,IACpHoO,GAAQjP,EAAKoH,SAAWpH,EAAK4W,WAAYxV,UAGpCkK,KAGHwM,OAAS,SAAC5H,EAAa2H,EAAQE,MAC/BF,GAAUvM,EAAKvE,UAAW,KACzB3S,EAAIoF,gBAAgB8D,GACvBtC,EAAI4V,EAAYvX,MAAM,CAAC/F,EAAGgY,EAAKhY,EAAIwd,EAAevd,EAAG+X,EAAK/X,EAAIsd,IAC9DmH,EAAKxe,gBAAgB8D,EAAOxK,YAAY,GACzCklB,EAAG3e,MAAM,CAAC/F,EAAGc,EAAEX,EAAIuH,EAAE1H,EAAGC,EAAGa,EAAEK,EAAIuG,EAAEzH,GAAIyH,GACvCsQ,EAAKhY,GAAK0H,EAAE1H,EAAI0kB,EAAGvkB,EACnB6X,EAAK/X,GAAKyH,EAAEzH,EAAIykB,EAAGvjB,EACnB2W,IAAO,GACPoF,SAEKld,EAASgY,EAAThY,EAAGC,EAAM+X,EAAN/X,SACT4c,IAAc0H,GACV3H,EACH5E,EAAK4E,eAELzE,GAASsM,GAAyB3M,IAAO,GACzC8B,IAAO,IAEJ2K,IACHnL,GAAmBpB,EAAKM,SAAUN,EAAKO,UACvCJ,GAASL,IAAO,IAEbE,EAAKvE,YAAc8Q,IAAYvL,GAAiC,IAAvB5R,KAAKua,IAAI3hB,EAAIgY,EAAKhY,IAAe6Y,GAAkC,IAAvBzR,KAAKua,IAAI1hB,EAAI+X,EAAK/X,KAAcoZ,IACxH6D,KAEGlF,EAAKX,aACRzM,GAAkBZ,EAAOxK,WAAYwY,EAAKC,YAC1CC,GAAwBF,EAAKC,WAC7BH,IAAO,GAEPzN,GAAsBL,EAAQoW,IAC9BlW,GAAmBF,EAAQoW,KAErBpI,KAGH9G,OAAS,SAAAnR,OAEZ4kB,EAAIplB,EAAG+hB,EADJsD,EAAU,CAAC9V,MAAM,OAED,IAAhBpC,EAAK6F,SACRqS,EAAQrS,OAAS7F,EAAK6F,QAAUD,IAE7B1L,GAAKqI,MAAM8E,YAAY,kBAC1B6Q,EAAQC,aAAe,QAEX,SAAT9kB,EAAiB,KACpB6H,GAAiCmX,EAAW/F,GAAWH,EAAU,OAAUnM,EAAKsS,2BAA8BhV,EAAOgG,eAAiBhG,EAAOkC,eAAmBlC,EAAO+F,cAAgB/F,EAAOkC,eAAkBQ,EAAK6K,kBAAoB,eAAiByB,EAAS,QAAU,SAC7QzZ,EAAIwf,EAASza,QACC,IAAL/E,GACR+hB,EAAUvC,EAASxf,GACnB4S,IAAoBpJ,GAAauY,EAAS,YAAarD,IACvDlV,GAAauY,EAAS,aAAcrD,IACpClV,GAAauY,EAAS,QAASjB,IAAS,GACxCzZ,GAAKiI,IAAIyS,EAASsD,GACdtD,EAAQ3c,SAAW2c,EAAQ7d,iBAAmBuV,GAAWH,GAC5DjS,GAAKiI,IAAIyS,EAAQ7d,gBAAiB,CAACsE,YAAa2E,EAAKsS,2BAA6BtS,EAAK6K,kBAAoB,eAAiByB,EAAS,QAAU,UAEhJtM,EAAKuS,kBAAoBlW,GAAauY,EAAS,cAAe1J,IAE/DnJ,GAAesQ,GAAU,UAE1B7U,GAAmBF,EAAQoW,IAC3BlC,GAAU,EACNtK,IAA0B,SAAT7T,GACpB6T,GAAckR,MAAMvL,GAAevP,EAASyP,EAAS,MAAQJ,EAAe,WAAa,YAE1FrP,EAAOmM,UAAYwO,EAAK,IAAOhQ,KAC/BnB,GAAQmR,GAAM3M,EACVuB,IACHA,EAAYrI,SACZqI,EAAYja,QAAQ6W,UAAYwO,IAEhCjY,EAAKiO,QAAUtB,IAAiB6D,KACjCxQ,EAAKiO,QAAU3C,EAAK4E,cACb5E,KAGHhH,QAAU,SAAAjR,WAGbuhB,EAFGyD,EAAW/M,EAAKC,WACnB1Y,EAAIwf,EAASza,QAEA,IAAL/E,GACR4L,GAAU4T,EAASxf,GAAI,SAAU,SAErB,SAATQ,EAAiB,KACpB6H,GAAiCmX,EAAU,MAC3Cxf,EAAIwf,EAASza,QACC,IAAL/E,GACR+hB,EAAUvC,EAASxf,GACnB4L,GAAUmW,EAAS,eAAgB,MACnChY,GAAgBgY,EAAS,YAAarD,IACtC3U,GAAgBgY,EAAS,aAAcrD,IACvC3U,GAAgBgY,EAAS,QAASjB,IAClC/W,GAAgBgY,EAAS,cAAe1J,IAEzCnJ,GAAesQ,GAAU,GACrBR,IACHjV,GAAgBiV,EAAkB,cAAeE,IACjDnV,GAAgBiV,EAAkB,WAAYE,IAC9CnV,GAAgBiV,EAAkB,YAAaG,KAEhDpV,GAAgBsP,GAAU,UAAW6F,IACrCnV,GAAgBsP,GAAU,YAAa8F,WAExCrU,GAAsBL,EAAQoW,IAC9BlC,GAAU,EACVtK,IAA0B,SAAT7T,GAAmB6T,GAAcoR,QAAQzL,GAAevP,EAASyP,EAAS,MAAQJ,EAAe,WAAa,YAC/HE,GAAeA,EAAYvI,UAC3BzI,GAAuBuP,IACvBE,EAAKC,WAAaD,EAAKvE,UAAYmL,GAAa,EAChDmG,GAAYxY,GAAeyL,EAAM,UAAW,aACrCA,KAGHkG,QAAU,SAASnX,EAAOhH,UACvBwQ,UAAUjM,OAAUyC,EAAQiR,EAAK9G,OAAOnR,GAAQiY,EAAKhH,QAAQjR,GAASme,KAGzEyB,KAAO,kBACX3H,EAAKoC,YAAa,EAClBpC,EAAK4D,OAAS5D,EAAK4D,MAAM+D,OACzB3H,EAAKhH,UACLpK,GAAKiI,IAAIkQ,EAAU,CAACkG,WAAW,sBACxBzR,GAAQxJ,EAAOmM,WACf6B,IAGHjY,EAAK2S,QAAQ,YACjB6G,EAAcxC,EAAKwC,YAAc,IAAIvK,GAAYhF,EArwDzC,SAAVkb,QAAW1d,EAAK2d,OACV,IAAIzd,KAAKyd,EACPzd,KAAKF,IACVA,EAAIE,GAAKyd,EAASzd,WAGbF,EA+vDmD0d,CAAQ,CAACvU,OAAO,kBACvEqH,EAAKvE,WAAagL,GAAU,QAC1B/R,IAEJ1C,EAAOrJ,MAAMykB,UAAavM,IAAWlG,GAAkB,OAAS,SAChE3I,EAAOrJ,MAAM0kB,UAAarM,IAAWrG,GAAkB,OAAS,SAChE3I,EAASuP,EAAY9J,SAGlB4J,EACHuG,EAAU5I,SAAW,GAEjBgC,IACH4G,EAAU7E,GAAS,GAEhBlC,IACH+G,EAAU5E,GAAS,IAIrBrD,GAAQtH,UAAW,YAAa3D,IAAQA,EAAK2D,UAExCa,YAzvDS,SAAfoU,aAAgB9d,EAAK2d,OACf,IAAIzd,KAAKyd,EACPzd,KAAKF,IACVA,EAAIE,GAAKyd,EAASzd,IA4yDtB4d,CAAazP,EAAU0P,UAAW,CAACjN,SAAS,EAAGC,SAAU,EAAGuF,OAAQ,EAAGC,OAAQ,EAAGzE,OAAQ,EAAGE,OAAQ,EAAGvB,YAAY,EAAOxE,WAAW,IAEtIoC,EAAUiK,OAAS,IACnBjK,EAAU2P,QAAU,SAEpB7e,KAAcC,GAAKC,eAAegP"}