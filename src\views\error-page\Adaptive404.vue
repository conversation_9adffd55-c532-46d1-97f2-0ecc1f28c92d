<template>
  <div class="adaptive-404">
    <!-- 移动端404页面 -->
    <div v-if="isMobile" class="mobile-404">
      <div class="mobile-404-content">
        <van-empty
          image="error"
          :image-size="120"
          description="页面不存在"
        >
          <template #description>
            <div class="error-description">
              <h3>抱歉，页面走丢了</h3>
              <p>您访问的页面不存在或已被移除</p>
              <p class="error-path">错误路径: {{ currentPath }}</p>
            </div>
          </template>
        </van-empty>
        
        <div class="mobile-404-actions">
          <van-button 
            type="primary" 
            block 
            @click="goHome"
          >
            返回首页
          </van-button>
          
          <van-button 
            block 
            @click="goBack"
            style="margin-top: 12px;"
          >
            返回上页
          </van-button>
          
          <van-button 
            block 
            plain 
            @click="reportError"
            style="margin-top: 12px;"
          >
            反馈问题
          </van-button>
        </div>
      </div>
    </div>

    <!-- PC端404页面 -->
    <div v-else class="desktop-404">
      <div class="desktop-404-content">
        <el-result
          icon="warning"
          title="404"
          sub-title="抱歉，您访问的页面不存在"
        >
          <template #extra>
            <div class="error-info">
              <p class="error-path">错误路径: {{ currentPath }}</p>
              <p class="error-time">时间: {{ currentTime }}</p>
            </div>
            
            <div class="desktop-404-actions">
              <el-button type="primary" @click="goHome">
                返回首页
              </el-button>
              <el-button @click="goBack">
                返回上页
              </el-button>
              <el-button type="info" @click="reportError">
                反馈问题
              </el-button>
            </div>
          </template>
        </el-result>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useDevice } from '@/composables/useDevice';
import { useRouter, useRoute } from 'vue-router';

defineOptions({
  name: "Adaptive404",
});

const { isMobile } = useDevice();
const router = useRouter();
const route = useRoute();

// 当前路径和时间
const currentPath = ref(route.fullPath);
const currentTime = ref(new Date().toLocaleString('zh-CN'));

// 返回首页
const goHome = () => {
  if (isMobile.value) {
    // 移动端跳转到移动端首页
    router.push('/mobile/dashboard').catch(() => {
      router.push('/');
    });
  } else {
    // PC端跳转到PC端首页
    router.push('/dashboard').catch(() => {
      router.push('/');
    });
  }
};

// 返回上一页
const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1);
  } else {
    goHome();
  }
};

// 反馈问题
const reportError = () => {
  const errorInfo = {
    path: currentPath.value,
    time: currentTime.value,
    device: isMobile.value ? 'mobile' : 'desktop',
    userAgent: navigator.userAgent
  };
  
  console.log('404错误反馈:', errorInfo);
  
  if (isMobile.value) {
    // 移动端可以使用Toast提示
    console.log('问题已反馈，感谢您的反馈！');
  } else {
    // PC端使用Message提示
    ElMessage.success('问题已反馈，感谢您的反馈！');
  }
};

// 监听路由变化，更新当前路径
watch(() => route.fullPath, (newPath) => {
  currentPath.value = newPath;
  currentTime.value = new Date().toLocaleString('zh-CN');
});
</script>

<style lang="scss" scoped>
.adaptive-404 {
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--el-bg-color-page, #f5f5f5);

  .mobile-404 {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    background: white;

    .mobile-404-content {
      width: 100%;
      max-width: 400px;
      text-align: center;

      .error-description {
        h3 {
          font-size: 18px;
          color: #323233;
          margin-bottom: 8px;
        }

        p {
          font-size: 14px;
          color: #646566;
          margin-bottom: 4px;
          line-height: 1.5;
        }

        .error-path {
          font-size: 12px;
          color: #969799;
          word-break: break-all;
          background: #f7f8fa;
          padding: 8px;
          border-radius: 4px;
          margin-top: 12px;
        }
      }
    }

    .mobile-404-actions {
      width: 100%;
      margin-top: 32px;
    }
  }

  .desktop-404 {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    .desktop-404-content {
      max-width: 600px;
      width: 100%;
      text-align: center;

      .error-info {
        margin-bottom: 24px;
        padding: 16px;
        background: var(--el-fill-color-light);
        border-radius: 8px;
        border-left: 4px solid var(--el-color-warning);

        p {
          margin: 4px 0;
          font-size: 14px;
          color: var(--el-text-color-regular);
        }

        .error-path {
          font-family: monospace;
          word-break: break-all;
          background: var(--el-fill-color-darker);
          padding: 8px;
          border-radius: 4px;
        }
      }

      .desktop-404-actions {
        display: flex;
        justify-content: center;
        gap: 16px;
        flex-wrap: wrap;
      }
    }
  }
}

// 确保在不同布局下都能正确显示
.layout-container .adaptive-404,
.mobile-layout .adaptive-404 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
}
</style>
