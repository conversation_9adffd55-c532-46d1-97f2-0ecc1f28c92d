<!DOCTYPE html>
<html>
<head>
  <title>built-in easings • anime.js</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <meta property="og:title" content="anime.js">
  <meta property="og:url" content="https://animejs.com">
  <meta property="og:description" content="Javascript Animation Engine">
  <meta property="og:image" content="https://animejs.com/documentation/assets/img/icons/og.png">
  <meta name="twitter:card" content="summary">
  <meta name="twitter:title" content="anime.js">
  <meta name="twitter:site" content="@juliangarnier">
  <meta name="twitter:description" content="Javascript Animation Engine">
  <meta name="twitter:image" content="https://animejs.com/documentation/assets/img/icons/twitter.png">
  <link rel="apple-touch-icon-precomposed" href="../assets/img/social-media-image.png">
  <link rel="icon" type="image/png" href="../assets/img/favicon.png" >
  <link href="../assets/css/animejs.css" rel="stylesheet">
  <link href="../assets/css/documentation.css" rel="stylesheet">
  <style>

    :root {
      font-size: 24px;
    }

    body {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      align-items: center;
      height: 100vh;
    }

    .easing-visualizer {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      max-width: 908px;
      padding-bottom: 50%;
    }

    .easing-visualizer .wrapper {
      position: absolute;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 100%;
    }

    .easing-visualizer .bar {
      width: 2px;
      height: 4px;
      margin: 0 3px;
      background-color: currentColor;
      transform-origin: 50% 50%;
    }

    .easing-visualizer .dot {
      position: relative;
      width: 8px;
      height: 8px;
      background-color: currentColor;
      border-radius: 50%;
    }

  </style>
</head>
<body>

  <div class="easing-visualizer">
    <div class="wrapper bars-wrapper"></div>
    <div class="wrapper dots-wrapper"></div>
  </div>

</body>
<script type="module">

  import anime from '../../src/index.js';

  const easingVisualizerEl = document.querySelector('.easing-visualizer');
  const barsWrapperEl = easingVisualizerEl.querySelector('.bars-wrapper');
  const dotsWrapperEl = easingVisualizerEl.querySelector('.dots-wrapper');
  const barsFragment = document.createDocumentFragment();
  const dotsFragment = document.createDocumentFragment();
  const numberOfBars = 101;

  for (let i = 0; i < numberOfBars; i++) {
    const barEl = document.createElement('div');
    const dotEl = document.createElement('div');
    barEl.classList.add('bar');
    dotEl.classList.add('dot');
    dotEl.classList.add('color-red');
    barsFragment.appendChild(barEl);
    dotsFragment.appendChild(dotEl);
  }

  barsWrapperEl.appendChild(barsFragment);
  dotsWrapperEl.appendChild(dotsFragment);

  // anime.setValue('.easing-visualizer .dot', { translateX: anime.stagger(6) });

  function animateEasing() {

    const easings = [];
    for (let ease in anime.penner) easings.push(ease);
    easings.push('steps('+anime.random(5, 20)+')');
    easings.push('cubicBezier(0.545, 0.475, 0.145, 1)');
    const ease = easings[anime.random(0, easings.length - 1)];
    const duration = 450;

    anime.timeline({
      complete: animateEasing,
      easing: ease,
      duration: duration
    })
    .add({
      targets: '.easing-visualizer .bar',
      scaleY: anime.stagger([1, 100], {easing: ease, from: 'center', direction: 'reverse'}),
      delay: anime.stagger(14, {from: 'center'})
    })
    .add({
      targets: '.easing-visualizer .dot',
      translateY: anime.stagger(['-6rem', '6rem'], {easing: ease, from: 'last'}),
      delay: anime.stagger(10, {from: 'center'})
    }, 0);

  }

  animateEasing();

</script>
</html>
