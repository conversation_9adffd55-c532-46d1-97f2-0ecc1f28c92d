# 动态路由实施代码示例

## 📝 基于您现有代码的具体实现

### 1. 增强权限存储模块

```typescript
// src/store/modules/permission.ts (基于您现有代码的增强版)
import type { RouteRecordRaw } from "vue-router";
import { constantRoutes } from "@/router";
import { store } from "@/store";
import router from "@/router";
import { useAppStore } from "@/store";
import { DeviceEnum } from "@/enums/DeviceEnum";

import MenuAPI, { type RouteVO } from "@/api/system/menu";

const modules = import.meta.glob("../../views/**/**.vue");
const Layout = () => import("@/layout/index.vue");
const MobileLayout = () => import("@/layout/MobileLayout.vue");

// 新增：自适应布局组件
const AdaptiveLayout = () => import("@/layout/AdaptiveLayout.vue");

export const usePermissionStore = defineStore("permission", () => {
  // 保持原有状态
  const routes = ref<RouteRecordRaw[]>([]);
  const mixLeftMenus = ref<RouteRecordRaw[]>([]);
  const isRoutesLoaded = ref(false);

  /**
   * 生成动态路由 - 增强版，支持设备类型
   */
  function generateRoutes() {
    return new Promise<RouteRecordRaw[]>((resolve, reject) => {
      const appStore = useAppStore();
      const platform = appStore.device === DeviceEnum.MOBILE ? 'mobile' : 'desktop';
      
      // 调用API时传入设备类型（如果后端支持）
      MenuAPI.getRoutes()
        .then((data) => {
          const dynamicRoutes = transformRoutes(data, platform);
          routes.value = constantRoutes.concat(dynamicRoutes);
          isRoutesLoaded.value = true;
          resolve(dynamicRoutes);
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  /**
   * 新增：重新生成路由（设备切换时使用）
   */
  function regenerateRoutes() {
    console.log('Regenerating routes for device change...');
    
    // 清除现有动态路由
    routes.value.forEach((route) => {
      if (route.name && !constantRoutes.find((r) => r.name === route.name)) {
        router.removeRoute(route.name);
      }
    });
    
    // 重置状态
    isRoutesLoaded.value = false;
    
    // 重新生成路由
    return generateRoutes().then((dynamicRoutes) => {
      dynamicRoutes.forEach((route: RouteRecordRaw) => router.addRoute(route));
      return dynamicRoutes;
    });
  }

  // 保持原有方法
  const setMixLeftMenus = (topMenuPath: string) => {
    const matchedItem = routes.value.find((item) => item.path === topMenuPath);
    if (matchedItem && matchedItem.children) {
      mixLeftMenus.value = matchedItem.children;
    }
  };

  const resetRouter = () => {
    routes.value.forEach((route) => {
      if (route.name && !constantRoutes.find((r) => r.name === route.name)) {
        router.removeRoute(route.name);
      }
    });

    routes.value = [];
    mixLeftMenus.value = [];
    isRoutesLoaded.value = false;
  };

  return {
    routes,
    generateRoutes,
    regenerateRoutes, // 新增方法
    mixLeftMenus,
    setMixLeftMenus,
    isRoutesLoaded,
    resetRouter,
  };
});

/**
 * 转换路由数据为组件 - 增强版
 */
const transformRoutes = (routes: RouteVO[], platform?: string) => {
  const asyncRoutes: RouteRecordRaw[] = [];
  const appStore = useAppStore();
  const isMobile = appStore.device === DeviceEnum.MOBILE;

  routes.forEach((route) => {
    const tmpRoute = { ...route } as RouteRecordRaw;
    
    // 增强布局组件选择逻辑
    if (tmpRoute.component?.toString() == "Layout") {
      tmpRoute.component = selectLayoutComponent(tmpRoute.meta, isMobile);
    } else {
      // 增强页面组件选择逻辑
      const componentPath = selectComponentPath(tmpRoute.component as string, tmpRoute.meta, isMobile);
      const component = modules[`../../views/${componentPath}.vue`];
      
      if (component) {
        tmpRoute.component = component;
      } else {
        console.warn(`Component not found: ${componentPath}, falling back to 404`);
        tmpRoute.component = modules["../../views/error-page/404.vue"];
      }
    }

    // 增强路由元信息
    tmpRoute.meta = {
      ...tmpRoute.meta,
      platform: isMobile ? 'mobile' : 'desktop',
      deviceType: isMobile ? DeviceEnum.MOBILE : DeviceEnum.DESKTOP,
      adaptive: tmpRoute.meta?.adaptive !== false // 默认为自适应
    };

    if (tmpRoute.children) {
      tmpRoute.children = transformRoutes(route.children, platform);
    }

    asyncRoutes.push(tmpRoute);
  });

  return asyncRoutes;
};

/**
 * 选择布局组件
 */
function selectLayoutComponent(meta: any, isMobile: boolean) {
  // 检查是否有明确的平台限制
  if (meta?.mobileOnly && isMobile) {
    return MobileLayout;
  }
  
  if (meta?.desktopOnly && !isMobile) {
    return Layout;
  }
  
  // 检查是否禁用自适应
  if (meta?.adaptive === false) {
    return isMobile ? MobileLayout : Layout;
  }
  
  // 默认使用自适应布局
  return AdaptiveLayout;
}

/**
 * 选择组件路径
 */
function selectComponentPath(originalPath: string, meta: any, isMobile: boolean): string {
  // 1. 检查是否有平台特定组件配置
  if (meta?.mobileComponent && isMobile) {
    return meta.mobileComponent;
  }
  
  if (meta?.desktopComponent && !isMobile) {
    return meta.desktopComponent;
  }
  
  // 2. 如果标记为自适应，直接返回原路径
  if (meta?.adaptive === true) {
    return originalPath;
  }
  
  // 3. 尝试平台特定路径
  const platformPath = isMobile 
    ? `mobile/${originalPath}` 
    : `desktop/${originalPath}`;
  
  if (modules[`../../views/${platformPath}.vue`]) {
    return platformPath;
  }
  
  // 4. 尝试共享路径
  const sharedPath = `shared/${originalPath}`;
  if (modules[`../../views/${sharedPath}.vue`]) {
    return sharedPath;
  }
  
  // 5. 返回原始路径
  return originalPath;
}
```

### 2. 创建自适应布局组件

```vue
<!-- src/layout/AdaptiveLayout.vue -->
<template>
  <div class="adaptive-layout" :class="layoutClasses">
    <!-- 动态加载对应的布局组件 -->
    <component 
      :is="currentLayoutComponent" 
      v-bind="layoutProps"
      @layout-event="handleLayoutEvent"
    />
  </div>
</template>

<script setup lang="ts">
import { defineAsyncComponent } from 'vue';
import { useAppStore } from '@/store';
import { DeviceEnum } from '@/enums/DeviceEnum';

defineOptions({
  name: "AdaptiveLayout",
});

const appStore = useAppStore();

// 动态选择布局组件
const currentLayoutComponent = computed(() => {
  if (appStore.device === DeviceEnum.MOBILE) {
    return defineAsyncComponent(() => import('./MobileLayout.vue'));
  } else {
    return defineAsyncComponent(() => import('./index.vue'));
  }
});

// 布局样式类
const layoutClasses = computed(() => ({
  'adaptive-layout--mobile': appStore.device === DeviceEnum.MOBILE,
  'adaptive-layout--desktop': appStore.device === DeviceEnum.DESKTOP,
}));

// 传递给子布局的属性
const layoutProps = computed(() => {
  const isMobile = appStore.device === DeviceEnum.MOBILE;
  
  return {
    adaptive: true,
    deviceType: appStore.device,
    // 可以根据需要传递更多属性
  };
});

// 处理来自子布局的事件
const handleLayoutEvent = (event: any) => {
  console.log('Adaptive layout event:', event);
  // 可以在这里处理布局切换等事件
};
</script>

<style lang="scss" scoped>
.adaptive-layout {
  width: 100%;
  height: 100vh;
  
  &--mobile {
    // 移动端特定样式
    font-size: 14px;
  }
  
  &--desktop {
    // 桌面端特定样式
    font-size: 16px;
  }
}
</style>
```

### 3. 增强设备检测组合式API

```typescript
// src/composables/useDevice.ts (增强版)
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { DeviceDetector } from '@/utils/device';
import { useAppStore } from '@/store';
import { usePermissionStore } from '@/store';
import { DeviceEnum } from '@/enums/DeviceEnum';
import router from '@/router';

export function useDevice() {
  const appStore = useAppStore();
  const permissionStore = usePermissionStore();
  
  const isMobile = computed(() => appStore.device === DeviceEnum.MOBILE);
  const isTablet = computed(() => DeviceDetector.isTablet());
  const isDesktop = computed(() => appStore.device === DeviceEnum.DESKTOP);

  // 设备切换处理函数
  const handleDeviceChange = async (newDeviceType: DeviceEnum) => {
    const oldDeviceType = appStore.device;
    
    if (oldDeviceType !== newDeviceType) {
      console.log(`Device switching: ${oldDeviceType} → ${newDeviceType}`);
      
      // 更新设备状态
      appStore.toggleDevice(newDeviceType);
      
      // 如果路由已加载且用户已登录，重新生成路由
      if (permissionStore.isRoutesLoaded) {
        try {
          console.log('Regenerating routes for device change...');
          await permissionStore.regenerateRoutes();
          
          // 检查当前路由是否需要重定向
          await checkAndRedirectRoute(newDeviceType);
          
          console.log('Device change handled successfully');
        } catch (error) {
          console.error('Failed to handle device change:', error);
        }
      }
    }
  };

  // 检查并重定向路由
  const checkAndRedirectRoute = async (deviceType: DeviceEnum) => {
    const currentRoute = router.currentRoute.value;
    
    // 检查当前路由是否与新设备兼容
    if (!isRouteCompatible(currentRoute, deviceType)) {
      const adaptedPath = findAdaptedRoute(currentRoute.path, deviceType);
      
      if (adaptedPath && adaptedPath !== currentRoute.path) {
        console.log(`Redirecting from ${currentRoute.path} to ${adaptedPath}`);
        await router.replace(adaptedPath);
      }
    }
  };

  // 路由兼容性检查
  const isRouteCompatible = (route: any, deviceType: DeviceEnum): boolean => {
    const platform = route.meta?.platform;
    
    if (!platform || platform === 'both') {
      return true;
    }
    
    return (deviceType === DeviceEnum.MOBILE && platform === 'mobile') ||
           (deviceType === DeviceEnum.DESKTOP && platform === 'desktop');
  };

  // 查找适配的路由
  const findAdaptedRoute = (currentPath: string, deviceType: DeviceEnum): string | null => {
    // 简单的路径映射逻辑
    const routeMap: Record<string, Record<string, string>> = {
      '/dashboard': {
        mobile: '/mobile/dashboard',
        desktop: '/dashboard'
      },
      '/mobile/dashboard': {
        mobile: '/mobile/dashboard',
        desktop: '/dashboard'
      },
      // 可以根据需要添加更多映射
    };
    
    const targetPlatform = deviceType === DeviceEnum.MOBILE ? 'mobile' : 'desktop';
    return routeMap[currentPath]?.[targetPlatform] || null;
  };

  let cleanup: (() => void) | null = null;

  onMounted(() => {
    // 监听窗口大小变化
    cleanup = DeviceDetector.onDeviceChange(handleDeviceChange);
    
    // 初始化设备状态
    const initialDevice = DeviceDetector.getDeviceType();
    if (appStore.device !== initialDevice) {
      handleDeviceChange(initialDevice);
    }
  });

  onUnmounted(() => {
    cleanup?.();
  });

  return {
    isMobile,
    isTablet,
    isDesktop,
    handleDeviceChange,
    checkAndRedirectRoute
  };
}
```

### 4. 增强权限守卫

```typescript
// src/plugins/permission.ts (在现有代码基础上增强)
import type { NavigationGuardNext, RouteLocationNormalized, RouteRecordRaw } from "vue-router";
import NProgress from "@/utils/nprogress";
import { getToken } from "@/utils/auth";
import router from "@/router";
import { usePermissionStore, useUserStore, useAppStore } from "@/store";
import { DeviceEnum } from "@/enums/DeviceEnum";

export function setupPermission() {
  // 保持原有白名单
  const whiteList = ["/login", "/warehouse", "/assetManagement"];

  router.beforeEach(async (to, from, next) => {
    NProgress.start();

    const isLogin = !!getToken();
    const appStore = useAppStore();
    
    if (isLogin) {
      if (to.path === "/login") {
        next({ path: "/" });
      } else {
        const permissionStore = usePermissionStore();
        
        if (permissionStore.isRoutesLoaded) {
          if (to.matched.length === 0) {
            next("/404");
          } else {
            // 新增：检查路由平台兼容性
            if (!isRoutePlatformCompatible(to, appStore.device)) {
              const adaptedRoute = findAdaptedRoute(to, appStore.device);
              if (adaptedRoute) {
                console.log(`Route incompatible, redirecting to: ${adaptedRoute}`);
                next(adaptedRoute);
                return;
              }
            }
            
            // 保持原有逻辑
            const title = (to.params.title as string) || (to.query.title as string);
            if (title) {
              to.meta.title = title;
            }
            next();
          }
        } else {
          try {
            const dynamicRoutes = await permissionStore.generateRoutes();
            dynamicRoutes.forEach((route: RouteRecordRaw) => router.addRoute(route));
            next({ ...to, replace: true });
          } catch (error) {
            console.error(error);
            await useUserStore().clearUserData();
            redirectToLogin(to, next);
            NProgress.done();
          }
        }
      }
    } else {
      if (whiteList.includes(to.path)) {
        next();
      } else {
        redirectToLogin(to, next);
        NProgress.done();
      }
    }
  });

  router.afterEach(() => {
    NProgress.done();
  });
}

// 新增：检查路由平台兼容性
function isRoutePlatformCompatible(route: RouteLocationNormalized, device: DeviceEnum): boolean {
  const platform = route.meta?.platform;
  
  if (!platform || platform === 'both') {
    return true;
  }
  
  return (device === DeviceEnum.MOBILE && platform === 'mobile') ||
         (device === DeviceEnum.DESKTOP && platform === 'desktop');
}

// 新增：查找适配的路由
function findAdaptedRoute(route: RouteLocationNormalized, device: DeviceEnum): string | null {
  const currentPlatform = device === DeviceEnum.MOBILE ? 'mobile' : 'desktop';
  
  // 简单的路径转换逻辑
  if (route.path.startsWith('/mobile/') && device === DeviceEnum.DESKTOP) {
    return route.path.replace('/mobile/', '/');
  }
  
  if (!route.path.startsWith('/mobile/') && device === DeviceEnum.MOBILE) {
    // 检查是否有对应的移动端路由
    const mobilePath = `/mobile${route.path}`;
    // 这里可以检查路由是否存在，简化处理直接返回
    return mobilePath;
  }
  
  return null;
}

// 保持原有函数
function redirectToLogin(to: RouteLocationNormalized, next: NavigationGuardNext) {
  const params = new URLSearchParams(to.query as Record<string, string>);
  const queryString = params.toString();
  const redirect = queryString ? `${to.path}?${queryString}` : to.path;
  next(`/login?redirect=${encodeURIComponent(redirect)}`);
}

// 保持原有权限检查函数
export function hasAuth(value: string | string[], type: "button" | "role" = "button") {
  const { roles, perms } = useUserStore().userInfo;

  if (type === "button" && roles.includes("ROOT")) {
    return true;
  }

  const auths = type === "button" ? perms : roles;
  return typeof value === "string"
    ? auths.includes(value)
    : value.some((perm) => auths.includes(perm));
}
```

### 5. 在主应用中初始化

```typescript
// src/main.ts (添加设备监听初始化)
import { createApp } from "vue";
import App from "./App.vue";
import { setupStore } from "@/store";
import { setupRouter } from "@/router";
import { setupPermission } from "@/plugins/permission";

// 新增：导入设备监听设置
import { setupRouteDeviceListener } from "@/store/modules/permission";

const app = createApp(App);

// 状态管理
setupStore(app);

// 路由
setupRouter(app);

// 权限
setupPermission();

// 新增：设置设备变化监听
setupRouteDeviceListener();

app.mount("#app");
```

## 🎯 使用示例

### 在组件中使用设备检测

```vue
<template>
  <div class="user-management">
    <!-- PC端表格 -->
    <el-table v-if="!isMobile" :data="userData">
      <el-table-column prop="name" label="姓名" />
      <el-table-column prop="email" label="邮箱" />
    </el-table>
    
    <!-- 移动端列表 -->
    <van-list v-else>
      <van-cell 
        v-for="user in userData" 
        :key="user.id"
        :title="user.name"
        :value="user.email"
      />
    </van-list>
  </div>
</template>

<script setup lang="ts">
import { useDevice } from '@/composables/useDevice';

const { isMobile } = useDevice();
const userData = ref([
  { id: 1, name: '张三', email: '<EMAIL>' },
  { id: 2, name: '李四', email: '<EMAIL>' }
]);
</script>
```

这套方案完全基于您现有的代码结构，只需要最小化的修改就能实现移动端和PC端的完美整合。主要优势是：

1. **保持现有功能不变**：所有现有的权限控制、动态路由生成都保持原样
2. **渐进式升级**：可以逐步迁移页面到自适应模式
3. **智能设备切换**：自动检测设备变化并重新生成路由
4. **向后兼容**：即使不做任何改动，现有功能也能正常工作
