import type { RouteRecordRaw } from "vue-router";
import { constantRoutes } from "@/router";
import { store } from "@/store";
import router from "@/router";
import { useAppStore } from "@/store";
import { DeviceEnum } from "@/enums/DeviceEnum";

import MenuAPI, { type RouteVO } from "@/api/system/menu";
const modules = import.meta.glob("../../views/**/**.vue");
const Layout = () => import("@/layout/index.vue");
const MobileLayout = () => import("@/layout/MobileLayout.vue");
const AdaptiveLayout = () => import("@/layout/AdaptiveLayout.vue");

export const usePermissionStore = defineStore("permission", () => {
  // 所有路由，包括静态和动态路由
  const routes = ref<RouteRecordRaw[]>([]);
  // 混合模式左侧菜单
  const mixLeftMenus = ref<RouteRecordRaw[]>([]);
  // 路由是否已加载
  const isRoutesLoaded = ref(false);

  /**
   * 生成动态路由 - 增强版，支持设备类型
   */
  function generateRoutes() {
    return new Promise<RouteRecordRaw[]>((resolve, reject) => {
      const appStore = useAppStore();
      const platform = appStore.device === DeviceEnum.MOBILE ? 'mobile' : 'desktop';

      console.log(`Generating routes for platform: ${platform}`);

      MenuAPI.getRoutes()
        .then((data) => {
          const dynamicRoutes = transformRoutes(data, platform);
          routes.value = constantRoutes.concat(dynamicRoutes);
          isRoutesLoaded.value = true;
          resolve(dynamicRoutes);
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  /**
   * 重新生成路由（设备切换时使用）
   */
  function regenerateRoutes() {
    console.log('Regenerating routes for device change...');

    // 清除现有动态路由
    routes.value.forEach((route) => {
      if (route.name && !constantRoutes.find((r) => r.name === route.name)) {
        router.removeRoute(route.name);
      }
    });

    // 重置状态
    isRoutesLoaded.value = false;

    // 重新生成路由
    return generateRoutes().then((dynamicRoutes) => {
      dynamicRoutes.forEach((route: RouteRecordRaw) => router.addRoute(route));
      return dynamicRoutes;
    });
  }

  /**
   * 混合模式菜单下根据顶部菜单路径设置左侧菜单
   *
   * @param topMenuPath - 顶部菜单路径
   */
  const setMixLeftMenus = (topMenuPath: string) => {
    const matchedItem = routes.value.find((item) => item.path === topMenuPath);
    if (matchedItem && matchedItem.children) {
      mixLeftMenus.value = matchedItem.children;
    }
  };

  /**
   * 重置路由
   */
  const resetRouter = () => {
    // 删除动态路由，保留静态路由
    routes.value.forEach((route) => {
      if (route.name && !constantRoutes.find((r) => r.name === route.name)) {
        // 从 router 实例中移除动态路由
        router.removeRoute(route.name);
      }
    });

    routes.value = [];
    mixLeftMenus.value = [];
    isRoutesLoaded.value = false;
  };

  return {
    routes,
    generateRoutes,
    regenerateRoutes, // 新增方法
    mixLeftMenus,
    setMixLeftMenus,
    isRoutesLoaded,
    resetRouter,
  };
});

/**
 * 转换路由数据为组件 - 增强版，支持设备适配
 */
const transformRoutes = (routes: RouteVO[], platform?: string) => {
  const asyncRoutes: RouteRecordRaw[] = [];
  const appStore = useAppStore();
  const isMobile = appStore.device === DeviceEnum.MOBILE;

  routes.forEach((route) => {
    const tmpRoute = { ...route } as RouteRecordRaw;

    // 增强布局组件选择逻辑
    if (tmpRoute.component?.toString() == "Layout") {
      tmpRoute.component = selectLayoutComponent(tmpRoute.meta, isMobile);
    } else {
      // 增强页面组件选择逻辑
      const componentPath = selectComponentPath(tmpRoute.component as string, tmpRoute.meta, isMobile);
      const component = modules[`../../views/${componentPath}.vue`];

      if (component) {
        tmpRoute.component = component;
      } else {
        console.warn(`Component not found: ${componentPath}, falling back to 404`);
        tmpRoute.component = modules["../../views/error-page/404.vue"];
      }
    }

    // 增强路由元信息
    tmpRoute.meta = {
      ...tmpRoute.meta,
      platform: isMobile ? 'mobile' : 'desktop',
      deviceType: isMobile ? DeviceEnum.MOBILE : DeviceEnum.DESKTOP,
      adaptive: tmpRoute.meta?.adaptive !== false // 默认为自适应
    };

    if (tmpRoute.children) {
      tmpRoute.children = transformRoutes(route.children, platform);
    }

    asyncRoutes.push(tmpRoute);
  });

  return asyncRoutes;
};

/**
 * 选择布局组件
 */
function selectLayoutComponent(meta: any, isMobile: boolean) {
  // 检查是否有明确的平台限制
  if (meta?.mobileOnly && isMobile) {
    return MobileLayout;
  }

  if (meta?.desktopOnly && !isMobile) {
    return Layout;
  }

  // 检查是否禁用自适应
  if (meta?.adaptive === false) {
    return isMobile ? MobileLayout : Layout;
  }

  // 默认使用自适应布局
  return AdaptiveLayout;
}

/**
 * 选择组件路径
 */
function selectComponentPath(originalPath: string, meta: any, isMobile: boolean): string {
  // 1. 检查是否有平台特定组件配置
  if (meta?.mobileComponent && isMobile) {
    return meta.mobileComponent;
  }

  if (meta?.desktopComponent && !isMobile) {
    return meta.desktopComponent;
  }

  // 2. 如果标记为自适应，直接返回原路径
  if (meta?.adaptive === true) {
    return originalPath;
  }

  // 3. 尝试平台特定路径
  const platformPath = isMobile
    ? `mobile/${originalPath}`
    : `desktop/${originalPath}`;

  if (modules[`../../views/${platformPath}.vue`]) {
    return platformPath;
  }

  // 4. 尝试共享路径
  const sharedPath = `shared/${originalPath}`;
  if (modules[`../../views/${sharedPath}.vue`]) {
    return sharedPath;
  }

  // 5. 返回原始路径
  return originalPath;
}

/**
 * 设备变化监听器设置
 */
export function setupRouteDeviceListener() {
  const appStore = useAppStore();
  const permissionStore = usePermissionStore();

  watch(
    () => appStore.device,
    async (newDevice, oldDevice) => {
      if (oldDevice && newDevice !== oldDevice && permissionStore.isRoutesLoaded) {
        console.log(`Device changed from ${oldDevice} to ${newDevice}, regenerating routes...`);

        try {
          await permissionStore.regenerateRoutes();
          console.log('Routes regenerated successfully');
        } catch (error) {
          console.error('Failed to regenerate routes:', error);
        }
      }
    }
  );
}

/**
 * 在组件外使用 Pinia store 实例 @see https://pinia.vuejs.org/core-concepts/outside-component-usage.html
 */
export function usePermissionStoreHook() {
  return usePermissionStore(store);
}
