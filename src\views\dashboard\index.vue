<template>
  <div class="dashboard-container">
    <el-row :gutter="10">
      <el-col :span="24" :xs="24">
        <el-card>
          <div style="display: flex; justify-content: space-between; align-items: center">
            <div>
              您好{{ userStore.userInfo.nickname }}老师，您负责保管
              <label v-if="Array.isArray(tj_data) && tj_data.length > 0">{{ tj_data[0].SL }}</label>
              <label v-else>0</label>
              件资产，总计
              <label v-if="Array.isArray(tj_data) && tj_data.length > 0">{{ tj_data[0].JE }}</label>
              <label v-else>0</label>
              元。您管辖
              <label v-if="Array.isArray(tj_data) && tj_data.length > 0">{{ tj_data[1].SL }}</label>
              <label v-else>0</label>
              件资产，总计
              <label v-if="Array.isArray(tj_data) && tj_data.length > 0">{{ tj_data[1].JE }}</label>
              <label v-else>0</label>
              元，当前有
              <label v-if="Array.isArray(tj_data) && tj_data.length > 0">{{ tj_data[2].SL }}</label>
              <label v-else>0</label>
              件资产正在变动中
            </div>
            <div>
              <el-button
                type="primary"
                @click="goToBigScreen"
                style="background: linear-gradient(45deg, #409eff, #67c23a); border: none"
              >
                <el-icon><Monitor /></el-icon>
                资产智能驾驶舱
              </el-button>
            </div>
          </div>
          <!-- <iframe
            src="http://192.168.100.116/chatbot/aa7fJ4BMJeaF1wN2"
            style="width: 100%; height: 100%; min-height: 700px"
            frameborder="0"
            allow="microphone"
          /> -->
        </el-card>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="8" :xs="24">
        <div>
          <ToDoList guid="" />
        </div>
      </el-col>
      <el-col :span="8" :xs="24">
        <div>
          <Notice />
        </div>
      </el-col>
      <el-col :span="8" :xs="24">
        <div><PropertyWarning /></div>
      </el-col>
    </el-row>

    <el-row :gutter="10">
      <el-col :span="8">
        <div>
          <Fast />
        </div>
      </el-col>
      <el-col :span="8"><Charts /></el-col>
      <el-col :span="8"><PropertyMarket /></el-col>
    </el-row>

    <el-row :gutter="10">
      <el-col :xs="12" :span="12" />
      <el-col :xs="12" :span="12" />
    </el-row>
  </div>
</template>

<script setup lang="ts">
import ToDoList from "@/views/dashboard/components/ToDoList/index.vue"; //待办
import Notice from "@/views/dashboard/components/Notice/index.vue"; //通知公告
import Fast from "@/views/dashboard/components/FastBoard/index.vue"; //快捷版面
import Charts from "@/views/dashboard/components/Charts/index.vue"; //快捷版面
import PropertyWarning from "@/views/dashboard/components/PropertyWarning/index.vue"; //资产预警
import PropertyMarket from "@/views/dashboard/components/PropertyMarket/index.vue";
import { useTagsViewStore, useUserStore } from "@/store";
import dashboardAPI, { tjType } from "@/api/properties/dashboard";
import { Monitor } from "@element-plus/icons-vue";
import { useRouter } from "vue-router";

defineOptions({
  name: "Dashboard",
  inheritAttrs: false,
});

const userStore = useUserStore();
const router = useRouter();

const tj_data = ref(<tjType[]>[]);

// 跳转到驾驶舱
const goToBigScreen = () => {
  window.open("/bigScreen", "_blank");
};
//const bg_data = ref<tjType | null>(null);
//const BgSl=tj_data[0]["SL"];

onMounted(() => {
  dashboardAPI.getPropertyMyCard().then((data) => {
    tj_data.value = data;
    // bg_data.value=data[0];
    // console.log('tj_data');
    //console.log(bg_data.value);
    //console.log(tj_data.value);
    //console.log(tj_data.value[0]);
  });
});

/*onBeforeMount(() => {
  dashboardAPI.getPropertyMyCard().then((data)=>{
    tj_data.value=data;
    bg_data.value=data[0];
    console.log('tj_data');
    console.log(bg_data.value);
    //console.log(tj_data.value);
    //console.log(tj_data.value[0]);
  })
});*/
</script>

<style lang="scss" scoped>
.el-row {
  margin-bottom: 20px;
}
.dashboard-container {
  padding: 10px;
}
</style>
