<template>
  <div class="mobile-remind">
    <!-- 顶部导航栏 -->
    <van-nav-bar title="待办中心" left-arrow fixed placeholder @click-left="goBack" />

    <!-- 搜索框 -->
    <div class="search-bar">
      <van-search
        v-model="searchValue"
        placeholder="请输入待办事项"
        shape="round"
        show-action
        @search="onSearch"
      >
        <template #action>
          <div @click="onSearch">搜索</div>
        </template>
      </van-search>
    </div>

    <!-- 待办统计 -->
    <div class="remind-stats">
      <div class="stat-item">
        <div class="stat-value">{{ totalTasks }}</div>
        <div class="stat-label">全部</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">{{ urgentTasks }}</div>
        <div class="stat-label">紧急</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">{{ todayTasks }}</div>
        <div class="stat-label">今日</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">{{ completedTasks }}</div>
        <div class="stat-label">已完成</div>
      </div>
    </div>

    <!-- 标签页 -->
    <van-tabs v-model:active="activeTab" sticky>
      <van-tab title="全部">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <div
            v-for="(task, index) in allTasks"
            :key="index"
            class="task-item"
            :class="{ 'is-done': task.status === 'done' }"
            @click="viewTaskDetail(task)"
          >
            <div class="task-checkbox" @click.stop="toggleTaskStatus(task)">
              <van-checkbox :checked="task.status === 'done'" />
            </div>
            <div class="task-content">
              <div class="task-title">
                {{ task.title }}
                <span v-if="task.relatedAsset" class="related-tag">关联资产</span>
              </div>
              <div v-if="task.description" class="task-desc">{{ task.description }}</div>
              <div class="task-meta">
                <div class="priority-tag" :class="getPriorityClass(task.priority)">
                  {{ getPriorityText(task.priority) }}
                </div>
                <div
                  v-if="task.deadline"
                  class="deadline"
                  :class="{ overdue: isOverdue(task.deadline) }"
                >
                  <van-icon name="clock-o" />
                  <span>{{ formatDate(task.deadline) }}</span>
                </div>
              </div>
            </div>
          </div>
        </van-list>
      </van-tab>
      <van-tab title="待处理">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <div
            v-for="(task, index) in pendingTasks"
            :key="index"
            class="task-item"
            :class="{ 'is-done': task.status === 'done' }"
            @click="viewTaskDetail(task)"
          >
            <div class="task-checkbox" @click.stop="toggleTaskStatus(task)">
              <van-checkbox :checked="task.status === 'done'" />
            </div>
            <div class="task-content">
              <div class="task-title">
                {{ task.title }}
                <span v-if="task.relatedAsset" class="related-tag">关联资产</span>
              </div>
              <div v-if="task.description" class="task-desc">{{ task.description }}</div>
              <div class="task-meta">
                <div class="priority-tag" :class="getPriorityClass(task.priority)">
                  {{ getPriorityText(task.priority) }}
                </div>
                <div
                  v-if="task.deadline"
                  class="deadline"
                  :class="{ overdue: isOverdue(task.deadline) }"
                >
                  <van-icon name="clock-o" />
                  <span>{{ formatDate(task.deadline) }}</span>
                </div>
              </div>
            </div>
          </div>
        </van-list>
      </van-tab>
      <van-tab title="已完成">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <div
            v-for="(task, index) in doneTasks"
            :key="index"
            class="task-item"
            :class="{ 'is-done': task.status === 'done' }"
            @click="viewTaskDetail(task)"
          >
            <div class="task-checkbox" @click.stop="toggleTaskStatus(task)">
              <van-checkbox :checked="task.status === 'done'" />
            </div>
            <div class="task-content">
              <div class="task-title">
                {{ task.title }}
                <span v-if="task.relatedAsset" class="related-tag">关联资产</span>
              </div>
              <div v-if="task.description" class="task-desc">{{ task.description }}</div>
              <div class="task-meta">
                <div class="priority-tag" :class="getPriorityClass(task.priority)">
                  {{ getPriorityText(task.priority) }}
                </div>
                <div
                  v-if="task.deadline"
                  class="deadline"
                  :class="{ overdue: isOverdue(task.deadline) }"
                >
                  <van-icon name="clock-o" />
                  <span>{{ formatDate(task.deadline) }}</span>
                </div>
              </div>
            </div>
          </div>
        </van-list>
      </van-tab>
    </van-tabs>

    <!-- 底部操作栏 -->
    <div class="bottom-bar">
      <van-button type="primary" block @click="showAddTask = true">新建待办</van-button>
    </div>

    <!-- 新建待办弹窗 -->
    <van-popup
      v-model:show="showAddTask"
      round
      position="bottom"
      :style="{ height: '60%' }"
      closeable
    >
      <div class="add-task-form">
        <div class="form-header">新建待办</div>
        <div class="form-content">
          <van-form @submit="onSubmitTask">
            <van-cell-group inset>
              <van-field
                v-model="newTask.title"
                name="title"
                label="标题"
                placeholder="请输入待办标题"
                :rules="[{ required: true, message: '请输入待办标题' }]"
              />
              <van-field
                v-model="newTask.description"
                name="description"
                label="描述"
                type="textarea"
                rows="3"
                placeholder="请输入待办描述"
              />
              <van-field
                readonly
                name="deadline"
                label="截止时间"
                :value="newTask.deadline"
                placeholder="请选择截止时间"
                @click="showDatePicker = true"
              />
              <van-field name="priority" label="优先级">
                <template #input>
                  <van-radio-group v-model="newTask.priority" direction="horizontal">
                    <van-radio name="low">低</van-radio>
                    <van-radio name="medium">中</van-radio>
                    <van-radio name="high">高</van-radio>
                  </van-radio-group>
                </template>
              </van-field>
              <van-field name="related" label="关联资产">
                <template #input>
                  <div class="select-asset" @click="showAssetSelector = true">
                    {{ newTask.relatedAsset ? newTask.relatedAsset : "请选择关联资产" }}
                    <van-icon name="arrow" />
                  </div>
                </template>
              </van-field>
            </van-cell-group>
            <div style="margin: 16px">
              <van-button round block type="primary" native-type="submit">保存</van-button>
            </div>
          </van-form>
        </div>
      </div>
    </van-popup>

    <!-- 日期选择器 -->
    <van-popup v-model:show="showDatePicker" position="bottom">
      <van-datetime-picker
        v-model="selectedDate"
        type="datetime"
        title="选择截止时间"
        :min-date="new Date()"
        @confirm="onConfirmDate"
        @cancel="showDatePicker = false"
      />
    </van-popup>

    <!-- 资产选择器 -->
    <van-popup v-model:show="showAssetSelector" position="bottom" :style="{ height: '50%' }">
      <div class="asset-selector">
        <div class="selector-header">
          <span>选择关联资产</span>
          <van-icon name="cross" @click="showAssetSelector = false" />
        </div>
        <div class="selector-content">
          <van-search v-model="assetSearchValue" placeholder="搜索资产" />
          <van-radio-group v-model="selectedAsset">
            <van-cell-group>
              <van-cell
                v-for="asset in assetList"
                :key="asset.id"
                clickable
                @click="selectedAsset = asset.id"
              >
                <template #title>
                  <div class="asset-item">
                    <van-radio :name="asset.id" />
                    <div class="asset-info">
                      <div class="asset-name">{{ asset.name }}</div>
                      <div class="asset-code">{{ asset.code }}</div>
                    </div>
                  </div>
                </template>
              </van-cell>
            </van-cell-group>
          </van-radio-group>
        </div>
        <div class="selector-footer">
          <van-button block type="primary" @click="onConfirmAsset">确定</van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { useRouter } from "vue-router";

const router = useRouter();
const searchValue = ref("");
const activeTab = ref(0);
const loading = ref(false);
const finished = ref(false);

// 新建待办相关
const showAddTask = ref(false);
const showDatePicker = ref(false);
const showAssetSelector = ref(false);
const selectedDate = ref(new Date());
const assetSearchValue = ref("");
const selectedAsset = ref("");
const newTask = ref({
  title: "",
  description: "",
  deadline: "",
  priority: "medium",
  relatedAsset: "",
});

// 统计数据
const totalTasks = ref(25);
const urgentTasks = ref(5);
const todayTasks = ref(8);
const completedTasks = ref(12);

// 待办列表
const allTasks = ref([
  {
    id: 1,
    title: "资产盘点任务",
    description: "完成办公设备季度盘点工作",
    deadline: "2025-06-15 18:00",
    priority: "high",
    status: "pending",
    relatedAsset: "办公设备",
  },
  {
    id: 2,
    title: "处理资产报废申请",
    description: "审核并处理行政部提交的资产报废申请",
    deadline: "2025-06-10 12:00",
    priority: "medium",
    status: "pending",
    relatedAsset: null,
  },
  {
    id: 3,
    title: "更新资产信息",
    description: "更新新采购设备的资产信息",
    deadline: "2025-06-05 17:30",
    priority: "low",
    status: "done",
    relatedAsset: "新采购设备",
  },
  {
    id: 4,
    title: "维修打印机",
    description: "联系维修人员修理财务部打印机",
    deadline: "2025-06-02 10:00",
    priority: "high",
    status: "done",
    relatedAsset: "打印机",
  },
  {
    id: 5,
    title: "资产调拨确认",
    description: "确认教务处资产调拨完成情况",
    deadline: "2025-06-20 15:00",
    priority: "medium",
    status: "pending",
    relatedAsset: null,
  },
]);

// 待处理任务
const pendingTasks = computed(() => {
  return allTasks.value.filter((task) => task.status === "pending");
});

// 已完成任务
const doneTasks = computed(() => {
  return allTasks.value.filter((task) => task.status === "done");
});

// 资产列表
const assetList = ref([
  { id: 1, name: "联想ThinkPad笔记本电脑", code: "ZC202501001" },
  { id: 2, name: "惠普LaserJet打印机", code: "ZC202501002" },
  { id: 3, name: "人体工学办公椅", code: "ZC202501003" },
  { id: 4, name: "爱普生投影仪", code: "ZC202501004" },
  { id: 5, name: "戴尔显示器", code: "ZC202501005" },
]);

// 返回上一页
const goBack = () => {
  router.go(-1);
};

// 搜索
const onSearch = () => {
  console.log("搜索:", searchValue.value);
  // 实现搜索逻辑
};

// 加载更多
const onLoad = () => {
  setTimeout(() => {
    // 模拟加载更多数据
    loading.value = false;
    finished.value = true;
  }, 1000);
};

// 获取优先级样式类
const getPriorityClass = (priority) => {
  const classMap = {
    high: "priority-high",
    medium: "priority-medium",
    low: "priority-low",
  };
  return classMap[priority] || "";
};

// 获取优先级文本
const getPriorityText = (priority) => {
  const textMap = {
    high: "高",
    medium: "中",
    low: "低",
  };
  return textMap[priority] || "未设置";
};

// 格式化日期
const formatDate = (dateStr) => {
  const date = new Date(dateStr);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")} ${String(date.getHours()).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
};

// 检查是否过期
const isOverdue = (deadline) => {
  if (!deadline) return false;
  return new Date(deadline) < new Date();
};

// 切换任务状态
const toggleTaskStatus = (task) => {
  task.status = task.status === "done" ? "pending" : "done";
};

// 查看任务详情
const viewTaskDetail = (task) => {
  console.log("查看待办详情:", task);
  // 实现查看详情逻辑
};

// 确认日期选择
const onConfirmDate = (date) => {
  const formattedDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")} ${String(date.getHours()).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
  newTask.value.deadline = formattedDate;
  showDatePicker.value = false;
};

// 确认资产选择
const onConfirmAsset = () => {
  if (selectedAsset.value) {
    const asset = assetList.value.find((item) => item.id === selectedAsset.value);
    if (asset) {
      newTask.value.relatedAsset = asset.name;
    }
  }
  showAssetSelector.value = false;
};

// 提交新建待办
const onSubmitTask = (values) => {
  console.log("提交新建待办:", {
    ...values,
    deadline: newTask.value.deadline,
    relatedAsset: newTask.value.relatedAsset,
  });

  // 添加到待办列表
  const newId = allTasks.value.length + 1;
  allTasks.value.unshift({
    id: newId,
    title: values.title,
    description: values.description,
    deadline: newTask.value.deadline,
    priority: values.priority,
    status: "pending",
    relatedAsset: newTask.value.relatedAsset || null,
  });

  // 重置表单
  newTask.value = {
    title: "",
    description: "",
    deadline: "",
    priority: "medium",
    relatedAsset: "",
  };

  // 关闭弹窗
  showAddTask.value = false;
};
</script>

<style lang="scss" scoped>
.mobile-remind {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 60px; // 为底部操作栏留出空间

  .search-bar {
    padding: 8px 0;
  }

  .remind-stats {
    margin: 12px;
    padding: 16px;
    background-color: #fff;
    border-radius: 8px;
    display: flex;
    justify-content: space-around;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    .stat-item {
      text-align: center;

      .stat-value {
        font-size: 18px;
        font-weight: 600;
        color: #1989fa;
        margin-bottom: 4px;
      }

      .stat-label {
        font-size: 12px;
        color: #666;
      }
    }
  }

  .task-item {
    margin: 12px;
    padding: 12px;
    background-color: #fff;
    border-radius: 8px;
    display: flex;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    &.is-done {
      opacity: 0.6;

      .task-title {
        text-decoration: line-through;
        color: #999;
      }
    }

    .task-checkbox {
      margin-right: 12px;
      padding-top: 2px;
    }

    .task-content {
      flex: 1;

      .task-title {
        font-size: 15px;
        font-weight: 500;
        margin-bottom: 8px;
        line-height: 1.4;

        .related-tag {
          display: inline-block;
          margin-left: 8px;
          padding: 0 6px;
          font-size: 12px;
          color: #1989fa;
          background-color: #f0f9ff;
          border-radius: 4px;
          font-weight: normal;
        }
      }

      .task-desc {
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
        line-height: 1.4;
      }

      .task-meta {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 8px;

        .priority-tag {
          padding: 2px 8px;
          border-radius: 4px;
          font-size: 12px;

          &.priority-high {
            background-color: #fff1f0;
            color: #ee0a24;
          }

          &.priority-medium {
            background-color: #fff7e6;
            color: #ff976a;
          }

          &.priority-low {
            background-color: #f0f9ff;
            color: #1989fa;
          }
        }

        .deadline {
          display: flex;
          align-items: center;
          font-size: 12px;
          color: #999;

          .van-icon {
            margin-right: 4px;
          }

          &.overdue {
            color: #ee0a24;
          }
        }
      }
    }
  }

  .bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 10px 12px;
    background-color: #fff;
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
    z-index: 10;
  }

  .add-task-form {
    height: 100%;
    display: flex;
    flex-direction: column;

    .form-header {
      padding: 16px;
      text-align: center;
      font-size: 16px;
      font-weight: 500;
      border-bottom: 1px solid #f5f5f5;
    }

    .form-content {
      flex: 1;
      overflow-y: auto;
      padding: 16px 0;
    }
  }

  .select-asset {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    color: #323233;
  }

  .asset-selector {
    height: 100%;
    display: flex;
    flex-direction: column;

    .selector-header {
      padding: 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #f5f5f5;
      font-weight: 500;
    }

    .selector-content {
      flex: 1;
      overflow-y: auto;

      .asset-item {
        display: flex;
        align-items: center;

        .asset-info {
          margin-left: 8px;

          .asset-name {
            font-size: 14px;
          }

          .asset-code {
            font-size: 12px;
            color: #999;
          }
        }
      }
    }

    .selector-footer {
      padding: 16px;
      border-top: 1px solid #f5f5f5;
    }
  }
}

:deep(.van-nav-bar) {
  background-color: #1989fa;

  .van-nav-bar__title {
    color: #fff;
  }

  .van-icon {
    color: #fff;
  }
}

:deep(.van-search__action) {
  color: #1989fa;
}

:deep(.van-tabs__line) {
  background-color: #1989fa;
}

:deep(.van-checkbox__icon--checked .van-icon) {
  background-color: #1989fa;
  border-color: #1989fa;
}
</style>
