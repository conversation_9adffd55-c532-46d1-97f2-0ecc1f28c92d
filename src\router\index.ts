import type { App } from "vue";
import { createRouter, createWebHashHistory, type RouteRecordRaw } from "vue-router";
import { useAppStore } from "@/store";
import { DeviceEnum } from "@/enums/DeviceEnum";

export const Layout = () => import("@/layout/index.vue");
export const MobileLayout = () => import("@/layout/MobileLayout.vue");
// 静态路由
export const constantRoutes: RouteRecordRaw[] = [
  {
    path: "/redirect",
    component: Layout,
    meta: { hidden: true },
    children: [
      {
        path: "/redirect/:path(.*)",
        component: () => import("@/views/redirect/index.vue"),
      },
    ],
  },
  {
    path: "/login",
    component: () => import("@/views/login/SmartLogin.vue"),
    meta: { hidden: true },
  },
  {
    path: "/warehouse",
    component: () => import("@/views/warehouse/AssetWarehouse.vue"),
    meta: { hidden: true },
  },
  {
    path: "/assetManagement",
    component: () => import("@/views/warehouse/AssetManagement.vue"),
    meta: { hidden: true },
  },
  {
    path: "/mobile",
    component: MobileLayout,
    meta: { hidden: true },
    redirect: "/mobile/dashboard",
    children: [
      {
        path: "dashboard",
        component: () => import("@/views/mobile/dashboard/index.vue"),
        meta: { title: "首页" },
      },
      {
        path: "401",
        component: () => import("@/views/error/401.vue"),
        meta: { hidden: true },
      },
      {
        path: "404",
        component: () => import("@/views/error-page/Adaptive404.vue"),
        meta: { hidden: true, platform: "mobile" },
      },
    ],
  },
  {
    path: "/",
    name: "/",
    component: Layout,
    redirect: "/dashboard",
    children: [
      {
        path: "dashboard",
        component: () => import("@/views/dashboard/index.vue"),
        // 用于 keep-alive 功能，需要与 SFC 中自动推导或显式声明的组件名称一致
        // 参考文档: https://cn.vuejs.org/guide/built-ins/keep-alive.html#include-exclude
        name: "Dashboard",
        meta: {
          title: "dashboard",
          icon: "homepage",
          affix: true,
          keepAlive: true,
        },
      },
      {
        path: "401",
        component: () => import("@/views/error/401.vue"),
        meta: { hidden: true },
      },
      {
        path: "404",
        component: () => import("@/views/error-page/Adaptive404.vue"),
        meta: { hidden: true, platform: "desktop" },
      },
      {
        path: "profile",
        name: "Profile",
        component: () => import("@/views/profile/index.vue"),
        meta: { title: "个人中心", icon: "user", hidden: true },
      },
      {
        path: "myNotice",
        name: "MyNotice",
        component: () => import("@/views/system/notice/components/MyNotice.vue"),
        meta: { title: "我的通知", icon: "user", hidden: true },
      },
      {
        path: "simple-test",
        name: "SimpleTest",
        component: () => import("@/views/test/SimpleTest.vue"),
        meta: { title: "简单测试", icon: "setting", hidden: true },
      },
    ],
  },
  // 自适应共享页面路由
  {
    path: "/shared",
    component: () => import("@/layout/AdaptiveLayout.vue"),
    redirect: "/shared/dashboard",
    name: "Shared",
    meta: { title: "共享页面", icon: "share", hidden: true },
    children: [
      {
        path: "dashboard",
        name: "SharedDashboard",
        component: () => import("@/views/shared/Dashboard.vue"),
        meta: { title: "自适应仪表板", icon: "dashboard", adaptive: true },
      },
      {
        path: "users",
        name: "SharedUserManagement",
        component: () => import("@/views/shared/UserManagement.vue"),
        meta: { title: "用户管理", icon: "user", adaptive: true },
      },
    ],
  },

  // 通配符路由 - 捕获所有未匹配的路由
  {
    path: "/:pathMatch(.*)*",
    redirect: (to) => {
      // 根据设备类型重定向到对应的404页面
      const appStore = useAppStore();
      return appStore.device === DeviceEnum.MOBILE ? "/mobile/404" : "/404";
    },
    meta: { hidden: true },
  },
];

/**
 * 创建路由
 */
const router = createRouter({
  history: createWebHashHistory(),
  routes: constantRoutes,
  // 刷新时，滚动条位置还原
  scrollBehavior: () => ({ left: 0, top: 0 }),
});

// 全局注册 router
export function setupRouter(app: App<Element>) {
  app.use(router);
}

export default router;
