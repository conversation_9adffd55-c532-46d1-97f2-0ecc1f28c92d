<template>
  <div class="warning-panel">
    <div class="panel-header">
      <div class="header-content">
        <div class="title-wrapper">
          <div class="title-icon">⚠️</div>
          <h3 class="title">资产预警</h3>
        </div>
        <div class="header-actions">
          <div class="warning-level" :class="overallLevel">
            <div class="level-dot"></div>
            <span>{{ levelText }}</span>
          </div>
        </div>
      </div>
      <div class="header-decoration"></div>
    </div>
    
    <div class="panel-content">
      <div class="warning-list">
        <div 
          class="warning-item" 
          v-for="(warning, index) in warningStats" 
          :key="index"
          :style="{ '--delay': index * 0.1 + 's' }"
          @click="handleWarningClick(warning)"
        >
          <div class="warning-left">
            <div class="warning-icon" :style="{ backgroundColor: warning.color }">
              <component :is="warning.iconComponent" />
            </div>
            <div class="warning-info">
              <div class="warning-label">{{ warning.label }}</div>
              <div class="warning-desc">{{ warning.description }}</div>
            </div>
          </div>
          
          <div class="warning-right">
            <div class="warning-value" :style="{ color: warning.color }">
              {{ warning.value }}
            </div>
            <div class="warning-status" :class="warning.status">
              <div class="status-dot"></div>
              <span>{{ getStatusText(warning.status) }}</span>
            </div>
          </div>
          
          <!-- 脉冲效果 -->
          <div class="pulse-effect" v-if="warning.urgent"></div>
        </div>
      </div>
      
      <!-- 预警趋势 -->
      <div class="warning-trend">
        <div class="trend-header">
          <div class="trend-title">预警趋势</div>
          <div class="trend-period">近7天</div>
        </div>
        <div ref="trendChart" class="trend-chart"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, computed } from 'vue'
import * as echarts from 'echarts'

const trendChart = ref(null)
let chartInstance = null

// 图标组件
const WarningIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M10.29 3.86L1.82 18A2 2 0 0 0 3.54 21H20.46A2 2 0 0 0 22.18 18L13.71 3.86A2 2 0 0 0 10.29 3.86Z" stroke="currentColor" stroke-width="2"/>
      <line x1="12" y1="9" x2="12" y2="13" stroke="currentColor" stroke-width="2"/>
      <line x1="12" y1="17" x2="12.01" y2="17" stroke="currentColor" stroke-width="2"/>
    </svg>
  `
}

const AlertIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
      <line x1="12" y1="8" x2="12" y2="12" stroke="currentColor" stroke-width="2"/>
      <line x1="12" y1="16" x2="12.01" y2="16" stroke="currentColor" stroke-width="2"/>
    </svg>
  `
}

const DangerIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2" stroke="currentColor" stroke-width="2"/>
    </svg>
  `
}

const StorageIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <ellipse cx="12" cy="5" rx="9" ry="3" stroke="currentColor" stroke-width="2"/>
      <path d="M21 12C21 13.66 16.97 15 12 15S3 13.66 3 12" stroke="currentColor" stroke-width="2"/>
      <path d="M3 5V19C3 20.66 7.03 22 12 22S21 20.66 21 19V5" stroke="currentColor" stroke-width="2"/>
    </svg>
  `
}

// 预警数据
const warningStats = ref([
  {
    label: '资产超期预警',
    description: '设备使用超过预定期限',
    value: '180',
    icon: 'warning',
    iconComponent: WarningIcon,
    color: '#ff6b6b',
    status: 'urgent',
    urgent: true
  },
  {
    label: '资产单点预警',
    description: '关键设备存在故障风险',
    value: '9833',
    icon: 'alert',
    iconComponent: AlertIcon,
    color: '#ffb800',
    status: 'warning',
    urgent: false
  },
  {
    label: '维护到期预警',
    description: '设备维护计划即将到期',
    value: '8',
    icon: 'danger',
    iconComponent: DangerIcon,
    color: '#00d4ff',
    status: 'normal',
    urgent: false
  },
  {
    label: '公物仓预警',
    description: '库存物品数量不足',
    value: '8',
    icon: 'storage',
    iconComponent: StorageIcon,
    color: '#8b5cf6',
    status: 'normal',
    urgent: false
  }
])

// 计算整体预警级别
const overallLevel = computed(() => {
  const urgentCount = warningStats.value.filter(w => w.status === 'urgent').length
  const warningCount = warningStats.value.filter(w => w.status === 'warning').length
  
  if (urgentCount > 0) return 'urgent'
  if (warningCount > 0) return 'warning'
  return 'normal'
})

const levelText = computed(() => {
  const levelMap = {
    'urgent': '紧急',
    'warning': '警告',
    'normal': '正常'
  }
  return levelMap[overallLevel.value]
})

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'urgent': '紧急',
    'warning': '警告',
    'normal': '正常'
  }
  return statusMap[status] || '未知'
}

// 处理预警点击
const handleWarningClick = (warning) => {
  console.log('点击预警:', warning)
  // 这里可以跳转到详细页面或显示详细信息
}

// 初始化趋势图表
const initTrendChart = () => {
  if (!trendChart.value) return
  
  chartInstance = echarts.init(trendChart.value)
  
  const dates = []
  const urgentData = []
  const warningData = []
  const normalData = []
  
  // 生成近7天的模拟数据
  for (let i = 6; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    dates.push(date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }))
    
    urgentData.push(Math.floor(Math.random() * 10) + 5)
    warningData.push(Math.floor(Math.random() * 20) + 10)
    normalData.push(Math.floor(Math.random() * 15) + 5)
  }
  
  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 20, 40, 0.9)',
      borderColor: '#ff6b6b',
      borderWidth: 1,
      textStyle: { 
        color: '#fff',
        fontSize: 12
      }
    },
    legend: {
      data: ['紧急', '警告', '正常'],
      textStyle: { 
        color: '#fff',
        fontSize: 10
      },
      top: 10,
      itemWidth: 8,
      itemHeight: 8
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '10%',
      top: '25%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLine: { 
        lineStyle: { color: 'rgba(255, 255, 255, 0.2)' }
      },
      axisLabel: { 
        color: '#fff', 
        fontSize: 9
      },
      splitLine: { show: false }
    },
    yAxis: {
      type: 'value',
      axisLine: { 
        lineStyle: { color: 'rgba(255, 255, 255, 0.2)' }
      },
      axisLabel: { 
        color: '#fff', 
        fontSize: 9
      },
      splitLine: { 
        lineStyle: { 
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: '紧急',
        type: 'line',
        data: urgentData,
        smooth: true,
        lineStyle: { color: '#ff6b6b', width: 2 },
        itemStyle: { color: '#ff6b6b' },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#ff6b6b40' },
            { offset: 1, color: '#ff6b6b10' }
          ])
        }
      },
      {
        name: '警告',
        type: 'line',
        data: warningData,
        smooth: true,
        lineStyle: { color: '#ffb800', width: 2 },
        itemStyle: { color: '#ffb800' },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#ffb80040' },
            { offset: 1, color: '#ffb80010' }
          ])
        }
      },
      {
        name: '正常',
        type: 'line',
        data: normalData,
        smooth: true,
        lineStyle: { color: '#00d4ff', width: 2 },
        itemStyle: { color: '#00d4ff' },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#00d4ff40' },
            { offset: 1, color: '#00d4ff10' }
          ])
        }
      }
    ]
  }
  
  chartInstance.setOption(option)
}

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

onMounted(async () => {
  await nextTick()
  initTrendChart()
  
  window.addEventListener('dashboard-resize', handleResize)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('dashboard-resize', handleResize)
})
</script>

<style lang="scss" scoped>
.warning-panel {
  background: linear-gradient(135deg, 
    rgba(255, 107, 107, 0.05) 0%, 
    rgba(255, 184, 0, 0.03) 100%
  );
  border: 1px solid rgba(255, 107, 107, 0.2);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  
  &:hover {
    border-color: rgba(255, 107, 107, 0.4);
    box-shadow: 0 12px 40px rgba(255, 107, 107, 0.2);
  }
  
  .panel-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid rgba(255, 107, 107, 0.1);
    position: relative;
    
    .header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .title-wrapper {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .title-icon {
          font-size: 20px;
          filter: drop-shadow(0 0 8px rgba(255, 107, 107, 0.6));
        }
        
        .title {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #fff;
          text-shadow: 0 0 10px rgba(255, 107, 107, 0.3);
        }
      }
      
      .header-actions {
        .warning-level {
          display: flex;
          align-items: center;
          gap: 6px;
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
          
          .level-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            animation: levelPulse 2s ease-in-out infinite;
          }
          
          &.urgent {
            background: rgba(255, 107, 107, 0.2);
            color: #ff6b6b;
            border: 1px solid rgba(255, 107, 107, 0.3);
            
            .level-dot {
              background: #ff6b6b;
            }
          }
          
          &.warning {
            background: rgba(255, 184, 0, 0.2);
            color: #ffb800;
            border: 1px solid rgba(255, 184, 0, 0.3);
            
            .level-dot {
              background: #ffb800;
            }
          }
          
          &.normal {
            background: rgba(0, 212, 255, 0.2);
            color: #00d4ff;
            border: 1px solid rgba(0, 212, 255, 0.3);
            
            .level-dot {
              background: #00d4ff;
            }
          }
        }
      }
    }
    
    .header-decoration {
      position: absolute;
      bottom: 0;
      left: 24px;
      width: 60px;
      height: 2px;
      background: linear-gradient(90deg, #ff6b6b, #ffb800);
      border-radius: 1px;
    }
  }
  
  .panel-content {
    padding: 20px 24px 24px;
    
    .warning-list {
      margin-bottom: 20px;
      
      .warning-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px;
        margin-bottom: 12px;
        background: rgba(255, 107, 107, 0.03);
        border: 1px solid rgba(255, 107, 107, 0.1);
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        animation: slideInLeft 0.6s ease-out var(--delay);
        
        &:last-child {
          margin-bottom: 0;
        }
        
        &:hover {
          background: rgba(255, 107, 107, 0.05);
          border-color: rgba(255, 107, 107, 0.2);
          transform: translateX(5px);
        }
        
        .warning-left {
          display: flex;
          align-items: center;
          gap: 12px;
          
          .warning-icon {
            width: 36px;
            height: 36px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            
            svg {
              width: 18px;
              height: 18px;
              color: #fff;
            }
          }
          
          .warning-info {
            .warning-label {
              font-size: 14px;
              color: #fff;
              font-weight: 500;
              margin-bottom: 2px;
            }
            
            .warning-desc {
              font-size: 11px;
              color: rgba(255, 255, 255, 0.6);
              line-height: 1.3;
            }
          }
        }
        
        .warning-right {
          text-align: right;
          
          .warning-value {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 4px;
            text-shadow: 0 0 10px currentColor;
          }
          
          .warning-status {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: 4px;
            font-size: 11px;
            
            .status-dot {
              width: 4px;
              height: 4px;
              border-radius: 50%;
              animation: statusPulse 1.5s ease-in-out infinite;
            }
            
            &.urgent {
              color: #ff6b6b;
              
              .status-dot {
                background: #ff6b6b;
              }
            }
            
            &.warning {
              color: #ffb800;
              
              .status-dot {
                background: #ffb800;
              }
            }
            
            &.normal {
              color: #00d4ff;
              
              .status-dot {
                background: #00d4ff;
              }
            }
          }
        }
        
        .pulse-effect {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          border-radius: 12px;
          background: rgba(255, 107, 107, 0.1);
          animation: pulseEffect 2s ease-in-out infinite;
          pointer-events: none;
        }
      }
    }
    
    .warning-trend {
      .trend-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;
        
        .trend-title {
          font-size: 14px;
          color: #fff;
          font-weight: 500;
        }
        
        .trend-period {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.6);
        }
      }
      
      .trend-chart {
        width: 100%;
        height: 120px;
      }
    }
  }
}

// 动画
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes levelPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

@keyframes statusPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes pulseEffect {
  0%, 100% {
    opacity: 0;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
}
</style>
