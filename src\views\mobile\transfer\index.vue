<template>
  <div class="mobile-transfer">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="资产调拨"
      left-arrow
      fixed
      placeholder
      @click-left="goBack"
    />
    
    <!-- 搜索框 -->
    <div class="search-bar">
      <van-search
        v-model="searchValue"
        placeholder="请输入调拨单号或资产名称"
        shape="round"
        show-action
        @search="onSearch"
      >
        <template #action>
          <div @click="onSearch">搜索</div>
        </template>
      </van-search>
    </div>
    
    <!-- 标签页 -->
    <van-tabs v-model:active="activeTab" sticky>
      <van-tab title="待调拨">
        <transfer-list :list="pendingList" :loading="loading" :finished="finished" @load="onLoad" />
      </van-tab>
      <van-tab title="调拨中">
        <transfer-list :list="processingList" :loading="loading" :finished="finished" @load="onLoad" />
      </van-tab>
      <van-tab title="已完成">
        <transfer-list :list="completedList" :loading="loading" :finished="finished" @load="onLoad" />
      </van-tab>
    </van-tabs>
    
    <!-- 底部操作栏 -->
    <div class="bottom-bar">
      <van-button type="primary" block @click="createTransfer">申请资产调拨</van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineComponent } from 'vue';
import { useRouter } from 'vue-router';

// 调拨列表组件
const TransferList = defineComponent({
  props: {
    list: Array,
    loading: Boolean,
    finished: Boolean
  },
  emits: ['load'],
  setup(props, { emit }) {
    const onLoad = () => {
      emit('load');
    };
    
    const getStatusClass = (status) => {
      const classMap = {
        'pending': 'status-pending',
        'processing': 'status-processing',
        'completed': 'status-completed',
        'rejected': 'status-rejected'
      };
      return classMap[status] || '';
    };
    
    const getStatusText = (status) => {
      const statusMap = {
        'pending': '待调拨',
        'processing': '调拨中',
        'completed': '已完成',
        'rejected': '已拒绝'
      };
      return statusMap[status] || '未知状态';
    };
    
    const goToDetail = (item) => {
      console.log('跳转到详情页:', item);
      // router.push(`/mobile/transfer/detail/${item.id}`);
    };
    
    const handleAction = (item, e) => {
      e.stopPropagation();
      console.log('处理操作:', item);
      if (item.status === 'pending') {
        // 开始调拨
        // router.push(`/mobile/transfer/process/${item.id}`);
      } else if (item.status === 'processing') {
        // 确认接收
        // 显示确认对话框
      } else if (item.status === 'completed') {
        // 查看结果
        // router.push(`/mobile/transfer/result/${item.id}`);
      }
    };
    
    const viewDetail = (item, e) => {
      e.stopPropagation();
      console.log('查看详情:', item);
      // router.push(`/mobile/transfer/detail/${item.id}`);
    };
    
    return () => (
      <van-list
        v-model:loading={props.loading}
        finished={props.finished}
        finished-text="没有更多了"
        onLoad={onLoad}
      >
        {props.list.map((item, index) => (
          <div key={index} class="transfer-item" onClick={() => goToDetail(item)}>
            <div class="transfer-header">
              <div class="transfer-title">
                <span class="transfer-id">[{item.id}]</span>
                {item.name}
              </div>
              <div class={['transfer-status', getStatusClass(item.status)]}>
                {getStatusText(item.status)}
              </div>
            </div>
            <div class="transfer-info">
              <div class="info-row">
                <span class="label">调拨类型:</span>
                <span class="value">{item.type}</span>
              </div>
              <div class="info-row">
                <span class="label">调出部门:</span>
                <span class="value">{item.fromDept}</span>
              </div>
              <div class="info-row">
                <span class="label">调入部门:</span>
                <span class="value">{item.toDept}</span>
              </div>
              <div class="info-row">
                <span class="label">申请时间:</span>
                <span class="value">{item.time}</span>
              </div>
              <div class="info-row">
                <span class="label">资产数量:</span>
                <span class="value">{item.count}件</span>
              </div>
            </div>
            <div class="transfer-footer">
              <van-button 
                size="small" 
                type="primary" 
                plain
                onClick={(e) => handleAction(item, e)}
              >
                {item.status === 'pending' ? '开始调拨' : 
                 item.status === 'processing' ? '确认接收' : '查看结果'}
              </van-button>
              <van-button 
                size="small" 
                type="info" 
                plain
                onClick={(e) => viewDetail(item, e)}
              >
                查看详情
              </van-button>
            </div>
          </div>
        ))}
      </van-list>
    );
  }
});

const router = useRouter();
const searchValue = ref('');
const activeTab = ref(0);
const loading = ref(false);
const finished = ref(false);

// 待调拨列表
const pendingList = ref([
  {
    id: 'DB20250601001',
    name: '办公设备调拨',
    type: '部门间调拨',
    fromDept: '行政部',
    toDept: '财务部',
    time: '2025-06-01',
    count: 3,
    status: 'pending'
  },
  {
    id: 'DB20250531002',
    name: '电脑设备调拨',
    type: '部门间调拨',
    fromDept: '信息中心',
    toDept: '教务处',
    time: '2025-05-31',
    count: 5,
    status: 'pending'
  }
]);

// 调拨中列表
const processingList = ref([
  {
    id: 'DB20250530001',
    name: '办公家具调拨',
    type: '部门间调拨',
    fromDept: '行政部',
    toDept: '人事部',
    time: '2025-05-30',
    count: 8,
    status: 'processing'
  },
  {
    id: 'DB20250529002',
    name: '实验设备调拨',
    type: '校区间调拨',
    fromDept: '物理实验室',
    toDept: '化学实验室',
    time: '2025-05-29',
    count: 2,
    status: 'processing'
  }
]);

// 已完成列表
const completedList = ref([
  {
    id: 'DB20250520001',
    name: '教学设备调拨',
    type: '校区间调拨',
    fromDept: '南校区',
    toDept: '北校区',
    time: '2025-05-20',
    count: 10,
    status: 'completed'
  },
  {
    id: 'DB20250515002',
    name: '办公设备调拨',
    type: '部门间调拨',
    fromDept: '教务处',
    toDept: '学生处',
    time: '2025-05-15',
    count: 4,
    status: 'completed'
  },
  {
    id: 'DB20250510003',
    name: '电子设备调拨',
    type: '部门间调拨',
    fromDept: '信息中心',
    toDept: '图书馆',
    time: '2025-05-10',
    count: 6,
    status: 'rejected'
  }
]);

// 返回上一页
const goBack = () => {
  router.go(-1);
};

// 搜索
const onSearch = () => {
  console.log('搜索:', searchValue.value);
  // 实现搜索逻辑
};

// 加载更多
const onLoad = () => {
  setTimeout(() => {
    // 模拟加载更多数据
    loading.value = false;
    finished.value = true;
  }, 1000);
};

// 申请资产调拨
const createTransfer = () => {
  console.log('申请资产调拨');
  // router.push('/mobile/transfer/create');
};
</script>

<style lang="scss" scoped>
.mobile-transfer {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 60px; // 为底部操作栏留出空间
  
  .search-bar {
    padding: 8px 0;
  }
  
  .transfer-item {
    margin: 12px;
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    
    .transfer-header {
      padding: 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #f5f5f5;
      
      .transfer-title {
        flex: 1;
        font-size: 15px;
        font-weight: 500;
        
        .transfer-id {
          color: #1989fa;
          margin-right: 4px;
        }
      }
      
      .transfer-status {
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
        
        &.status-pending {
          background-color: #fff7e6;
          color: #ff976a;
        }
        
        &.status-processing {
          background-color: #f0f9ff;
          color: #1989fa;
        }
        
        &.status-completed {
          background-color: #f0fff0;
          color: #07c160;
        }
        
        &.status-rejected {
          background-color: #fff1f0;
          color: #ee0a24;
        }
      }
    }
    
    .transfer-info {
      padding: 12px;
      
      .info-row {
        display: flex;
        margin-bottom: 8px;
        font-size: 14px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .label {
          color: #666;
          width: 80px;
        }
        
        .value {
          flex: 1;
          color: #333;
        }
      }
    }
    
    .transfer-footer {
      padding: 12px;
      display: flex;
      justify-content: flex-end;
      gap: 8px;
      border-top: 1px solid #f5f5f5;
    }
  }
  
  .bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 10px 12px;
    background-color: #fff;
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
    z-index: 10;
  }
}

:deep(.van-nav-bar) {
  background-color: #1989fa;
  
  .van-nav-bar__title {
    color: #fff;
  }
  
  .van-icon {
    color: #fff;
  }
}

:deep(.van-search__action) {
  color: #1989fa;
}

:deep(.van-tabs__line) {
  background-color: #1989fa;
}
</style>
