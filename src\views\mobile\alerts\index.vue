<template>
  <div class="mobile-alerts">
    <!-- 顶部导航栏 -->
    <van-nav-bar title="资产警示" left-arrow fixed placeholder @click-left="goBack" />

    <!-- 搜索框 -->
    <div class="search-bar">
      <van-search
        v-model="searchValue"
        placeholder="请输入资产名称或编号"
        shape="round"
        show-action
        @search="onSearch"
      >
        <template #action>
          <div @click="onSearch">搜索</div>
        </template>
      </van-search>
    </div>

    <!-- 警示统计 -->
    <div class="alert-stats">
      <div class="stat-item urgent">
        <div class="stat-value">{{ urgentCount }}</div>
        <div class="stat-label">紧急警示</div>
      </div>
      <div class="stat-item warning">
        <div class="stat-value">{{ warningCount }}</div>
        <div class="stat-label">一般警示</div>
      </div>
      <div class="stat-item info">
        <div class="stat-value">{{ infoCount }}</div>
        <div class="stat-label">提醒事项</div>
      </div>
    </div>

    <!-- 标签页 -->
    <van-tabs v-model:active="activeTab" sticky>
      <van-tab title="全部">
        <alert-list :list="allAlerts" :loading="loading" :finished="finished" @load="onLoad" />
      </van-tab>
      <van-tab title="紧急">
        <alert-list :list="urgentAlerts" :loading="loading" :finished="finished" @load="onLoad" />
      </van-tab>
      <van-tab title="一般">
        <alert-list :list="warningAlerts" :loading="loading" :finished="finished" @load="onLoad" />
      </van-tab>
      <van-tab title="提醒">
        <alert-list :list="infoAlerts" :loading="loading" :finished="finished" @load="onLoad" />
      </van-tab>
    </van-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, defineComponent } from "vue";
import { useRouter } from "vue-router";

// 警示列表组件
const AlertList = defineComponent({
  props: {
    list: Array,
    loading: Boolean,
    finished: Boolean,
  },
  emits: ["load"],
  setup(props, { emit }) {
    const onLoad = () => {
      emit("load");
    };

    const getAlertIcon = (type) => {
      const iconMap = {
        urgent: "warning",
        warning: "info-o",
        info: "bell",
      };
      return iconMap[type] || "info-o";
    };

    const getAlertClass = (type) => {
      const classMap = {
        urgent: "alert-urgent",
        warning: "alert-warning",
        info: "alert-info",
      };
      return classMap[type] || "";
    };

    const goToDetail = (item) => {
      console.log("跳转到详情页:", item);
      // router.push(`/mobile/alerts/detail/${item.id}`);
    };

    const handleAlert = (item, e) => {
      e.stopPropagation();
      console.log("处理警示:", item);
      // 根据警示类型执行不同操作
    };

    const markAsRead = (item, e) => {
      e.stopPropagation();
      console.log("标记为已读:", item);
      item.isRead = true;
    };

    return {
      onLoad,
      getAlertIcon,
      getAlertClass,
      goToDetail,
      handleAlert,
      markAsRead,
    };
  },
  template: `
    <van-list
      v-model:loading="loading"
      :finished="finished"
      finished-text="没有更多了"
      @load="onLoad"
    >
      <div
        v-for="(item, index) in list"
        :key="index"
        :class="['alert-item', getAlertClass(item.type), { 'is-read': item.isRead }]"
        @click="goToDetail(item)"
      >
        <div class="alert-header">
          <div class="alert-icon">
            <van-icon :name="getAlertIcon(item.type)" />
          </div>
          <div class="alert-title">
            <div class="title-text">{{ item.title }}</div>
            <div class="alert-time">{{ item.time }}</div>
          </div>
          <div v-if="!item.isRead" class="unread-dot"></div>
        </div>
        <div class="alert-content">
          <div class="alert-desc">{{ item.description }}</div>
          <div class="alert-info">
            <div class="info-item">
              <span class="label">资产编号:</span>
              <span class="value">{{ item.assetCode }}</span>
            </div>
            <div class="info-item">
              <span class="label">所在位置:</span>
              <span class="value">{{ item.location }}</span>
            </div>
            <div v-if="item.deadline" class="info-item">
              <span class="label">截止时间:</span>
              <span class="value deadline">{{ item.deadline }}</span>
            </div>
          </div>
        </div>
        <div class="alert-footer">
          <van-button
            size="small"
            type="primary"
            plain
            @click.stop="handleAlert(item, $event)"
          >
            立即处理
          </van-button>
          <van-button
            v-if="!item.isRead"
            size="small"
            type="info"
            plain
            @click.stop="markAsRead(item, $event)"
          >
            标记已读
          </van-button>
        </div>
      </div>
    </van-list>
  `,
});

const router = useRouter();
const searchValue = ref("");
const activeTab = ref(0);
const loading = ref(false);
const finished = ref(false);

// 统计数据
const urgentCount = ref(5);
const warningCount = ref(12);
const infoCount = ref(8);

// 全部警示
const allAlerts = ref([
  {
    id: 1,
    type: "urgent",
    title: "资产即将到期报废",
    description: "联想ThinkPad笔记本电脑使用年限即将到期，需要及时处理",
    assetCode: "ZC202501001",
    location: "A栋201室",
    deadline: "2025-06-15",
    time: "2025-06-01 09:30",
    isRead: false,
  },
  {
    id: 2,
    type: "warning",
    title: "资产维护提醒",
    description: "惠普LaserJet打印机需要定期维护保养",
    assetCode: "ZC202501002",
    location: "A栋203室",
    time: "2025-06-01 10:15",
    isRead: false,
  },
  {
    id: 3,
    type: "info",
    title: "盘点任务提醒",
    description: "办公设备季度盘点任务即将开始，请做好准备",
    assetCode: "ZC202501003",
    location: "B栋301室",
    time: "2025-06-01 11:00",
    isRead: true,
  },
  {
    id: 4,
    type: "urgent",
    title: "资产异常状态",
    description: "投影仪设备状态异常，可能存在故障风险",
    assetCode: "ZC202501004",
    location: "C栋会议室",
    deadline: "2025-06-03",
    time: "2025-06-01 14:20",
    isRead: false,
  },
  {
    id: 5,
    type: "warning",
    title: "资产位置变更",
    description: "办公椅位置发生变更，请及时更新资产信息",
    assetCode: "ZC202501005",
    location: "A栋205室",
    time: "2025-06-01 16:45",
    isRead: true,
  },
]);

// 紧急警示
const urgentAlerts = ref(allAlerts.value.filter((item) => item.type === "urgent"));

// 一般警示
const warningAlerts = ref(allAlerts.value.filter((item) => item.type === "warning"));

// 提醒事项
const infoAlerts = ref(allAlerts.value.filter((item) => item.type === "info"));

// 返回上一页
const goBack = () => {
  router.go(-1);
};

// 搜索
const onSearch = () => {
  console.log("搜索:", searchValue.value);
  // 实现搜索逻辑
};

// 加载更多
const onLoad = () => {
  setTimeout(() => {
    // 模拟加载更多数据
    loading.value = false;
    finished.value = true;
  }, 1000);
};
</script>

<style lang="scss" scoped>
.mobile-alerts {
  min-height: 100vh;
  background-color: #f7f8fa;

  .search-bar {
    padding: 8px 0;
  }

  .alert-stats {
    margin: 12px;
    padding: 16px;
    background-color: #fff;
    border-radius: 8px;
    display: flex;
    justify-content: space-around;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    .stat-item {
      text-align: center;

      .stat-value {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 4px;
      }

      .stat-label {
        font-size: 12px;
        color: #666;
      }

      &.urgent .stat-value {
        color: #ee0a24;
      }

      &.warning .stat-value {
        color: #ff976a;
      }

      &.info .stat-value {
        color: #1989fa;
      }
    }
  }

  .alert-item {
    margin: 12px;
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border-left: 4px solid #ddd;

    &.alert-urgent {
      border-left-color: #ee0a24;
    }

    &.alert-warning {
      border-left-color: #ff976a;
    }

    &.alert-info {
      border-left-color: #1989fa;
    }

    &.is-read {
      opacity: 0.7;
    }

    .alert-header {
      padding: 12px;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #f5f5f5;
      position: relative;

      .alert-icon {
        margin-right: 12px;
        font-size: 20px;

        .van-icon {
          color: #666;
        }
      }

      .alert-title {
        flex: 1;

        .title-text {
          font-size: 15px;
          font-weight: 500;
          margin-bottom: 4px;
        }

        .alert-time {
          font-size: 12px;
          color: #999;
        }
      }

      .unread-dot {
        width: 8px;
        height: 8px;
        background-color: #ee0a24;
        border-radius: 50%;
      }
    }

    .alert-content {
      padding: 12px;

      .alert-desc {
        font-size: 14px;
        color: #666;
        margin-bottom: 12px;
        line-height: 1.5;
      }

      .alert-info {
        .info-item {
          display: flex;
          margin-bottom: 6px;
          font-size: 13px;

          &:last-child {
            margin-bottom: 0;
          }

          .label {
            color: #999;
            width: 80px;
          }

          .value {
            flex: 1;
            color: #333;

            &.deadline {
              color: #ee0a24;
              font-weight: 500;
            }
          }
        }
      }
    }

    .alert-footer {
      padding: 12px;
      display: flex;
      justify-content: flex-end;
      gap: 8px;
      border-top: 1px solid #f5f5f5;
    }
  }
}

:deep(.van-nav-bar) {
  background-color: #1989fa;

  .van-nav-bar__title {
    color: #fff;
  }

  .van-icon {
    color: #fff;
  }
}

:deep(.van-search__action) {
  color: #1989fa;
}

:deep(.van-tabs__line) {
  background-color: #1989fa;
}
</style>
