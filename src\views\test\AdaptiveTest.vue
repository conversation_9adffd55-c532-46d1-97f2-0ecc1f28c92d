<template>
  <div class="adaptive-test">
    <div class="test-header">
      <h2>自适应组件测试页面</h2>
      <div class="device-info">
        <el-tag :type="isMobile ? 'success' : 'primary'">
          当前设备: {{ isMobile ? "移动端" : "PC端" }}
        </el-tag>
        <el-tag type="info">屏幕宽度: {{ screenWidth }}px</el-tag>
        <el-tag type="warning">设备类型: {{ deviceInfo.type }}</el-tag>
      </div>
    </div>

    <div class="test-content">
      <!-- 设备信息展示 -->
      <div class="test-section">
        <h3>设备信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">设备类型:</span>
            <span class="value">{{ deviceInfo.type }}</span>
          </div>
          <div class="info-item">
            <span class="label">屏幕尺寸:</span>
            <span class="value">{{ deviceInfo.width }} × {{ deviceInfo.height }}</span>
          </div>
          <div class="info-item">
            <span class="label">屏幕方向:</span>
            <span class="value">{{ deviceInfo.orientation }}</span>
          </div>
          <div class="info-item">
            <span class="label">触摸支持:</span>
            <span class="value">{{ deviceInfo.isTouch ? "是" : "否" }}</span>
          </div>
        </div>
      </div>

      <!-- 自适应按钮测试 -->
      <div class="test-section">
        <h3>自适应按钮测试</h3>
        <div class="button-group">
          <AdaptiveButton type="primary" @click="showMessage('主要按钮')">主要按钮</AdaptiveButton>
          <AdaptiveButton type="success" @click="showMessage('成功按钮')">成功按钮</AdaptiveButton>
          <AdaptiveButton type="warning" @click="showMessage('警告按钮')">警告按钮</AdaptiveButton>
          <AdaptiveButton type="danger" @click="showMessage('危险按钮')">危险按钮</AdaptiveButton>
        </div>

        <div class="button-group">
          <AdaptiveButton size="large" @click="showMessage('大按钮')">大按钮</AdaptiveButton>
          <AdaptiveButton size="default" @click="showMessage('默认按钮')">默认按钮</AdaptiveButton>
          <AdaptiveButton size="small" @click="showMessage('小按钮')">小按钮</AdaptiveButton>
        </div>

        <div class="button-group">
          <AdaptiveButton :block="isMobile" type="primary" @click="showMessage('块级按钮')">
            {{ isMobile ? "块级按钮（移动端）" : "内联按钮（PC端）" }}
          </AdaptiveButton>
        </div>
      </div>

      <!-- 布局配置展示 -->
      <div class="test-section">
        <h3>布局配置</h3>
        <div class="config-grid">
          <div class="config-item">
            <span class="label">侧边栏宽度:</span>
            <span class="value">{{ layoutConfig.sidebarWidth }}px</span>
          </div>
          <div class="config-item">
            <span class="label">头部高度:</span>
            <span class="value">{{ layoutConfig.headerHeight }}px</span>
          </div>
          <div class="config-item">
            <span class="label">标签栏高度:</span>
            <span class="value">{{ layoutConfig.tabbarHeight }}px</span>
          </div>
          <div class="config-item">
            <span class="label">内容边距:</span>
            <span class="value">{{ layoutConfig.contentPadding }}px</span>
          </div>
          <div class="config-item">
            <span class="label">显示侧边栏:</span>
            <span class="value">{{ layoutConfig.showSidebar ? "是" : "否" }}</span>
          </div>
          <div class="config-item">
            <span class="label">显示标签栏:</span>
            <span class="value">{{ layoutConfig.showTabbar ? "是" : "否" }}</span>
          </div>
        </div>
      </div>

      <!-- 主题配置展示 -->
      <div class="test-section">
        <h3>主题配置</h3>
        <div class="theme-demo">
          <div class="font-sizes">
            <div :style="{ fontSize: themeConfig.fontSize.small }">
              小字体 ({{ themeConfig.fontSize.small }})
            </div>
            <div :style="{ fontSize: themeConfig.fontSize.normal }">
              正常字体 ({{ themeConfig.fontSize.normal }})
            </div>
            <div :style="{ fontSize: themeConfig.fontSize.large }">
              大字体 ({{ themeConfig.fontSize.large }})
            </div>
            <div :style="{ fontSize: themeConfig.fontSize.title }">
              标题字体 ({{ themeConfig.fontSize.title }})
            </div>
          </div>

          <div class="spacing-demo">
            <div
              v-for="(size, key) in themeConfig.spacing"
              :key="key"
              class="spacing-item"
              :style="{ padding: size, backgroundColor: '#f0f0f0', margin: '4px' }"
            >
              {{ key }}: {{ size }}
            </div>
          </div>
        </div>
      </div>

      <!-- 操作测试 -->
      <div class="test-section">
        <h3>功能测试</h3>
        <div class="action-group">
          <AdaptiveButton type="primary" @click="simulateDeviceChange">模拟设备切换</AdaptiveButton>
          <AdaptiveButton type="success" @click="testRouteNavigation">测试路由跳转</AdaptiveButton>
          <AdaptiveButton type="info" @click="showDeviceDetails">显示详细信息</AdaptiveButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { useDevice } from "@/composables/useDevice";
import { useAppStore } from "@/store";
import { DeviceEnum } from "@/enums/DeviceEnum";
import AdaptiveButton from "@/components/adaptive/AdaptiveButton.vue";

defineOptions({
  name: "AdaptiveTest",
});

const { isMobile, isTablet, isDesktop, deviceInfo, layoutConfig, themeConfig } = useDevice();

const appStore = useAppStore();
const router = useRouter();

// 响应式屏幕宽度
const screenWidth = ref(window.innerWidth);

// 监听窗口大小变化
const updateScreenWidth = () => {
  screenWidth.value = window.innerWidth;
};

onMounted(() => {
  window.addEventListener("resize", updateScreenWidth);
});

onUnmounted(() => {
  window.removeEventListener("resize", updateScreenWidth);
});

// 显示消息
const showMessage = (message: string) => {
  if (isMobile.value) {
    // 移动端使用 Toast
    console.log(`移动端消息: ${message}`);
    // 这里可以使用 Vant 的 Toast
  } else {
    // PC端使用 Message
    ElMessage.success(`PC端消息: ${message}`);
  }
};

// 模拟设备切换
const simulateDeviceChange = () => {
  const currentDevice = appStore.device;
  const newDevice = currentDevice === DeviceEnum.MOBILE ? DeviceEnum.DESKTOP : DeviceEnum.MOBILE;

  console.log(`模拟设备切换: ${currentDevice} → ${newDevice}`);
  appStore.toggleDevice(newDevice);

  showMessage(`设备已切换到: ${newDevice}`);
};

// 测试路由跳转
const testRouteNavigation = () => {
  const targetPath = isMobile.value ? "/mobile/dashboard" : "/dashboard";
  console.log(`测试路由跳转到: ${targetPath}`);

  router
    .push(targetPath)
    .then(() => {
      showMessage(`成功跳转到: ${targetPath}`);
    })
    .catch((error) => {
      console.error("路由跳转失败:", error);
      showMessage("路由跳转失败");
    });
};

// 显示设备详细信息
const showDeviceDetails = () => {
  const details = {
    设备类型: deviceInfo.value.type,
    屏幕尺寸: `${deviceInfo.value.width} × ${deviceInfo.value.height}`,
    屏幕方向: deviceInfo.value.orientation,
    用户代理: deviceInfo.value.userAgent,
    触摸支持: deviceInfo.value.isTouch,
    是否平板: isTablet.value,
    布局配置: layoutConfig.value,
    主题配置: themeConfig.value,
  };

  console.log("设备详细信息:", details);
  showMessage("设备详细信息已输出到控制台");
};
</script>

<style lang="scss" scoped>
.adaptive-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .test-header {
    margin-bottom: 30px;
    text-align: center;

    h2 {
      margin-bottom: 15px;
      color: var(--el-text-color-primary);
    }

    .device-info {
      display: flex;
      justify-content: center;
      gap: 10px;
      flex-wrap: wrap;
    }
  }

  .test-section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid var(--el-border-color);
    border-radius: 8px;
    background: var(--el-bg-color);

    h3 {
      margin-bottom: 15px;
      color: var(--el-text-color-primary);
      border-bottom: 2px solid var(--el-color-primary);
      padding-bottom: 5px;
    }
  }

  .info-grid,
  .config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;

    .info-item,
    .config-item {
      display: flex;
      justify-content: space-between;
      padding: 10px;
      background: var(--el-fill-color-light);
      border-radius: 4px;

      .label {
        font-weight: 500;
        color: var(--el-text-color-regular);
      }

      .value {
        color: var(--el-text-color-primary);
        font-weight: 600;
      }
    }
  }

  .button-group {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .theme-demo {
    .font-sizes {
      margin-bottom: 20px;

      > div {
        margin-bottom: 8px;
        padding: 5px;
        background: var(--el-fill-color-light);
        border-radius: 4px;
      }
    }

    .spacing-demo {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;

      .spacing-item {
        border-radius: 4px;
        font-size: 12px;
        white-space: nowrap;
      }
    }
  }

  .action-group {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
  }
}

// 移动端适配
@media screen and (max-width: 991px) {
  .adaptive-test {
    padding: 15px;

    .test-header {
      .device-info {
        flex-direction: column;
        align-items: center;
      }
    }

    .test-section {
      padding: 15px;
    }

    .info-grid,
    .config-grid {
      grid-template-columns: 1fr;
    }

    .button-group {
      flex-direction: column;

      .adaptive-button {
        width: 100%;
      }
    }

    .action-group {
      flex-direction: column;
    }
  }
}
</style>
