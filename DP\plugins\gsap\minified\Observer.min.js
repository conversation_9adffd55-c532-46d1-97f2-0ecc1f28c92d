/*!
 * Observer 3.11.3
 * https://greensock.com
 * 
 * @license Copyright 2022, GreenSock. All rights reserved.
 * Subject to the terms at https://greensock.com/standard-license or for Club GreenSock members, the agreement issued with that membership.
 * @author: <PERSON>, <EMAIL>
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).window=e.window||{})}(this,function(a){"use strict";function _defineProperties(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function q(){return be||"undefined"!=typeof window&&(be=window.gsap)&&be.registerPlugin&&be}var be,we,<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>,o=1,Te=[];a._scrollers=[],a._proxies=[];function w(e,t){return~a._proxies.indexOf(e)&&a._proxies[a._proxies.indexOf(e)+1][t]}function x(e){return!!~t.indexOf(e)}function y(e,t,n,r,o){return e.addEventListener(t,n,{passive:!r,capture:!!o})}function z(e,t,n,r){return e.removeEventListener(t,n,!!r)}function C(){return Ee&&Ee.isPressed||a._scrollers.cache++}function D(n,r){function pa(e){if(e||0===e){o&&(Me.history.scrollRestoration="manual");var t=Ee&&Ee.isPressed;e=pa.v=Math.round(e)||(Ee&&Ee.iOS?1:0),n(e),pa.cacheID=a._scrollers.cache,t&&i("ss",e)}else(r||a._scrollers.cache!==pa.cacheID||i("ref"))&&(pa.cacheID=a._scrollers.cache,pa.v=n());return pa.v+pa.offset}return pa.offset=0,n&&pa}function G(e){return be.utils.toArray(e)[0]||("string"==typeof e&&!1!==be.config().nullTargetWarn?console.warn("Element not found:",e):null)}function H(t,e){var n=e.s,r=e.sc;x(t)&&(t=De.scrollingElement||Pe);var o=a._scrollers.indexOf(t),i=r===Se.sc?1:2;~o||(o=a._scrollers.push(t)-1),a._scrollers[o+i]||t.addEventListener("scroll",C);var c=a._scrollers[o+i],s=c||(a._scrollers[o+i]=D(w(t,n),!0)||(x(t)?r:D(function(e){return arguments.length?t[n]=e:t[n]})));return s.target=t,c||(s.smooth="smooth"===be.getProperty(t,"scrollBehavior")),s}function I(e,t,o){function Na(e,t){var n=ke();t||r<n-s?(c=i,i=e,a=s,s=n):o?i+=e:i=c+(e-c)/(n-a)*(s-a)}var i=e,c=e,s=ke(),a=s,r=t||50,l=Math.max(500,3*r);return{update:Na,reset:function reset(){c=i=o?0:i,a=s=0},getVelocity:function getVelocity(e){var t=a,n=c,r=ke();return!e&&0!==e||e===i||Na(e),s===a||l<r-a?0:(i+(o?n:-n))/((o?r:s)-t)*1e3}}}function J(e,t){return t&&!e._gsapAllow&&e.preventDefault(),e.changedTouches?e.changedTouches[0]:e}function K(e){var t=Math.max.apply(Math,e),n=Math.min.apply(Math,e);return Math.abs(t)>=Math.abs(n)?t:n}function L(){(Ye=be.core.globals().ScrollTrigger)&&Ye.core&&function _integrate(){var e=Ye.core,n=e.bridge||{},t=e._scrollers,r=e._proxies;t.push.apply(t,a._scrollers),r.push.apply(r,a._proxies),a._scrollers=t,a._proxies=r,i=function _bridge(e,t){return n[e](t)}}()}function M(e){return(be=e||q())&&"undefined"!=typeof document&&document.body&&(Me=window,Pe=(De=document).documentElement,Oe=De.body,t=[Me,De,Pe,Oe],be.utils.clamp,Xe="onpointerenter"in Oe?"pointer":"mouse",ze=c.isTouch=Me.matchMedia&&Me.matchMedia("(hover: none), (pointer: coarse)").matches?1:"ontouchstart"in Me||0<navigator.maxTouchPoints||0<navigator.msMaxTouchPoints?2:0,Ce=c.eventTypes=("ontouchstart"in Pe?"touchstart,touchmove,touchcancel,touchend":"onpointerdown"in Pe?"pointerdown,pointermove,pointercancel,pointerup":"mousedown,mousemove,mouseup,mouseup").split(","),setTimeout(function(){return o=0},500),L(),we=1),we}var ke=Date.now,i=function _bridge(e,t){return t},n="scrollLeft",r="scrollTop",Ae={s:n,p:"left",p2:"Left",os:"right",os2:"Right",d:"width",d2:"Width",a:"x",sc:D(function(e){return arguments.length?Me.scrollTo(e,Se.sc()):Me.pageXOffset||De[n]||Pe[n]||Oe[n]||0})},Se={s:r,p:"top",p2:"Top",os:"bottom",os2:"Bottom",d:"height",d2:"Height",a:"y",op:Ae,sc:D(function(e){return arguments.length?Me.scrollTo(Ae.sc(),e):Me.pageYOffset||De[r]||Pe[r]||Oe[r]||0})};Ae.op=Se,a._scrollers.cache=0;var c=(Observer.prototype.init=function init(e){we||M(be)||console.warn("Please gsap.registerPlugin(Observer)"),Ye||L();var o=e.tolerance,c=e.dragMinimum,t=e.type,r=e.target,n=e.lineHeight,i=e.debounce,s=e.preventDefault,a=e.onStop,l=e.onStopDelay,u=e.ignore,d=e.wheelSpeed,f=e.event,p=e.onDragStart,g=e.onDragEnd,v=e.onDrag,h=e.onPress,_=e.onRelease,m=e.onRight,b=e.onLeft,w=e.onUp,D=e.onDown,P=e.onChangeX,O=e.onChangeY,X=e.onChange,Y=e.onToggleX,E=e.onToggleY,T=e.onHover,k=e.onHoverEnd,A=e.onMove,S=e.ignoreCheck,q=e.isNormalizer,B=e.onGestureStart,V=e.onGestureEnd,F=e.onWheel,N=e.onEnable,R=e.onDisable,j=e.onClick,W=e.scrollSpeed,U=e.capture,Q=e.allowClicks,Z=e.lockAxis,$=e.onLockAxis;function mc(){return _e=ke()}function nc(e,t){return(se.event=e)&&u&&~u.indexOf(e.target)||t&&ge&&"touch"!==e.pointerType||S&&S(e,t)}function pc(){var e=se.deltaX=K(ye),t=se.deltaY=K(xe),n=Math.abs(e)>=o,r=Math.abs(t)>=o;X&&(n||r)&&X(se,e,t,ye,xe),n&&(m&&0<se.deltaX&&m(se),b&&se.deltaX<0&&b(se),P&&P(se),Y&&se.deltaX<0!=ae<0&&Y(se),ae=se.deltaX,ye[0]=ye[1]=ye[2]=0),r&&(D&&0<se.deltaY&&D(se),w&&se.deltaY<0&&w(se),O&&O(se),E&&se.deltaY<0!=le<0&&E(se),le=se.deltaY,xe[0]=xe[1]=xe[2]=0),(re||ne)&&(A&&A(se),ne&&(v(se),ne=!1),re=!1),ie&&!(ie=!1)&&$&&$(se),oe&&(F(se),oe=!1),ee=0}function qc(e,t,n){ye[n]+=e,xe[n]+=t,se._vx.update(e),se._vy.update(t),i?ee=ee||requestAnimationFrame(pc):pc()}function rc(e,t){Z&&!ce&&(se.axis=ce=Math.abs(e)>Math.abs(t)?"x":"y",ie=!0),"y"!==ce&&(ye[2]+=e,se._vx.update(e,!0)),"x"!==ce&&(xe[2]+=t,se._vy.update(t,!0)),i?ee=ee||requestAnimationFrame(pc):pc()}function sc(e){if(!nc(e,1)){var t=(e=J(e,s)).clientX,n=e.clientY,r=t-se.x,o=n-se.y,i=se.isDragging;se.x=t,se.y=n,(i||Math.abs(se.startX-t)>=c||Math.abs(se.startY-n)>=c)&&(v&&(ne=!0),i||(se.isDragging=!0),rc(r,o),i||p&&p(se))}}function uc(t){if(!nc(t,1)){z(q?r:he,Ce[1],sc,!0);var e=se.isDragging&&(3<Math.abs(se.x-se.startX)||3<Math.abs(se.y-se.startY)),n=J(t);e||(se._vx.reset(),se._vy.reset(),s&&Q&&be.delayedCall(.08,function(){if(300<ke()-_e&&!t.defaultPrevented)if(t.target.click)t.target.click();else if(he.createEvent){var e=he.createEvent("MouseEvents");e.initMouseEvent("click",!0,!0,Me,1,n.screenX,n.screenY,n.clientX,n.clientY,!1,!1,!1,!1,0,null),t.target.dispatchEvent(e)}})),se.isDragging=se.isGesturing=se.isPressed=!1,a&&!q&&te.restart(!0),g&&e&&g(se),_&&_(se,e)}}function vc(e){return e.touches&&1<e.touches.length&&(se.isGesturing=!0)&&B(e,se.isDragging)}function wc(){return(se.isGesturing=!1)||V(se)}function xc(e){if(!nc(e)){var t=ue(),n=de();qc((t-fe)*W,(n-pe)*W,1),fe=t,pe=n,a&&te.restart(!0)}}function yc(e){if(!nc(e)){e=J(e,s),F&&(oe=!0);var t=(1===e.deltaMode?n:2===e.deltaMode?Me.innerHeight:1)*d;qc(e.deltaX*t,e.deltaY*t,0),a&&!q&&te.restart(!0)}}function zc(e){if(!nc(e)){var t=e.clientX,n=e.clientY,r=t-se.x,o=n-se.y;se.x=t,se.y=n,re=!0,(r||o)&&rc(r,o)}}function Ac(e){se.event=e,T(se)}function Bc(e){se.event=e,k(se)}function Cc(e){return nc(e)||J(e,s)&&j(se)}this.target=r=G(r)||Pe,this.vars=e,u=u&&be.utils.toArray(u),o=o||1e-9,c=c||0,d=d||1,W=W||1,t=t||"wheel,touch,pointer",i=!1!==i,n=n||parseFloat(Me.getComputedStyle(Oe).lineHeight)||22;var ee,te,ne,re,oe,ie,ce,se=this,ae=0,le=0,ue=H(r,Ae),de=H(r,Se),fe=ue(),pe=de(),ge=~t.indexOf("touch")&&!~t.indexOf("pointer")&&"pointerdown"===Ce[0],ve=x(r),he=r.ownerDocument||De,ye=[0,0,0],xe=[0,0,0],_e=0,me=se.onPress=function(e){nc(e,1)||(se.axis=ce=null,te.pause(),se.isPressed=!0,e=J(e),ae=le=0,se.startX=se.x=e.clientX,se.startY=se.y=e.clientY,se._vx.reset(),se._vy.reset(),y(q?r:he,Ce[1],sc,s,!0),se.deltaX=se.deltaY=0,h&&h(se))};te=se._dc=be.delayedCall(l||.25,function onStopFunc(){se._vx.reset(),se._vy.reset(),te.pause(),a&&a(se)}).pause(),se.deltaX=se.deltaY=0,se._vx=I(0,50,!0),se._vy=I(0,50,!0),se.scrollX=ue,se.scrollY=de,se.isDragging=se.isGesturing=se.isPressed=!1,se.enable=function(e){return se.isEnabled||(y(ve?he:r,"scroll",C),0<=t.indexOf("scroll")&&y(ve?he:r,"scroll",xc,s,U),0<=t.indexOf("wheel")&&y(r,"wheel",yc,s,U),(0<=t.indexOf("touch")&&ze||0<=t.indexOf("pointer"))&&(y(r,Ce[0],me,s,U),y(he,Ce[2],uc),y(he,Ce[3],uc),Q&&y(r,"click",mc,!1,!0),j&&y(r,"click",Cc),B&&y(he,"gesturestart",vc),V&&y(he,"gestureend",wc),T&&y(r,Xe+"enter",Ac),k&&y(r,Xe+"leave",Bc),A&&y(r,Xe+"move",zc)),se.isEnabled=!0,e&&e.type&&me(e),N&&N(se)),se},se.disable=function(){se.isEnabled&&(Te.filter(function(e){return e!==se&&x(e.target)}).length||z(ve?he:r,"scroll",C),se.isPressed&&(se._vx.reset(),se._vy.reset(),z(q?r:he,Ce[1],sc,!0)),z(ve?he:r,"scroll",xc,U),z(r,"wheel",yc,U),z(r,Ce[0],me,U),z(he,Ce[2],uc),z(he,Ce[3],uc),z(r,"click",mc,!0),z(r,"click",Cc),z(he,"gesturestart",vc),z(he,"gestureend",wc),z(r,Xe+"enter",Ac),z(r,Xe+"leave",Bc),z(r,Xe+"move",zc),se.isEnabled=se.isPressed=se.isDragging=!1,R&&R(se))},se.kill=function(){se.disable();var e=Te.indexOf(se);0<=e&&Te.splice(e,1),Ee===se&&(Ee=0)},Te.push(se),q&&x(r)&&(Ee=se),se.enable(f)},function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),e}(Observer,[{key:"velocityX",get:function get(){return this._vx.getVelocity()}},{key:"velocityY",get:function get(){return this._vy.getVelocity()}}]),Observer);function Observer(e){this.init(e)}c.version="3.11.3",c.create=function(e){return new c(e)},c.register=M,c.getAll=function(){return Te.slice()},c.getById=function(t){return Te.filter(function(e){return e.vars.id===t})[0]},q()&&be.registerPlugin(c),a.Observer=c,a._getProxyProp=w,a._getScrollFunc=H,a._getTarget=G,a._getVelocityProp=I,a._horizontal=Ae,a._isViewport=x,a._vertical=Se,a.default=c;if (typeof(window)==="undefined"||window!==a){Object.defineProperty(a,"__esModule",{value:!0})} else {delete a.default}});

