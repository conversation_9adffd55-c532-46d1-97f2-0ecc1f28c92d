@font-face {
 font-family: "Roobert";
 font-weight: 400;
 font-style: normal;
 src: url("../fonts/Roobert-Regular.woff2") format("woff2");
}

@font-face {
 font-family: "Roobert";
 font-weight: 400;
 font-style: italic;
 src: url("../fonts/Roobert-Regular.woff2") format("woff2");
}

@font-face {
 font-family: "Roobert";
 font-weight: 600;
 font-style: normal;
 src: url("../fonts/Roobert-SemiBold.woff2") format("woff2");
}

@font-face {
 font-family: "Roobert";
 font-weight: 600;
 font-style: italic;
 src: url("../fonts/Roobert-SemiBoldItalic.woff2") format("woff2");
}

@font-face {
  font-family: "InputMono";
  src: url("../fonts/InputMono-Regular.woff2") format("woff2"),
      url("../fonts/InputMono-Regular.woff") format("woff");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "InputMono";
  src: url("../fonts/InputMono-Bold.woff2") format("woff2"),
      url("../fonts/InputMono-Bold.woff") format("woff");
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: "InputMono";
  src: url("../fonts/InputMono-BoldItalic.woff2") format("woff2"),
      url("../fonts/InputMono-BoldItalic.woff") format("woff");
  font-weight: bold;
  font-style: italic;
}

@font-face {
  font-family: "InputMono";
  src: url("../fonts/InputMono-Italic.woff2") format("woff2"),
      url("../fonts/InputMono-Italic.woff") format("woff");
  font-weight: normal;
  font-style: italic;
}

*, *:before, *:after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  border: 0;
  font: inherit;
  vertical-align: baseline;
}

html,
body {
  background-color: #252423;
  color: #F6F4F2;
}

body {
  font-family: Roobert, Helvetica Neue, Helvetica, Arial, sans-serif;
  font-weight: 400;
  font-feature-settings:"tnum" 1, "ss03" 1;
  -webkit-font-smoothing: antialiased;
}

h1, h2 {
  font-weight: 600;
}

input {
  font-family: Roobert, Helvetica Neue, Helvetica, Arial, sans-serif;
  font-variant-numeric: tabular-nums;
}

sup {
  vertical-align: super;
  font-size: .625em;
}

strong {
  font-weight: 600;
}

.color-white { color: #F6F4F2; } /* White */
.color-black { color: #2E2C2C; } /* Black */
.color-red, .color-targets { color: #FF4B4B; }/* red: */
.color-orange, .color-properties { color: #FF8F42; }/* orange: */
.color-lightorange, .color-prop-params { color: #FFC730; }/* lightorange: */
.color-yellow, .color-anim-params { color: #F6FF56; }/* yellow: */
.color-citrus, .color-values { color: #A4FF4F; }/* citrus: */
.color-green, .color-keyframes { color: #18FF74; }/* green: */
.color-darkgreen, .color-staggering { color: #00D672; }/* darkgreen: */
.color-turquoise, .color-tl { color: #3CFFEC; }/* turquoise: */
.color-skyblue, .color-controls { color: #61C3FF; }/* skyblue: */
.color-kingblue, .color-callbacks { color: #5A87FF; }/* kingblue: */
.color-lavender, .color-svg { color: #8453E3; }/* lavender: */
.color-purple, .color-easings { color: #C26EFF; }/* purple: */
.color-pink, .color-helpers { color: #FB89FB; }/* pink: */

.anime-mini-logo {
  width: 100px;
  height: 24px;
  transform: scaleY(.5);
}