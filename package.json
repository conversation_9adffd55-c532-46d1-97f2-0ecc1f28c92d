{"name": "浙江工越信息科技有限公司", "version": "2.20.1", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit & vite build", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit", "lint:eslint": "eslint --fix ./src", "lint:prettier": "prettier --write \"**/*.{js,cjs,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint  \"**/*.{css,scss,vue}\" --fix", "lint:lint-staged": "lint-staged", "preinstall": "npx only-allow pnpm", "prepare": "husky", "commit": "git-cz"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{cjs,json}": ["prettier --write"], "*.{vue,html}": ["eslint --fix", "prettier --write", "stylelint --fix"], "*.{scss,css}": ["stylelint --fix", "prettier --write"], "*.md": ["prettier --write"]}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@stomp/stompjs": "^7.0.0", "@vueuse/core": "^10.11.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "5.1.10", "animate.css": "^4.1.1", "antd-mobile": "^5.39.0", "axios": "^1.7.9", "codemirror": "^5.65.18", "codemirror-editor-vue3": "^2.8.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "docx-preview": "^0.3.4", "echarts": "^5.5.1", "element-plus": "^2.9.1", "exceljs": "^4.4.0", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "path-to-regexp": "^6.3.0", "pinia": "^2.3.0", "qs": "^6.13.1", "sortablejs": "^1.15.6", "uuid": "^11.1.0", "vant": "^4.9.21", "vue": "^3.5.13", "vue-i18n": "9.9.1", "vue-router": "^4.5.0"}, "devDependencies": {"@commitlint/cli": "^19.6.1", "@commitlint/config-conventional": "^19.6.0", "@eslint/js": "^9.17.0", "@types/codemirror": "^5.60.15", "@types/lodash": "^4.17.13", "@types/node": "^22.10.2", "@types/nprogress": "^0.2.3", "@types/path-browserify": "^1.0.3", "@types/qs": "^6.9.17", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^8.18.1", "@typescript-eslint/parser": "^8.18.1", "@vant/auto-import-resolver": "^1.3.0", "@vitejs/plugin-vue": "^5.2.1", "autoprefixer": "^10.4.20", "commitizen": "^4.3.1", "cz-git": "1.9.4", "eslint": "^9.17.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.32.0", "globals": "^15.13.0", "husky": "^9.1.7", "lint-staged": "^15.2.11", "postcss": "^8.4.49", "postcss-html": "^1.7.0", "postcss-scss": "^4.0.9", "prettier": "^3.4.2", "sass": "^1.83.0", "stylelint": "^16.12.0", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^5.1.1", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^36.0.1", "terser": "^5.37.0", "typescript": "5.5.4", "typescript-eslint": "^8.18.1", "unocss": "0.65.1", "unplugin-auto-import": "^0.18.6", "unplugin-vue-components": "^0.27.5", "vite": "^6.0.1", "vite-plugin-mock-dev-server": "^1.8.3", "vite-plugin-svg-icons": "^2.0.1", "vue-eslint-parser": "^9.4.3", "vue-tsc": "^2.1.10"}, "engines": {"node": ">=18.0.0"}, "repository": "https://gitee.com/youlaiorg/vue3-element-admin.git", "author": "有来开源组织", "license": "MIT"}