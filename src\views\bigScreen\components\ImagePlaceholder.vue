<template>
  <div class="image-placeholder" :style="{ width: width, height: height }">
    <div class="placeholder-content">
      <div class="placeholder-icon">🖼️</div>
      <div class="placeholder-text">{{ text || '图片加载中...' }}</div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  width: {
    type: String,
    default: '100%'
  },
  height: {
    type: String,
    default: '100%'
  },
  text: {
    type: String,
    default: ''
  }
})
</script>

<style lang="scss" scoped>
.image-placeholder {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(0, 255, 136, 0.1) 100%);
  border: 2px dashed rgba(0, 212, 255, 0.3);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .placeholder-content {
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
    
    .placeholder-icon {
      font-size: 2em;
      margin-bottom: 8px;
      opacity: 0.7;
    }
    
    .placeholder-text {
      font-size: 12px;
      font-weight: 500;
    }
  }
}
</style>
