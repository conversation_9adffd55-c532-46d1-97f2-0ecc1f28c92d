html,
body,
header {
  background-color: #252423;
}

body {
  position: absolute;
  width: 100%;
  height: 100%;
  color: white;
  font-variant-numeric: tabular-nums;
  -webkit-font-smoothing: antialiased;
  overflow-x: hidden;
}

.content {
  overflow: hidden;
  opacity: 0;
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 100%;
  transition: opacity 1s ease;
}

.ready .content {
  opacity: 1;
}

/* Headers */

.header {
  position: absolute;
  z-index: 1;
  top: 0;
  width: 100%;
}

.logo {
  display: flex;
  justify-content: space-between;
  opacity: .8;
  width: 100%;
  height: 20px;
  text-decoration: none;
  color: currentColor;
}

.logo:visited {
  color: currentColor;
}

.logo svg {
  width: 100%;
  height: 100%;
}

.logo:hover {
  opacity: 1;
}

.version-number {
  opacity: .6;
  color: currentColor;
}

header {
  position: sticky;
  top: 0;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 60px;
  padding-left: 20px;
  padding-right: 20px;
  border-bottom: 1px solid rgba(0,0,0,.65);
}

h2 {
  font-size: 16px;
  letter-spacing: 1px;
  text-transform: uppercase;
  font-weight: 200;
}

/* Panes */

.pane {
  position: relative;
  height: 100%;
}

/* Sidebar */

.sidebar {
  display: flex;
  flex-direction: column;
  width: 240px;
  padding-top: 60px;
  border-right: 1px solid rgba(0,0,0,.65);
}

.sidebar-info {
  display: flex;
  justify-content: space-between;
  padding: 20px;
}

.navigation {
  overflow-y: auto;
  width: 100%;
  height: 100%;
  padding: 10px 0 20px 20px;
}

.navigation ul {
  height: 30px;
  overflow-y: hidden;
  -webkit-transition:opacity .4s ease;
  transition: opacity .4s ease;
}

.navigation ul li {
  position: relative;
}

.navigation ul li:before {
  content: "";
  display: block;
  position: absolute;
  top: 4px;
  left: 2px;
  width: 1px;
  height: calc(100% - 8px);
  background-color: white;
}

.navigation ul li:first-child:before {
  display: none;
}

.navigation a {
  display: block;
  padding-left: 10px;
  color: white;
  text-decoration: none;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: .5px;
  text-transform: uppercase;
}

.navigation ul li:first-child a {
  padding-left: 0;
  padding-top: 10px;
  padding-bottom: 10px;
}

.navigation ul li a,
.navigation ul li:before {
  opacity: .4;
}

.navigation ul li a {
  transition: opacity .4s ease, color .4s ease;
}

.navigation ul.active a {
  opacity: .7;
}

.navigation ul li:hover a,
.navigation ul li:hover:before {
  transition: none;
  opacity: 1;
}

.navigation ul li.active a,
.navigation ul li.active:before {
  opacity: 1;
  color: currentColor;
}

.navigation ul li.active:before {
  background-color: currentColor;
}

.navigation ul.active li:first-child a,
.navigation ul.active li:first-child a:hover {
  opacity: 1;
  color: currentColor;
}

/* Info pane */

.demo-info {
  flex-direction: column;
  font-size: 16px;
  line-height: 22px;
}

.info-output {
  overflow-x: hidden;
  overflow-y: auto;
  position: relative;
  top: 0;
  width: 100%;
  height: calc(100% - 60px);
  padding: 20px;
}

.demo-info pre {
  font-variant-ligatures: none;
  -webkit-font-variant-ligatures: none;
}

.demo-info code {
  font-family: "Input Mono Reg", monospace;
  font-size: 14px;
  line-height: 22px;
  white-space: pre-wrap;
}

.demo-info p {
  margin-bottom: 20px;
}

.demo-info p,
.demo-info td {
  color: #F6F4F2;
}

.demo-info i {
  font-style: italic;
}

.demo-info a {
  text-decoration: none;
  border-bottom: 1px dotted currentColor;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.demo-info a:hover {
  border-bottom: 1px solid currentColor;
}

.demo-info a[href^="http"],
.demo-info a[href^="http"]:visited {
  color: #F6F4F2;
}

.demo-info p.bubble {
  display: inline-block;
  position: relative;
  padding: 8px 12px;
}

.demo-info p.bubble.warning {
  color: #FBF38C;
}

.demo-info p.bubble.info {
  color: #61C3FF;
}

.demo-info p.bubble:after {
  content: "";
  pointer-events: none;
  color: currentColor;
  display: inline-block;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: currentColor;
  border-radius: 3px;
  opacity: .05;
}

.demo-info code.hljs {
  display: inline-block;
}

.demo-info pre {
  margin-bottom: 20px;
}

.demo-info pre code {
  position: relative;
  display: inline-block;
  width: 100%;
  padding: 8px 12px;
  background-color: rgba(0,0,0,.2);
  border: 1px solid rgba(0,0,0,.2);
  border-radius: 3px;
}

.demo-info p code {
  position: relative;
  padding: 0 2px;
  height: 20px;
  background-color: rgba(0,0,0,.2);
  box-shadow: inset 0 0 1px rgba(0,0,0,.4);
  border-radius: 3px;
}

.demo-info table {
  margin-bottom: 20px;
  border: 1px solid rgba(255,255,255,.15);
  border-spacing: 0;
  border-radius: 3px;
}

.demo-info td {
  vertical-align: middle;
  padding: 8px 12px;
  border-left: 1px solid rgba(255,255,255,.15);
  border-top: 1px solid rgba(255,255,255,.15);
}

.demo-info thead td {
  text-transform: uppercase;
  font-size: 16px;
  line-height: 22px;
  letter-spacing: 1px;
  color: rgba(255,255,255,.4);
  border-top: none;
}

.demo-info td:first-child {
  border-left: none;
}

.code-preview {
  width: 100%;
  padding-top: 40px;
}

.code-preview h2 {
  margin-bottom: 20px;
}

/* Demos */

.demos {
  overflow-y: scroll;
  width: calc(50% - 240px);
  min-width: 330px;
}

article {
  width: 100%;
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: flex-start;
}

.demo {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-height: 200px;
  padding-top: 35px;
  border-bottom: 1px solid rgba(0,0,0,.65);
  cursor: pointer;
}

.demo-description {
  display: none;
}

.demo.controls {
  padding-top: 50px;
}

.demo.active.controls {
  cursor: default;
}

.demo:before {
  content: "";
  position: absolute;
  z-index: 0;
  top: 0;
  left: 0;
  opacity: 0;
  display: block;
  width: 100%;
  height: 100%;
  background-color: currentColor;
}

.demo.active:before {
  opacity: .05;
}

.demo-content {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  width: 290px;
  height: 100%;
}

.demo-title {
  position: absolute;
  top: 0;
  left: 20px;
  width: calc(100% - 40px);
  padding: 23px 0;
  font-size: 14px;
  letter-spacing: 2px;
  text-transform: uppercase;
  color: #F6F4F2;
  opacity: .9;
}

.active .demo-title {
  color: currentColor;
  opacity: 1;
}

.line {
  width: 100%;
  padding: 1px 0px;
}

.line.align-center {
  display: flex;
  justify-content: center;
}

.grid {
  display: flex;
  flex-wrap: wrap;
}

.controls {
  opacity: .4;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 20px;
  padding-bottom: 10px;
}

.controls button {
  position: relative;
  display: block;
  margin: 0 0 0 -1px;
  padding: 5px 10px;
  font-size: 14px;
  text-transform: uppercase;
  border: 1px solid currentColor;
  background-color: transparent;
  color: currentColor;
}

.controls button:focus {
  outline: none;
}

.active .controls {
  opacity: 1;
}

.active .controls button:after {
  content: '';
  opacity: 0;
  position: absolute;
  left: 0;
  top: 0;
  display: block;
  width: 100%;
  height: 100%;
  background-color: currentColor;
}

.active .controls button:hover:after {
  opacity: .2;
}

.controls button.is-active:after,
.controls button.is-active:hover:after {
  opacity: .4;
}

.controls button:first-child {
  margin-left: 0;
}

.controls input {
  margin-left: 10px;
}

.text-output {
  padding-top: 10px;
  padding-bottom: 10px;
  font-size: 16px;
  color: currentColor;
  text-align: center;
  background-color: transparent;
  border: 1px solid currentColor;
}

.log {
  width: 100%;
  height: 100%;
  background: transparent;
  color: currentColor;
  border: none;
  font-size: 16px;
  text-align: center;
}

.bottom.log {
  padding-top: 10px;
  padding-bottom: 10px;
}

.logs {
  pointer-events: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: absolute;
  width: 100%;
  height: auto;
}

.log:focus {
  outline: none;
}

input.progress {
  cursor: ew-resize;
}

input.progress[value] {
  position: relative;
  width: 100%;
  height: 27px;
  -webkit-appearance: none;
  background-color: transparent;
  color: currentColor;
}

input.progress[value]:focus {
  outline: none;
}

input.progress[value]:after {
  content: "";
  position: absolute;
  top: 13px;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: currentColor;
}

input.progress[value]::-webkit-slider-thumb {
  -webkit-appearance: none;
  background-color: currentColor;
  border-radius: 50%;
  width: 16px;
  height: 16px;
}

.square,
.circle {
  pointer-events: none;
  position: relative;
  width: 28px;
  height: 28px;
  margin: 1px;
  background-color: currentColor;
  font-size: 14px;
}

.circle {
  border-radius: 50%;
}

.square.outline,
.circle.outline {
  background-color: transparent;
  border: 1px solid currentColor;
}

.triangle {
  pointer-events: none;
  position: relative;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 14px 24px 14px;
  border-color: transparent transparent currentColor transparent;
}

.small {
  width: 18px;
  height: 18px;
}

.small.triangle {
  border-width: 0 10px 17px 10px;
}

.large {
  width: 48px;
  height: 48px;
}

.stretched {
  height: 2px;
}

.shadow {
  position: absolute;
  opacity: .2;
}

.label {
  position: absolute;
  opacity: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: left;
  width: 100%;
  height: auto;
  padding-left: 38px;
  transition: opacity .5s ease;
}

.demo.active .label {
  opacity: .2;
}

.small.label {
  height: 18px;
  padding-left: 28px;
}

.large.label {
  height: 48px;
  padding-left: 58px;
}

.motion-path {
  position: relative;
  width: 256px;
  margin: auto;
}

.follow-path {
  position: absolute;
  top: -9px;
  left: -9px;
}

.align-center {
  align-items: center;
}

.add-states-demo .el {
  border: 1px solid rgba(0,0,0,0);
}

#cssProperties .el {
  transform: translate3d(0,0,0);
}

/* Responsive */

.sidebar,
.demo-info {
  display: none;
}

.demos {
  top: 60px;
  width: 100%;
  min-width: auto;
  height: calc(100% - 60px);
  -webkit-overflow-scrolling: touch;
}

@media (min-width: 800px) {
  .header {
    width: 239px;
  }
  .demo-info {
    display: inherit;
    width: calc(100% - 380px);
  }
  .demos {
    top: 0;
    width: 380px;
    height: 100%;
  }
  .demos::-webkit-scrollbar {
    width: 1px;
    background-color: transparent;
  }

  .demos::-webkit-scrollbar-track {
    background-color: rgba(0,0,0,.65);
  }

  .demos::-webkit-scrollbar-thumb {
    background-color: rgba(255,255,255,.3);
  }

  .demos::-webkit-scrollbar-thumb:hover {
    background-color: rgba(255,255,255,.6);
  }
}

@media (min-width: 1100px) {
  .sidebar {
    display: inherit;
  }
  .demos {
    width: calc(50% - 240px);
  }
  .demo-info {
    width: 50%;
  }
}
