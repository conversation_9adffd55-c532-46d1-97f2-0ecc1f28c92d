// 自适应样式文件

// 响应式断点
$mobile-breakpoint: 992px;
$tablet-breakpoint: 768px;

// 自适应混入
@mixin mobile-only {
  @media screen and (max-width: #{$mobile-breakpoint - 1px}) {
    @content;
  }
}

@mixin tablet-only {
  @media screen and (min-width: #{$tablet-breakpoint}) and (max-width: #{$mobile-breakpoint - 1px}) {
    @content;
  }
}

@mixin desktop-only {
  @media screen and (min-width: #{$mobile-breakpoint}) {
    @content;
  }
}

@mixin tablet-and-up {
  @media screen and (min-width: #{$tablet-breakpoint}) {
    @content;
  }
}

@mixin mobile-and-tablet {
  @media screen and (max-width: #{$mobile-breakpoint - 1px}) {
    @content;
  }
}

// 自适应容器
.adaptive-container {
  width: 100%;
  
  @include mobile-only {
    padding: 12px;
  }
  
  @include desktop-only {
    padding: 24px;
  }
}

// 自适应间距
.adaptive-spacing {
  @include mobile-only {
    margin-bottom: 16px;
  }
  
  @include desktop-only {
    margin-bottom: 24px;
  }
}

// 自适应按钮
.adaptive-button {
  @include mobile-only {
    width: 100%;
    height: 44px;
    font-size: 16px;
    border-radius: 6px;
  }
  
  @include desktop-only {
    height: 32px;
    font-size: 14px;
    border-radius: 4px;
  }
}

// 自适应表单
.adaptive-form {
  @include mobile-only {
    .form-item {
      margin-bottom: 16px;
    }
    
    .form-label {
      font-size: 14px;
      margin-bottom: 8px;
    }
    
    .form-input {
      height: 44px;
      font-size: 16px;
      border-radius: 6px;
    }
  }
  
  @include desktop-only {
    .form-item {
      margin-bottom: 24px;
    }
    
    .form-label {
      font-size: 14px;
      margin-bottom: 8px;
    }
    
    .form-input {
      height: 32px;
      font-size: 14px;
      border-radius: 4px;
    }
  }
}

// 自适应卡片
.adaptive-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  @include mobile-only {
    margin: 8px;
    padding: 16px;
  }
  
  @include desktop-only {
    margin: 16px;
    padding: 24px;
  }
}

// 自适应网格
.adaptive-grid {
  display: grid;
  gap: 16px;
  
  @include mobile-only {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  @include tablet-only {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @include desktop-only {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
  }
}

// 平台特定显示控制
.mobile-only {
  @include tablet-and-up {
    display: none !important;
  }
}

.desktop-only {
  @include mobile-and-tablet {
    display: none !important;
  }
}

.tablet-only {
  @include mobile-only {
    display: none !important;
  }
  
  @include desktop-only {
    display: none !important;
  }
}

// 自适应文字大小
.adaptive-text {
  &--small {
    @include mobile-only {
      font-size: 12px;
    }
    
    @include desktop-only {
      font-size: 12px;
    }
  }
  
  &--normal {
    @include mobile-only {
      font-size: 14px;
    }
    
    @include desktop-only {
      font-size: 14px;
    }
  }
  
  &--large {
    @include mobile-only {
      font-size: 16px;
    }
    
    @include desktop-only {
      font-size: 16px;
    }
  }
  
  &--title {
    @include mobile-only {
      font-size: 18px;
    }
    
    @include desktop-only {
      font-size: 20px;
    }
  }
}

// 自适应表格
.adaptive-table {
  @include mobile-only {
    // 移动端隐藏表格，显示列表
    .el-table {
      display: none;
    }
    
    .mobile-list {
      display: block;
    }
  }
  
  @include desktop-only {
    // 桌面端显示表格，隐藏列表
    .el-table {
      display: table;
    }
    
    .mobile-list {
      display: none;
    }
  }
}

// 自适应导航
.adaptive-nav {
  @include mobile-only {
    // 移动端底部导航
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 50px;
    background: white;
    border-top: 1px solid #eee;
    z-index: 1000;
  }
  
  @include desktop-only {
    // 桌面端侧边导航
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    width: 200px;
    background: white;
    border-right: 1px solid #eee;
    z-index: 1000;
  }
}

// 自适应模态框
.adaptive-modal {
  @include mobile-only {
    // 移动端全屏模态框
    .el-dialog {
      width: 100vw !important;
      height: 100vh !important;
      margin: 0 !important;
      border-radius: 0 !important;
    }
  }
  
  @include desktop-only {
    // 桌面端居中模态框
    .el-dialog {
      width: 600px;
      margin: 15vh auto;
      border-radius: 8px;
    }
  }
}

// 触摸优化
@include mobile-only {
  // 移动端触摸优化
  * {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
  }
  
  // 可点击元素最小尺寸
  button,
  .clickable {
    min-height: 44px;
    min-width: 44px;
  }
  
  // 滚动优化
  .scrollable {
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
  }
}

// 安全区域适配
@supports (padding: max(0px)) {
  .safe-area-inset {
    padding-left: max(16px, env(safe-area-inset-left));
    padding-right: max(16px, env(safe-area-inset-right));
  }
  
  .safe-area-inset-top {
    padding-top: max(16px, env(safe-area-inset-top));
  }
  
  .safe-area-inset-bottom {
    padding-bottom: max(16px, env(safe-area-inset-bottom));
  }
}
