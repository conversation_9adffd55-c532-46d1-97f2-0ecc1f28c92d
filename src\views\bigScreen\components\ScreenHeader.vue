<template>
  <div class="header">
    <!-- 左侧装饰 -->
    <div class="header-left">
      <div class="left-decoration"></div>
    </div>

    <!-- 中央标题区域 -->
    <div class="header-center">
      <div class="title-container">
        <div class="title-content">
          <h1 class="main-title">资产智能驾驶舱</h1>
        </div>
      </div>
    </div>

    <!-- 右侧信息区域 -->
    <div class="header-right">
      <div class="right-decoration"></div>
      <div class="datetime-info">
        <div class="date-time">
          <div class="time">{{ currentTime }}</div>
          <div class="date">{{ currentDate }} {{ currentWeek }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";

const currentDate = ref("");
const currentTime = ref("");
const currentWeek = ref("");

// 更新时间
const updateTime = () => {
  const now = new Date();

  // 格式化日期
  currentDate.value = now
    .toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
    })
    .replace(/\//g, ".");

  // 格式化时间
  currentTime.value = now.toLocaleTimeString("zh-CN", {
    hour12: false,
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
  });

  // 格式化星期
  const weekdays = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];
  currentWeek.value = weekdays[now.getDay()];
};

let timeInterval = null;

onMounted(() => {
  updateTime();
  timeInterval = setInterval(updateTime, 1000);
});

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval);
  }
});
</script>

<style lang="scss" scoped>
.header {
  position: relative;
  height: 10vh;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 2vw;
  z-index: 10000;
  background: url("@/assets/images/dashboard/title1.png") no-repeat center center;

  .header-left {
    flex: 1;
    display: flex;
    align-items: center;

    .left-decoration {
      img {
        height: 8vh;
        width: auto;
      }
    }
  }

  .header-center {
    flex: 2;
    display: flex;
    justify-content: center;
    align-items: center;

    .title-container {
      position: relative;
      text-align: center;

      .title-bg {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1;

        img {
          height: 8vh;
          width: auto;
        }
      }

      .title-content {
        position: relative;
        z-index: 2;
        padding: 1vh 2vw;

        .main-title {
          margin: 0;
          font-size: 2.5vw;
          font-weight: 700;
          color: #ffffff;
          text-shadow: 0 0 20px rgba(0, 212, 255, 0.8);
          letter-spacing: 0.2vw;
          margin-bottom: 0.5vh;
        }

        .subtitle {
          font-size: 0.8vw;
          color: rgba(255, 255, 255, 0.8);
          letter-spacing: 0.1vw;
          text-transform: uppercase;
        }
      }
    }
  }

  .header-right {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 2vw;

    .right-decoration {
      img {
        height: 8vh;
        width: auto;
      }
    }

    .datetime-info {
      .date-time {
        text-align: right;

        .time {
          font-size: 1.8vw;
          font-weight: 700;
          color: #00d4ff;
          font-family: "electronicFont", "Courier New", monospace;
          text-shadow: 0 0 10px rgba(0, 212, 255, 0.8);
          margin-bottom: 0.5vh;
        }

        .date {
          font-size: 1vw;
          color: rgba(255, 255, 255, 0.9);
          font-weight: 500;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .header {
    .header-center {
      .title-container {
        .title-content {
          .main-title {
            font-size: 3vw;
          }

          .subtitle {
            font-size: 1vw;
          }
        }
      }
    }

    .header-right {
      .datetime-info {
        .date-time {
          .time {
            font-size: 2.2vw;
          }

          .date {
            font-size: 1.2vw;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .header {
    height: 8vh;
    padding: 0 4vw;

    .header-left,
    .header-right {
      .left-decoration,
      .right-decoration {
        display: none;
      }
    }

    .header-center {
      .title-container {
        .title-content {
          .main-title {
            font-size: 4vw;
          }

          .subtitle {
            font-size: 1.5vw;
          }
        }
      }
    }

    .header-right {
      .datetime-info {
        .date-time {
          .time {
            font-size: 3vw;
          }

          .date {
            font-size: 1.8vw;
          }
        }
      }
    }
  }
}
</style>
