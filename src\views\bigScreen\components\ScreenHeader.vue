<template>
  <header class="screen-header">
    <!-- 左侧Logo区域 -->
    <div class="header-left">
      <div class="logo">
        <div class="logo-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M9 22V12H15V22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <div class="logo-text">
          <div class="company-name">资产管理系统</div>
          <div class="system-version">V2.0</div>
        </div>
      </div>
    </div>
    
    <!-- 中央标题区域 -->
    <div class="header-center">
      <h1 class="main-title">
        <span class="title-text">资产智能驾驶舱</span>
        <div class="title-decoration">
          <div class="decoration-line"></div>
          <div class="decoration-dot"></div>
          <div class="decoration-line"></div>
        </div>
      </h1>
      <div class="subtitle">Asset Management Dashboard</div>
    </div>
    
    <!-- 右侧信息区域 -->
    <div class="header-right">
      <div class="system-info">
        <div class="weather-info">
          <div class="weather-icon">🌤️</div>
          <div class="weather-text">
            <div class="temperature">22°C</div>
            <div class="weather-desc">晴朗</div>
          </div>
        </div>
        <div class="datetime">
          <div class="date">{{ currentDate }}</div>
          <div class="time">{{ currentTime }}</div>
          <div class="week">{{ currentWeek }}</div>
        </div>
      </div>
    </div>
    
    <!-- 装饰元素 -->
    <div class="header-decoration">
      <div class="corner-decoration corner-tl"></div>
      <div class="corner-decoration corner-tr"></div>
      <div class="corner-decoration corner-bl"></div>
      <div class="corner-decoration corner-br"></div>
    </div>
  </header>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

const currentDate = ref('')
const currentTime = ref('')
const currentWeek = ref('')

// 更新时间
const updateTime = () => {
  const now = new Date()
  
  // 格式化日期
  currentDate.value = now.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  }).replace(/\//g, '.')
  
  // 格式化时间
  currentTime.value = now.toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
  
  // 格式化星期
  const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
  currentWeek.value = weekdays[now.getDay()]
}

let timeInterval = null

onMounted(() => {
  updateTime()
  timeInterval = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style lang="scss" scoped>
.screen-header {
  position: relative;
  z-index: 20;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 40px;
  background: linear-gradient(135deg, 
    rgba(0, 212, 255, 0.1) 0%, 
    rgba(0, 255, 136, 0.08) 50%, 
    rgba(255, 184, 0, 0.1) 100%
  );
  border-bottom: 2px solid rgba(0, 212, 255, 0.3);
  backdrop-filter: blur(20px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  
  .header-left {
    flex: 1;
    
    .logo {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .logo-icon {
        width: 48px;
        height: 48px;
        background: linear-gradient(135deg, #00d4ff, #00ff88);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
        
        svg {
          width: 24px;
          height: 24px;
          color: #fff;
        }
      }
      
      .logo-text {
        .company-name {
          font-size: 18px;
          font-weight: 600;
          color: #fff;
          margin-bottom: 2px;
        }
        
        .system-version {
          font-size: 12px;
          color: rgba(0, 212, 255, 0.8);
          font-weight: 500;
        }
      }
    }
  }
  
  .header-center {
    flex: 2;
    text-align: center;
    
    .main-title {
      margin: 0;
      margin-bottom: 8px;
      
      .title-text {
        font-size: 36px;
        font-weight: 700;
        background: linear-gradient(90deg, #00d4ff 0%, #00ff88 50%, #ffb800 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
        letter-spacing: 2px;
      }
      
      .title-decoration {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        margin-top: 8px;
        
        .decoration-line {
          width: 60px;
          height: 2px;
          background: linear-gradient(90deg, transparent, #00d4ff, transparent);
        }
        
        .decoration-dot {
          width: 8px;
          height: 8px;
          background: #00d4ff;
          border-radius: 50%;
          box-shadow: 0 0 10px #00d4ff;
          animation: pulse 2s ease-in-out infinite;
        }
      }
    }
    
    .subtitle {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.7);
      letter-spacing: 3px;
      text-transform: uppercase;
      font-weight: 300;
    }
  }
  
  .header-right {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    
    .system-info {
      display: flex;
      align-items: center;
      gap: 20px;
      
      .weather-info {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 12px;
        background: rgba(0, 212, 255, 0.1);
        border-radius: 8px;
        border: 1px solid rgba(0, 212, 255, 0.2);
        
        .weather-icon {
          font-size: 20px;
        }
        
        .weather-text {
          .temperature {
            font-size: 14px;
            font-weight: 600;
            color: #00d4ff;
          }
          
          .weather-desc {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
          }
        }
      }
      
      .datetime {
        text-align: right;
        
        .date {
          font-size: 16px;
          font-weight: 500;
          color: #fff;
          margin-bottom: 2px;
        }
        
        .time {
          font-size: 20px;
          font-weight: 700;
          color: #00d4ff;
          font-family: 'Courier New', monospace;
          margin-bottom: 2px;
        }
        
        .week {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.6);
        }
      }
    }
  }
  
  .header-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    
    .corner-decoration {
      position: absolute;
      width: 20px;
      height: 20px;
      border: 2px solid #00d4ff;
      
      &.corner-tl {
        top: 10px;
        left: 10px;
        border-right: none;
        border-bottom: none;
      }
      
      &.corner-tr {
        top: 10px;
        right: 10px;
        border-left: none;
        border-bottom: none;
      }
      
      &.corner-bl {
        bottom: 10px;
        left: 10px;
        border-right: none;
        border-top: none;
      }
      
      &.corner-br {
        bottom: 10px;
        right: 10px;
        border-left: none;
        border-top: none;
      }
    }
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .screen-header {
    .header-center {
      .main-title .title-text {
        font-size: 28px;
      }
    }
    
    .header-right {
      .system-info {
        .weather-info {
          display: none;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .screen-header {
    height: 60px;
    padding: 0 20px;
    
    .header-left,
    .header-right {
      display: none;
    }
    
    .header-center {
      flex: 1;
      
      .main-title .title-text {
        font-size: 20px;
      }
      
      .subtitle {
        font-size: 12px;
      }
    }
  }
}
</style>
