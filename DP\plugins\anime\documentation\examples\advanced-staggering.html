<!DOCTYPE html>
<html>
<head>
  <title>advanced staggering • anime.js</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <meta property="og:title" content="anime.js">
  <meta property="og:url" content="https://animejs.com">
  <meta property="og:description" content="Javascript Animation Engine">
  <meta property="og:image" content="https://animejs.com/documentation/assets/img/icons/og.png">
  <meta name="twitter:card" content="summary">
  <meta name="twitter:title" content="anime.js">
  <meta name="twitter:site" content="@juliangarnier">
  <meta name="twitter:description" content="Javascript Animation Engine">
  <meta name="twitter:image" content="https://animejs.com/documentation/assets/img/icons/twitter.png">
  <link rel="apple-touch-icon-precomposed" href="../assets/img/social-media-image.png">
  <link rel="icon" type="image/png" href="../assets/img/favicon.png" >
  <link href="../assets/css/animejs.css" rel="stylesheet">
  <link href="../assets/css/documentation.css" rel="stylesheet">
  <style>

    :root {
      font-size: 24px;
    }

    body {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      align-items: center;
      height: 100vh;
    }

    .stagger-visualizer {
      position: relative;
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      align-items: center;
      width: 16rem;
      height: 16rem;
    }

    .stagger-visualizer .dot {
      position: relative;
      width: .25rem;
      height: .25rem;
      margin: .375rem;
      background-color: currentColor;
      border-radius: 50%;
    }

    .stagger-visualizer .cursor {
      position: absolute;
      z-index: 1;
      top: 0;
      left: 0;
      width: 1rem;
      height: 1rem;
      background-color: currentColor;
      border-radius: 50%;
    }

  </style>
</head>
<body>

  <div class="stagger-visualizer">
    <div class="cursor color-red"></div>
  </div>

</body>
<script type="module">

  import anime from '../../src/index.js';

  const staggerVisualizerEl = document.querySelector('.stagger-visualizer');
  const fragment = document.createDocumentFragment();
  const numberOfElements = 16*16;
  var animation;

  for (let i = 0; i < numberOfElements; i++) {
    const dotEl = document.createElement('div');
    dotEl.classList.add('dot');
    fragment.appendChild(dotEl);
  }

  staggerVisualizerEl.appendChild(fragment);

  let index = anime.random(0, numberOfElements);
  let nextIndex = 0;

  function animateGrid() {

    if (animation) animation.pause();

    nextIndex = anime.random(0, numberOfElements);

    anime.set('.cursor', {
      translateX: anime.stagger('-1rem', {grid: [16, 16], from: index, axis: 'x'}),
      translateY: anime.stagger('-1rem', {grid: [16, 16], from: index, axis: 'y'})
    });

    animation = anime.timeline({
      easing: 'easeInOutQuad',
      complete: animateGrid
    })
    .add({
      targets: '.cursor',
      keyframes: [
        { scale: .625 }, 
        { scale: 1.125 },
        { scale: 1 }
      ],
      duration: 600
    })
    .add({
      targets: '.dot',
      keyframes: [
        {
          translateX: anime.stagger('-.175rem', {grid: [16, 16], from: index, axis: 'x'}),
          translateY: anime.stagger('-.175rem', {grid: [16, 16], from: index, axis: 'y'}),
          duration: 200
        }, {
          translateX: anime.stagger('.125rem', {grid: [16, 16], from: index, axis: 'x'}),
          translateY: anime.stagger('.125rem', {grid: [16, 16], from: index, axis: 'y'}),
          scale: 2,
          duration: 500
        }, {
          translateX: 0,
          translateY: 0,
          scale: 1,
          duration: 600,
        }
      ],
      delay: anime.stagger(50, {grid: [16, 16], from: index})
    }, '-=600')
    .add({
      targets: '.cursor',
      translateX: { value: anime.stagger('-1rem', {grid: [16, 16], from: nextIndex, axis: 'x'}), duration: anime.random(400, 1200) },
      translateY: { value: anime.stagger('-1rem', {grid: [16, 16], from: nextIndex, axis: 'y'}), duration: anime.random(400, 1200) },
      easing: 'easeOutSine'
    }, '-=1100')

    index = nextIndex;

  }

  animateGrid();

</script>
</html>
