{"name": "animejs", "description": "JavaScript animation engine", "main": "anime.js", "repository": {"type": "git", "url": "https://github.com/juliangarnier/anime.git"}, "keywords": ["anime", "animation", "javascript", "CSS", "transforms", "SVG", "canvas"], "authors": ["<PERSON> <<EMAIL>>"], "license": "MIT", "homepage": "https://animejs.com/", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests"]}