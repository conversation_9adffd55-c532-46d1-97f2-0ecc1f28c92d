<template>
  <div class="chart-panel">
    <div class="panel-header">
      <div class="header-content">
        <div class="title-wrapper">
          <div class="title-icon">📊</div>
          <h3 class="title">资产分类统计</h3>
        </div>
        <div class="header-actions">
          <div class="refresh-btn" @click="refreshData">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M1 4V10H7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M23 20V14H17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10M23 14L18.36 18.36A9 9 0 0 1 3.51 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
        </div>
      </div>
      <div class="header-decoration"></div>
    </div>
    
    <div class="panel-content">
      <div ref="chartContainer" class="chart-container"></div>
      
      <!-- 数据统计 -->
      <div class="data-summary">
        <div class="summary-item" v-for="(item, index) in summaryData" :key="index">
          <div class="item-color" :style="{ backgroundColor: item.color }"></div>
          <div class="item-info">
            <div class="item-name">{{ item.name }}</div>
            <div class="item-value">{{ item.value }}</div>
          </div>
          <div class="item-percentage">{{ item.percentage }}%</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'

const chartContainer = ref(null)
let chartInstance = null

// 图表数据
const chartData = ref([
  { value: 1048, name: '房屋及构筑物', color: '#00d4ff' },
  { value: 735, name: '设备', color: '#00ff88' },
  { value: 580, name: '文物', color: '#ffb800' },
  { value: 484, name: '生物', color: '#ff6b6b' },
  { value: 300, name: '图书档案', color: '#8b5cf6' },
  { value: 300, name: '软件及信息系统', color: '#06d6a0' }
])

// 计算汇总数据
const summaryData = ref([])

const calculateSummary = () => {
  const total = chartData.value.reduce((sum, item) => sum + item.value, 0)
  summaryData.value = chartData.value.map(item => ({
    ...item,
    percentage: ((item.value / total) * 100).toFixed(1)
  })).slice(0, 4) // 只显示前4项
}

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return
  
  chartInstance = echarts.init(chartContainer.value)
  
  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0, 20, 40, 0.9)',
      borderColor: '#00d4ff',
      borderWidth: 1,
      textStyle: { 
        color: '#fff',
        fontSize: 12
      },
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      show: false
    },
    series: [
      {
        name: '资产分类',
        type: 'pie',
        radius: ['45%', '75%'],
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 8,
          borderColor: 'rgba(0, 20, 40, 0.8)',
          borderWidth: 2,
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.3)'
        },
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 14,
            fontWeight: 'bold',
            color: '#fff'
          },
          itemStyle: {
            shadowBlur: 20,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        data: chartData.value.map(item => ({
          ...item,
          itemStyle: { 
            color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [
              { offset: 0, color: item.color },
              { offset: 1, color: item.color + '80' }
            ])
          }
        })),
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: function (idx) {
          return Math.random() * 200
        }
      }
    ]
  }
  
  chartInstance.setOption(option)
  
  // 添加动画效果
  chartInstance.on('mouseover', function(params) {
    chartInstance.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
      dataIndex: params.dataIndex
    })
  })
  
  chartInstance.on('mouseout', function(params) {
    chartInstance.dispatchAction({
      type: 'downplay',
      seriesIndex: 0,
      dataIndex: params.dataIndex
    })
  })
}

// 刷新数据
const refreshData = () => {
  // 模拟数据刷新
  chartData.value = chartData.value.map(item => ({
    ...item,
    value: Math.floor(Math.random() * 500) + 300
  }))
  
  calculateSummary()
  
  if (chartInstance) {
    chartInstance.setOption({
      series: [{
        data: chartData.value.map(item => ({
          ...item,
          itemStyle: { 
            color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [
              { offset: 0, color: item.color },
              { offset: 1, color: item.color + '80' }
            ])
          }
        }))
      }]
    })
  }
}

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

onMounted(async () => {
  await nextTick()
  calculateSummary()
  initChart()
  
  window.addEventListener('dashboard-resize', handleResize)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('dashboard-resize', handleResize)
})
</script>

<style lang="scss" scoped>
.chart-panel {
  background: linear-gradient(135deg, 
    rgba(0, 212, 255, 0.05) 0%, 
    rgba(0, 255, 136, 0.03) 100%
  );
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  
  &:hover {
    border-color: rgba(0, 212, 255, 0.4);
    box-shadow: 0 12px 40px rgba(0, 212, 255, 0.2);
  }
  
  .panel-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid rgba(0, 212, 255, 0.1);
    position: relative;
    
    .header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .title-wrapper {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .title-icon {
          font-size: 20px;
          filter: drop-shadow(0 0 8px rgba(0, 212, 255, 0.6));
        }
        
        .title {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #fff;
          text-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
        }
      }
      
      .header-actions {
        .refresh-btn {
          width: 32px;
          height: 32px;
          border-radius: 8px;
          background: rgba(0, 212, 255, 0.1);
          border: 1px solid rgba(0, 212, 255, 0.2);
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.3s ease;
          
          svg {
            width: 16px;
            height: 16px;
            color: #00d4ff;
          }
          
          &:hover {
            background: rgba(0, 212, 255, 0.2);
            transform: rotate(180deg);
          }
        }
      }
    }
    
    .header-decoration {
      position: absolute;
      bottom: 0;
      left: 24px;
      width: 60px;
      height: 2px;
      background: linear-gradient(90deg, #00d4ff, #00ff88);
      border-radius: 1px;
    }
  }
  
  .panel-content {
    padding: 20px 24px 24px;
    
    .chart-container {
      width: 100%;
      height: 200px;
      margin-bottom: 20px;
    }
    
    .data-summary {
      .summary-item {
        display: flex;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        
        &:last-child {
          border-bottom: none;
        }
        
        .item-color {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          margin-right: 12px;
          box-shadow: 0 0 8px currentColor;
        }
        
        .item-info {
          flex: 1;
          
          .item-name {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 2px;
          }
          
          .item-value {
            font-size: 14px;
            font-weight: 600;
            color: #fff;
          }
        }
        
        .item-percentage {
          font-size: 12px;
          color: #00d4ff;
          font-weight: 500;
        }
      }
    }
  }
}
</style>
