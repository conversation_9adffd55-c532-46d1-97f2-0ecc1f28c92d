<template>
  <div class="adaptive-500">
    <!-- 移动端500页面 -->
    <div v-if="isMobile" class="mobile-500">
      <div class="mobile-500-content">
        <van-empty
          image="error"
          :image-size="120"
          description="服务器错误"
        >
          <template #description>
            <div class="error-description">
              <h3>抱歉，服务器出现了问题</h3>
              <p>请稍后再试，或联系技术支持</p>
              <p class="error-path">错误路径: {{ currentPath }}</p>
            </div>
          </template>
        </van-empty>
        
        <div class="mobile-500-actions">
          <van-button 
            type="primary" 
            block 
            @click="refreshPage"
          >
            刷新页面
          </van-button>
          
          <van-button 
            block 
            @click="goHome"
            style="margin-top: 12px;"
          >
            返回首页
          </van-button>
          
          <van-button 
            block 
            plain 
            @click="reportError"
            style="margin-top: 12px;"
          >
            反馈问题
          </van-button>
        </div>
      </div>
    </div>

    <!-- PC端500页面 -->
    <div v-else class="desktop-500">
      <div class="desktop-500-content">
        <el-result
          icon="error"
          title="500"
          sub-title="抱歉，服务器出现了内部错误"
        >
          <template #extra>
            <div class="error-info">
              <p class="error-path">错误路径: {{ currentPath }}</p>
              <p class="error-time">时间: {{ currentTime }}</p>
              <p class="error-id">错误ID: {{ errorId }}</p>
            </div>
            
            <div class="desktop-500-actions">
              <el-button type="primary" @click="refreshPage">
                刷新页面
              </el-button>
              <el-button @click="goHome">
                返回首页
              </el-button>
              <el-button type="warning" @click="reportError">
                反馈问题
              </el-button>
            </div>
          </template>
        </el-result>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useDevice } from '@/composables/useDevice';
import { useRouter, useRoute } from 'vue-router';

defineOptions({
  name: "Adaptive500",
});

const { isMobile } = useDevice();
const router = useRouter();
const route = useRoute();

// 当前路径和时间
const currentPath = ref(route.fullPath);
const currentTime = ref(new Date().toLocaleString('zh-CN'));
const errorId = ref(generateErrorId());

// 生成错误ID
function generateErrorId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// 刷新页面
const refreshPage = () => {
  window.location.reload();
};

// 返回首页
const goHome = () => {
  if (isMobile.value) {
    router.push('/mobile/dashboard').catch(() => {
      router.push('/');
    });
  } else {
    router.push('/dashboard').catch(() => {
      router.push('/');
    });
  }
};

// 反馈问题
const reportError = () => {
  const errorInfo = {
    path: currentPath.value,
    time: currentTime.value,
    errorId: errorId.value,
    device: isMobile.value ? 'mobile' : 'desktop',
    userAgent: navigator.userAgent
  };
  
  console.log('500错误反馈:', errorInfo);
  
  if (isMobile.value) {
    console.log('错误已反馈，我们会尽快处理！');
  } else {
    ElMessage.success('错误已反馈，我们会尽快处理！');
  }
};

// 监听路由变化，更新当前路径
watch(() => route.fullPath, (newPath) => {
  currentPath.value = newPath;
  currentTime.value = new Date().toLocaleString('zh-CN');
  errorId.value = generateErrorId();
});
</script>

<style lang="scss" scoped>
.adaptive-500 {
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--el-bg-color-page, #f5f5f5);

  .mobile-500 {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    background: white;

    .mobile-500-content {
      width: 100%;
      max-width: 400px;
      text-align: center;

      .error-description {
        h3 {
          font-size: 18px;
          color: #323233;
          margin-bottom: 8px;
        }

        p {
          font-size: 14px;
          color: #646566;
          margin-bottom: 4px;
          line-height: 1.5;
        }

        .error-path {
          font-size: 12px;
          color: #969799;
          word-break: break-all;
          background: #f7f8fa;
          padding: 8px;
          border-radius: 4px;
          margin-top: 12px;
        }
      }
    }

    .mobile-500-actions {
      width: 100%;
      margin-top: 32px;
    }
  }

  .desktop-500 {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    .desktop-500-content {
      max-width: 600px;
      width: 100%;
      text-align: center;

      .error-info {
        margin-bottom: 24px;
        padding: 16px;
        background: var(--el-fill-color-light);
        border-radius: 8px;
        border-left: 4px solid var(--el-color-danger);

        p {
          margin: 4px 0;
          font-size: 14px;
          color: var(--el-text-color-regular);
        }

        .error-path,
        .error-id {
          font-family: monospace;
          word-break: break-all;
          background: var(--el-fill-color-darker);
          padding: 8px;
          border-radius: 4px;
        }
      }

      .desktop-500-actions {
        display: flex;
        justify-content: center;
        gap: 16px;
        flex-wrap: wrap;
      }
    }
  }
}

// 确保在不同布局下都能正确显示
.layout-container .adaptive-500,
.mobile-layout .adaptive-500 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
}
</style>
