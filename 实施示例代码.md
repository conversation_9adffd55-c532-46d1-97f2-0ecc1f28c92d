# 移动端PC端整合方案 - 实施示例代码

## 1. 设备检测工具增强

```typescript
// src/utils/device.ts
import { DeviceEnum } from '@/enums/DeviceEnum';

export interface DeviceInfo {
  type: DeviceEnum;
  width: number;
  height: number;
  orientation: 'portrait' | 'landscape';
  userAgent: string;
  isTouch: boolean;
}

export class DeviceDetector {
  private static readonly MOBILE_BREAKPOINT = 992;
  private static readonly TABLET_BREAKPOINT = 768;

  /**
   * 获取完整设备信息
   */
  static getDeviceInfo(): DeviceInfo {
    const width = window.innerWidth;
    const height = window.innerHeight;
    
    return {
      type: width < this.MOBILE_BREAKPOINT ? DeviceEnum.MOBILE : DeviceEnum.DESKTOP,
      width,
      height,
      orientation: width > height ? 'landscape' : 'portrait',
      userAgent: navigator.userAgent.toLowerCase(),
      isTouch: 'ontouchstart' in window
    };
  }

  /**
   * 判断是否为移动设备
   */
  static isMobile(): boolean {
    return window.innerWidth < this.MOBILE_BREAKPOINT;
  }

  /**
   * 判断是否为平板设备
   */
  static isTablet(): boolean {
    const width = window.innerWidth;
    return width >= this.TABLET_BREAKPOINT && width < this.MOBILE_BREAKPOINT;
  }

  /**
   * 判断是否为桌面设备
   */
  static isDesktop(): boolean {
    return window.innerWidth >= this.MOBILE_BREAKPOINT;
  }

  /**
   * 监听设备变化
   */
  static onDeviceChange(callback: (deviceInfo: DeviceInfo) => void) {
    const handleResize = () => {
      callback(this.getDeviceInfo());
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleResize);

    // 返回清理函数
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleResize);
    };
  }
}
```

## 2. 自适应组合式API

```typescript
// src/composables/useDevice.ts
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { DeviceDetector, type DeviceInfo } from '@/utils/device';
import { useAppStore } from '@/store';

export function useDevice() {
  const appStore = useAppStore();
  const deviceInfo = ref<DeviceInfo>(DeviceDetector.getDeviceInfo());

  // 计算属性
  const isMobile = computed(() => deviceInfo.value.type === 'mobile');
  const isTablet = computed(() => DeviceDetector.isTablet());
  const isDesktop = computed(() => deviceInfo.value.type === 'desktop');
  const isLandscape = computed(() => deviceInfo.value.orientation === 'landscape');
  const isPortrait = computed(() => deviceInfo.value.orientation === 'portrait');

  // 更新设备信息
  const updateDeviceInfo = (newDeviceInfo: DeviceInfo) => {
    deviceInfo.value = newDeviceInfo;
    appStore.toggleDevice(newDeviceInfo.type);
  };

  // 生命周期
  let cleanup: (() => void) | null = null;

  onMounted(() => {
    cleanup = DeviceDetector.onDeviceChange(updateDeviceInfo);
    // 初始化设备状态
    appStore.toggleDevice(deviceInfo.value.type);
  });

  onUnmounted(() => {
    cleanup?.();
  });

  return {
    deviceInfo: readonly(deviceInfo),
    isMobile,
    isTablet,
    isDesktop,
    isLandscape,
    isPortrait,
    updateDeviceInfo
  };
}
```

## 3. 自适应布局容器

```vue
<!-- src/layout/AdaptiveLayout.vue -->
<template>
  <div class="adaptive-layout" :class="layoutClasses">
    <!-- 动态加载对应平台的布局组件 -->
    <component 
      :is="currentLayoutComponent" 
      v-bind="layoutProps"
      @layout-event="handleLayoutEvent"
    />
  </div>
</template>

<script setup lang="ts">
import { defineAsyncComponent } from 'vue';
import { useDevice } from '@/composables/useDevice';
import { useAppStore } from '@/store';

// 组合式API
const { isMobile, isTablet } = useDevice();
const appStore = useAppStore();

// 动态布局组件
const currentLayoutComponent = computed(() => {
  if (isMobile.value) {
    return defineAsyncComponent(() => import('./MobileLayout.vue'));
  } else {
    return defineAsyncComponent(() => import('./index.vue'));
  }
});

// 布局样式类
const layoutClasses = computed(() => ({
  'adaptive-layout--mobile': isMobile.value,
  'adaptive-layout--tablet': isTablet.value,
  'adaptive-layout--desktop': !isMobile.value && !isTablet.value,
  'adaptive-layout--sidebar-open': appStore.sidebar.opened
}));

// 传递给子布局的属性
const layoutProps = computed(() => {
  const baseProps = {
    showHeader: true,
    showFooter: true
  };

  if (isMobile.value) {
    return {
      ...baseProps,
      showSidebar: false,
      showTabbar: true,
      showNavbar: true
    };
  } else {
    return {
      ...baseProps,
      showSidebar: true,
      showTabbar: false,
      showNavbar: false
    };
  }
});

// 处理布局事件
const handleLayoutEvent = (event: any) => {
  console.log('Layout event:', event);
  // 可以在这里处理来自子布局的事件
};
</script>

<style lang="scss" scoped>
.adaptive-layout {
  width: 100%;
  height: 100vh;
  overflow: hidden;

  &--mobile {
    // 移动端特定样式
    .mobile-specific {
      display: block;
    }
    .desktop-specific {
      display: none;
    }
  }

  &--desktop {
    // 桌面端特定样式
    .mobile-specific {
      display: none;
    }
    .desktop-specific {
      display: block;
    }
  }

  &--tablet {
    // 平板特定样式
    font-size: 16px;
  }
}
</style>
```

## 4. 自适应表格组件

```vue
<!-- src/components/adaptive/AdaptiveTable.vue -->
<template>
  <div class="adaptive-table">
    <!-- PC端表格 -->
    <el-table
      v-if="!isMobile"
      :data="tableData"
      v-bind="tableConfig"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
    >
      <el-table-column
        v-if="showSelection"
        type="selection"
        width="55"
      />
      <el-table-column
        v-for="column in adaptedColumns"
        :key="column.prop"
        v-bind="column"
      >
        <template v-if="column.slot" #default="scope">
          <slot :name="column.slot" :row="scope.row" :column="column" :index="scope.$index" />
        </template>
      </el-table-column>
    </el-table>

    <!-- 移动端列表 -->
    <div v-else class="mobile-table">
      <!-- 搜索栏 -->
      <van-search
        v-if="showSearch"
        v-model="searchValue"
        placeholder="请输入搜索关键词"
        @search="handleSearch"
      />

      <!-- 列表内容 -->
      <van-list
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="handleLoad"
      >
        <van-cell
          v-for="(item, index) in tableData"
          :key="getRowKey(item, index)"
          :title="getMobileTitle(item)"
          :value="getMobileValue(item)"
          :label="getMobileLabel(item)"
          is-link
          @click="handleRowClick(item, index)"
        >
          <template #right-icon>
            <van-icon name="arrow" />
          </template>
        </van-cell>
      </van-list>

      <!-- 空状态 -->
      <van-empty
        v-if="!loading && tableData.length === 0"
        description="暂无数据"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useDevice } from '@/composables/useDevice';

// 接口定义
interface TableColumn {
  prop: string;
  label: string;
  width?: string | number;
  minWidth?: string | number;
  sortable?: boolean;
  slot?: string;
  mobileDisplay?: 'title' | 'value' | 'label' | 'hidden';
}

interface AdaptiveTableProps {
  data: any[];
  columns: TableColumn[];
  showSelection?: boolean;
  showSearch?: boolean;
  loading?: boolean;
  finished?: boolean;
  rowKey?: string | ((row: any, index: number) => string);
  mobileConfig?: {
    titleField?: string;
    valueField?: string;
    labelField?: string;
  };
}

// Props
const props = withDefaults(defineProps<AdaptiveTableProps>(), {
  showSelection: false,
  showSearch: false,
  loading: false,
  finished: false,
  rowKey: 'id'
});

// Emits
const emit = defineEmits<{
  'selection-change': [selection: any[]];
  'sort-change': [sortInfo: any];
  'row-click': [row: any, index: number];
  'search': [value: string];
  'load': [];
}>();

// 组合式API
const { isMobile } = useDevice();

// 响应式数据
const searchValue = ref('');
const tableData = toRef(props, 'data');

// 计算属性
const adaptedColumns = computed(() => {
  return props.columns.filter(column => {
    // 移动端隐藏某些列
    if (isMobile.value && column.mobileDisplay === 'hidden') {
      return false;
    }
    return true;
  });
});

const tableConfig = computed(() => ({
  stripe: true,
  border: true,
  size: 'default',
  'highlight-current-row': true
}));

// 移动端显示逻辑
const getMobileTitle = (row: any) => {
  const titleColumn = props.columns.find(col => col.mobileDisplay === 'title');
  return titleColumn ? row[titleColumn.prop] : row[props.mobileConfig?.titleField || 'name'];
};

const getMobileValue = (row: any) => {
  const valueColumn = props.columns.find(col => col.mobileDisplay === 'value');
  return valueColumn ? row[valueColumn.prop] : row[props.mobileConfig?.valueField || 'status'];
};

const getMobileLabel = (row: any) => {
  const labelColumn = props.columns.find(col => col.mobileDisplay === 'label');
  return labelColumn ? row[labelColumn.prop] : row[props.mobileConfig?.labelField || 'description'];
};

const getRowKey = (row: any, index: number) => {
  if (typeof props.rowKey === 'function') {
    return props.rowKey(row, index);
  }
  return row[props.rowKey] || index;
};

// 事件处理
const handleSelectionChange = (selection: any[]) => {
  emit('selection-change', selection);
};

const handleSortChange = (sortInfo: any) => {
  emit('sort-change', sortInfo);
};

const handleRowClick = (row: any, index: number) => {
  emit('row-click', row, index);
};

const handleSearch = (value: string) => {
  emit('search', value);
};

const handleLoad = () => {
  emit('load');
};
</script>

<style lang="scss" scoped>
.adaptive-table {
  width: 100%;

  .mobile-table {
    .van-search {
      padding: 12px 16px;
      background: #f8f9fa;
    }

    .van-cell {
      padding: 12px 16px;
      
      &:not(:last-child) {
        border-bottom: 1px solid #ebedf0;
      }
    }
  }
}
</style>
```

## 5. 自适应表单组件

```vue
<!-- src/components/adaptive/AdaptiveForm.vue -->
<template>
  <div class="adaptive-form">
    <!-- PC端表单 -->
    <el-form
      v-if="!isMobile"
      ref="formRef"
      :model="formData"
      :rules="formRules"
      v-bind="formConfig"
    >
      <el-row :gutter="20">
        <el-col
          v-for="field in adaptedFields"
          :key="field.prop"
          :span="field.span || 24"
        >
          <el-form-item
            :label="field.label"
            :prop="field.prop"
            :required="field.required"
          >
            <component
              :is="getFieldComponent(field)"
              v-model="formData[field.prop]"
              v-bind="getFieldProps(field)"
              @change="handleFieldChange(field.prop, $event)"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <!-- 移动端表单 -->
    <van-form v-else ref="mobileFormRef" @submit="handleSubmit">
      <van-cell-group inset>
        <template v-for="field in adaptedFields" :key="field.prop">
          <van-field
            v-if="field.type === 'input'"
            v-model="formData[field.prop]"
            :name="field.prop"
            :label="field.label"
            :placeholder="field.placeholder"
            :required="field.required"
            :rules="getMobileRules(field)"
            @change="handleFieldChange(field.prop, $event)"
          />
          
          <van-field
            v-else-if="field.type === 'select'"
            v-model="formData[field.prop]"
            :name="field.prop"
            :label="field.label"
            :placeholder="field.placeholder"
            :required="field.required"
            readonly
            is-link
            @click="showPicker(field)"
          />
          
          <van-field
            v-else-if="field.type === 'date'"
            v-model="formData[field.prop]"
            :name="field.prop"
            :label="field.label"
            :placeholder="field.placeholder"
            :required="field.required"
            readonly
            is-link
            @click="showDatePicker(field)"
          />
        </template>
      </van-cell-group>
    </van-form>

    <!-- 移动端选择器 -->
    <van-popup v-model:show="showPickerPopup" position="bottom">
      <van-picker
        :columns="pickerColumns"
        @confirm="onPickerConfirm"
        @cancel="showPickerPopup = false"
      />
    </van-popup>

    <!-- 移动端日期选择器 -->
    <van-popup v-model:show="showDatePickerPopup" position="bottom">
      <van-date-picker
        v-model="currentDate"
        @confirm="onDateConfirm"
        @cancel="showDatePickerPopup = false"
      />
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { useDevice } from '@/composables/useDevice';

// 接口定义
interface FormField {
  prop: string;
  label: string;
  type: 'input' | 'select' | 'date' | 'textarea' | 'number';
  required?: boolean;
  placeholder?: string;
  span?: number;
  options?: Array<{ label: string; value: any }>;
  rules?: any[];
  mobileProps?: Record<string, any>;
  desktopProps?: Record<string, any>;
}

interface AdaptiveFormProps {
  modelValue: Record<string, any>;
  fields: FormField[];
  labelWidth?: string;
  size?: 'large' | 'default' | 'small';
}

// Props & Emits
const props = withDefaults(defineProps<AdaptiveFormProps>(), {
  labelWidth: '100px',
  size: 'default'
});

const emit = defineEmits<{
  'update:modelValue': [value: Record<string, any>];
  'field-change': [prop: string, value: any];
  'submit': [formData: Record<string, any>];
}>();

// 组合式API
const { isMobile } = useDevice();

// 响应式数据
const formRef = ref();
const mobileFormRef = ref();
const formData = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 选择器相关
const showPickerPopup = ref(false);
const showDatePickerPopup = ref(false);
const pickerColumns = ref([]);
const currentPickerField = ref<FormField | null>(null);
const currentDate = ref(new Date());

// 计算属性
const adaptedFields = computed(() => {
  return props.fields.filter(field => {
    // 可以根据设备类型过滤字段
    return true;
  });
});

const formConfig = computed(() => ({
  'label-width': props.labelWidth,
  size: props.size,
  'label-position': isMobile.value ? 'top' : 'right'
}));

const formRules = computed(() => {
  const rules: Record<string, any[]> = {};
  props.fields.forEach(field => {
    if (field.rules) {
      rules[field.prop] = field.rules;
    } else if (field.required) {
      rules[field.prop] = [
        { required: true, message: `请输入${field.label}`, trigger: 'blur' }
      ];
    }
  });
  return rules;
});

// 方法
const getFieldComponent = (field: FormField) => {
  const componentMap = {
    input: 'el-input',
    select: 'el-select',
    date: 'el-date-picker',
    textarea: 'el-input',
    number: 'el-input-number'
  };
  return componentMap[field.type] || 'el-input';
};

const getFieldProps = (field: FormField) => {
  const baseProps = {
    placeholder: field.placeholder || `请输入${field.label}`,
    clearable: true
  };

  const platformProps = isMobile.value ? field.mobileProps : field.desktopProps;

  if (field.type === 'textarea') {
    baseProps.type = 'textarea';
    baseProps.rows = 3;
  }

  if (field.type === 'select' && field.options) {
    baseProps.options = field.options;
  }

  return { ...baseProps, ...platformProps };
};

const getMobileRules = (field: FormField) => {
  if (field.required) {
    return [{ required: true, message: `请输入${field.label}` }];
  }
  return [];
};

// 事件处理
const handleFieldChange = (prop: string, value: any) => {
  emit('field-change', prop, value);
};

const handleSubmit = () => {
  emit('submit', formData.value);
};

// 移动端选择器
const showPicker = (field: FormField) => {
  currentPickerField.value = field;
  pickerColumns.value = field.options || [];
  showPickerPopup.value = true;
};

const onPickerConfirm = ({ selectedValues }: any) => {
  if (currentPickerField.value) {
    formData.value[currentPickerField.value.prop] = selectedValues[0];
    handleFieldChange(currentPickerField.value.prop, selectedValues[0]);
  }
  showPickerPopup.value = false;
};

const showDatePicker = (field: FormField) => {
  currentPickerField.value = field;
  currentDate.value = formData.value[field.prop] ? new Date(formData.value[field.prop]) : new Date();
  showDatePickerPopup.value = true;
};

const onDateConfirm = (value: Date) => {
  if (currentPickerField.value) {
    formData.value[currentPickerField.value.prop] = value;
    handleFieldChange(currentPickerField.value.prop, value);
  }
  showDatePickerPopup.value = false;
};

// 暴露方法
defineExpose({
  validate: () => {
    if (isMobile.value) {
      return mobileFormRef.value?.validate();
    } else {
      return formRef.value?.validate();
    }
  },
  resetFields: () => {
    if (isMobile.value) {
      mobileFormRef.value?.resetValidation();
    } else {
      formRef.value?.resetFields();
    }
  }
});
</script>

<style lang="scss" scoped>
.adaptive-form {
  width: 100%;

  // 移动端样式调整
  @media screen and (max-width: 991px) {
    .van-cell-group {
      margin: 16px;
    }

    .van-field {
      padding: 12px 16px;
    }
  }
}
</style>
```
