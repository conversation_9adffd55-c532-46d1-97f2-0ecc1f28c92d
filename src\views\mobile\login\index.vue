<template>
  <div class="loginbody">
    <div class="logintitle">
      <span>美院资产管理系统</span>
    </div>
    <div class="logintypein">
      <van-form @submit="handleLoginSubmit">
        <van-cell-group inset>
          <van-field
            v-model="loginData.username"
            name="用户名"
            placeholder="请输入用户名"
            left-icon="manager"
            :rules="userFromRules.username"
          />
          <van-field
            v-model="loginData.password"
            type="password"
            name="密码"
            placeholder="请输入密码"
            left-icon="lock"
            :rules="userFromRules.password"
          />
        </van-cell-group>
        <div style="margin: 16px">
          <van-button block type="primary" native-type="submit">登陆</van-button>
        </div>
      </van-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";

import AuthAPI, { type LoginData } from "@/api/auth";
import defaultSettings from "@/settings";
import { useUserStore, useDictStore } from "@/store";

const router = useRouter();
const userStore = useUserStore();
const dictStore = useDictStore();

const logo = ref(new URL("../../../assets/logo.png", import.meta.url).href);
const loading = ref(false);
const showPassword = ref(false);
const rememberMe = ref(false);
const captchaBase64 = ref("");

const loginData = reactive<LoginData>({
  username: "",
  password: "",
  captchaKey: "",
  captchaCode: "",
});

// 获取验证码
const getCaptcha = async () => {
  try {
    const data = await AuthAPI.getCaptcha();
    loginData.captchaKey = data.captchaKey;
    captchaBase64.value = data.captchaBase64;
  } catch (error) {
    ElMessage.error("获取验证码失败");
  }
};

const userFromRules = {
  username: [{ required: true, message: "请填写用户名" }],
  password: [{ required: true, message: "请填写密码" }],
};

// 登录提交
const handleLoginSubmit = async () => {
  // 简单验证
  if (!loginData.username) {
    ElMessage.error("请输入用户名");
    return;
  }
  if (!loginData.password) {
    ElMessage.error("请输入密码");
    return;
  }

  loading.value = true;

  try {
    console.log("loginData", loginData);
    // 登录
    await userStore.login(loginData);
    await userStore.getUserInfo();
    await dictStore.loadDictionaries();

    ElMessage.success("登录成功");
    // 跳转到移动端首页
    router.push("/mobile/dashboard");
  } catch (error) {
    // 重新获取验证码
  } finally {
    loading.value = false;
  }
};

// 忘记密码
const handleForgotPassword = () => {
  ElMessage.info("请联系管理员重置密码");
};

onMounted(() => {
  getCaptcha();
});
</script>

<style scoped>
.loginbody {
  background-color: #efeff4;
  background-image: url("@/assets/loginbg.png");
  width: 100vw;
  height: 100vh;
  background-size: 100% auto;
  background-repeat: no-repeat;
}

.logintypein {
  background-repeat: no-repeat;
  background-size: 100%;
  background-image: url("@/assets/logintypein.png");
  width: 100%;
  /* height: 370px; */
  padding: 50px 50px 0px 50px;
  position: fixed;
  bottom: 8%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -12%);
}

.logintitle {
  width: 100vw;
  text-align: center;
}

.logintitle span {
  line-height: 300px;
  color: #ffffff;
  font-family: SimHei;
  font-size: 30px;
}
</style>
