<template>
  <div class="trend-chart-panel">
    <div class="panel-header">
      <div class="header-content">
        <div class="title-wrapper">
          <div class="title-icon">📈</div>
          <h3 class="title">资产使用方向统计</h3>
        </div>
        <div class="chart-tabs">
          <div 
            class="tab" 
            v-for="(tab, index) in tabs" 
            :key="index"
            :class="{ active: activeTab === index }"
            @click="switchTab(index)"
          >
            {{ tab.name }}
          </div>
        </div>
      </div>
      <div class="header-decoration"></div>
    </div>
    
    <div class="panel-content">
      <div ref="chartContainer" class="chart-container"></div>
      
      <!-- 数据指标 -->
      <div class="chart-indicators">
        <div class="indicator-item" v-for="(indicator, index) in indicators" :key="index">
          <div class="indicator-dot" :style="{ backgroundColor: indicator.color }"></div>
          <div class="indicator-info">
            <div class="indicator-label">{{ indicator.label }}</div>
            <div class="indicator-value">{{ indicator.value }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue'
import * as echarts from 'echarts'

const chartContainer = ref(null)
const activeTab = ref(0)
let chartInstance = null

// 标签页数据
const tabs = ref([
  { name: '资产使用', type: 'usage' },
  { name: '资产分布', type: 'distribution' },
  { name: '部门统计', type: 'department' }
])

// 指标数据
const indicators = ref([
  { label: '总资产', value: '30,883万元', color: '#00d4ff' },
  { label: '增长率', value: '+12.5%', color: '#00ff88' },
  { label: '利用率', value: '87.3%', color: '#ffb800' },
  { label: '预警数', value: '23项', color: '#ff6b6b' }
])

// 图表数据
const chartDataSets = ref({
  usage: {
    categories: ['数字', '行政', '科研', '生产', '生活', '技术开发', '出租', '经营', '其他', '社会公益', '其他'],
    series: [
      {
        name: '资产数量',
        data: [60000, 45000, 38000, 42000, 35000, 28000, 32000, 25000, 30000, 22000, 18000],
        color: '#00d4ff'
      },
      {
        name: '设备数量',
        data: [200000, 180000, 160000, 150000, 140000, 120000, 110000, 100000, 95000, 85000, 75000],
        color: '#00ff88'
      }
    ]
  },
  distribution: {
    categories: ['A区', 'B区', 'C区', 'D区', 'E区', 'F区', 'G区', 'H区', 'I区', 'J区', 'K区'],
    series: [
      {
        name: '固定资产',
        data: [45000, 38000, 42000, 35000, 28000, 32000, 25000, 30000, 22000, 18000, 15000],
        color: '#ffb800'
      },
      {
        name: '流动资产',
        data: [25000, 22000, 28000, 20000, 18000, 24000, 16000, 19000, 14000, 12000, 10000],
        color: '#8b5cf6'
      }
    ]
  },
  department: {
    categories: ['财务部', '人事部', '技术部', '市场部', '运营部', '法务部', '行政部', '采购部', '质量部', '研发部', '销售部'],
    series: [
      {
        name: '部门资产',
        data: [85000, 72000, 68000, 55000, 48000, 42000, 38000, 35000, 32000, 28000, 25000],
        color: '#ff6b6b'
      }
    ]
  }
})

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return
  
  if (chartInstance) {
    chartInstance.dispose()
  }
  
  chartInstance = echarts.init(chartContainer.value)
  updateChart()
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) return
  
  const currentData = chartDataSets.value[tabs.value[activeTab.value].type]
  
  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 20, 40, 0.9)',
      borderColor: '#00d4ff',
      borderWidth: 1,
      textStyle: { 
        color: '#fff',
        fontSize: 12
      },
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#00d4ff'
        }
      }
    },
    legend: {
      data: currentData.series.map(s => s.name),
      textStyle: { 
        color: '#fff',
        fontSize: 12
      },
      top: 20,
      right: 50,
      itemWidth: 12,
      itemHeight: 8
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '8%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: currentData.categories,
      axisLine: { 
        lineStyle: { color: 'rgba(255, 255, 255, 0.2)' }
      },
      axisLabel: { 
        color: '#fff', 
        fontSize: 10,
        interval: 0,
        rotate: 30
      },
      splitLine: { 
        show: true,
        lineStyle: { 
          color: 'rgba(255, 255, 255, 0.05)',
          type: 'dashed'
        }
      }
    },
    yAxis: currentData.series.length > 1 ? [
      {
        type: 'value',
        name: currentData.series[0].name,
        position: 'left',
        axisLine: { 
          lineStyle: { color: 'rgba(255, 255, 255, 0.2)' }
        },
        axisLabel: { 
          color: '#fff', 
          fontSize: 10,
          formatter: function(value) {
            return value >= 10000 ? (value / 10000) + 'w' : value
          }
        },
        splitLine: { 
          lineStyle: { 
            color: 'rgba(255, 255, 255, 0.1)',
            type: 'dashed'
          }
        }
      },
      {
        type: 'value',
        name: currentData.series[1].name,
        position: 'right',
        axisLine: { 
          lineStyle: { color: 'rgba(255, 255, 255, 0.2)' }
        },
        axisLabel: { 
          color: '#fff', 
          fontSize: 10,
          formatter: function(value) {
            return value >= 10000 ? (value / 10000) + 'w' : value
          }
        },
        splitLine: { show: false }
      }
    ] : [
      {
        type: 'value',
        axisLine: { 
          lineStyle: { color: 'rgba(255, 255, 255, 0.2)' }
        },
        axisLabel: { 
          color: '#fff', 
          fontSize: 10,
          formatter: function(value) {
            return value >= 10000 ? (value / 10000) + 'w' : value
          }
        },
        splitLine: { 
          lineStyle: { 
            color: 'rgba(255, 255, 255, 0.1)',
            type: 'dashed'
          }
        }
      }
    ],
    series: currentData.series.map((seriesData, index) => ({
      name: seriesData.name,
      type: 'line',
      yAxisIndex: currentData.series.length > 1 ? index : 0,
      data: seriesData.data,
      smooth: true,
      lineStyle: { 
        color: seriesData.color, 
        width: 3,
        shadowColor: seriesData.color,
        shadowBlur: 10
      },
      itemStyle: { 
        color: seriesData.color,
        borderWidth: 2,
        borderColor: '#fff'
      },
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: seriesData.color + '40' },
          { offset: 1, color: seriesData.color + '10' }
        ])
      },
      emphasis: {
        focus: 'series',
        itemStyle: {
          borderWidth: 4,
          shadowBlur: 20,
          shadowColor: seriesData.color
        }
      },
      animationDelay: function (idx) {
        return idx * 50
      }
    }))
  }
  
  chartInstance.setOption(option, true)
}

// 切换标签页
const switchTab = (index) => {
  activeTab.value = index
  updateChart()
}

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 监听activeTab变化
watch(activeTab, () => {
  updateChart()
})

onMounted(async () => {
  await nextTick()
  initChart()
  
  window.addEventListener('dashboard-resize', handleResize)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('dashboard-resize', handleResize)
})
</script>

<style lang="scss" scoped>
.trend-chart-panel {
  background: linear-gradient(135deg, 
    rgba(0, 212, 255, 0.05) 0%, 
    rgba(0, 255, 136, 0.03) 100%
  );
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .panel-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid rgba(0, 212, 255, 0.1);
    position: relative;
    
    .header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .title-wrapper {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .title-icon {
          font-size: 20px;
          filter: drop-shadow(0 0 8px rgba(0, 212, 255, 0.6));
        }
        
        .title {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #fff;
          text-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
        }
      }
      
      .chart-tabs {
        display: flex;
        gap: 20px;
        
        .tab {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.6);
          cursor: pointer;
          transition: all 0.3s ease;
          padding: 6px 12px;
          border-radius: 6px;
          border: 1px solid transparent;
          
          &.active {
            color: #00d4ff;
            background: rgba(0, 212, 255, 0.1);
            border-color: rgba(0, 212, 255, 0.3);
          }
          
          &:hover:not(.active) {
            color: #00ff88;
            background: rgba(0, 255, 136, 0.05);
          }
        }
      }
    }
    
    .header-decoration {
      position: absolute;
      bottom: 0;
      left: 24px;
      width: 60px;
      height: 2px;
      background: linear-gradient(90deg, #00d4ff, #00ff88);
      border-radius: 1px;
    }
  }
  
  .panel-content {
    flex: 1;
    padding: 20px 24px 24px;
    display: flex;
    flex-direction: column;
    
    .chart-container {
      flex: 1;
      min-height: 300px;
      margin-bottom: 20px;
    }
    
    .chart-indicators {
      display: flex;
      justify-content: space-around;
      padding: 16px;
      background: rgba(0, 212, 255, 0.03);
      border-radius: 12px;
      border: 1px solid rgba(0, 212, 255, 0.1);
      
      .indicator-item {
        display: flex;
        align-items: center;
        gap: 8px;
        
        .indicator-dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          box-shadow: 0 0 8px currentColor;
        }
        
        .indicator-info {
          .indicator-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 2px;
          }
          
          .indicator-value {
            font-size: 14px;
            font-weight: 600;
            color: #fff;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .trend-chart-panel {
    .panel-header {
      .header-content {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;
        
        .chart-tabs {
          gap: 12px;
          
          .tab {
            font-size: 11px;
            padding: 4px 8px;
          }
        }
      }
    }
    
    .panel-content {
      .chart-indicators {
        flex-wrap: wrap;
        gap: 12px;
        
        .indicator-item {
          flex: 1;
          min-width: 120px;
        }
      }
    }
  }
}
</style>
