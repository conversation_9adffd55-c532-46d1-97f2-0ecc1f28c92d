import { DeviceEnum } from '@/enums/DeviceEnum';

export interface DeviceInfo {
  type: DeviceEnum;
  width: number;
  height: number;
  orientation: 'portrait' | 'landscape';
  userAgent: string;
  isTouch: boolean;
}

export class DeviceDetector {
  private static readonly MOBILE_BREAKPOINT = 992;
  private static readonly TABLET_BREAKPOINT = 768;

  /**
   * 获取完整设备信息
   */
  static getDeviceInfo(): DeviceInfo {
    const width = window.innerWidth;
    const height = window.innerHeight;
    
    return {
      type: width < this.MOBILE_BREAKPOINT ? DeviceEnum.MOBILE : DeviceEnum.DESKTOP,
      width,
      height,
      orientation: width > height ? 'landscape' : 'portrait',
      userAgent: navigator.userAgent.toLowerCase(),
      isTouch: 'ontouchstart' in window
    };
  }

  /**
   * 判断是否为移动设备
   */
  static isMobile(): boolean {
    return window.innerWidth < this.MOBILE_BREAKPOINT;
  }

  /**
   * 判断是否为平板设备
   */
  static isTablet(): boolean {
    const width = window.innerWidth;
    return width >= this.TABLET_BREAKPOINT && width < this.MOBILE_BREAKPOINT;
  }

  /**
   * 判断是否为桌面设备
   */
  static isDesktop(): boolean {
    return window.innerWidth >= this.MOBILE_BREAKPOINT;
  }

  /**
   * 获取设备类型
   */
  static getDeviceType(): DeviceEnum {
    return this.isMobile() ? DeviceEnum.MOBILE : DeviceEnum.DESKTOP;
  }

  /**
   * 监听设备变化
   */
  static onDeviceChange(callback: (deviceInfo: DeviceInfo) => void) {
    const handleResize = () => {
      callback(this.getDeviceInfo());
    };

    const handleOrientationChange = () => {
      // 延迟执行，等待屏幕旋转完成
      setTimeout(() => {
        callback(this.getDeviceInfo());
      }, 100);
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleOrientationChange);

    // 返回清理函数
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleOrientationChange);
    };
  }

  /**
   * 检查是否为移动设备用户代理
   */
  static isMobileUserAgent(): boolean {
    const userAgent = navigator.userAgent.toLowerCase();
    const mobileKeywords = ['mobile', 'android', 'iphone', 'ipad', 'ipod', 'blackberry', 'windows phone'];
    return mobileKeywords.some(keyword => userAgent.includes(keyword));
  }

  /**
   * 综合判断设备类型（结合屏幕尺寸和用户代理）
   */
  static getComprehensiveDeviceType(): DeviceEnum {
    const screenBasedType = this.getDeviceType();
    const userAgentBasedType = this.isMobileUserAgent() ? DeviceEnum.MOBILE : DeviceEnum.DESKTOP;
    
    // 如果两种判断方式一致，直接返回
    if (screenBasedType === userAgentBasedType) {
      return screenBasedType;
    }
    
    // 如果不一致，优先使用屏幕尺寸判断，但在边界情况下考虑用户代理
    if (window.innerWidth <= 1024 && this.isMobileUserAgent()) {
      return DeviceEnum.MOBILE;
    }
    
    return screenBasedType;
  }
}
