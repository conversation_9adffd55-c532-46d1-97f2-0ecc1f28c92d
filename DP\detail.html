﻿<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title></title>
    <link href="css/index.css" rel="stylesheet" type="text/css" />
    <script src="js/main.js"></script>
    <script src="js/jquery.js"></script>
    <script src="js/index.js"></script>
    <script src="plugins/echarts/echarts.min.js"></script>
    <script src="plugins/gsap/minified/gsap.min.js"></script>

    <style>
        .Main_top ul li {
            padding-top: 2%;
        }

        .Main_top {
            height: 53%;
        }

        .liIn {
            background: #00000030;
        }

        .content::-webkit-scrollbar {
            /*滚动条整体样式*/
            width: 6px;
            /*高宽分别对应横竖滚动条的尺寸*/
            height: 1px;
        }

        .content::-webkit-scrollbar-thumb {
            /*滚动条里面小方块*/
            border-radius: 10px;
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
            background: #89898961;
            ;
        }

        .content::-webkit-scrollbar-track {
            /*滚动条里面轨道*/
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
            border-radius: 10px;
        }

        .matter {
            width: 6%;
            position: absolute;
            left: -2%;
            bottom: -38px;
            font-size: 12px;
            color: aqua;
            z-index: 10;
            text-align: center;
        }

            .matter img {
                width: 100%;
            }

        .guide, .guide a {
            font-size: 14px;
            color: #41bbfb
        }

        .btn {
            padding: 3px 10px;
            background: #e8fcff4d;
        }

        a {
            text-decoration: none
        }
    </style>
    <style>
        .content_box {
            width: 32%;
            margin: 0.5%;
        }

        .data_main {
            display: flex;
            justify-content: flex-start;
            flex-wrap: wrap;
            align-content: flex-start;
        }

        .resbox {
            padding: 10px;
            margin: 1% 2%;
        }

        .resbox-content div {
            color: #00d0ff
        }

        .resbox-content label {
            color: cyan;
        }

        .resbox-title {
            color: cyan;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
    </style>
    <style>
        .flasheffectoutbox {
            /* width: 1000px;
            height: 550px; */
            margin: auto;
            /* background-image: url("./images/chain_bg.png");
            background-repeat: no-repeat;
            background-position: center; */
            position: relative;
            background-size: contain;
        }

        .line {
            position: absolute;
            transform-origin: left;
            animation-iteration-count: infinite;
            z-index: 1;
            left: 50%;
            top: 0;
            width: 0;
            height: 10px;
            background-image: url("./images/linght_ef.png");
            background-repeat: no-repeat;
            background-position: 100%;
        }
        .line_check {
            background-image: url("./images/linght_check.png");
            background-repeat: no-repeat;
            background-position: 100%;
        }

        .line1 {
            transform: rotate(126.9deg);
        }

        .line2 {
            transform: rotate(122.8deg);
        }

        .line3 {
            transform: rotate(118.2deg);
        }

        .line4 {
            transform: rotate(113.2deg);
        }

        .line5 {
            transform: rotate(107.8deg);
        }

        .line6 {
            transform: rotate(102deg);
        }

        .line7 {
            transform: rotate(96deg);
        }

        .line8 {
            transform: rotate(90deg);
        }

        .line9 {
            transform: rotate(83.8deg);
        }

        .line10 {
            transform: rotate(77.8deg);
        }

        .line11 {
            transform: rotate(72deg);
        }

        .line12 {
            transform: rotate(66.5deg);
        }

        .line13 {
            transform: rotate(61.6deg);
        }

        .line14 {
            transform: rotate(57deg);
        }

        .line15 {
            transform: rotate(53deg);
        }
    </style>
</head>

<body>
    <!--header-->
    <div id="header" class="header">
        <div id="bg_header" class="bg_header">
            <div class="header_nav header_title" style="font-size: 25px;">
                <!-- <img src="images/logo.png">&nbsp; -->
                资产预警监测大屏
            </div>
        </div>
    </div>
    <a href="cg.html" style="position: absolute; left: 60px; top: 60px; color: #00a1ff; text-decoration: none; font-size: 14px;z-index: 900"><<返回 </a>

    <div style="display:flex;height:90%">
        <div style="width: 65%;height:100%;position: relative;">
            <!-- <div style="position: absolute; left: 10%;top:0;" class="guide">当前：<a href="cg.html" style="cursor:pointer">"采购安"</a>  -  事项监控</div> -->
            <div style="position: absolute; left: 10%;top:8%;z-index:10" class="btn"><a href="#" onclick="initGsap()">更新</a></div>
            <div style="position: absolute; left: 17%;top:8%;z-index:10" class="btn"><a href="#" onclick="initGsap(8000)">循环</a></div>
            <div style="width:100%;height:100%;display: flex;
            align-items: center;
            align-content: center;">
                <div class="flasheffectoutbox">
                    <img src="./images/chain_bg.png" style="width: 100%;" />
                    <div class="matter" style="    left: 45.8%;
                    top: -37px;
                    width: 8%;"><img src="./images/blockchain.png" /></div>
                    <div class="matter"><img src="./images/2.png" />监控点</div>
                    <div class="matter" style="left: 5%; "><img src="./images/2.png" />监控点</div>
                    <div class="matter" style="left: 12%; "><img src="./images/2.png"/>监控点</div>
                    <div class="matter" style="left: 19%;"><img src="./images/2.png" />监控点</div>
                    <div class="matter" style="left: 26%;"><img src="./images/2.png" />监控点</div>
                    <div class="matter" style="left: 33%;"><img src="./images/2.png" />监控点</div>
                    <div class="matter" style="left: 40%;"><img src="./images/2.png" />监控点</div>
                    <div class="matter" style="left: 47%;"><img src="./images/2.png" />监控点</div>
                    <div class="matter" style="left: 54%;"><img src="./images/2.png" />监控点</div>
                    <div class="matter" style="left: 61%;"><img src="./images/2.png" />监控点</div>
                    <div class="matter" style="left: 68%;"><img src="./images/2.png" />监控点</div>
                    <div class="matter" style="left: 75%;"><img src="./images/2.png" />监控点</div>
                    <div class="matter" style="left: 82%;"><img src="./images/2.png" />监控点</div>
                    <div class="matter" style="left: 89%;"><img src="./images/2.png" />监控点</div>
                    <div class="matter" style="left: 96%;"><img src="./images/2.png" />监控点</div>

                    <div class="line1 line"></div>
                    <div class="line2 line"></div>
                    <div class="line3 line"></div>
                    <div class="line4 line"></div>
                    <div class="line5 line"></div>
                    <div class="line6 line"></div>
                    <div class="line7 line"></div>
                    <div class="line8 line"></div>
                    <div class="line9 line"></div>
                    <div class="line10 line"></div>
                    <div class="line11 line"></div>
                    <div class="line12 line"></div>
                    <div class="line13 line"></div>
                    <div class="line14 line"></div>
                    <div class="line15 line"></div>
                    <div class="line16 line"></div>
                    <div class="line17 line"></div>
                    <div class="line18 line"></div>
                    <div class="line19 line"></div>
                </div>
            </div>

            <div style="
                position: absolute;
                top: 5%;
                right: 2%;
                width: 25%;
                height: 25%;background: url(images/right_con1.png) no-repeat;
                background-size: contain;padding: 1rem;">
                <div class="resbox-title" style="text-align: center;color:#c1f6ff">监控统计</div>
                <div class="resbox-content">
                    <div>
                        监测总数：<label id="total" style="color:#c1f6ff">0</label>
                    </div>
                    <div style="margin-top:.7rem;padding-left: .5rem;">
                        <img src="./images/state2.png" style="height: 20px;vertical-align: bottom;margin-right: 5px;" />红色预警数：<label id="r" style="color:#c1f6ff">0</label>
                    </div>
                    <div style="margin-top:.7rem;padding-left: .5rem;">
                        <img src="./images/state.png" style="height: 20px;vertical-align: bottom;margin-right: 5px;" />黄色预警数：<label id="y" style="color:#c1f6ff">0</label>
                    </div>
                    <div style="margin-top:.7rem;padding-left: .5rem;">
                        <img src="./images/state1.png" style="height: 20px;vertical-align: bottom;margin-right: 5px;" />蓝色预警数：<label id="b" style="color:#c1f6ff">0</label>
                    </div>
                </div>
            </div>
        </div>
        <!--main-->
        <div style="width: 33%;overflow-y: scroll;" class="content">
            <div style="display: flex; flex-wrap: wrap; justify-content: space-between; position: relative;" id="infos">
                <div style="position: absolute;left: 40%;top: 100px;font-size: 18px;">数据获取中...</div>
            </div>
        </div>
    </div>
    <script>

        let tl = gsap.timeline({ defaults: { duration: 4 } });
        tl.to(".line1", { width: '83%' }, '1')
            .to(".line2", { width: '79%' }, '1.1')
            .to(".line3", { width: '75%' }, '1.2')
            .to(".line4", { width: '72.5%' }, '1.3')
            .to(".line5", { width: '70%' }, '1.4')
            .to(".line6", { width: '68%' }, '1.5')
            .to(".line7", { width: '67%' }, '1.6')
            .to(".line8", { width: '66.5%' }, '1.7')
            .to(".line9", { width: '67%' }, '1.8')
            .to(".line10", { width: '68%' }, '1.9')
            .to(".line11", { width: '70%' }, '2')
            .to(".line12", { width: '72.5%' }, '2.1')
            .to(".line13", { width: '75%' }, '2.2')
            .to(".line14", { width: '79%' }, '2.3')
            .to(".line15", { width: '83%' }, '2.4');
       
       
    </script>
    <script>
       
        $(function () {
            setTimeout(()=>{
            startGsap();
            startInterval();}
            ,2000)
        })
        
        function startGsap() {
            $.get(serviceUrl + "GetJcInfoBySx",
                function (result) {
                    try {
                        obj = JSON.parse(result);
                    } catch (e) {
                        return;
                    }

                    if (obj.result == "0") {
                        return;
                    }
                    var html = "";
                    var total=0,r=0,y=0,b=0;
                    for (var i = 0; i < obj.data.length; i++) {
                        total+=parseInt(obj.data[i].total);
                        r+=parseInt(obj.data[i].r);
                        y+=parseInt(obj.data[i].y);
                        b+=parseInt(obj.data[i].b);
                        html += ' <div class="content_box resbox" style="width:46%;">\
                            <div class="resbox-title">\
                                监测信息\
                            </div>\
                            <div class="resbox-content">\
                                <div>\
                                    事项名称：<label>'+ obj.data[i].sxmc + '</label>\
                                </div>\
                                <div>\
                                    监测项目数：<label>'+ obj.data[i].total + '</label>\
                                </div>\
                                <div>\
                                    <img src="./images/red.png" style="height: 32px;vertical-align: bottom;margin-right: 5px;" />红色预警数：<label>'+ obj.data[i].r + '</label>\
                                </div>\
                                <div>\
                                    <img src="./images/yellow.png" style="height: 32px;vertical-align: bottom;margin-right: 5px;" />黄色预警数：<label>'+ obj.data[i].y + '</label>\
                                </div>\
                                <div>\
                                    <img src="./images/blue.png" style="height: 32px;vertical-align: bottom;margin-right: 5px;" />蓝色预警数：<label>'+ obj.data[i].b + '</label>\
                                </div>\
                            </div>\
                        </div>';
                    }
                    $("#infos").html(html);
                    $("#total").html(total);
                    $("#r").html(r);
                    $("#y").html(y);
                    $("#b").html(b);
                });
       
        }

        function update() {
            //更新时加快动画频率
            initGsap(8000);
            $(".line").addClass("line_check");
            $.get(serviceUrl + "CheckActualTime",
                function (result) {
                    try {
                        obj = JSON.parse(result);
                    } catch (e) {
                        return;
                    }

                    if (obj.result == "0") {
                        return;
                    }
                    var html = "";
                    var total=0,r=0,y=0,b=0;
                    for (var i = 0; i < obj.data.length; i++) {
                        total+=parseInt(obj.data[i].total);
                        r+=parseInt(obj.data[i].r);
                        y+=parseInt(obj.data[i].y);
                        b+=parseInt(obj.data[i].b);
                        html += ' <div class="content_box resbox" style="width:46%;">\
                            <div class="resbox-title">\
                                监测信息\
                            </div>\
                            <div class="resbox-content">\
                                <div>\
                                    事项名称：<label>'+ obj.data[i].sxmc + '</label>\
                                </div>\
                                <div>\
                                    监测项目数：<label>'+ obj.data[i].total + '</label>\
                                </div>\
                                <div>\
                                    <img src="./images/red.png" style="height: 32px;vertical-align: bottom;margin-right: 5px;" />红色预警数：<label>'+ obj.data[i].r + '</label>\
                                </div>\
                                <div>\
                                    <img src="./images/yellow.png" style="height: 32px;vertical-align: bottom;margin-right: 5px;" />黄色预警数：<label>'+ obj.data[i].y + '</label>\
                                </div>\
                                <div>\
                                    <img src="./images/blue.png" style="height: 32px;vertical-align: bottom;margin-right: 5px;" />蓝色预警数：<label>'+ obj.data[i].b + '</label>\
                                </div>\
                            </div>\
                        </div>';
                    }
                    $("#infos").html(html);
                    $("#total").html(total);
                    $("#r").html(r);
                    $("#y").html(y);
                    $("#b").html(b);
                    //更新完还原动画频率
                    initGsap(20000);
                    $(".line").removeClass("line_check");

                });
       
        }
       
        function updateGsap() {
            $("#infos").html('正在实时监测...');
            update();
        }
        var intervalID,animeId;
        var againtime = 600000;
        let animetime = 20000;
        function startInterval() {
            intervalID = setInterval(updateGsap, againtime);
            animeId = setInterval(runAnime, animetime);
        }

        function runAnime() {
            tl.restart();
           
        }
        //暂只影响动画
        function initGsap(t) {
            if (t)
                animetime = t;
            else
                animetime=20000;
            //清除定时器
            clearInterval(animeId);
            
            runAnime();
            //创建新定时器
            animeId = setInterval(runAnime, animetime);
        }
       
    </script>
</body>

</html>