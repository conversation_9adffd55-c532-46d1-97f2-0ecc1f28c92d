<template>
  <div class="adaptive-form">
    <!-- PC端表单 -->
    <el-form
      v-if="!isMobile"
      ref="formRef"
      :model="formData"
      :rules="formRules"
      v-bind="formConfig"
    >
      <el-row :gutter="20">
        <el-col
          v-for="field in adaptedFields"
          :key="field.prop"
          :span="field.span || 24"
        >
          <el-form-item
            :label="field.label"
            :prop="field.prop"
            :required="field.required"
          >
            <component
              :is="getFieldComponent(field)"
              v-model="formData[field.prop]"
              v-bind="getFieldProps(field)"
              @change="handleFieldChange(field.prop, $event)"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <!-- 移动端表单 -->
    <van-form v-else ref="mobileFormRef" @submit="handleSubmit">
      <van-cell-group inset>
        <template v-for="field in adaptedFields" :key="field.prop">
          <van-field
            v-if="field.type === 'input' || field.type === 'textarea'"
            v-model="formData[field.prop]"
            :name="field.prop"
            :label="field.label"
            :placeholder="field.placeholder"
            :required="field.required"
            :rules="getMobileRules(field)"
            :type="field.type === 'textarea' ? 'textarea' : 'text'"
            :rows="field.type === 'textarea' ? 3 : undefined"
            @change="handleFieldChange(field.prop, $event)"
          />
          
          <van-field
            v-else-if="field.type === 'select'"
            v-model="formData[field.prop]"
            :name="field.prop"
            :label="field.label"
            :placeholder="field.placeholder"
            :required="field.required"
            readonly
            is-link
            @click="showPicker(field)"
          />
          
          <van-field
            v-else-if="field.type === 'date'"
            v-model="formData[field.prop]"
            :name="field.prop"
            :label="field.label"
            :placeholder="field.placeholder"
            :required="field.required"
            readonly
            is-link
            @click="showDatePicker(field)"
          />
          
          <van-field
            v-else-if="field.type === 'number'"
            v-model="formData[field.prop]"
            :name="field.prop"
            :label="field.label"
            :placeholder="field.placeholder"
            :required="field.required"
            type="number"
            @change="handleFieldChange(field.prop, $event)"
          />
        </template>
      </van-cell-group>
    </van-form>

    <!-- 移动端选择器 -->
    <van-popup v-model:show="showPickerPopup" position="bottom">
      <van-picker
        :columns="pickerColumns"
        @confirm="onPickerConfirm"
        @cancel="showPickerPopup = false"
      />
    </van-popup>

    <!-- 移动端日期选择器 -->
    <van-popup v-model:show="showDatePickerPopup" position="bottom">
      <van-date-picker
        v-model="currentDate"
        @confirm="onDateConfirm"
        @cancel="showDatePickerPopup = false"
      />
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { useDevice } from '@/composables/useDevice';
import type { FormField } from './index';

interface AdaptiveFormProps {
  modelValue: Record<string, any>;
  fields: FormField[];
  labelWidth?: string;
  size?: 'large' | 'default' | 'small';
}

const props = withDefaults(defineProps<AdaptiveFormProps>(), {
  labelWidth: '100px',
  size: 'default'
});

const emit = defineEmits<{
  'update:modelValue': [value: Record<string, any>];
  'field-change': [prop: string, value: any];
  'submit': [formData: Record<string, any>];
}>();

const { isMobile } = useDevice();

// 响应式数据
const formRef = ref();
const mobileFormRef = ref();
const formData = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 选择器相关
const showPickerPopup = ref(false);
const showDatePickerPopup = ref(false);
const pickerColumns = ref([]);
const currentPickerField = ref<FormField | null>(null);
const currentDate = ref(new Date());

// 计算属性
const adaptedFields = computed(() => {
  return props.fields.filter(field => {
    // 可以根据设备类型过滤字段
    return true;
  });
});

const formConfig = computed(() => ({
  'label-width': props.labelWidth,
  size: props.size,
  'label-position': isMobile.value ? 'top' : 'right'
}));

const formRules = computed(() => {
  const rules: Record<string, any[]> = {};
  props.fields.forEach(field => {
    if (field.rules) {
      rules[field.prop] = field.rules;
    } else if (field.required) {
      rules[field.prop] = [
        { required: true, message: `请输入${field.label}`, trigger: 'blur' }
      ];
    }
  });
  return rules;
});

// 方法
const getFieldComponent = (field: FormField) => {
  const componentMap = {
    input: 'el-input',
    select: 'el-select',
    date: 'el-date-picker',
    textarea: 'el-input',
    number: 'el-input-number'
  };
  return componentMap[field.type] || 'el-input';
};

const getFieldProps = (field: FormField) => {
  const baseProps = {
    placeholder: field.placeholder || `请输入${field.label}`,
    clearable: true
  };

  const platformProps = isMobile.value ? field.mobileProps : field.desktopProps;

  if (field.type === 'textarea') {
    baseProps.type = 'textarea';
    baseProps.rows = 3;
  }

  if (field.type === 'select' && field.options) {
    baseProps.options = field.options;
  }

  return { ...baseProps, ...platformProps };
};

const getMobileRules = (field: FormField) => {
  if (field.required) {
    return [{ required: true, message: `请输入${field.label}` }];
  }
  return [];
};

// 事件处理
const handleFieldChange = (prop: string, value: any) => {
  emit('field-change', prop, value);
};

const handleSubmit = () => {
  emit('submit', formData.value);
};

// 移动端选择器
const showPicker = (field: FormField) => {
  currentPickerField.value = field;
  pickerColumns.value = field.options || [];
  showPickerPopup.value = true;
};

const onPickerConfirm = ({ selectedValues }: any) => {
  if (currentPickerField.value) {
    formData.value[currentPickerField.value.prop] = selectedValues[0];
    handleFieldChange(currentPickerField.value.prop, selectedValues[0]);
  }
  showPickerPopup.value = false;
};

const showDatePicker = (field: FormField) => {
  currentPickerField.value = field;
  currentDate.value = formData.value[field.prop] ? new Date(formData.value[field.prop]) : new Date();
  showDatePickerPopup.value = true;
};

const onDateConfirm = (value: Date) => {
  if (currentPickerField.value) {
    formData.value[currentPickerField.value.prop] = value;
    handleFieldChange(currentPickerField.value.prop, value);
  }
  showDatePickerPopup.value = false;
};

// 暴露方法
defineExpose({
  validate: () => {
    if (isMobile.value) {
      return mobileFormRef.value?.validate();
    } else {
      return formRef.value?.validate();
    }
  },
  resetFields: () => {
    if (isMobile.value) {
      mobileFormRef.value?.resetValidation();
    } else {
      formRef.value?.resetFields();
    }
  }
});
</script>

<style lang="scss" scoped>
.adaptive-form {
  width: 100%;

  // 移动端样式调整
  @media screen and (max-width: 991px) {
    .van-cell-group {
      margin: 16px;
    }

    .van-field {
      padding: 12px 16px;
    }
  }
}
</style>
