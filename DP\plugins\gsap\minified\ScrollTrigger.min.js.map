{"version": 3, "file": "ScrollTrigger.min.js", "sources": ["../src/Observer.js", "../src/ScrollTrigger.js"], "sourcesContent": ["/*!\n * Observer 3.11.3\n * https://greensock.com\n *\n * @license Copyright 2008-2022, GreenSock. All rights reserved.\n * Subject to the terms at https://greensock.com/standard-license or for\n * Club GreenSock members, the agreement issued with that membership.\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nlet gsap, _coreInitted, _clamp, _win, _doc, _docEl, _body, _isTouch, _pointerType, ScrollTrigger, _root, _normalizer, _eventTypes,\n\t_getGSAP = () => gsap || (typeof(window) !== \"undefined\" && (gsap = window.gsap) && gsap.registerPlugin && gsap),\n\t_passThrough = p => p,\n\t_startup = 1,\n\t_observers = [],\n\t_scrollers = [],\n\t_proxies = [],\n\t_getTime = Date.now,\n\t_bridge = (name, value) => value,\n\t_integrate = () => {\n\t\tlet core = ScrollTrigger.core,\n\t\t\tdata = core.bridge || {},\n\t\t\tscrollers = core._scrollers,\n\t\t\tproxies = core._proxies;\n\t\tscrollers.push(..._scrollers);\n\t\tproxies.push(..._proxies);\n\t\t_scrollers = scrollers;\n\t\t_proxies = proxies;\n\t\t_bridge = (name, value) => data[name](value);\n\t},\n\t_getProxyProp = (element, property) => ~_proxies.indexOf(element) && _proxies[_proxies.indexOf(element) + 1][property],\n\t_isViewport = el => !!~_root.indexOf(el),\n\t_addListener = (element, type, func, nonPassive, capture) => element.addEventListener(type, func, {passive: !nonPassive, capture: !!capture}),\n\t_removeListener = (element, type, func, capture) => element.removeEventListener(type, func, !!capture),\n\t_scrollLeft = \"scrollLeft\",\n\t_scrollTop = \"scrollTop\",\n\t_onScroll = () => (_normalizer && _normalizer.isPressed) || _scrollers.cache++,\n\t_scrollCacheFunc = (f, doNotCache) => {\n\t\tlet cachingFunc = value => { // since reading the scrollTop/scrollLeft/pageOffsetY/pageOffsetX can trigger a layout, this function allows us to cache the value so it only gets read fresh after a \"scroll\" event fires (or while we're refreshing because that can lengthen the page and alter the scroll position). when \"soft\" is true, that means don't actually set the scroll, but cache the new value instead (useful in ScrollSmoother)\n\t\t\tif (value || value === 0) {\n\t\t\t\t_startup && (_win.history.scrollRestoration = \"manual\"); // otherwise the new position will get overwritten by the browser onload.\n\t\t\t\tlet isNormalizing = _normalizer && _normalizer.isPressed;\n\t\t\t\tvalue = cachingFunc.v = Math.round(value) || (_normalizer && _normalizer.iOS ? 1 : 0); //TODO: iOS Bug: if you allow it to go to 0, Safari can start to report super strange (wildly inaccurate) touch positions!\n\t\t\t\tf(value);\n\t\t\t\tcachingFunc.cacheID = _scrollers.cache;\n\t\t\t\tisNormalizing && _bridge(\"ss\", value); // set scroll (notify ScrollTrigger so it can dispatch a \"scrollStart\" event if necessary\n\t\t\t} else if (doNotCache || _scrollers.cache !== cachingFunc.cacheID || _bridge(\"ref\")) {\n\t\t\t\tcachingFunc.cacheID = _scrollers.cache;\n\t\t\t\tcachingFunc.v = f();\n\t\t\t}\n\t\t\treturn cachingFunc.v + cachingFunc.offset;\n\t\t};\n\t\tcachingFunc.offset = 0;\n\t\treturn f && cachingFunc;\n\t},\n\t_horizontal = {s: _scrollLeft, p: \"left\", p2: \"Left\", os: \"right\", os2: \"Right\", d: \"width\", d2: \"Width\", a: \"x\", sc: _scrollCacheFunc(function(value) { return arguments.length ? _win.scrollTo(value, _vertical.sc()) : _win.pageXOffset || _doc[_scrollLeft] || _docEl[_scrollLeft] || _body[_scrollLeft] || 0})},\n\t_vertical = {s: _scrollTop, p: \"top\", p2: \"Top\", os: \"bottom\", os2: \"Bottom\", d: \"height\", d2: \"Height\", a: \"y\", op: _horizontal, sc: _scrollCacheFunc(function(value) { return arguments.length ? _win.scrollTo(_horizontal.sc(), value) : _win.pageYOffset || _doc[_scrollTop] || _docEl[_scrollTop] || _body[_scrollTop] || 0})},\n\t_getTarget = t => gsap.utils.toArray(t)[0] || (typeof(t) === \"string\" && gsap.config().nullTargetWarn !== false ? console.warn(\"Element not found:\", t) : null),\n\n\t_getScrollFunc = (element, {s, sc}) => { // we store the scroller functions in an alternating sequenced Array like [element, verticalScrollFunc, horizontalScrollFunc, ...] so that we can minimize memory, maximize performance, and we also record the last position as a \".rec\" property in order to revert to that after refreshing to ensure things don't shift around.\n\t\t_isViewport(element) && (element = _doc.scrollingElement || _docEl);\n\t\tlet i = _scrollers.indexOf(element),\n\t\t\toffset = sc === _vertical.sc ? 1 : 2;\n\t\t!~i && (i = _scrollers.push(element) - 1);\n\t\t_scrollers[i + offset] || element.addEventListener(\"scroll\", _onScroll); // clear the cache when a scroll occurs\n\t\tlet prev = _scrollers[i + offset],\n\t\t\tfunc = prev || (_scrollers[i + offset] = _scrollCacheFunc(_getProxyProp(element, s), true) || (_isViewport(element) ? sc : _scrollCacheFunc(function(value) { return arguments.length ? (element[s] = value) : element[s]; })));\n\t\tfunc.target = element;\n\t\tprev || (func.smooth = gsap.getProperty(element, \"scrollBehavior\") === \"smooth\"); // only set it the first time (don't reset every time a scrollFunc is requested because perhaps it happens during a refresh() when it's disabled in ScrollTrigger.\n\t\treturn func;\n\t},\n\t_getVelocityProp = (value, minTimeRefresh, useDelta) => {\n\t\tlet v1 = value,\n\t\t\tv2 = value,\n\t\t\tt1 = _getTime(),\n\t\t\tt2 = t1,\n\t\t\tmin = minTimeRefresh || 50,\n\t\t\tdropToZeroTime = Math.max(500, min * 3),\n\t\t\tupdate = (value, force) => {\n\t\t\t\tlet t = _getTime();\n\t\t\t\tif (force || t - t1 > min) {\n\t\t\t\t\tv2 = v1;\n\t\t\t\t\tv1 = value;\n\t\t\t\t\tt2 = t1;\n\t\t\t\t\tt1 = t;\n\t\t\t\t} else if (useDelta) {\n\t\t\t\t\tv1 += value;\n\t\t\t\t} else { // not totally necessary, but makes it a bit more accurate by adjusting the v1 value according to the new slope. This way we're not just ignoring the incoming data. Removing for now because it doesn't seem to make much practical difference and it's probably not worth the kb.\n\t\t\t\t\tv1 = v2 + (value - v2) / (t - t2) * (t1 - t2);\n\t\t\t\t}\n\t\t\t},\n\t\t\treset = () => { v2 = v1 = useDelta ? 0 : v1; t2 = t1 = 0; },\n\t\t\tgetVelocity = latestValue => {\n\t\t\t\tlet tOld = t2,\n\t\t\t\t\tvOld = v2,\n\t\t\t\t\tt = _getTime();\n\t\t\t\t(latestValue || latestValue === 0) && latestValue !== v1 && update(latestValue);\n\t\t\t\treturn (t1 === t2 || t - t2 > dropToZeroTime) ? 0 : (v1 + (useDelta ? vOld : -vOld)) / ((useDelta ? t : t1) - tOld) * 1000;\n\t\t\t};\n\t\treturn {update, reset, getVelocity};\n\t},\n\t_getEvent = (e, preventDefault) => {\n\t\tpreventDefault && !e._gsapAllow && e.preventDefault();\n\t\treturn e.changedTouches ? e.changedTouches[0] : e;\n\t},\n\t_getAbsoluteMax = a => {\n\t\tlet max = Math.max(...a),\n\t\t\tmin = Math.min(...a);\n\t\treturn Math.abs(max) >= Math.abs(min) ? max : min;\n\t},\n\t_setScrollTrigger = () => {\n\t\tScrollTrigger = gsap.core.globals().ScrollTrigger;\n\t\tScrollTrigger && ScrollTrigger.core && _integrate();\n\t},\n\t_initCore = core => {\n\t\tgsap = core || _getGSAP();\n\t\tif (gsap && typeof(document) !== \"undefined\" && document.body) {\n\t\t\t_win = window;\n\t\t\t_doc = document;\n\t\t\t_docEl = _doc.documentElement;\n\t\t\t_body = _doc.body;\n\t\t\t_root = [_win, _doc, _docEl, _body];\n\t\t\t_clamp = gsap.utils.clamp;\n\t\t\t_pointerType = \"onpointerenter\" in _body ? \"pointer\" : \"mouse\";\n\t\t\t// isTouch is 0 if no touch, 1 if ONLY touch, and 2 if it can accommodate touch but also other types like mouse/pointer.\n\t\t\t_isTouch = Observer.isTouch = _win.matchMedia && _win.matchMedia(\"(hover: none), (pointer: coarse)\").matches ? 1 : (\"ontouchstart\" in _win || navigator.maxTouchPoints > 0 || navigator.msMaxTouchPoints > 0) ? 2 : 0;\n\t\t\t_eventTypes = Observer.eventTypes = (\"ontouchstart\" in _docEl ? \"touchstart,touchmove,touchcancel,touchend\" : !(\"onpointerdown\" in _docEl) ? \"mousedown,mousemove,mouseup,mouseup\" : \"pointerdown,pointermove,pointercancel,pointerup\").split(\",\");\n\t\t\tsetTimeout(() => _startup = 0, 500);\n\t\t\t_setScrollTrigger();\n\t\t\t_coreInitted = 1;\n\t\t}\n\t\treturn _coreInitted;\n\t};\n\n_horizontal.op = _vertical;\n_scrollers.cache = 0;\n\nexport class Observer {\n\tconstructor(vars) {\n\t\tthis.init(vars);\n\t}\n\n\tinit(vars) {\n\t\t_coreInitted || _initCore(gsap) || console.warn(\"Please gsap.registerPlugin(Observer)\");\n\t\tScrollTrigger || _setScrollTrigger();\n\t\tlet {tolerance, dragMinimum, type, target, lineHeight, debounce, preventDefault, onStop, onStopDelay, ignore, wheelSpeed, event, onDragStart, onDragEnd, onDrag, onPress, onRelease, onRight, onLeft, onUp, onDown, onChangeX, onChangeY, onChange, onToggleX, onToggleY, onHover, onHoverEnd, onMove, ignoreCheck, isNormalizer, onGestureStart, onGestureEnd, onWheel, onEnable, onDisable, onClick, scrollSpeed, capture, allowClicks, lockAxis, onLockAxis} = vars;\n\t\tthis.target = target = _getTarget(target) || _docEl;\n\t\tthis.vars = vars;\n\t\tignore && (ignore = gsap.utils.toArray(ignore));\n\t\ttolerance = tolerance || 1e-9;\n\t\tdragMinimum = dragMinimum || 0;\n\t\twheelSpeed = wheelSpeed || 1;\n\t\tscrollSpeed = scrollSpeed || 1;\n\t\ttype = type || \"wheel,touch,pointer\";\n\t\tdebounce = debounce !== false;\n\t\tlineHeight || (lineHeight = parseFloat(_win.getComputedStyle(_body).lineHeight) || 22); // note: browser may report \"normal\", so default to 22.\n\t\tlet id, onStopDelayedCall, dragged, moved, wheeled, locked, axis,\n\t\t\tself = this,\n\t\t\tprevDeltaX = 0,\n\t\t\tprevDeltaY = 0,\n\t\t\tscrollFuncX = _getScrollFunc(target, _horizontal),\n\t\t\tscrollFuncY = _getScrollFunc(target, _vertical),\n\t\t\tscrollX = scrollFuncX(),\n\t\t\tscrollY = scrollFuncY(),\n\t\t\tlimitToTouch = ~type.indexOf(\"touch\") && !~type.indexOf(\"pointer\") && _eventTypes[0] === \"pointerdown\", // for devices that accommodate mouse events and touch events, we need to distinguish.\n\t\t\tisViewport = _isViewport(target),\n\t\t\townerDoc = target.ownerDocument || _doc,\n\t\t\tdeltaX = [0, 0, 0], // wheel, scroll, pointer/touch\n\t\t\tdeltaY = [0, 0, 0],\n\t\t\tonClickTime = 0,\n\t\t\tclickCapture = () => onClickTime = _getTime(),\n\t\t\t_ignoreCheck = (e, isPointerOrTouch) => (self.event = e) && (ignore && ~ignore.indexOf(e.target)) || (isPointerOrTouch && limitToTouch && e.pointerType !== \"touch\") || (ignoreCheck && ignoreCheck(e, isPointerOrTouch)),\n\t\t\tonStopFunc = () => {\n\t\t\t\tself._vx.reset();\n\t\t\t\tself._vy.reset();\n\t\t\t\tonStopDelayedCall.pause();\n\t\t\t\tonStop && onStop(self);\n\t\t\t},\n\t\t\tupdate = () => {\n\t\t\t\tlet dx = self.deltaX = _getAbsoluteMax(deltaX),\n\t\t\t\t\tdy = self.deltaY = _getAbsoluteMax(deltaY),\n\t\t\t\t\tchangedX = Math.abs(dx) >= tolerance,\n\t\t\t\t\tchangedY = Math.abs(dy) >= tolerance;\n\t\t\t\tonChange && (changedX || changedY) && onChange(self, dx, dy, deltaX, deltaY); // in ScrollTrigger.normalizeScroll(), we need to know if it was touch/pointer so we need access to the deltaX/deltaY Arrays before we clear them out.\n\t\t\t\tif (changedX) {\n\t\t\t\t\tonRight && self.deltaX > 0 && onRight(self);\n\t\t\t\t\tonLeft && self.deltaX < 0 && onLeft(self);\n\t\t\t\t\tonChangeX && onChangeX(self);\n\t\t\t\t\tonToggleX && ((self.deltaX < 0) !== (prevDeltaX < 0)) && onToggleX(self);\n\t\t\t\t\tprevDeltaX = self.deltaX;\n\t\t\t\t\tdeltaX[0] = deltaX[1] = deltaX[2] = 0\n\t\t\t\t}\n\t\t\t\tif (changedY) {\n\t\t\t\t\tonDown && self.deltaY > 0 && onDown(self);\n\t\t\t\t\tonUp && self.deltaY < 0 && onUp(self);\n\t\t\t\t\tonChangeY && onChangeY(self);\n\t\t\t\t\tonToggleY && ((self.deltaY < 0) !== (prevDeltaY < 0)) && onToggleY(self);\n\t\t\t\t\tprevDeltaY = self.deltaY;\n\t\t\t\t\tdeltaY[0] = deltaY[1] = deltaY[2] = 0\n\t\t\t\t}\n\t\t\t\tif (moved || dragged) {\n\t\t\t\t\tonMove && onMove(self);\n\t\t\t\t\tif (dragged) {\n\t\t\t\t\t\tonDrag(self);\n\t\t\t\t\t\tdragged = false;\n\t\t\t\t\t}\n\t\t\t\t\tmoved = false;\n\t\t\t\t}\n\t\t\t\tlocked && !(locked = false) && onLockAxis && onLockAxis(self);\n\t\t\t\tif (wheeled) {\n\t\t\t\t\tonWheel(self);\n\t\t\t\t\twheeled = false;\n\t\t\t\t}\n\t\t\t\tid = 0;\n\t\t\t},\n\t\t\tonDelta = (x, y, index) => {\n\t\t\t\tdeltaX[index] += x;\n\t\t\t\tdeltaY[index] += y;\n\t\t\t\tself._vx.update(x);\n\t\t\t\tself._vy.update(y);\n\t\t\t\tdebounce ? id || (id = requestAnimationFrame(update)) : update();\n\t\t\t},\n\t\t\tonTouchOrPointerDelta = (x, y) => {\n\t\t\t\tif (lockAxis && !axis) {\n\t\t\t\t\tself.axis = axis = Math.abs(x) > Math.abs(y) ? \"x\" : \"y\";\n\t\t\t\t\tlocked = true;\n\t\t\t\t}\n\t\t\t\tif (axis !== \"y\") {\n\t\t\t\t\tdeltaX[2] += x;\n\t\t\t\t\tself._vx.update(x, true); // update the velocity as frequently as possible instead of in the debounced function so that very quick touch-scrolls (flicks) feel natural. If it's the mouse/touch/pointer, force it so that we get snappy/accurate momentum scroll.\n\t\t\t\t}\n\t\t\t\tif (axis !== \"x\") {\n\t\t\t\t\tdeltaY[2] += y;\n\t\t\t\t\tself._vy.update(y, true);\n\t\t\t\t}\n\t\t\t\tdebounce ? id || (id = requestAnimationFrame(update)) : update();\n\t\t\t},\n\t\t\t_onDrag = e => {\n\t\t\t\tif (_ignoreCheck(e, 1)) {return;}\n\t\t\t\te = _getEvent(e, preventDefault);\n\t\t\t\tlet x = e.clientX,\n\t\t\t\t\ty = e.clientY,\n\t\t\t\t\tdx = x - self.x,\n\t\t\t\t\tdy = y - self.y,\n\t\t\t\t\tisDragging = self.isDragging;\n\t\t\t\tself.x = x;\n\t\t\t\tself.y = y;\n\t\t\t\tif (isDragging || Math.abs(self.startX - x) >= dragMinimum || Math.abs(self.startY - y) >= dragMinimum) {\n\t\t\t\t\tonDrag && (dragged = true);\n\t\t\t\t\tisDragging || (self.isDragging = true);\n\t\t\t\t\tonTouchOrPointerDelta(dx, dy);\n\t\t\t\t\tisDragging || onDragStart && onDragStart(self);\n\t\t\t\t}\n\t\t\t},\n\t\t\t_onPress = self.onPress = e => {\n\t\t\t\tif (_ignoreCheck(e, 1)) {return;}\n\t\t\t\tself.axis = axis = null;\n\t\t\t\tonStopDelayedCall.pause();\n\t\t\t\tself.isPressed = true;\n\t\t\t\te = _getEvent(e); // note: may need to preventDefault(?) Won't side-scroll on iOS Safari if we do, though.\n\t\t\t\tprevDeltaX = prevDeltaY = 0;\n\t\t\t\tself.startX = self.x = e.clientX;\n\t\t\t\tself.startY = self.y = e.clientY;\n\t\t\t\tself._vx.reset(); // otherwise the t2 may be stale if the user touches and flicks super fast and releases in less than 2 requestAnimationFrame ticks, causing velocity to be 0.\n\t\t\t\tself._vy.reset();\n\t\t\t\t_addListener(isNormalizer ? target : ownerDoc, _eventTypes[1], _onDrag, preventDefault, true);\n\t\t\t\tself.deltaX = self.deltaY = 0;\n\t\t\t\tonPress && onPress(self);\n\t\t\t},\n\t\t\t_onRelease = e => {\n\t\t\t\tif (_ignoreCheck(e, 1)) {return;}\n\t\t\t\t_removeListener(isNormalizer ? target : ownerDoc, _eventTypes[1], _onDrag, true);\n\t\t\t\tlet wasDragging = self.isDragging && (Math.abs(self.x - self.startX) > 3 || Math.abs(self.y - self.startY) > 3), // some touch devices need some wiggle room in terms of sensing clicks - the finger may move a few pixels.\n\t\t\t\t\teventData = _getEvent(e);\n\t\t\t\tif (!wasDragging) {\n\t\t\t\t\tself._vx.reset();\n\t\t\t\t\tself._vy.reset();\n\t\t\t\t\tif (preventDefault && allowClicks) {\n\t\t\t\t\t\tgsap.delayedCall(0.08, () => { // some browsers (like Firefox) won't trust script-generated clicks, so if the user tries to click on a video to play it, for example, it simply won't work. Since a regular \"click\" event will most likely be generated anyway (one that has its isTrusted flag set to true), we must slightly delay our script-generated click so that the \"real\"/trusted one is prioritized. Remember, when there are duplicate events in quick succession, we suppress all but the first one. Some browsers don't even trigger the \"real\" one at all, so our synthetic one is a safety valve that ensures that no matter what, a click event does get dispatched.\n\t\t\t\t\t\t\tif (_getTime() - onClickTime > 300 && !e.defaultPrevented) {\n\t\t\t\t\t\t\t\tif (e.target.click) { //some browsers (like mobile Safari) don't properly trigger the click event\n\t\t\t\t\t\t\t\t\te.target.click();\n\t\t\t\t\t\t\t\t} else if (ownerDoc.createEvent) {\n\t\t\t\t\t\t\t\t\tlet syntheticEvent = ownerDoc.createEvent(\"MouseEvents\");\n\t\t\t\t\t\t\t\t\tsyntheticEvent.initMouseEvent(\"click\", true, true, _win, 1, eventData.screenX, eventData.screenY, eventData.clientX, eventData.clientY, false, false, false, false, 0, null);\n\t\t\t\t\t\t\t\t\te.target.dispatchEvent(syntheticEvent);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tself.isDragging = self.isGesturing = self.isPressed = false;\n\t\t\t\tonStop && !isNormalizer && onStopDelayedCall.restart(true);\n\t\t\t\tonDragEnd && wasDragging && onDragEnd(self);\n\t\t\t\tonRelease && onRelease(self, wasDragging);\n\t\t\t},\n\t\t\t_onGestureStart = e => e.touches && e.touches.length > 1 && (self.isGesturing = true) && onGestureStart(e, self.isDragging),\n\t\t\t_onGestureEnd = () => (self.isGesturing = false) || onGestureEnd(self),\n\t\t\tonScroll = e => {\n\t\t\t\tif (_ignoreCheck(e)) {return;}\n\t\t\t\tlet x = scrollFuncX(),\n\t\t\t\t\ty = scrollFuncY();\n\t\t\t\tonDelta((x - scrollX) * scrollSpeed, (y - scrollY) * scrollSpeed, 1);\n\t\t\t\tscrollX = x;\n\t\t\t\tscrollY = y;\n\t\t\t\tonStop && onStopDelayedCall.restart(true);\n\t\t\t},\n\t\t\t_onWheel = e => {\n\t\t\t\tif (_ignoreCheck(e)) {return;}\n\t\t\t\te = _getEvent(e, preventDefault);\n\t\t\t\tonWheel && (wheeled = true);\n\t\t\t\tlet multiplier = (e.deltaMode === 1 ? lineHeight : e.deltaMode === 2 ? _win.innerHeight : 1) * wheelSpeed;\n\t\t\t\tonDelta(e.deltaX * multiplier, e.deltaY * multiplier, 0);\n\t\t\t\tonStop && !isNormalizer && onStopDelayedCall.restart(true);\n\t\t\t},\n\t\t\t_onMove = e => {\n\t\t\t\tif (_ignoreCheck(e)) {return;}\n\t\t\t\tlet x = e.clientX,\n\t\t\t\t\ty = e.clientY,\n\t\t\t\t\tdx = x - self.x,\n\t\t\t\t\tdy = y - self.y;\n\t\t\t\tself.x = x;\n\t\t\t\tself.y = y;\n\t\t\t\tmoved = true;\n\t\t\t\t(dx || dy) && onTouchOrPointerDelta(dx, dy);\n\t\t\t},\n\t\t\t_onHover = e => {self.event = e; onHover(self);},\n\t\t\t_onHoverEnd = e => {self.event = e; onHoverEnd(self);},\n\t\t\t_onClick = e => _ignoreCheck(e) || (_getEvent(e, preventDefault) && onClick(self));\n\n\t\tonStopDelayedCall = self._dc = gsap.delayedCall(onStopDelay || 0.25, onStopFunc).pause();\n\n\t\tself.deltaX = self.deltaY = 0;\n\t\tself._vx = _getVelocityProp(0, 50, true);\n\t\tself._vy = _getVelocityProp(0, 50, true);\n\t\tself.scrollX = scrollFuncX;\n\t\tself.scrollY = scrollFuncY;\n\t\tself.isDragging = self.isGesturing = self.isPressed = false;\n\t\tself.enable = e => {\n\t\t\tif (!self.isEnabled) {\n\t\t\t\t_addListener(isViewport ? ownerDoc : target, \"scroll\", _onScroll);\n\t\t\t\ttype.indexOf(\"scroll\") >= 0 && _addListener(isViewport ? ownerDoc : target, \"scroll\", onScroll, preventDefault, capture);\n\t\t\t\ttype.indexOf(\"wheel\") >= 0 && _addListener(target, \"wheel\", _onWheel, preventDefault, capture);\n\t\t\t\tif ((type.indexOf(\"touch\") >= 0 && _isTouch) || type.indexOf(\"pointer\") >= 0) {\n\t\t\t\t\t_addListener(target, _eventTypes[0], _onPress, preventDefault, capture);\n\t\t\t\t\t_addListener(ownerDoc, _eventTypes[2], _onRelease);\n\t\t\t\t\t_addListener(ownerDoc, _eventTypes[3], _onRelease);\n\t\t\t\t\tallowClicks && _addListener(target, \"click\", clickCapture, false, true);\n\t\t\t\t\tonClick && _addListener(target, \"click\", _onClick);\n\t\t\t\t\tonGestureStart && _addListener(ownerDoc, \"gesturestart\", _onGestureStart);\n\t\t\t\t\tonGestureEnd && _addListener(ownerDoc, \"gestureend\", _onGestureEnd);\n\t\t\t\t\tonHover && _addListener(target, _pointerType + \"enter\", _onHover);\n\t\t\t\t\tonHoverEnd && _addListener(target, _pointerType + \"leave\", _onHoverEnd);\n\t\t\t\t\tonMove && _addListener(target, _pointerType + \"move\", _onMove);\n\t\t\t\t}\n\t\t\t\tself.isEnabled = true;\n\t\t\t\te && e.type && _onPress(e);\n\t\t\t\tonEnable && onEnable(self);\n\t\t\t}\n\t\t\treturn self;\n\t\t};\n\t\tself.disable = () => {\n\t\t\tif (self.isEnabled) {\n\t\t\t\t// only remove the _onScroll listener if there aren't any others that rely on the functionality.\n\t\t\t\t_observers.filter(o => o !== self && _isViewport(o.target)).length || _removeListener(isViewport ? ownerDoc : target, \"scroll\", _onScroll);\n\t\t\t\tif (self.isPressed) {\n\t\t\t\t\tself._vx.reset();\n\t\t\t\t\tself._vy.reset();\n\t\t\t\t\t_removeListener(isNormalizer ? target : ownerDoc, _eventTypes[1], _onDrag, true);\n\t\t\t\t}\n\t\t\t\t_removeListener(isViewport ? ownerDoc : target, \"scroll\", onScroll, capture);\n\t\t\t\t_removeListener(target, \"wheel\", _onWheel, capture);\n\t\t\t\t_removeListener(target, _eventTypes[0], _onPress, capture);\n\t\t\t\t_removeListener(ownerDoc, _eventTypes[2], _onRelease);\n\t\t\t\t_removeListener(ownerDoc, _eventTypes[3], _onRelease);\n\t\t\t\t_removeListener(target, \"click\", clickCapture, true);\n\t\t\t\t_removeListener(target, \"click\", _onClick);\n\t\t\t\t_removeListener(ownerDoc, \"gesturestart\", _onGestureStart);\n\t\t\t\t_removeListener(ownerDoc, \"gestureend\", _onGestureEnd);\n\t\t\t\t_removeListener(target, _pointerType + \"enter\", _onHover);\n\t\t\t\t_removeListener(target, _pointerType + \"leave\", _onHoverEnd);\n\t\t\t\t_removeListener(target, _pointerType + \"move\", _onMove);\n\t\t\t\tself.isEnabled = self.isPressed = self.isDragging = false;\n\t\t\t\tonDisable && onDisable(self);\n\t\t\t}\n\t\t};\n\n\t\tself.kill = () => {\n\t\t\tself.disable();\n\t\t\tlet i = _observers.indexOf(self);\n\t\t\ti >= 0 && _observers.splice(i, 1);\n\t\t\t_normalizer === self && (_normalizer = 0);\n\t\t}\n\n\t\t_observers.push(self);\n\t\tisNormalizer && _isViewport(target) && (_normalizer = self);\n\n\t\tself.enable(event);\n\t}\n\n\tget velocityX() {\n\t\treturn this._vx.getVelocity();\n\t}\n\tget velocityY() {\n\t\treturn this._vy.getVelocity();\n\t}\n\n}\n\nObserver.version = \"3.11.3\";\nObserver.create = vars => new Observer(vars);\nObserver.register = _initCore;\nObserver.getAll = () => _observers.slice();\nObserver.getById = id => _observers.filter(o => o.vars.id === id)[0];\n\n_getGSAP() && gsap.registerPlugin(Observer);\n\nexport { Observer as default, _isViewport, _scrollers, _getScrollFunc, _getProxyProp, _proxies, _getVelocityProp, _vertical, _horizontal, _getTarget };", "/*!\n * ScrollTrigger 3.11.3\n * https://greensock.com\n *\n * @license Copyright 2008-2022, GreenSock. All rights reserved.\n * Subject to the terms at https://greensock.com/standard-license or for\n * Club GreenSock members, the agreement issued with that membership.\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nimport { Observer, _getTarget, _vertical, _horizontal, _scrollers, _proxies, _getScrollFunc, _getProxyProp, _getVelocityProp } from \"./Observer.js\";\n\nlet gsap, _coreInitted, _win, _doc, _docEl, _body, _root, _resizeDelay, _toArray, _clamp, _time2, _syncInterval, _refreshing, _pointerIsDown, _transformProp, _i, _prevWidth, _prevHeight, _autoRefresh, _sort, _suppressOverwrites, _ignoreResize, _normalizer, _ignoreMobileResize, _baseScreenHeight, _baseScreenWidth, _fixIOSBug, _context, _scrollRestoration,\n\t_limitCallbacks, // if true, we'll only trigger callbacks if the active state toggles, so if you scroll immediately past both the start and end positions of a ScrollTrigger (thus inactive to inactive), neither its onEnter nor onLeave will be called. This is useful during startup.\n\t_startup = 1,\n\t_getTime = Date.now,\n\t_time1 = _getTime(),\n\t_lastScrollTime = 0,\n\t_enabled = 0,\n\t_pointerDownHandler = () => _pointerIsDown = 1,\n\t_pointerUpHandler = () => _pointerIsDown = 0,\n\t_passThrough = v => v,\n\t_round = value => Math.round(value * 100000) / 100000 || 0,\n\t_windowExists = () => typeof(window) !== \"undefined\",\n\t_getGSAP = () => gsap || (_windowExists() && (gsap = window.gsap) && gsap.registerPlugin && gsap),\n\t_isViewport = e => !!~_root.indexOf(e),\n\t_getBoundsFunc = element => _getProxyProp(element, \"getBoundingClientRect\") || (_isViewport(element) ? () => {_winOffsets.width = _win.innerWidth; _winOffsets.height = _win.innerHeight; return _winOffsets;} : () => _getBounds(element)),\n\t_getSizeFunc = (scroller, isViewport, {d, d2, a}) => (a = _getProxyProp(scroller, \"getBoundingClientRect\")) ? () => a()[d] : () => (isViewport ? _win[\"inner\" + d2] : scroller[\"client\" + d2]) || 0,\n\t_getOffsetsFunc = (element, isViewport) => !isViewport || ~_proxies.indexOf(element) ? _getBoundsFunc(element) : () => _winOffsets,\n\t_maxScroll = (element, {s, d2, d, a}) => (s = \"scroll\" + d2) && (a = _getProxyProp(element, s)) ? a() - _getBoundsFunc(element)()[d] : _isViewport(element) ? (_docEl[s] || _body[s]) - (_win[\"inner\" + d2] || _docEl[\"client\" + d2] || _body[\"client\" + d2]) : element[s] - element[\"offset\" + d2],\n\t_iterateAutoRefresh = (func, events) => {\n\t\tfor (let i = 0; i < _autoRefresh.length; i += 3) {\n\t\t\t(!events || ~events.indexOf(_autoRefresh[i+1])) && func(_autoRefresh[i], _autoRefresh[i+1], _autoRefresh[i+2]);\n\t\t}\n\t},\n\t_isString = value => typeof(value) === \"string\",\n\t_isFunction = value => typeof(value) === \"function\",\n\t_isNumber = value => typeof(value) === \"number\",\n\t_isObject = value => typeof(value) === \"object\",\n\t_callIfFunc = value => _isFunction(value) && value(),\n\t_combineFunc = (f1, f2) => () => {\n\t\tlet result1 = _callIfFunc(f1),\n\t\t\tresult2 = _callIfFunc(f2);\n\t\treturn () => {\n\t\t\t_callIfFunc(result1);\n\t\t\t_callIfFunc(result2);\n\t\t}\n\t},\n\t_endAnimation = (animation, reversed, pause) => animation && animation.progress(reversed ? 0 : 1) && pause && animation.pause(),\n\t_callback = (self, func) => {\n\t\tif (self.enabled) {\n\t\t\tlet result = func(self);\n\t\t\tresult && result.totalTime && (self.callbackAnimation = result);\n\t\t}\n\t},\n\t_abs = Math.abs,\n\t_scrollLeft = \"scrollLeft\",\n\t_scrollTop = \"scrollTop\",\n\t_left = \"left\",\n\t_top = \"top\",\n\t_right = \"right\",\n\t_bottom = \"bottom\",\n\t_width = \"width\",\n\t_height = \"height\",\n\t_Right = \"Right\",\n\t_Left = \"Left\",\n\t_Top = \"Top\",\n\t_Bottom = \"Bottom\",\n\t_padding = \"padding\",\n\t_margin = \"margin\",\n\t_Width = \"Width\",\n\t_Height = \"Height\",\n\t_px = \"px\",\n\t_getComputedStyle = element => _win.getComputedStyle(element),\n\t_makePositionable = element => { // if the element already has position: absolute or fixed, leave that, otherwise make it position: relative\n\t\tlet position = _getComputedStyle(element).position;\n\t\telement.style.position = (position === \"absolute\" || position === \"fixed\") ? position : \"relative\";\n\t},\n\t_setDefaults = (obj, defaults) => {\n\t\tfor (let p in defaults) {\n\t\t\t(p in obj) || (obj[p] = defaults[p]);\n\t\t}\n\t\treturn obj;\n\t},\n\t_getBounds = (element, withoutTransforms) => {\n\t\tlet tween = withoutTransforms && _getComputedStyle(element)[_transformProp] !== \"matrix(1, 0, 0, 1, 0, 0)\" && gsap.to(element, {x: 0, y: 0, xPercent: 0, yPercent: 0, rotation: 0, rotationX: 0, rotationY: 0, scale: 1, skewX: 0, skewY: 0}).progress(1),\n\t\t\tbounds = element.getBoundingClientRect();\n\t\ttween && tween.progress(0).kill();\n\t\treturn bounds;\n\t},\n\t_getSize = (element, {d2}) => element[\"offset\" + d2] || element[\"client\" + d2] || 0,\n\t_getLabelRatioArray = timeline => {\n\t\tlet a = [],\n\t\t\tlabels = timeline.labels,\n\t\t\tduration = timeline.duration(),\n\t\t\tp;\n\t\tfor (p in labels) {\n\t\t\ta.push(labels[p] / duration);\n\t\t}\n\t\treturn a;\n\t},\n\t_getClosestLabel = animation => value => gsap.utils.snap(_getLabelRatioArray(animation), value),\n\t_snapDirectional = snapIncrementOrArray => {\n\t\tlet snap = gsap.utils.snap(snapIncrementOrArray),\n\t\t\ta = Array.isArray(snapIncrementOrArray) && snapIncrementOrArray.slice(0).sort((a, b) => a - b);\n\t\treturn a ? (value, direction, threshold= 1e-3) => {\n\t\t\tlet i;\n\t\t\tif (!direction) {\n\t\t\t\treturn snap(value);\n\t\t\t}\n\t\t\tif (direction > 0) {\n\t\t\t\tvalue -= threshold; // to avoid rounding errors. If we're too strict, it might snap forward, then immediately again, and again.\n\t\t\t\tfor (i = 0; i < a.length; i++) {\n\t\t\t\t\tif (a[i] >= value) {\n\t\t\t\t\t\treturn a[i];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn a[i-1];\n\t\t\t} else {\n\t\t\t\ti = a.length;\n\t\t\t\tvalue += threshold;\n\t\t\t\twhile (i--) {\n\t\t\t\t\tif (a[i] <= value) {\n\t\t\t\t\t\treturn a[i];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn a[0];\n\t\t} : (value, direction, threshold= 1e-3) => {\n\t\t\tlet snapped = snap(value);\n\t\t\treturn !direction || Math.abs(snapped - value) < threshold || ((snapped - value < 0) === direction < 0) ? snapped : snap(direction < 0 ? value - snapIncrementOrArray : value + snapIncrementOrArray);\n\t\t};\n\t},\n\t_getLabelAtDirection = timeline => (value, st) => _snapDirectional(_getLabelRatioArray(timeline))(value, st.direction),\n\t_multiListener = (func, element, types, callback) => types.split(\",\").forEach(type => func(element, type, callback)),\n\t_addListener = (element, type, func, nonPassive, capture) => element.addEventListener(type, func, {passive: !nonPassive, capture: !!capture}),\n\t_removeListener = (element, type, func, capture) => element.removeEventListener(type, func, !!capture),\n\t_wheelListener = (func, el, scrollFunc) => scrollFunc && scrollFunc.wheelHandler && func(el, \"wheel\", scrollFunc),\n\t_markerDefaults = {startColor: \"green\", endColor: \"red\", indent: 0, fontSize: \"16px\", fontWeight:\"normal\"},\n\t_defaults = {toggleActions: \"play\", anticipatePin: 0},\n\t_keywords = {top: 0, left: 0, center: 0.5, bottom: 1, right: 1},\n\t_offsetToPx = (value, size) => {\n\t\tif (_isString(value)) {\n\t\t\tlet eqIndex = value.indexOf(\"=\"),\n\t\t\t\trelative = ~eqIndex ? +(value.charAt(eqIndex-1) + 1) * parseFloat(value.substr(eqIndex + 1)) : 0;\n\t\t\tif (~eqIndex) {\n\t\t\t\t(value.indexOf(\"%\") > eqIndex) && (relative *= size / 100);\n\t\t\t\tvalue = value.substr(0, eqIndex-1);\n\t\t\t}\n\t\t\tvalue = relative + ((value in _keywords) ? _keywords[value] * size : ~value.indexOf(\"%\") ? parseFloat(value) * size / 100 : parseFloat(value) || 0);\n\t\t}\n\t\treturn value;\n\t},\n\t_createMarker = (type, name, container, direction, {startColor, endColor, fontSize, indent, fontWeight}, offset, matchWidthEl, containerAnimation) => {\n\t\tlet e = _doc.createElement(\"div\"),\n\t\t\tuseFixedPosition = _isViewport(container) || _getProxyProp(container, \"pinType\") === \"fixed\",\n\t\t\tisScroller = type.indexOf(\"scroller\") !== -1,\n\t\t\tparent = useFixedPosition ? _body : container,\n\t\t\tisStart = type.indexOf(\"start\") !== -1,\n\t\t\tcolor = isStart ? startColor : endColor,\n\t\t\tcss = \"border-color:\" + color + \";font-size:\" + fontSize + \";color:\" + color + \";font-weight:\" + fontWeight + \";pointer-events:none;white-space:nowrap;font-family:sans-serif,Arial;z-index:1000;padding:4px 8px;border-width:0;border-style:solid;\";\n\t\tcss += \"position:\" + ((isScroller || containerAnimation) && useFixedPosition ? \"fixed;\" : \"absolute;\");\n\t\t(isScroller || containerAnimation || !useFixedPosition) && (css += (direction === _vertical ? _right : _bottom) + \":\" + (offset + parseFloat(indent)) + \"px;\");\n\t\tmatchWidthEl && (css += \"box-sizing:border-box;text-align:left;width:\" + matchWidthEl.offsetWidth + \"px;\");\n\t\te._isStart = isStart;\n\t\te.setAttribute(\"class\", \"gsap-marker-\" + type + (name ? \" marker-\" + name : \"\"));\n\t\te.style.cssText = css;\n\t\te.innerText = name || name === 0 ? type + \"-\" + name : type;\n\t\tparent.children[0] ? parent.insertBefore(e, parent.children[0]) : parent.appendChild(e);\n\t\te._offset = e[\"offset\" + direction.op.d2];\n\t\t_positionMarker(e, 0, direction, isStart);\n\t\treturn e;\n\t},\n\t_positionMarker = (marker, start, direction, flipped) => {\n\t\tlet vars = {display: \"block\"},\n\t\t\tside = direction[flipped ? \"os2\" : \"p2\"],\n\t\t\toppositeSide = direction[flipped ? \"p2\" : \"os2\"];\n\t\tmarker._isFlipped = flipped;\n\t\tvars[direction.a + \"Percent\"] = flipped ? -100 : 0;\n\t\tvars[direction.a] = flipped ? \"1px\" : 0;\n\t\tvars[\"border\" + side + _Width] = 1;\n\t\tvars[\"border\" + oppositeSide + _Width] = 0;\n\t\tvars[direction.p] = start + \"px\";\n\t\tgsap.set(marker, vars);\n\t},\n\t_triggers = [],\n\t_ids = {},\n\t_rafID,\n\t_sync = () => _getTime() - _lastScrollTime > 34 && (_rafID || (_rafID = requestAnimationFrame(_updateAll))),\n\t_onScroll = () => { // previously, we tried to optimize performance by batching/deferring to the next requestAnimationFrame(), but discovered that Safari has a few bugs that make this unworkable (especially on iOS). See https://codepen.io/GreenSock/pen/16c435b12ef09c38125204818e7b45fc?editors=0010 and https://codepen.io/GreenSock/pen/JjOxYpQ/3dd65ccec5a60f1d862c355d84d14562?editors=0010 and https://codepen.io/GreenSock/pen/ExbrPNa/087cef197dc35445a0951e8935c41503?editors=0010\n\t\tif (!_normalizer || !_normalizer.isPressed || _normalizer.startX > _body.clientWidth) { // if the user is dragging the scrollbar, allow it.\n\t\t\t_scrollers.cache++\n\t\t\tif (_normalizer) {\n\t\t\t\t_rafID || (_rafID = requestAnimationFrame(_updateAll));\n\t\t\t} else {\n\t\t\t\t_updateAll(); // Safari in particular (on desktop) NEEDS the immediate update rather than waiting for a requestAnimationFrame() whereas iOS seems to benefit from waiting for the requestAnimationFrame() tick, at least when normalizing. See https://codepen.io/GreenSock/pen/qBYozqO?editors=0110\n\t\t\t}\n\t\t\t_lastScrollTime || _dispatch(\"scrollStart\");\n\t\t\t_lastScrollTime = _getTime();\n\t\t}\n\t},\n\t_setBaseDimensions = () => {\n\t\t_baseScreenWidth = _win.innerWidth;\n\t\t_baseScreenHeight = _win.innerHeight;\n\t},\n\t_onResize = () => {\n\t\t_scrollers.cache++;\n\t\t!_refreshing && !_ignoreResize && !_doc.fullscreenElement && !_doc.webkitFullscreenElement && (!_ignoreMobileResize || _baseScreenWidth !== _win.innerWidth || Math.abs(_win.innerHeight - _baseScreenHeight) > _win.innerHeight * 0.25) && _resizeDelay.restart(true);\n\t}, // ignore resizes triggered by refresh()\n\t_listeners = {},\n\t_emptyArray = [],\n\t_softRefresh = () => _removeListener(ScrollTrigger, \"scrollEnd\", _softRefresh) || _refreshAll(true),\n\t_dispatch = type => (_listeners[type] && _listeners[type].map(f => f())) || _emptyArray,\n\t_savedStyles = [], // when ScrollTrigger.saveStyles() is called, the inline styles are recorded in this Array in a sequential format like [element, cssText, gsCache, media]. This keeps it very memory-efficient and fast to iterate through.\n\t_revertRecorded = media => {\n\t\tfor (let i = 0; i < _savedStyles.length; i+=5) {\n\t\t\tif (!media || _savedStyles[i+4] && _savedStyles[i+4].query === media) {\n\t\t\t\t_savedStyles[i].style.cssText = _savedStyles[i+1];\n\t\t\t\t_savedStyles[i].getBBox && _savedStyles[i].setAttribute(\"transform\", _savedStyles[i+2] || \"\");\n\t\t\t\t_savedStyles[i+3].uncache = 1;\n\t\t\t}\n\t\t}\n\t},\n\t_revertAll = (kill, media) => {\n\t\tlet trigger;\n\t\tfor (_i = 0; _i < _triggers.length; _i++) {\n\t\t\ttrigger = _triggers[_i];\n\t\t\tif (trigger && (!media || trigger._ctx === media)) {\n\t\t\t\tif (kill) {\n\t\t\t\t\ttrigger.kill(1);\n\t\t\t\t} else {\n\t\t\t\t\ttrigger.revert(true, true);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tmedia && _revertRecorded(media);\n\t\tmedia || _dispatch(\"revert\");\n\t},\n\t_clearScrollMemory = (scrollRestoration, force) => { // zero-out all the recorded scroll positions. Don't use _triggers because if, for example, .matchMedia() is used to create some ScrollTriggers and then the user resizes and it removes ALL ScrollTriggers, and then go back to a size where there are ScrollTriggers, it would have kept the position(s) saved from the initial state.\n\t\t_scrollers.cache++;\n\t\t(force || !_refreshingAll) && _scrollers.forEach(obj => _isFunction(obj) && obj.cacheID++ && (obj.rec = 0));\n\t\t_isString(scrollRestoration) && (_win.history.scrollRestoration = _scrollRestoration = scrollRestoration);\n\t},\n\t_refreshingAll,\n\t_refreshID = 0,\n\t_queueRefreshID,\n\t_queueRefreshAll = () => { // we don't want to call _refreshAll() every time we create a new ScrollTrigger (for performance reasons) - it's better to batch them. Some frameworks dynamically load content and we can't rely on the window's \"load\" or \"DOMContentLoaded\" events to trigger it.\n\t\tif (_queueRefreshID !== _refreshID) {\n\t\t\tlet id = _queueRefreshID = _refreshID;\n\t\t\trequestAnimationFrame(() => id === _refreshID && _refreshAll(true));\n\t\t}\n\t},\n\t_refreshAll = (force, skipRevert) => {\n\t\tif (_lastScrollTime && !force) {\n\t\t\t_addListener(ScrollTrigger, \"scrollEnd\", _softRefresh);\n\t\t\treturn;\n\t\t}\n\t\t_refreshingAll = ScrollTrigger.isRefreshing = true;\n\t\t_scrollers.forEach(obj => _isFunction(obj) && obj.cacheID++ && (obj.rec = obj())); // force the clearing of the cache because some browsers take a little while to dispatch the \"scroll\" event and the user may have changed the scroll position and then called ScrollTrigger.refresh() right away\n\t\tlet refreshInits = _dispatch(\"refreshInit\");\n\t\t_sort && ScrollTrigger.sort();\n\t\tskipRevert || _revertAll();\n\t\t_scrollers.forEach(obj => {\n\t\t\tif (_isFunction(obj)) {\n\t\t\t\tobj.smooth && (obj.target.style.scrollBehavior = \"auto\"); // smooth scrolling interferes\n\t\t\t\tobj(0);\n\t\t\t}\n\t\t});\n\t\t_triggers.slice(0).forEach(t => t.refresh()) // don't loop with _i because during a refresh() someone could call ScrollTrigger.update() which would iterate through _i resulting in a skip.\n\t\t_triggers.forEach((t, i) => { // nested pins (pinnedContainer) with pinSpacing may expand the container, so we must accommodate that here.\n\t\t\tif (t._subPinOffset && t.pin) {\n\t\t\t\tlet prop = t.vars.horizontal ? \"offsetWidth\" : \"offsetHeight\",\n\t\t\t\t\toriginal = t.pin[prop];\n\t\t\t\tt.revert(true, 1);\n\t\t\t\tt.adjustPinSpacing(t.pin[prop] - original);\n\t\t\t\tt.revert(false, 1);\n\t\t\t}\n\t\t});\n\t\t_triggers.forEach(t => t.vars.end === \"max\" && t.setPositions(t.start, Math.max(t.start+1, _maxScroll(t.scroller, t._dir)))); // the scroller's max scroll position may change after all the ScrollTriggers refreshed (like pinning could push it down), so we need to loop back and correct any with end: \"max\".\n\t\trefreshInits.forEach(result => result && result.render && result.render(-1)); // if the onRefreshInit() returns an animation (typically a gsap.set()), revert it. This makes it easy to put things in a certain spot before refreshing for measurement purposes, and then put things back.\n\t\t_scrollers.forEach(obj => {\n\t\t\tif (_isFunction(obj)) {\n\t\t\t\tobj.smooth && requestAnimationFrame(() => obj.target.style.scrollBehavior = \"smooth\");\n\t\t\t\tobj.rec && obj(obj.rec);\n\t\t\t}\n\t\t});\n\t\t_clearScrollMemory(_scrollRestoration, 1);\n\t\t_resizeDelay.pause();\n\t\t_refreshID++;\n\t\t_updateAll(2);\n\t\t_triggers.forEach(t => _isFunction(t.vars.onRefresh) && t.vars.onRefresh(t));\n\t\t_refreshingAll = ScrollTrigger.isRefreshing = false;\n\t\t_dispatch(\"refresh\");\n\t},\n\t_lastScroll = 0,\n\t_direction = 1,\n\t_primary,\n\t_updateAll = (force) => {\n\t\tif (!_refreshingAll || force === 2) {\n\t\t\tScrollTrigger.isUpdating = true;\n\t\t\t_primary && _primary.update(0); // ScrollSmoother uses refreshPriority -9999 to become the primary that gets updated before all others because it affects the scroll position.\n\t\t\tlet l = _triggers.length,\n\t\t\t\ttime = _getTime(),\n\t\t\t\trecordVelocity = time - _time1 >= 50,\n\t\t\t\tscroll = l && _triggers[0].scroll();\n\t\t\t_direction = _lastScroll > scroll ? -1 : 1;\n\t\t\t_lastScroll = scroll;\n\t\t\tif (recordVelocity) {\n\t\t\t\tif (_lastScrollTime && !_pointerIsDown && time - _lastScrollTime > 200) {\n\t\t\t\t\t_lastScrollTime = 0;\n\t\t\t\t\t_dispatch(\"scrollEnd\");\n\t\t\t\t}\n\t\t\t\t_time2 = _time1;\n\t\t\t\t_time1 = time;\n\t\t\t}\n\t\t\tif (_direction < 0) {\n\t\t\t\t_i = l;\n\t\t\t\twhile (_i-- > 0) {\n\t\t\t\t\t_triggers[_i] && _triggers[_i].update(0, recordVelocity);\n\t\t\t\t}\n\t\t\t\t_direction = 1;\n\t\t\t} else {\n\t\t\t\tfor (_i = 0; _i < l; _i++) {\n\t\t\t\t\t_triggers[_i] && _triggers[_i].update(0, recordVelocity);\n\t\t\t\t}\n\t\t\t}\n\t\t\tScrollTrigger.isUpdating = false;\n\t\t}\n\t\t_rafID = 0;\n\t},\n\t_propNamesToCopy = [_left, _top, _bottom, _right, _margin + _Bottom, _margin + _Right, _margin + _Top, _margin + _Left, \"display\", \"flexShrink\", \"float\", \"zIndex\", \"gridColumnStart\", \"gridColumnEnd\", \"gridRowStart\", \"gridRowEnd\", \"gridArea\", \"justifySelf\", \"alignSelf\", \"placeSelf\", \"order\"],\n\t_stateProps = _propNamesToCopy.concat([_width, _height, \"boxSizing\", \"max\" + _Width, \"max\" + _Height, \"position\", _margin, _padding, _padding + _Top, _padding + _Right, _padding + _Bottom, _padding + _Left]),\n\t_swapPinOut = (pin, spacer, state) => {\n\t\t_setState(state);\n\t\tlet cache = pin._gsap;\n\t\tif (cache.spacerIsNative) {\n\t\t\t_setState(cache.spacerState);\n\t\t} else if (pin._gsap.swappedIn) {\n\t\t\tlet parent = spacer.parentNode;\n\t\t\tif (parent) {\n\t\t\t\tparent.insertBefore(pin, spacer);\n\t\t\t\tparent.removeChild(spacer);\n\t\t\t}\n\t\t}\n\t\tpin._gsap.swappedIn = false;\n\t},\n\t_swapPinIn = (pin, spacer, cs, spacerState) => {\n\t\tif (!pin._gsap.swappedIn) {\n\t\t\tlet i = _propNamesToCopy.length,\n\t\t\t\tspacerStyle = spacer.style,\n\t\t\t\tpinStyle = pin.style,\n\t\t\t\tp;\n\t\t\twhile (i--) {\n\t\t\t\tp = _propNamesToCopy[i];\n\t\t\t\tspacerStyle[p] = cs[p];\n\t\t\t}\n\t\t\tspacerStyle.position = cs.position === \"absolute\" ? \"absolute\" : \"relative\";\n\t\t\t(cs.display === \"inline\") && (spacerStyle.display = \"inline-block\");\n\t\t\tpinStyle[_bottom] = pinStyle[_right] = \"auto\";\n\t\t\tspacerStyle.flexBasis = cs.flexBasis || \"auto\";\n\t\t\tspacerStyle.overflow = \"visible\";\n\t\t\tspacerStyle.boxSizing = \"border-box\";\n\t\t\tspacerStyle[_width] = _getSize(pin, _horizontal) + _px;\n\t\t\tspacerStyle[_height] = _getSize(pin, _vertical) + _px;\n\t\t\tspacerStyle[_padding] = pinStyle[_margin] = pinStyle[_top] = pinStyle[_left] = \"0\";\n\t\t\t_setState(spacerState);\n\t\t\tpinStyle[_width] = pinStyle[\"max\" + _Width] = cs[_width];\n\t\t\tpinStyle[_height] = pinStyle[\"max\" + _Height] = cs[_height];\n\t\t\tpinStyle[_padding] = cs[_padding];\n\t\t\tif (pin.parentNode !== spacer) {\n\t\t\t\tpin.parentNode.insertBefore(spacer, pin);\n\t\t\t\tspacer.appendChild(pin);\n\t\t\t}\n\t\t\tpin._gsap.swappedIn = true;\n\t\t}\n\t},\n\t_capsExp = /([A-Z])/g,\n\t_setState = state => {\n\t\tif (state) {\n\t\t\tlet style = state.t.style,\n\t\t\t\tl = state.length,\n\t\t\t\ti = 0,\n\t\t\t\tp, value;\n\t\t\t(state.t._gsap || gsap.core.getCache(state.t)).uncache = 1; // otherwise transforms may be off\n\t\t\tfor (; i < l; i +=2) {\n\t\t\t\tvalue = state[i+1];\n\t\t\t\tp = state[i];\n\t\t\t\tif (value) {\n\t\t\t\t\tstyle[p] = value;\n\t\t\t\t} else if (style[p]) {\n\t\t\t\t\tstyle.removeProperty(p.replace(_capsExp, \"-$1\").toLowerCase());\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t},\n\t_getState = element => { // returns an Array with alternating values like [property, value, property, value] and a \"t\" property pointing to the target (element). Makes it fast and cheap.\n\t\tlet l = _stateProps.length,\n\t\t\tstyle = element.style,\n\t\t\tstate = [],\n\t\t\ti = 0;\n\t\tfor (; i < l; i++) {\n\t\t\tstate.push(_stateProps[i], style[_stateProps[i]]);\n\t\t}\n\t\tstate.t = element;\n\t\treturn state;\n\t},\n\t_copyState = (state, override, omitOffsets) => {\n\t\tlet result = [],\n\t\t\tl = state.length,\n\t\t\ti = omitOffsets ? 8 : 0, // skip top, left, right, bottom if omitOffsets is true\n\t\t\tp;\n\t\tfor (; i < l; i += 2) {\n\t\t\tp = state[i];\n\t\t\tresult.push(p, (p in override) ? override[p] : state[i+1]);\n\t\t}\n\t\tresult.t = state.t;\n\t\treturn result;\n\t},\n\t_winOffsets = {left:0, top:0},\n\t// // potential future feature (?) Allow users to calculate where a trigger hits (scroll position) like getScrollPosition(\"#id\", \"top bottom\")\n\t// _getScrollPosition = (trigger, position, {scroller, containerAnimation, horizontal}) => {\n\t// \tscroller = _getTarget(scroller || _win);\n\t// \tlet direction = horizontal ? _horizontal : _vertical,\n\t// \t\tisViewport = _isViewport(scroller);\n\t// \t_getSizeFunc(scroller, isViewport, direction);\n\t// \treturn _parsePosition(position, _getTarget(trigger), _getSizeFunc(scroller, isViewport, direction)(), direction, _getScrollFunc(scroller, direction)(), 0, 0, 0, _getOffsetsFunc(scroller, isViewport)(), isViewport ? 0 : parseFloat(_getComputedStyle(scroller)[\"border\" + direction.p2 + _Width]) || 0, 0, containerAnimation ? containerAnimation.duration() : _maxScroll(scroller), containerAnimation);\n\t// },\n\t_parsePosition = (value, trigger, scrollerSize, direction, scroll, marker, markerScroller, self, scrollerBounds, borderWidth, useFixedPosition, scrollerMax, containerAnimation) => {\n\t\t_isFunction(value) && (value = value(self));\n\t\tif (_isString(value) && value.substr(0,3) === \"max\") {\n\t\t\tvalue = scrollerMax + (value.charAt(4) === \"=\" ? _offsetToPx(\"0\" + value.substr(3), scrollerSize) : 0);\n\t\t}\n\t\tlet time = containerAnimation ? containerAnimation.time() : 0,\n\t\t\tp1, p2, element;\n\t\tcontainerAnimation && containerAnimation.seek(0);\n\t\tif (!_isNumber(value)) {\n\t\t\t_isFunction(trigger) && (trigger = trigger(self));\n\t\t\tlet offsets = (value || \"0\").split(\" \"),\n\t\t\t\tbounds, localOffset, globalOffset, display;\n\t\t\telement = _getTarget(trigger) || _body;\n\t\t\tbounds = _getBounds(element) || {};\n\t\t\tif ((!bounds || (!bounds.left && !bounds.top)) && _getComputedStyle(element).display === \"none\") { // if display is \"none\", it won't report getBoundingClientRect() properly\n\t\t\t\tdisplay = element.style.display;\n\t\t\t\telement.style.display = \"block\";\n\t\t\t\tbounds = _getBounds(element);\n\t\t\t\tdisplay ? (element.style.display = display) : element.style.removeProperty(\"display\");\n\t\t\t}\n\t\t\tlocalOffset = _offsetToPx(offsets[0], bounds[direction.d]);\n\t\t\tglobalOffset = _offsetToPx(offsets[1] || \"0\", scrollerSize);\n\t\t\tvalue = bounds[direction.p] - scrollerBounds[direction.p] - borderWidth + localOffset + scroll - globalOffset;\n\t\t\tmarkerScroller && _positionMarker(markerScroller, globalOffset, direction, (scrollerSize - globalOffset < 20 || (markerScroller._isStart && globalOffset > 20)));\n\t\t\tscrollerSize -= scrollerSize - globalOffset; // adjust for the marker\n\t\t} else if (markerScroller) {\n\t\t\t_positionMarker(markerScroller, scrollerSize, direction, true);\n\t\t}\n\t\tif (marker) {\n\t\t\tlet position = value + scrollerSize,\n\t\t\t\tisStart = marker._isStart;\n\t\t\tp1 = \"scroll\" + direction.d2;\n\t\t\t_positionMarker(marker, position, direction, (isStart && position > 20) || (!isStart && (useFixedPosition ? Math.max(_body[p1], _docEl[p1]) : marker.parentNode[p1]) <= position + 1));\n\t\t\tif (useFixedPosition) {\n\t\t\t\tscrollerBounds = _getBounds(markerScroller);\n\t\t\t\tuseFixedPosition && (marker.style[direction.op.p] = (scrollerBounds[direction.op.p] - direction.op.m - marker._offset) + _px);\n\t\t\t}\n\t\t}\n\t\tif (containerAnimation && element) {\n\t\t\tp1 = _getBounds(element);\n\t\t\tcontainerAnimation.seek(scrollerMax);\n\t\t\tp2 = _getBounds(element);\n\t\t\tcontainerAnimation._caScrollDist = p1[direction.p] - p2[direction.p];\n\t\t\tvalue = value / (containerAnimation._caScrollDist) * scrollerMax;\n\t\t}\n\t\tcontainerAnimation && containerAnimation.seek(time);\n\t\treturn containerAnimation ? value : Math.round(value);\n\t},\n\t_prefixExp = /(webkit|moz|length|cssText|inset)/i,\n\t_reparent = (element, parent, top, left) => {\n\t\tif (element.parentNode !== parent) {\n\t\t\tlet style = element.style,\n\t\t\t\tp, cs;\n\t\t\tif (parent === _body) {\n\t\t\t\telement._stOrig = style.cssText; // record original inline styles so we can revert them later\n\t\t\t\tcs = _getComputedStyle(element);\n\t\t\t\tfor (p in cs) { // must copy all relevant styles to ensure that nothing changes visually when we reparent to the <body>. Skip the vendor prefixed ones.\n\t\t\t\t\tif (!+p && !_prefixExp.test(p) && cs[p] && typeof style[p] === \"string\" && p !== \"0\") {\n\t\t\t\t\t\tstyle[p] = cs[p];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tstyle.top = top;\n\t\t\t\tstyle.left = left;\n\t\t\t} else {\n\t\t\t\tstyle.cssText = element._stOrig;\n\t\t\t}\n\t\t\tgsap.core.getCache(element).uncache = 1;\n\t\t\tparent.appendChild(element);\n\t\t}\n\t},\n\t// _mergeAnimations = animations => {\n\t// \tlet tl = gsap.timeline({smoothChildTiming: true}).startTime(Math.min(...animations.map(a => a.globalTime(0))));\n\t// \tanimations.forEach(a => {let time = a.totalTime(); tl.add(a); a.totalTime(time); });\n\t// \ttl.smoothChildTiming = false;\n\t// \treturn tl;\n\t// },\n\n\t// returns a function that can be used to tween the scroll position in the direction provided, and when doing so it'll add a .tween property to the FUNCTION itself, and remove it when the tween completes or gets killed. This gives us a way to have multiple ScrollTriggers use a central function for any given scroller and see if there's a scroll tween running (which would affect if/how things get updated)\n\t_getTweenCreator = (scroller, direction) => {\n\t\tlet getScroll = _getScrollFunc(scroller, direction),\n\t\t\tprop = \"_scroll\" + direction.p2, // add a tweenable property to the scroller that's a getter/setter function, like _scrollTop or _scrollLeft. This way, if someone does gsap.killTweensOf(scroller) it'll kill the scroll tween.\n\t\t\tlastScroll1, lastScroll2,\n\t\t\tgetTween = (scrollTo, vars, initialValue, change1, change2) => {\n\t\t\t\tlet tween = getTween.tween,\n\t\t\t\t\tonComplete = vars.onComplete,\n\t\t\t\t\tmodifiers = {};\n\t\t\t\tinitialValue = initialValue || getScroll();\n\t\t\t\tchange2 = (change1 && change2) || 0; // if change1 is 0, we set that to the difference and ignore change2. Otherwise, there would be a compound effect.\n\t\t\t\tchange1 = change1 || (scrollTo - initialValue);\n\t\t\t\ttween && tween.kill();\n\t\t\t\tlastScroll1 = Math.round(initialValue);\n\t\t\t\tvars[prop] = scrollTo;\n\t\t\t\tvars.modifiers = modifiers;\n\t\t\t\tmodifiers[prop] = value => {\n\t\t\t\t\tvalue = Math.round(getScroll()); // round because in some [very uncommon] Windows environments, it can get reported with decimals even though it was set without.\n\t\t\t\t\tif (value !== lastScroll1 && value !== lastScroll2 && Math.abs(value - lastScroll1) > 3 && Math.abs(value - lastScroll2) > 3) { // if the user scrolls, kill the tween. iOS Safari intermittently misreports the scroll position, it may be the most recently-set one or the one before that! When Safari is zoomed (CMD-+), it often misreports as 1 pixel off too! So if we set the scroll position to 125, for example, it'll actually report it as 124.\n\t\t\t\t\t\ttween.kill();\n\t\t\t\t\t\tgetTween.tween = 0;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tvalue = initialValue + change1 * tween.ratio + change2 * tween.ratio * tween.ratio;\n\t\t\t\t\t}\n\t\t\t\t\tlastScroll2 = lastScroll1;\n\t\t\t\t\treturn (lastScroll1 = Math.round(value));\n\t\t\t\t};\n\t\t\t\tvars.onComplete = () => {\n\t\t\t\t\tgetTween.tween = 0;\n\t\t\t\t\tonComplete && onComplete.call(tween);\n\t\t\t\t};\n\t\t\t\ttween = getTween.tween = gsap.to(scroller, vars);\n\t\t\t\treturn tween;\n\t\t\t};\n\t\tscroller[prop] = getScroll;\n\t\tgetScroll.wheelHandler = () => getTween.tween && getTween.tween.kill() && (getTween.tween = 0);\n\t\t_addListener(scroller, \"wheel\", getScroll.wheelHandler); // Windows machines handle mousewheel scrolling in chunks (like \"3 lines per scroll\") meaning the typical strategy for cancelling the scroll isn't as sensitive. It's much more likely to match one of the previous 2 scroll event positions. So we kill any snapping as soon as there's a wheel event.\n\t\treturn getTween;\n\t};\n\n\n\n\nexport class ScrollTrigger {\n\n\tconstructor(vars, animation) {\n\t\t_coreInitted || ScrollTrigger.register(gsap) || console.warn(\"Please gsap.registerPlugin(ScrollTrigger)\");\n\t\tthis.init(vars, animation);\n\t}\n\n\tinit(vars, animation) {\n\t\tthis.progress = this.start = 0;\n\t\tthis.vars && this.kill(true, true); // in case it's being initted again\n\t\tif (!_enabled) {\n\t\t\tthis.update = this.refresh = this.kill = _passThrough;\n\t\t\treturn;\n\t\t}\n\t\tvars = _setDefaults((_isString(vars) || _isNumber(vars) || vars.nodeType) ? {trigger: vars} : vars, _defaults);\n\t\tlet {onUpdate, toggleClass, id, onToggle, onRefresh, scrub, trigger, pin, pinSpacing, invalidateOnRefresh, anticipatePin, onScrubComplete, onSnapComplete, once, snap, pinReparent, pinSpacer, containerAnimation, fastScrollEnd, preventOverlaps} = vars,\n\t\t\tdirection = vars.horizontal || (vars.containerAnimation && vars.horizontal !== false) ? _horizontal : _vertical,\n\t\t\tisToggle = !scrub && scrub !== 0,\n\t\t\tscroller = _getTarget(vars.scroller || _win),\n\t\t\tscrollerCache = gsap.core.getCache(scroller),\n\t\t\tisViewport = _isViewport(scroller),\n\t\t\tuseFixedPosition = (\"pinType\" in vars ? vars.pinType : _getProxyProp(scroller, \"pinType\") || (isViewport && \"fixed\")) === \"fixed\",\n\t\t\tcallbacks = [vars.onEnter, vars.onLeave, vars.onEnterBack, vars.onLeaveBack],\n\t\t\ttoggleActions = isToggle && vars.toggleActions.split(\" \"),\n\t\t\tmarkers = \"markers\" in vars ? vars.markers : _defaults.markers,\n\t\t\tborderWidth = isViewport ? 0 : parseFloat(_getComputedStyle(scroller)[\"border\" + direction.p2 + _Width]) || 0,\n\t\t\tself = this,\n\t\t\tonRefreshInit = vars.onRefreshInit && (() => vars.onRefreshInit(self)),\n\t\t\tgetScrollerSize = _getSizeFunc(scroller, isViewport, direction),\n\t\t\tgetScrollerOffsets = _getOffsetsFunc(scroller, isViewport),\n\t\t\tlastSnap = 0,\n\t\t\tlastRefresh = 0,\n\t\t\tscrollFunc = _getScrollFunc(scroller, direction),\n\t\t\ttweenTo, pinCache, snapFunc, scroll1, scroll2, start, end, markerStart, markerEnd, markerStartTrigger, markerEndTrigger, markerVars,\n\t\t\tchange, pinOriginalState, pinActiveState, pinState, spacer, offset, pinGetter, pinSetter, pinStart, pinChange, spacingStart, spacerState, markerStartSetter,\n\t\t\tmarkerEndSetter, cs, snap1, snap2, scrubTween, scrubSmooth, snapDurClamp, snapDelayedCall, prevProgress, prevScroll, prevAnimProgress, caMarkerSetter, customRevertReturn;\n\n\t\t_context(self);\n\t\tself._dir = direction;\n\t\tanticipatePin *= 45;\n\t\tself.scroller = scroller;\n\t\tself.scroll = containerAnimation ? containerAnimation.time.bind(containerAnimation) : scrollFunc;\n\t\tscroll1 = scrollFunc();\n\t\tself.vars = vars;\n\t\tanimation = animation || vars.animation;\n\t\tif (\"refreshPriority\" in vars) {\n\t\t\t_sort = 1;\n\t\t\tvars.refreshPriority === -9999 && (_primary = self); // used by ScrollSmoother\n\t\t}\n\t\tscrollerCache.tweenScroll = scrollerCache.tweenScroll || {\n\t\t\ttop: _getTweenCreator(scroller, _vertical),\n\t\t\tleft: _getTweenCreator(scroller, _horizontal)\n\t\t};\n\t\tself.tweenTo = tweenTo = scrollerCache.tweenScroll[direction.p];\n\t\tself.scrubDuration = value => {\n\t\t\tscrubSmooth = _isNumber(value) && value;\n\t\t\tif (!scrubSmooth) {\n\t\t\t\tscrubTween && scrubTween.progress(1).kill();\n\t\t\t\tscrubTween = 0;\n\t\t\t} else {\n\t\t\t\tscrubTween ? scrubTween.duration(value) : (scrubTween = gsap.to(animation, {ease: \"expo\", totalProgress: \"+=0.001\", duration: scrubSmooth, paused: true, onComplete: () => onScrubComplete && onScrubComplete(self)}));\n\t\t\t}\n\t\t};\n\t\tif (animation) {\n\t\t\tanimation.vars.lazy = false;\n\t\t\tanimation._initted || (animation.vars.immediateRender !== false && vars.immediateRender !== false && animation.duration() && animation.render(0, true, true));\n\t\t\tself.animation = animation.pause();\n\t\t\tanimation.scrollTrigger = self;\n\t\t\tself.scrubDuration(scrub);\n\t\t\tsnap1 = 0;\n\t\t\tid || (id = animation.vars.id);\n\t\t}\n\t\t_triggers.push(self);\n\t\tif (snap) {\n\t\t\t// TODO: potential idea: use legitimate CSS scroll snapping by pushing invisible elements into the DOM that serve as snap positions, and toggle the document.scrollingElement.style.scrollSnapType onToggle. See https://codepen.io/GreenSock/pen/JjLrgWM for a quick proof of concept.\n\t\t\tif (!_isObject(snap) || snap.push) {\n\t\t\t\tsnap = {snapTo: snap};\n\t\t\t}\n\t\t\t(\"scrollBehavior\" in _body.style) && gsap.set(isViewport ? [_body, _docEl] : scroller, {scrollBehavior: \"auto\"}); // smooth scrolling doesn't work with snap.\n\t\t\t_scrollers.forEach(o => _isFunction(o) && o.target === (isViewport ? _doc.scrollingElement || _docEl : scroller) && (o.smooth = false)); // note: set smooth to false on both the vertical and horizontal scroll getters/setters\n\t\t\tsnapFunc = _isFunction(snap.snapTo) ? snap.snapTo : snap.snapTo === \"labels\" ? _getClosestLabel(animation) : snap.snapTo === \"labelsDirectional\" ? _getLabelAtDirection(animation) : snap.directional !== false ? (value, st) => _snapDirectional(snap.snapTo)(value, _getTime() - lastRefresh < 500 ? 0 : st.direction) : gsap.utils.snap(snap.snapTo);\n\t\t\tsnapDurClamp = snap.duration || {min: 0.1, max: 2};\n\t\t\tsnapDurClamp = _isObject(snapDurClamp) ? _clamp(snapDurClamp.min, snapDurClamp.max) : _clamp(snapDurClamp, snapDurClamp);\n\t\t\tsnapDelayedCall = gsap.delayedCall(snap.delay || (scrubSmooth / 2) || 0.1, () => {\n\t\t\t\tlet scroll = scrollFunc(),\n\t\t\t\t\trefreshedRecently = _getTime() - lastRefresh < 500,\n\t\t\t\t\ttween = tweenTo.tween;\n\t\t\t\tif ((refreshedRecently || Math.abs(self.getVelocity()) < 10) && !tween && !_pointerIsDown && lastSnap !== scroll) {\n\t\t\t\t\tlet progress = (scroll - start) / change, // don't use self.progress because this might run between the refresh() and when the scroll position updates and self.progress is set properly in the update() method.\n\t\t\t\t\t\ttotalProgress = animation && !isToggle ? animation.totalProgress() : progress,\n\t\t\t\t\t\tvelocity = refreshedRecently ? 0 : ((totalProgress - snap2) / (_getTime() - _time2) * 1000) || 0,\n\t\t\t\t\t\tchange1 = gsap.utils.clamp(-progress, 1 - progress, _abs(velocity / 2) * velocity / 0.185),\n\t\t\t\t\t\tnaturalEnd = progress + (snap.inertia === false ? 0 : change1),\n\t\t\t\t\t\tendValue = _clamp(0, 1, snapFunc(naturalEnd, self)),\n\t\t\t\t\t\tendScroll = Math.round(start + endValue * change),\n\t\t\t\t\t\t{ onStart, onInterrupt, onComplete } = snap;\n\t\t\t\t\tif (scroll <= end && scroll >= start && endScroll !== scroll) {\n\t\t\t\t\t\tif (tween && !tween._initted && tween.data <= _abs(endScroll - scroll)) { // there's an overlapping snap! So we must figure out which one is closer and let that tween live.\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (snap.inertia === false) {\n\t\t\t\t\t\t\tchange1 = endValue - progress;\n\t\t\t\t\t\t}\n\t\t\t\t\t\ttweenTo(endScroll, {\n\t\t\t\t\t\t\tduration: snapDurClamp(_abs( (Math.max(_abs(naturalEnd - totalProgress), _abs(endValue - totalProgress)) * 0.185 / velocity / 0.05) || 0)),\n\t\t\t\t\t\t\tease: snap.ease || \"power3\",\n\t\t\t\t\t\t\tdata: _abs(endScroll - scroll), // record the distance so that if another snap tween occurs (conflict) we can prioritize the closest snap.\n\t\t\t\t\t\t\tonInterrupt: () => snapDelayedCall.restart(true) && onInterrupt && onInterrupt(self),\n\t\t\t\t\t\t\tonComplete: () => {\n\t\t\t\t\t\t\t\tself.update();\n\t\t\t\t\t\t\t\tlastSnap = scrollFunc();\n\t\t\t\t\t\t\t\tsnap1 = snap2 = animation && !isToggle ? animation.totalProgress() : self.progress;\n\t\t\t\t\t\t\t\tonSnapComplete && onSnapComplete(self);\n\t\t\t\t\t\t\t\tonComplete && onComplete(self);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}, scroll, change1 * change, endScroll - scroll - change1 * change);\n\t\t\t\t\t\tonStart && onStart(self, tweenTo.tween);\n\t\t\t\t\t}\n\t\t\t\t} else if (self.isActive && lastSnap !== scroll) {\n\t\t\t\t\tsnapDelayedCall.restart(true);\n\t\t\t\t}\n\t\t\t}).pause();\n\t\t}\n\t\tid && (_ids[id] = self);\n\t\ttrigger = self.trigger = _getTarget(trigger || pin);\n\n\t\t// if a trigger has some kind of scroll-related effect applied that could contaminate the \"y\" or \"x\" position (like a ScrollSmoother effect), we needed a way to temporarily revert it, so we use the stRevert property of the gsCache. It can return another function that we'll call at the end so it can return to its normal state.\n\t\tcustomRevertReturn = trigger && trigger._gsap && trigger._gsap.stRevert;\n\t\tcustomRevertReturn && (customRevertReturn = customRevertReturn(self));\n\n\t\tpin = pin === true ? trigger : _getTarget(pin);\n\t\t_isString(toggleClass) && (toggleClass = {targets: trigger, className: toggleClass});\n\t\tif (pin) {\n\t\t\t(pinSpacing === false || pinSpacing === _margin) || (pinSpacing = !pinSpacing && pin.parentNode && pin.parentNode.style && _getComputedStyle(pin.parentNode).display === \"flex\" ? false : _padding); // if the parent is display: flex, don't apply pinSpacing by default. We should check that pin.parentNode is an element (not shadow dom window)\n\t\t\tself.pin = pin;\n\t\t\tpinCache = gsap.core.getCache(pin);\n\t\t\tif (!pinCache.spacer) { // record the spacer and pinOriginalState on the cache in case someone tries pinning the same element with MULTIPLE ScrollTriggers - we don't want to have multiple spacers or record the \"original\" pin state after it has already been affected by another ScrollTrigger.\n\t\t\t\tif (pinSpacer) {\n\t\t\t\t\tpinSpacer = _getTarget(pinSpacer);\n\t\t\t\t\tpinSpacer && !pinSpacer.nodeType && (pinSpacer = pinSpacer.current || pinSpacer.nativeElement); // for React & Angular\n\t\t\t\t\tpinCache.spacerIsNative = !!pinSpacer;\n\t\t\t\t\tpinSpacer && (pinCache.spacerState = _getState(pinSpacer));\n\t\t\t\t}\n\t\t\t\tpinCache.spacer = spacer = pinSpacer || _doc.createElement(\"div\");\n\t\t\t\tspacer.classList.add(\"pin-spacer\");\n\t\t\t\tid && spacer.classList.add(\"pin-spacer-\" + id);\n\t\t\t\tpinCache.pinState = pinOriginalState = _getState(pin);\n\t\t\t} else {\n\t\t\t\tpinOriginalState = pinCache.pinState;\n\t\t\t}\n\t\t\tvars.force3D !== false && gsap.set(pin, {force3D: true});\n\t\t\tself.spacer = spacer = pinCache.spacer;\n\t\t\tcs = _getComputedStyle(pin);\n\t\t\tspacingStart = cs[pinSpacing + direction.os2];\n\t\t\tpinGetter = gsap.getProperty(pin);\n\t\t\tpinSetter = gsap.quickSetter(pin, direction.a, _px);\n\t\t\t// pin.firstChild && !_maxScroll(pin, direction) && (pin.style.overflow = \"hidden\"); // protects from collapsing margins, but can have unintended consequences as demonstrated here: https://codepen.io/GreenSock/pen/1e42c7a73bfa409d2cf1e184e7a4248d so it was removed in favor of just telling people to set up their CSS to avoid the collapsing margins (overflow: hidden | auto is just one option. Another is border-top: 1px solid transparent).\n\t\t\t_swapPinIn(pin, spacer, cs);\n\t\t\tpinState = _getState(pin);\n\t\t}\n\t\tif (markers) {\n\t\t\tmarkerVars = _isObject(markers) ? _setDefaults(markers, _markerDefaults) : _markerDefaults;\n\t\t\tmarkerStartTrigger = _createMarker(\"scroller-start\", id, scroller, direction, markerVars, 0);\n\t\t\tmarkerEndTrigger = _createMarker(\"scroller-end\", id, scroller, direction, markerVars, 0, markerStartTrigger);\n\t\t\toffset = markerStartTrigger[\"offset\" + direction.op.d2];\n\t\t\tlet content = _getTarget(_getProxyProp(scroller, \"content\") || scroller);\n\t\t\tmarkerStart = this.markerStart = _createMarker(\"start\", id, content, direction, markerVars, offset, 0, containerAnimation);\n\t\t\tmarkerEnd = this.markerEnd = _createMarker(\"end\", id, content, direction, markerVars, offset, 0, containerAnimation);\n\t\t\tcontainerAnimation && (caMarkerSetter = gsap.quickSetter([markerStart, markerEnd], direction.a, _px));\n\t\t\tif ((!useFixedPosition && !(_proxies.length && _getProxyProp(scroller, \"fixedMarkers\") === true))) {\n\t\t\t\t_makePositionable(isViewport ? _body : scroller);\n\t\t\t\tgsap.set([markerStartTrigger, markerEndTrigger], {force3D: true});\n\t\t\t\tmarkerStartSetter = gsap.quickSetter(markerStartTrigger, direction.a, _px);\n\t\t\t\tmarkerEndSetter = gsap.quickSetter(markerEndTrigger, direction.a, _px);\n\t\t\t}\n\t\t}\n\n\t\tif (containerAnimation) {\n\t\t\tlet oldOnUpdate = containerAnimation.vars.onUpdate,\n\t\t\t\toldParams = containerAnimation.vars.onUpdateParams;\n\t\t\tcontainerAnimation.eventCallback(\"onUpdate\", () => {\n\t\t\t\tself.update(0, 0, 1);\n\t\t\t\toldOnUpdate && oldOnUpdate.apply(oldParams || []);\n\t\t\t});\n\t\t}\n\n\t\tself.previous = () => _triggers[_triggers.indexOf(self) - 1];\n\t\tself.next = () => _triggers[_triggers.indexOf(self) + 1];\n\n\t\tself.revert = (revert, temp) => {\n\t\t\tif (!temp) { return self.kill(true); } // for compatibility with gsap.context() and gsap.matchMedia() which call revert()\n\t\t\tlet r = revert !== false || !self.enabled,\n\t\t\t\tprevRefreshing = _refreshing;\n\t\t\tif (r !== self.isReverted) {\n\t\t\t\tif (r) {\n\t\t\t\t\t// if (!self.scroll.rec && (_refreshing || _refreshingAll)) {\n\t\t\t\t\t// \tself.scroll.rec = scrollFunc();\n\t\t\t\t\t// \t_refreshingAll && scrollFunc(0);\n\t\t\t\t\t// }\n\t\t\t\t\tprevScroll = Math.max(scrollFunc(), self.scroll.rec || 0); // record the scroll so we can revert later (repositioning/pinning things can affect scroll position). In the static refresh() method, we first record all the scroll positions as a reference.\n\t\t\t\t\tprevProgress = self.progress;\n\t\t\t\t\tprevAnimProgress = animation && animation.progress();\n\t\t\t\t}\n\t\t\t\tmarkerStart && [markerStart, markerEnd, markerStartTrigger, markerEndTrigger].forEach(m => m.style.display = r ? \"none\" : \"block\");\n\t\t\t\tif (r) {\n\t\t\t\t\t_refreshing = 1;\n\t\t\t\t\tself.update(r); // make sure the pin is back in its original position so that all the measurements are correct. do this BEFORE swapping the pin out\n\t\t\t\t}\n\t\t\t\tif (pin) {\n\t\t\t\t\tif (r) {\n\t\t\t\t\t\t_swapPinOut(pin, spacer, pinOriginalState);\n\t\t\t\t\t} else {\n\t\t\t\t\t\t(!pinReparent || !self.isActive) && _swapPinIn(pin, spacer, _getComputedStyle(pin), spacerState);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tr || self.update(r); // when we're restoring, the update should run AFTER swapping the pin into its pin-spacer.\n\t\t\t\t_refreshing = prevRefreshing; // restore. We set it to true during the update() so that things fire properly in there.\n\t\t\t\tself.isReverted = r;\n\t\t\t}\n\t\t}\n\n\n\t\tself.refresh = (soft, force) => {\n\t\t\tif ((_refreshing || !self.enabled) && !force) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (pin && soft && _lastScrollTime) {\n\t\t\t\t_addListener(ScrollTrigger, \"scrollEnd\", _softRefresh);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t!_refreshingAll && onRefreshInit && onRefreshInit(self);\n\t\t\t_refreshing = 1;\n\t\t\tlastRefresh = _getTime();\n\t\t\tif (tweenTo.tween) {\n\t\t\t\ttweenTo.tween.kill();\n\t\t\t\ttweenTo.tween = 0;\n\t\t\t}\n\t\t\tscrubTween && scrubTween.pause();\n\t\t\tinvalidateOnRefresh && animation && animation.revert({kill: false}).invalidate();\n\t\t\tself.isReverted || self.revert(true, true);\n\t\t\tself._subPinOffset = false; // we'll set this to true in the sub-pins if we find any\n\t\t\tlet size = getScrollerSize(),\n\t\t\t\tscrollerBounds = getScrollerOffsets(),\n\t\t\t\tmax = containerAnimation ? containerAnimation.duration() : _maxScroll(scroller, direction),\n\t\t\t\toffset = 0,\n\t\t\t\totherPinOffset = 0,\n\t\t\t\tparsedEnd = vars.end,\n\t\t\t\tparsedEndTrigger = vars.endTrigger || trigger,\n\t\t\t\tparsedStart = vars.start || (vars.start === 0 || !trigger ? 0 : (pin ? \"0 0\" : \"0 100%\")),\n\t\t\t\tpinnedContainer = self.pinnedContainer = vars.pinnedContainer && _getTarget(vars.pinnedContainer),\n\t\t\t\ttriggerIndex = (trigger && Math.max(0, _triggers.indexOf(self))) || 0,\n\t\t\t\ti = triggerIndex,\n\t\t\t\tcs, bounds, scroll, isVertical, override, curTrigger, curPin, oppositeScroll, initted, revertedPins;\n\t\t\twhile (i--) { // user might try to pin the same element more than once, so we must find any prior triggers with the same pin, revert them, and determine how long they're pinning so that we can offset things appropriately. Make sure we revert from last to first so that things \"rewind\" properly.\n\t\t\t\tcurTrigger = _triggers[i];\n\t\t\t\tcurTrigger.end || curTrigger.refresh(0, 1) || (_refreshing = 1); // if it's a timeline-based trigger that hasn't been fully initialized yet because it's waiting for 1 tick, just force the refresh() here, otherwise if it contains a pin that's supposed to affect other ScrollTriggers further down the page, they won't be adjusted properly.\n\t\t\t\tcurPin = curTrigger.pin;\n\t\t\t\tif (curPin && (curPin === trigger || curPin === pin) && !curTrigger.isReverted) {\n\t\t\t\t\trevertedPins || (revertedPins = []);\n\t\t\t\t\trevertedPins.unshift(curTrigger); // we'll revert from first to last to make sure things reach their end state properly\n\t\t\t\t\tcurTrigger.revert(true, true);\n\t\t\t\t}\n\t\t\t\tif (curTrigger !== _triggers[i]) { // in case it got removed.\n\t\t\t\t\ttriggerIndex--;\n\t\t\t\t\ti--;\n\t\t\t\t}\n\t\t\t}\n\t\t\t_isFunction(parsedStart) && (parsedStart = parsedStart(self));\n\t\t\tstart = _parsePosition(parsedStart, trigger, size, direction, scrollFunc(), markerStart, markerStartTrigger, self, scrollerBounds, borderWidth, useFixedPosition, max, containerAnimation) || (pin ? -0.001 : 0);\n\t\t\t_isFunction(parsedEnd) && (parsedEnd = parsedEnd(self));\n\t\t\tif (_isString(parsedEnd) && !parsedEnd.indexOf(\"+=\")) {\n\t\t\t\tif (~parsedEnd.indexOf(\" \")) {\n\t\t\t\t\tparsedEnd = (_isString(parsedStart) ? parsedStart.split(\" \")[0] : \"\") + parsedEnd;\n\t\t\t\t} else {\n\t\t\t\t\toffset = _offsetToPx(parsedEnd.substr(2), size);\n\t\t\t\t\tparsedEnd = _isString(parsedStart) ? parsedStart : start + offset; // _parsePosition won't factor in the offset if the start is a number, so do it here.\n\t\t\t\t\tparsedEndTrigger = trigger;\n\t\t\t\t}\n\t\t\t}\n\t\t\tend = Math.max(start, _parsePosition(parsedEnd || (parsedEndTrigger ? \"100% 0\" : max), parsedEndTrigger, size, direction, scrollFunc() + offset, markerEnd, markerEndTrigger, self, scrollerBounds, borderWidth, useFixedPosition, max, containerAnimation)) || -0.001;\n\t\t\tchange = (end - start) || ((start -= 0.01) && 0.001);\n\n\t\t\toffset = 0;\n\t\t\ti = triggerIndex;\n\t\t\twhile (i--) {\n\t\t\t\tcurTrigger = _triggers[i];\n\t\t\t\tcurPin = curTrigger.pin;\n\t\t\t\tif (curPin && curTrigger.start - curTrigger._pinPush <= start && !containerAnimation && curTrigger.end > 0) {\n\t\t\t\t\tcs = curTrigger.end - curTrigger.start;\n\t\t\t\t\tif (((curPin === trigger && curTrigger.start - curTrigger._pinPush < start) || curPin === pinnedContainer) && !_isNumber(parsedStart)) { // numeric start values shouldn't be offset at all - treat them as absolute\n\t\t\t\t\t\toffset += cs * (1 - curTrigger.progress);\n\t\t\t\t\t}\n\t\t\t\t\tcurPin === pin && (otherPinOffset += cs);\n\t\t\t\t}\n\t\t\t}\n\t\t\tstart += offset;\n\t\t\tend += offset;\n\t\t\tself._pinPush = otherPinOffset;\n\t\t\tif (markerStart && offset) { // offset the markers if necessary\n\t\t\t\tcs = {};\n\t\t\t\tcs[direction.a] = \"+=\" + offset;\n\t\t\t\tpinnedContainer && (cs[direction.p] = \"-=\" + scrollFunc());\n\t\t\t\tgsap.set([markerStart, markerEnd], cs);\n\t\t\t}\n\n\t\t\tif (pin) {\n\t\t\t\tcs = _getComputedStyle(pin);\n\t\t\t\tisVertical = direction === _vertical;\n\t\t\t\tscroll = scrollFunc(); // recalculate because the triggers can affect the scroll\n\t\t\t\tpinStart = parseFloat(pinGetter(direction.a)) + otherPinOffset;\n\t\t\t\t!max && end > 1 && ((isViewport ? _body : scroller).style[\"overflow-\" + direction.a] = \"scroll\"); // makes sure the scroller has a scrollbar, otherwise if something has width: 100%, for example, it would be too big (exclude the scrollbar). See https://greensock.com/forums/topic/25182-scrolltrigger-width-of-page-increase-where-markers-are-set-to-false/\n\t\t\t\t_swapPinIn(pin, spacer, cs);\n\t\t\t\tpinState = _getState(pin);\n\t\t\t\t// transforms will interfere with the top/left/right/bottom placement, so remove them temporarily. getBoundingClientRect() factors in transforms.\n\t\t\t\tbounds = _getBounds(pin, true);\n\t\t\t\toppositeScroll = useFixedPosition && _getScrollFunc(scroller, isVertical ? _horizontal : _vertical)();\n\t\t\t\tif (pinSpacing) {\n\t\t\t\t\tspacerState = [pinSpacing + direction.os2, change + otherPinOffset + _px];\n\t\t\t\t\tspacerState.t = spacer;\n\t\t\t\t\ti = (pinSpacing === _padding) ? _getSize(pin, direction) + change + otherPinOffset : 0;\n\t\t\t\t\ti && spacerState.push(direction.d, i + _px); // for box-sizing: border-box (must include padding).\n\t\t\t\t\t_setState(spacerState);\n\t\t\t\t\tif (pinnedContainer) { // in ScrollTrigger.refresh(), we need to re-evaluate the pinContainer's size because this pinSpacing may stretch it out, but we can't just add the exact distance because depending on layout, it may not push things down or it may only do so partially.\n\t\t\t\t\t\t_triggers.forEach(t => {\n\t\t\t\t\t\t\tif (t.pin === pinnedContainer && t.vars.pinSpacing !== false) {\n\t\t\t\t\t\t\t\tt._subPinOffset = true;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\tuseFixedPosition && scrollFunc(prevScroll);\n\t\t\t\t}\n\t\t\t\tif (useFixedPosition) {\n\t\t\t\t\toverride = {\n\t\t\t\t\t\ttop: (bounds.top + (isVertical ? scroll - start : oppositeScroll)) + _px,\n\t\t\t\t\t\tleft: (bounds.left + (isVertical ? oppositeScroll : scroll - start)) + _px,\n\t\t\t\t\t\tboxSizing: \"border-box\",\n\t\t\t\t\t\tposition: \"fixed\"\n\t\t\t\t\t};\n\t\t\t\t\toverride[_width] = override[\"max\" + _Width] = Math.ceil(bounds.width) + _px;\n\t\t\t\t\toverride[_height] = override[\"max\" + _Height] = Math.ceil(bounds.height) + _px;\n\t\t\t\t\toverride[_margin] = override[_margin + _Top] = override[_margin + _Right] = override[_margin + _Bottom] = override[_margin + _Left] = \"0\";\n\t\t\t\t\toverride[_padding] = cs[_padding];\n\t\t\t\t\toverride[_padding + _Top] = cs[_padding + _Top];\n\t\t\t\t\toverride[_padding + _Right] = cs[_padding + _Right];\n\t\t\t\t\toverride[_padding + _Bottom] = cs[_padding + _Bottom];\n\t\t\t\t\toverride[_padding + _Left] = cs[_padding + _Left];\n\t\t\t\t\tpinActiveState = _copyState(pinOriginalState, override, pinReparent);\n\t\t\t\t\t_refreshingAll && scrollFunc(0);\n\t\t\t\t}\n\t\t\t\tif (animation) { // the animation might be affecting the transform, so we must jump to the end, check the value, and compensate accordingly. Otherwise, when it becomes unpinned, the pinSetter() will get set to a value that doesn't include whatever the animation did.\n\t\t\t\t\tinitted = animation._initted; // if not, we must invalidate() after this step, otherwise it could lock in starting values prematurely.\n\t\t\t\t\t_suppressOverwrites(1);\n\t\t\t\t\tanimation.render(animation.duration(), true, true);\n\t\t\t\t\tpinChange = pinGetter(direction.a) - pinStart + change + otherPinOffset;\n\t\t\t\t\tchange !== pinChange && useFixedPosition && pinActiveState.splice(pinActiveState.length - 2, 2); // transform is the last property/value set in the state Array. Since the animation is controlling that, we should omit it.\n\t\t\t\t\tanimation.render(0, true, true);\n\t\t\t\t\tinitted || animation.invalidate(true);\n\t\t\t\t\tanimation.parent || animation.totalTime(animation.totalTime()); // if, for example, a toggleAction called play() and then refresh() happens and when we render(1) above, it would cause the animation to complete and get removed from its parent, so this makes sure it gets put back in.\n\t\t\t\t\t_suppressOverwrites(0);\n\t\t\t\t} else {\n\t\t\t\t\tpinChange = change\n\t\t\t\t}\n\t\t\t} else if (trigger && scrollFunc() && !containerAnimation) { // it may be INSIDE a pinned element, so walk up the tree and look for any elements with _pinOffset to compensate because anything with pinSpacing that's already scrolled would throw off the measurements in getBoundingClientRect()\n\t\t\t\tbounds = trigger.parentNode;\n\t\t\t\twhile (bounds && bounds !== _body) {\n\t\t\t\t\tif (bounds._pinOffset) {\n\t\t\t\t\t\tstart -= bounds._pinOffset;\n\t\t\t\t\t\tend -= bounds._pinOffset;\n\t\t\t\t\t}\n\t\t\t\t\tbounds = bounds.parentNode;\n\t\t\t\t}\n\t\t\t}\n\t\t\trevertedPins && revertedPins.forEach(t => t.revert(false, true));\n\t\t\tself.start = start;\n\t\t\tself.end = end;\n\t\t\tscroll1 = scroll2 = _refreshingAll ? prevScroll : scrollFunc(); // reset velocity\n\t\t\tif (!containerAnimation && !_refreshingAll) {\n\t\t\t\tscroll1 < prevScroll && scrollFunc(prevScroll);\n\t\t\t\tself.scroll.rec = 0;\n\t\t\t}\n\t\t\tself.revert(false, true);\n\t\t\tif (snapDelayedCall)  {\n\t\t\t\tlastSnap = -1;\n\t\t\t\tself.isActive && scrollFunc(start + change * prevProgress); // just so snapping gets re-enabled, clear out any recorded last value\n\t\t\t\tsnapDelayedCall.restart(true);\n\t\t\t}\n\t\t\t_refreshing = 0;\n\t\t\tanimation && isToggle && (animation._initted || prevAnimProgress) && animation.progress() !== prevAnimProgress && animation.progress(prevAnimProgress, true).render(animation.time(), true, true); // must force a re-render because if saveStyles() was used on the target(s), the styles could have been wiped out during the refresh().\n\t\t\tif (prevProgress !== self.progress || containerAnimation) { // ensures that the direction is set properly (when refreshing, progress is set back to 0 initially, then back again to wherever it needs to be) and that callbacks are triggered.\n\t\t\t\tanimation && !isToggle && animation.totalProgress(prevProgress, true); // to avoid issues where animation callbacks like onStart aren't triggered.\n\t\t\t\tself.progress = (scroll1 - start) / change === prevProgress ? 0 : prevProgress;\n\t\t\t}\n\t\t\tpin && pinSpacing && (spacer._pinOffset = Math.round(self.progress * pinChange));\n//\t\t\tscrubTween && scrubTween.invalidate();\n\t\t\tonRefresh && !_refreshingAll && onRefresh(self); // when refreshing all, we do extra work to correct pinnedContainer sizes and ensure things don't exceed the maxScroll, so we should do all the refreshes at the end after all that work so that the start/end values are corrected.\n\t\t};\n\n\t\tself.getVelocity = () => ((scrollFunc() - scroll2) / (_getTime() - _time2) * 1000) || 0;\n\n\t\tself.endAnimation = () => {\n\t\t\t_endAnimation(self.callbackAnimation);\n\t\t\tif (animation) {\n\t\t\t\tscrubTween ? scrubTween.progress(1) : (!animation.paused() ? _endAnimation(animation, animation.reversed()) : isToggle || _endAnimation(animation, self.direction < 0, 1));\n\t\t\t}\n\t\t};\n\n\t\tself.labelToScroll = label => animation && animation.labels && ((start || self.refresh() || start) + (animation.labels[label] / animation.duration()) * change) || 0;\n\n\t\tself.getTrailing = name => {\n\t\t\tlet i = _triggers.indexOf(self),\n\t\t\t\ta = self.direction > 0 ? _triggers.slice(0, i).reverse() : _triggers.slice(i+1);\n\t\t\treturn (_isString(name) ? a.filter(t => t.vars.preventOverlaps === name) : a).filter(t => self.direction > 0 ? t.end <= start : t.start >= end);\n\t\t};\n\n\n\t\tself.update = (reset, recordVelocity, forceFake) => {\n\t\t\tif (containerAnimation && !forceFake && !reset) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tlet scroll = _refreshingAll ? prevScroll : self.scroll(),\n\t\t\t\tp = reset ? 0 : (scroll - start) / change,\n\t\t\t\tclipped = p < 0 ? 0 : p > 1 ? 1 : p || 0,\n\t\t\t\tprevProgress = self.progress,\n\t\t\t\tisActive, wasActive, toggleState, action, stateChanged, toggled, isAtMax, isTakingAction;\n\t\t\tif (recordVelocity) {\n\t\t\t\tscroll2 = scroll1;\n\t\t\t\tscroll1 = containerAnimation ? scrollFunc() : scroll;\n\t\t\t\tif (snap) {\n\t\t\t\t\tsnap2 = snap1;\n\t\t\t\t\tsnap1 = animation && !isToggle ? animation.totalProgress() : clipped;\n\t\t\t\t}\n\t\t\t}\n\t\t\t// anticipate the pinning a few ticks ahead of time based on velocity to avoid a visual glitch due to the fact that most browsers do scrolling on a separate thread (not synced with requestAnimationFrame).\n\t\t\t(anticipatePin && !clipped && pin && !_refreshing && !_startup && _lastScrollTime && start < scroll + ((scroll - scroll2) / (_getTime() - _time2)) * anticipatePin) && (clipped = 0.0001);\n\t\t\tif (clipped !== prevProgress && self.enabled) {\n\t\t\t\tisActive = self.isActive = !!clipped && clipped < 1;\n\t\t\t\twasActive = !!prevProgress && prevProgress < 1;\n\t\t\t\ttoggled = isActive !== wasActive;\n\t\t\t\tstateChanged = toggled || !!clipped !== !!prevProgress; // could go from start all the way to end, thus it didn't toggle but it did change state in a sense (may need to fire a callback)\n\t\t\t\tself.direction = clipped > prevProgress ? 1 : -1;\n\t\t\t\tself.progress = clipped;\n\n\t\t\t\tif (stateChanged && !_refreshing) {\n\t\t\t\t\ttoggleState = clipped && !prevProgress ? 0 : clipped === 1 ? 1 : prevProgress === 1 ? 2 : 3; // 0 = enter, 1 = leave, 2 = enterBack, 3 = leaveBack (we prioritize the FIRST encounter, thus if you scroll really fast past the onEnter and onLeave in one tick, it'd prioritize onEnter.\n\t\t\t\t\tif (isToggle) {\n\t\t\t\t\t\taction = (!toggled && toggleActions[toggleState + 1] !== \"none\" && toggleActions[toggleState + 1]) || toggleActions[toggleState]; // if it didn't toggle, that means it shot right past and since we prioritize the \"enter\" action, we should switch to the \"leave\" in this case (but only if one is defined)\n\t\t\t\t\t\tisTakingAction = animation && (action === \"complete\" || action === \"reset\" || action in animation);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tpreventOverlaps && (toggled || isTakingAction) && (isTakingAction || scrub || !animation) && (_isFunction(preventOverlaps) ? preventOverlaps(self) : self.getTrailing(preventOverlaps).forEach(t => t.endAnimation()));\n\n\t\t\t\tif (!isToggle) {\n\t\t\t\t\tif (scrubTween && !_refreshing && !_startup) {\n\t\t\t\t\t\t(containerAnimation || (_primary && _primary !== self)) && scrubTween.render(scrubTween._dp._time - scrubTween._start); // if there's a scrub on both the container animation and this one (or a ScrollSmoother), the update order would cause this one not to have rendered yet, so it wouldn't make any progress before we .restart() it heading toward the new progress so it'd appear stuck thus we force a render here.\n\t\t\t\t\t\tif (scrubTween.resetTo) {\n\t\t\t\t\t\t\tscrubTween.resetTo(\"totalProgress\", clipped, animation._tTime / animation._tDur);\n\t\t\t\t\t\t} else { // legacy support (courtesy), before 3.10.0\n\t\t\t\t\t\t\tscrubTween.vars.totalProgress = clipped;\n\t\t\t\t\t\t\tscrubTween.invalidate().restart();\n\t\t\t\t\t\t}\n\t\t\t\t\t} else if (animation) {\n\t\t\t\t\t\tanimation.totalProgress(clipped, !!_refreshing);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (pin) {\n\t\t\t\t\treset && pinSpacing && (spacer.style[pinSpacing + direction.os2] = spacingStart);\n\t\t\t\t\tif (!useFixedPosition) {\n\t\t\t\t\t\tpinSetter(_round(pinStart + pinChange * clipped));\n\t\t\t\t\t} else if (stateChanged) {\n\t\t\t\t\t\tisAtMax = !reset && clipped > prevProgress && end + 1 > scroll && scroll + 1 >= _maxScroll(scroller, direction); // if it's at the VERY end of the page, don't switch away from position: fixed because it's pointless and it could cause a brief flash when the user scrolls back up (when it gets pinned again)\n\t\t\t\t\t\tif (pinReparent) {\n\t\t\t\t\t\t\tif (!reset && (isActive || isAtMax)) {\n\t\t\t\t\t\t\t\tlet bounds = _getBounds(pin, true),\n\t\t\t\t\t\t\t\t\toffset = scroll - start;\n\t\t\t\t\t\t\t\t_reparent(pin, _body, (bounds.top + (direction === _vertical ? offset : 0)) + _px, (bounds.left + (direction === _vertical ? 0 : offset)) + _px);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t_reparent(pin, spacer);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\t_setState(isActive || isAtMax ? pinActiveState : pinState);\n\t\t\t\t\t\t(pinChange !== change && clipped < 1 && isActive) || pinSetter(pinStart + (clipped === 1 && !isAtMax ? pinChange : 0));\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tsnap && !tweenTo.tween && !_refreshing && !_startup && snapDelayedCall.restart(true);\n\t\t\t\ttoggleClass && (toggled || (once && clipped && (clipped < 1 || !_limitCallbacks))) && _toArray(toggleClass.targets).forEach(el => el.classList[isActive || once ? \"add\" : \"remove\"](toggleClass.className)); // classes could affect positioning, so do it even if reset or refreshing is true.\n\t\t\t\tonUpdate && !isToggle && !reset && onUpdate(self);\n\t\t\t\tif (stateChanged && !_refreshing) {\n\t\t\t\t\tif (isToggle) {\n\t\t\t\t\t\tif (isTakingAction) {\n\t\t\t\t\t\t\tif (action === \"complete\") {\n\t\t\t\t\t\t\t\tanimation.pause().totalProgress(1);\n\t\t\t\t\t\t\t} else if (action === \"reset\") {\n\t\t\t\t\t\t\t\tanimation.restart(true).pause();\n\t\t\t\t\t\t\t} else if (action === \"restart\") {\n\t\t\t\t\t\t\t\tanimation.restart(true);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tanimation[action]();\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tonUpdate && onUpdate(self);\n\t\t\t\t\t}\n\t\t\t\t\tif (toggled || !_limitCallbacks) { // on startup, the page could be scrolled and we don't want to fire callbacks that didn't toggle. For example onEnter shouldn't fire if the ScrollTrigger isn't actually entered.\n\t\t\t\t\t\tonToggle && toggled && _callback(self, onToggle);\n\t\t\t\t\t\tcallbacks[toggleState] && _callback(self, callbacks[toggleState]);\n\t\t\t\t\t\tonce && (clipped === 1 ? self.kill(false, 1) : (callbacks[toggleState] = 0)); // a callback shouldn't be called again if once is true.\n\t\t\t\t\t\tif (!toggled) { // it's possible to go completely past, like from before the start to after the end (or vice-versa) in which case BOTH callbacks should be fired in that order\n\t\t\t\t\t\t\ttoggleState = clipped === 1 ? 1 : 3;\n\t\t\t\t\t\t\tcallbacks[toggleState] && _callback(self, callbacks[toggleState]);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif (fastScrollEnd && !isActive && Math.abs(self.getVelocity()) > (_isNumber(fastScrollEnd) ? fastScrollEnd : 2500)) {\n\t\t\t\t\t\t_endAnimation(self.callbackAnimation);\n\t\t\t\t\t\tscrubTween ? scrubTween.progress(1) : _endAnimation(animation, action === \"reverse\" ? 1 : !clipped, 1);\n\t\t\t\t\t}\n\t\t\t\t} else if (isToggle && onUpdate && !_refreshing) {\n\t\t\t\t\tonUpdate(self);\n\t\t\t\t}\n\t\t\t}\n\t\t\t// update absolutely-positioned markers (only if the scroller isn't the viewport)\n\t\t\tif (markerEndSetter) {\n\t\t\t\tlet n = containerAnimation ? scroll / containerAnimation.duration() * (containerAnimation._caScrollDist || 0) : scroll;\n\t\t\t\tmarkerStartSetter(n + (markerStartTrigger._isFlipped ? 1 : 0));\n\t\t\t\tmarkerEndSetter(n);\n\t\t\t}\n\t\t\tcaMarkerSetter && caMarkerSetter(-scroll / containerAnimation.duration() * (containerAnimation._caScrollDist || 0));\n\t\t};\n\n\t\tself.enable = (reset, refresh) => {\n\t\t\tif (!self.enabled) {\n\t\t\t\tself.enabled = true;\n\t\t\t\t_addListener(scroller, \"resize\", _onResize);\n\t\t\t\t_addListener(isViewport ? _doc : scroller, \"scroll\", _onScroll);\n\t\t\t\tonRefreshInit && _addListener(ScrollTrigger, \"refreshInit\", onRefreshInit);\n\t\t\t\tif (reset !== false) {\n\t\t\t\t\tself.progress = prevProgress = 0;\n\t\t\t\t\tscroll1 = scroll2 = lastSnap = scrollFunc();\n\t\t\t\t}\n\t\t\t\trefresh !== false && self.refresh();\n\t\t\t}\n\t\t};\n\n\t\tself.getTween = snap => snap && tweenTo ? tweenTo.tween : scrubTween;\n\n\t\tself.setPositions = (newStart, newEnd) => { // doesn't persist after refresh()! Intended to be a way to override values that were set during refresh(), like you could set it in onRefresh()\n\t\t\tif (pin) {\n\t\t\t\tpinStart += newStart - start;\n\t\t\t\tpinChange += (newEnd - newStart) - change;\n\t\t\t\tpinSpacing === _padding && self.adjustPinSpacing((newEnd - newStart) - change);\n\t\t\t}\n\t\t\tself.start = start = newStart;\n\t\t\tself.end = end = newEnd;\n\t\t\tchange = newEnd - newStart;\n\t\t\tself.update();\n\t\t};\n\n\t\tself.adjustPinSpacing = amount => {\n\t\t\tif (spacerState) {\n\t\t\t\tlet i = spacerState.indexOf(direction.d) + 1;\n\t\t\t\tspacerState[i] = (parseFloat(spacerState[i]) + amount) + _px;\n\t\t\t\tspacerState[1] = (parseFloat(spacerState[1]) + amount) + _px;\n\t\t\t\t_setState(spacerState);\n\t\t\t}\n\t\t};\n\n\t\tself.disable = (reset, allowAnimation) => {\n\t\t\tif (self.enabled) {\n\t\t\t\treset !== false && self.revert(true, true);\n\t\t\t\tself.enabled = self.isActive = false;\n\t\t\t\tallowAnimation || (scrubTween && scrubTween.pause());\n\t\t\t\tprevScroll = 0;\n\t\t\t\tpinCache && (pinCache.uncache = 1);\n\t\t\t\tonRefreshInit && _removeListener(ScrollTrigger, \"refreshInit\", onRefreshInit);\n\t\t\t\tif (snapDelayedCall) {\n\t\t\t\t\tsnapDelayedCall.pause();\n\t\t\t\t\ttweenTo.tween && tweenTo.tween.kill() && (tweenTo.tween = 0);\n\t\t\t\t}\n\t\t\t\tif (!isViewport) {\n\t\t\t\t\tlet i = _triggers.length;\n\t\t\t\t\twhile (i--) {\n\t\t\t\t\t\tif (_triggers[i].scroller === scroller && _triggers[i] !== self) {\n\t\t\t\t\t\t\treturn; //don't remove the listeners if there are still other triggers referencing it.\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t_removeListener(scroller, \"resize\", _onResize);\n\t\t\t\t\t_removeListener(scroller, \"scroll\", _onScroll);\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\n\t\tself.kill = (revert, allowAnimation) => {\n\t\t\tself.disable(revert, allowAnimation);\n\t\t\tscrubTween && !allowAnimation && scrubTween.kill();\n\t\t\tid && (delete _ids[id]);\n\t\t\tlet i = _triggers.indexOf(self);\n\t\t\ti >= 0 && _triggers.splice(i, 1);\n\t\t\ti === _i && _direction > 0 && _i--; // if we're in the middle of a refresh() or update(), splicing would cause skips in the index, so adjust...\n\n\t\t\t// if no other ScrollTrigger instances of the same scroller are found, wipe out any recorded scroll position. Otherwise, in a single page application, for example, it could maintain scroll position when it really shouldn't.\n\t\t\ti = 0;\n\t\t\t_triggers.forEach(t => t.scroller === self.scroller && (i = 1));\n\t\t\ti || _refreshingAll || (self.scroll.rec = 0);\n\n\t\t\tif (animation) {\n\t\t\t\tanimation.scrollTrigger = null;\n\t\t\t\trevert && animation.revert({kill: false});\n\t\t\t\tallowAnimation || animation.kill();\n\t\t\t}\n\t\t\tmarkerStart && [markerStart, markerEnd, markerStartTrigger, markerEndTrigger].forEach(m => m.parentNode && m.parentNode.removeChild(m));\n\t\t\t_primary === self && (_primary = 0);\n\t\t\tif (pin) {\n\t\t\t\tpinCache && (pinCache.uncache = 1);\n\t\t\t\ti = 0;\n\t\t\t\t_triggers.forEach(t => t.pin === pin && i++);\n\t\t\t\ti || (pinCache.spacer = 0); // if there aren't any more ScrollTriggers with the same pin, remove the spacer, otherwise it could be contaminated with old/stale values if the user re-creates a ScrollTrigger for the same element.\n\t\t\t}\n\t\t\tvars.onKill && vars.onKill(self);\n\t\t};\n\n\t\tself.enable(false, false);\n\t\tcustomRevertReturn && customRevertReturn(self);\n\t\t!animation || !animation.add || change ? self.refresh() : gsap.delayedCall(0.01, () => start || end || self.refresh()) && (change = 0.01) && (start = end = 0); // if the animation is a timeline, it may not have been populated yet, so it wouldn't render at the proper place on the first refresh(), thus we should schedule one for the next tick. If \"change\" is defined, we know it must be re-enabling, thus we can refresh() right away.\n\t\tpin && _queueRefreshAll(); // pinning could affect the positions of other things, so make sure we queue a full refresh()\n\t}\n\n\n\tstatic register(core) {\n\t\tif (!_coreInitted) {\n\t\t\tgsap = core || _getGSAP();\n\t\t\t_windowExists() && window.document && ScrollTrigger.enable();\n\t\t\t_coreInitted = _enabled;\n\t\t}\n\t\treturn _coreInitted;\n\t}\n\n\tstatic defaults(config) {\n\t\tif (config) {\n\t\t\tfor (let p in config) {\n\t\t\t\t_defaults[p] = config[p];\n\t\t\t}\n\t\t}\n\t\treturn _defaults;\n\t}\n\n\tstatic disable(reset, kill) {\n\t\t_enabled = 0;\n\t\t_triggers.forEach(trigger => trigger[kill ? \"kill\" : \"disable\"](reset));\n\t\t_removeListener(_win, \"wheel\", _onScroll);\n\t\t_removeListener(_doc, \"scroll\", _onScroll);\n\t\tclearInterval(_syncInterval);\n\t\t_removeListener(_doc, \"touchcancel\", _passThrough);\n\t\t_removeListener(_body, \"touchstart\", _passThrough);\n\t\t_multiListener(_removeListener, _doc, \"pointerdown,touchstart,mousedown\", _pointerDownHandler);\n\t\t_multiListener(_removeListener, _doc, \"pointerup,touchend,mouseup\", _pointerUpHandler);\n\t\t_resizeDelay.kill();\n\t\t_iterateAutoRefresh(_removeListener);\n\t\tfor (let i = 0; i < _scrollers.length; i+=3) {\n\t\t\t_wheelListener(_removeListener, _scrollers[i], _scrollers[i+1]);\n\t\t\t_wheelListener(_removeListener, _scrollers[i], _scrollers[i+2]);\n\t\t}\n\t}\n\n\tstatic enable() {\n\t\t_win = window;\n\t\t_doc = document;\n\t\t_docEl = _doc.documentElement;\n\t\t_body = _doc.body;\n\t\tif (gsap) {\n\t\t\t_toArray = gsap.utils.toArray;\n\t\t\t_clamp = gsap.utils.clamp;\n\t\t\t_context = gsap.core.context || _passThrough;\n\t\t\t_suppressOverwrites = gsap.core.suppressOverwrites || _passThrough;\n\t\t\t_scrollRestoration = _win.history.scrollRestoration || \"auto\";\n\t\t\tgsap.core.globals(\"ScrollTrigger\", ScrollTrigger); // must register the global manually because in Internet Explorer, functions (classes) don't have a \"name\" property.\n\t\t\tif (_body) {\n\t\t\t\t_enabled = 1;\n\t\t\t\tObserver.register(gsap);\n\t\t\t\t// isTouch is 0 if no touch, 1 if ONLY touch, and 2 if it can accommodate touch but also other types like mouse/pointer.\n\t\t\t\tScrollTrigger.isTouch = Observer.isTouch;\n\t\t\t\t_fixIOSBug = Observer.isTouch && /(iPad|iPhone|iPod|Mac)/g.test(navigator.userAgent); // since 2017, iOS has had a bug that causes event.clientX/Y to be inaccurate when a scroll occurs, thus we must alternate ignoring every other touchmove event to work around it. See https://bugs.webkit.org/show_bug.cgi?id=181954 and https://codepen.io/GreenSock/pen/ExbrPNa/087cef197dc35445a0951e8935c41503\n\t\t\t\t_addListener(_win, \"wheel\", _onScroll); // mostly for 3rd party smooth scrolling libraries.\n\t\t\t\t_root = [_win, _doc, _docEl, _body];\n\t\t\t\tif (gsap.matchMedia) {\n\t\t\t\t\tScrollTrigger.matchMedia = vars => {\n\t\t\t\t\t\tlet mm = gsap.matchMedia(),\n\t\t\t\t\t\t\tp;\n\t\t\t\t\t\tfor (p in vars) {\n\t\t\t\t\t\t\tmm.add(p, vars[p]);\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn mm;\n\t\t\t\t\t};\n\t\t\t\t\tgsap.addEventListener(\"matchMediaInit\", () => _revertAll());\n\t\t\t\t\tgsap.addEventListener(\"matchMediaRevert\", () => _revertRecorded());\n\t\t\t\t\tgsap.addEventListener(\"matchMedia\", () => {\n\t\t\t\t\t\t_refreshAll(0, 1);\n\t\t\t\t\t\t_dispatch(\"matchMedia\");\n\t\t\t\t\t});\n\t\t\t\t\tgsap.matchMedia(\"(orientation: portrait)\", () => { // when orientation changes, we should take new base measurements for the ignoreMobileResize feature.\n\t\t\t\t\t\t_setBaseDimensions();\n\t\t\t\t\t\treturn _setBaseDimensions;\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tconsole.warn(\"Requires GSAP 3.11.0 or later\");\n\t\t\t\t}\n\t\t\t\t_setBaseDimensions();\n\t\t\t\t_addListener(_doc, \"scroll\", _onScroll); // some browsers (like Chrome), the window stops dispatching scroll events on the window if you scroll really fast, but it's consistent on the document!\n\t\t\t\tlet bodyStyle = _body.style,\n\t\t\t\t\tborder = bodyStyle.borderTopStyle,\n\t\t\t\t\tAnimationProto = gsap.core.Animation.prototype,\n\t\t\t\t\tbounds, i;\n\t\t\t\tAnimationProto.revert || Object.defineProperty(AnimationProto, \"revert\", { value: function() { return this.time(-0.01, true); }}); // only for backwards compatibility (Animation.revert() was added after 3.10.4)\n\t\t\t\tbodyStyle.borderTopStyle = \"solid\"; // works around an issue where a margin of a child element could throw off the bounds of the _body, making it seem like there's a margin when there actually isn't. The border ensures that the bounds are accurate.\n\t\t\t\tbounds = _getBounds(_body);\n\t\t\t\t_vertical.m = Math.round(bounds.top + _vertical.sc()) || 0; // accommodate the offset of the <body> caused by margins and/or padding\n\t\t\t\t_horizontal.m = Math.round(bounds.left + _horizontal.sc()) || 0;\n\t\t\t\tborder ? (bodyStyle.borderTopStyle = border) : bodyStyle.removeProperty(\"border-top-style\");\n\t\t\t\t// TODO: (?) maybe move to leveraging the velocity mechanism in Observer and skip intervals.\n\t\t\t\t_syncInterval = setInterval(_sync, 250);\n\t\t\t\tgsap.delayedCall(0.5, () => _startup = 0);\n\t\t\t\t_addListener(_doc, \"touchcancel\", _passThrough); // some older Android devices intermittently stop dispatching \"touchmove\" events if we don't listen for \"touchcancel\" on the document.\n\t\t\t\t_addListener(_body, \"touchstart\", _passThrough); //works around Safari bug: https://greensock.com/forums/topic/21450-draggable-in-iframe-on-mobile-is-buggy/\n\t\t\t\t_multiListener(_addListener, _doc, \"pointerdown,touchstart,mousedown\", _pointerDownHandler);\n\t\t\t\t_multiListener(_addListener, _doc, \"pointerup,touchend,mouseup\", _pointerUpHandler);\n\t\t\t\t_transformProp = gsap.utils.checkPrefix(\"transform\");\n\t\t\t\t_stateProps.push(_transformProp);\n\t\t\t\t_coreInitted = _getTime();\n\t\t\t\t_resizeDelay = gsap.delayedCall(0.2, _refreshAll).pause();\n\t\t\t\t_autoRefresh = [_doc, \"visibilitychange\", () => {\n\t\t\t\t\tlet w = _win.innerWidth,\n\t\t\t\t\t\th = _win.innerHeight;\n\t\t\t\t\tif (_doc.hidden) {\n\t\t\t\t\t\t_prevWidth = w;\n\t\t\t\t\t\t_prevHeight = h;\n\t\t\t\t\t} else if (_prevWidth !== w || _prevHeight !== h) {\n\t\t\t\t\t\t_onResize();\n\t\t\t\t\t}\n\t\t\t\t}, _doc, \"DOMContentLoaded\", _refreshAll, _win, \"load\", _refreshAll, _win, \"resize\", _onResize];\n\t\t\t\t_iterateAutoRefresh(_addListener);\n\t\t\t\t_triggers.forEach(trigger => trigger.enable(0, 1));\n\t\t\t\tfor (i = 0; i < _scrollers.length; i+=3) {\n\t\t\t\t\t_wheelListener(_removeListener, _scrollers[i], _scrollers[i+1]);\n\t\t\t\t\t_wheelListener(_removeListener, _scrollers[i], _scrollers[i+2]);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tstatic config(vars) {\n\t\t(\"limitCallbacks\" in vars) && (_limitCallbacks = !!vars.limitCallbacks);\n\t\tlet ms = vars.syncInterval;\n\t\tms && clearInterval(_syncInterval) || ((_syncInterval = ms) && setInterval(_sync, ms));\n\t\t(\"ignoreMobileResize\" in vars) && (_ignoreMobileResize = ScrollTrigger.isTouch === 1 && vars.ignoreMobileResize);\n\t\tif (\"autoRefreshEvents\" in vars) {\n\t\t\t_iterateAutoRefresh(_removeListener) || _iterateAutoRefresh(_addListener, vars.autoRefreshEvents || \"none\");\n\t\t\t_ignoreResize = (vars.autoRefreshEvents + \"\").indexOf(\"resize\") === -1;\n\t\t}\n\t}\n\n\tstatic scrollerProxy(target, vars) {\n\t\tlet t = _getTarget(target),\n\t\t\ti = _scrollers.indexOf(t),\n\t\t\tisViewport = _isViewport(t);\n\t\tif (~i) {\n\t\t\t_scrollers.splice(i, isViewport ? 6 : 2);\n\t\t}\n\t\tif (vars) {\n\t\t\tisViewport ? _proxies.unshift(_win, vars, _body, vars, _docEl, vars) : _proxies.unshift(t, vars);\n\t\t}\n\t}\n\n\tstatic clearMatchMedia(query) {\n\t\t_triggers.forEach(t => t._ctx && t._ctx.query === query && t._ctx.kill(true, true));\n\t}\n\n\tstatic isInViewport(element, ratio, horizontal) {\n\t\tlet bounds = (_isString(element) ? _getTarget(element) : element).getBoundingClientRect(),\n\t\t\toffset = bounds[horizontal ? _width : _height] * ratio || 0;\n\t\treturn horizontal ? bounds.right - offset > 0 && bounds.left + offset < _win.innerWidth : bounds.bottom - offset > 0 && bounds.top + offset < _win.innerHeight;\n\t}\n\n\tstatic positionInViewport(element, referencePoint, horizontal) {\n\t\t_isString(element) && (element = _getTarget(element));\n\t\tlet bounds = element.getBoundingClientRect(),\n\t\t\tsize = bounds[horizontal ? _width : _height],\n\t\t\toffset = referencePoint == null ? size / 2 : ((referencePoint in _keywords) ? _keywords[referencePoint] * size : ~referencePoint.indexOf(\"%\") ? parseFloat(referencePoint) * size / 100 : parseFloat(referencePoint) || 0);\n\t\treturn horizontal ? (bounds.left + offset) / _win.innerWidth : (bounds.top + offset) / _win.innerHeight;\n\t}\n\n\tstatic killAll(allowListeners) {\n\t\t_triggers.forEach(t => t.vars.id !== \"ScrollSmoother\" && t.kill());\n\t\tif (allowListeners !== true) {\n\t\t\tlet listeners = _listeners.killAll || [];\n\t\t\t_listeners = {};\n\t\t\tlisteners.forEach(f => f());\n\t\t}\n\t}\n\n}\n\nScrollTrigger.version = \"3.11.3\";\nScrollTrigger.saveStyles = targets => targets ? _toArray(targets).forEach(target => { // saved styles are recorded in a consecutive alternating Array, like [element, cssText, transform attribute, cache, matchMedia, ...]\n\tif (target && target.style) {\n\t\tlet i = _savedStyles.indexOf(target);\n\t\ti >= 0 && _savedStyles.splice(i, 5);\n\t\t_savedStyles.push(target, target.style.cssText, target.getBBox && target.getAttribute(\"transform\"), gsap.core.getCache(target), _context());\n\t}\n}) : _savedStyles;\nScrollTrigger.revert = (soft, media) => _revertAll(!soft, media);\nScrollTrigger.create = (vars, animation) => new ScrollTrigger(vars, animation);\nScrollTrigger.refresh = safe => safe ? _onResize() : (_coreInitted || ScrollTrigger.register()) && _refreshAll(true);\nScrollTrigger.update = _updateAll;\nScrollTrigger.clearScrollMemory = _clearScrollMemory;\nScrollTrigger.maxScroll = (element, horizontal) => _maxScroll(element, horizontal ? _horizontal : _vertical);\nScrollTrigger.getScrollFunc = (element, horizontal) => _getScrollFunc(_getTarget(element), horizontal ? _horizontal : _vertical);\nScrollTrigger.getById = id => _ids[id];\nScrollTrigger.getAll = () => _triggers.filter(t => t.vars.id !== \"ScrollSmoother\"); // it's common for people to ScrollTrigger.getAll(t => t.kill()) on page routes, for example, and we don't want it to ruin smooth scrolling by killing the main ScrollSmoother one.\nScrollTrigger.isScrolling = () => !!_lastScrollTime;\nScrollTrigger.snapDirectional = _snapDirectional;\nScrollTrigger.addEventListener = (type, callback) => {\n\tlet a = _listeners[type] || (_listeners[type] = []);\n\t~a.indexOf(callback) || a.push(callback);\n};\nScrollTrigger.removeEventListener = (type, callback) => {\n\tlet a = _listeners[type],\n\t\ti = a && a.indexOf(callback);\n\ti >= 0 && a.splice(i, 1);\n};\nScrollTrigger.batch = (targets, vars) => {\n\tlet result = [],\n\t\tvarsCopy = {},\n\t\tinterval = vars.interval || 0.016,\n\t\tbatchMax = vars.batchMax || 1e9,\n\t\tproxyCallback = (type, callback) => {\n\t\t\tlet elements = [],\n\t\t\t\ttriggers = [],\n\t\t\t\tdelay = gsap.delayedCall(interval, () => {callback(elements, triggers); elements = []; triggers = [];}).pause();\n\t\t\treturn self => {\n\t\t\t\telements.length || delay.restart(true);\n\t\t\t\telements.push(self.trigger);\n\t\t\t\ttriggers.push(self);\n\t\t\t\tbatchMax <= elements.length && delay.progress(1);\n\t\t\t};\n\t\t},\n\t\tp;\n\tfor (p in vars) {\n\t\tvarsCopy[p] = (p.substr(0, 2) === \"on\" && _isFunction(vars[p]) && p !== \"onRefreshInit\") ? proxyCallback(p, vars[p]) : vars[p];\n\t}\n\tif (_isFunction(batchMax)) {\n\t\tbatchMax = batchMax();\n\t\t_addListener(ScrollTrigger, \"refresh\", () => batchMax = vars.batchMax());\n\t}\n\t_toArray(targets).forEach(target => {\n\t\tlet config = {};\n\t\tfor (p in varsCopy) {\n\t\t\tconfig[p] = varsCopy[p];\n\t\t}\n\t\tconfig.trigger = target;\n\t\tresult.push(ScrollTrigger.create(config));\n\t});\n\treturn result;\n}\n\n\n// to reduce file size. clamps the scroll and also returns a duration multiplier so that if the scroll gets chopped shorter, the duration gets curtailed as well (otherwise if you're very close to the top of the page, for example, and swipe up really fast, it'll suddenly slow down and take a long time to reach the top).\nlet _clampScrollAndGetDurationMultiplier = (scrollFunc, current, end, max) => {\n\t\tcurrent > max ? scrollFunc(max) : current < 0 && scrollFunc(0);\n\t\treturn end > max ? (max - current) / (end - current) : end < 0 ? current / (current - end) : 1;\n\t},\n\t_allowNativePanning = (target, direction) => {\n\t\tif (direction === true) {\n\t\t\ttarget.style.removeProperty(\"touch-action\");\n\t\t} else {\n\t\t\ttarget.style.touchAction = direction === true ? \"auto\" : direction ? \"pan-\" + direction + (Observer.isTouch ? \" pinch-zoom\" : \"\") : \"none\"; // note: Firefox doesn't support it pinch-zoom properly, at least in addition to a pan-x or pan-y.\n\t\t}\n\t\ttarget === _docEl && _allowNativePanning(_body, direction);\n\t},\n\t_overflow = {auto: 1, scroll: 1},\n\t_nestedScroll = ({event, target, axis}) => {\n\t\tlet node = (event.changedTouches ? event.changedTouches[0] : event).target,\n\t\t\tcache = node._gsap || gsap.core.getCache(node),\n\t\t\ttime = _getTime(), cs;\n\t\tif (!cache._isScrollT || time - cache._isScrollT > 2000) { // cache for 2 seconds to improve performance.\n\t\t\twhile (node && node.scrollHeight <= node.clientHeight) node = node.parentNode;\n\t\t\tcache._isScroll = node && !_isViewport(node) && node !== target && (_overflow[(cs = _getComputedStyle(node)).overflowY] || _overflow[cs.overflowX]);\n\t\t\tcache._isScrollT = time;\n\t\t}\n\t\tif (cache._isScroll || axis === \"x\") {\n\t\t\tevent.stopPropagation();\n\t\t\tevent._gsapAllow = true\n\t\t}\n\t},\n\t// capture events on scrollable elements INSIDE the <body> and allow those by calling stopPropagation() when we find a scrollable ancestor\n\t_inputObserver = (target, type, inputs, nested) => Observer.create({\n\t\ttarget: target,\n\t\tcapture: true,\n\t\tdebounce: false,\n\t\tlockAxis: true,\n\t\ttype: type,\n\t\tonWheel: (nested = nested && _nestedScroll),\n\t\tonPress: nested,\n\t\tonDrag: nested,\n\t\tonScroll: nested,\n\t\tonEnable: () => inputs && _addListener(_doc, Observer.eventTypes[0], _captureInputs, false, true),\n\t\tonDisable: () => _removeListener(_doc, Observer.eventTypes[0], _captureInputs, true)\n\t}),\n\t_inputExp = /(input|label|select|textarea)/i,\n\t_inputIsFocused,\n\t_captureInputs = e => {\n\t\tlet isInput = _inputExp.test(e.target.tagName);\n\t\tif (isInput || _inputIsFocused) {\n\t\t\te._gsapAllow = true;\n\t\t\t_inputIsFocused = isInput;\n\t\t}\n\t},\n\t_getScrollNormalizer = vars => {\n\t\t_isObject(vars) || (vars = {});\n\t\tvars.preventDefault = vars.isNormalizer = vars.allowClicks = true;\n\t\tvars.type || (vars.type = \"wheel,touch\");\n\t\tvars.debounce = !!vars.debounce;\n\t\tvars.id = vars.id || \"normalizer\";\n\t\tlet {normalizeScrollX, momentum, allowNestedScroll} = vars,\n\t\t\tself, maxY,\n\t\t\ttarget = _getTarget(vars.target) || _docEl,\n\t\t\tsmoother = gsap.core.globals().ScrollSmoother,\n\t\t\tsmootherInstance = smoother && smoother.get(),\n\t\t\tcontent = _fixIOSBug && ((vars.content && _getTarget(vars.content)) || (smootherInstance && vars.content !== false && !smootherInstance.smooth() && smootherInstance.content())),\n\t\t\tscrollFuncY = _getScrollFunc(target, _vertical),\n\t\t\tscrollFuncX = _getScrollFunc(target, _horizontal),\n\t\t\tscale = 1,\n\t\t\tinitialScale = (Observer.isTouch && _win.visualViewport ? _win.visualViewport.scale * _win.visualViewport.width : _win.outerWidth) / _win.innerWidth,\n\t\t\twheelRefresh = 0,\n\t\t\tresolveMomentumDuration = _isFunction(momentum) ? () => momentum(self) : () => momentum || 2.8,\n\t\t\tlastRefreshID, skipTouchMove,\n\t\t\tinputObserver = _inputObserver(target, vars.type, true, allowNestedScroll),\n\t\t\tresumeTouchMove = () => skipTouchMove = false,\n\t\t\tscrollClampX = _passThrough,\n\t\t\tscrollClampY = _passThrough,\n\t\t\tupdateClamps = () => {\n\t\t\t\tmaxY = _maxScroll(target, _vertical);\n\t\t\t\tscrollClampY = _clamp(_fixIOSBug ? 1 : 0, maxY);\n\t\t\t\tnormalizeScrollX && (scrollClampX = _clamp(0, _maxScroll(target, _horizontal)));\n\t\t\t\tlastRefreshID = _refreshID;\n\t\t\t},\n\t\t\tremoveContentOffset = () => {\n\t\t\t\tcontent._gsap.y = _round(parseFloat(content._gsap.y) + scrollFuncY.offset) + \"px\";\n\t\t\t\tcontent.style.transform = \"matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, \" + parseFloat(content._gsap.y) + \", 0, 1)\";\n\t\t\t\tscrollFuncY.offset = scrollFuncY.cacheID = 0;\n\t\t\t},\n\t\t\tignoreDrag = () => {\n\t\t\t\tif (skipTouchMove) {\n\t\t\t\t\trequestAnimationFrame(resumeTouchMove);\n\t\t\t\t\tlet offset = _round(self.deltaY / 2),\n\t\t\t\t\t\tscroll = scrollClampY(scrollFuncY.v - offset);\n\t\t\t\t\tif (content && scroll !== scrollFuncY.v + scrollFuncY.offset) {\n\t\t\t\t\t\tscrollFuncY.offset = scroll - scrollFuncY.v;\n\t\t\t\t\t\tlet y = _round((parseFloat(content && content._gsap.y) || 0) - scrollFuncY.offset);\n\t\t\t\t\t\tcontent.style.transform = \"matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, \" + y + \", 0, 1)\";\n\t\t\t\t\t\tcontent._gsap.y = y + \"px\";\n\t\t\t\t\t\tscrollFuncY.cacheID = _scrollers.cache;\n\t\t\t\t\t\t_updateAll();\n\t\t\t\t\t}\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t\tscrollFuncY.offset && removeContentOffset();\n\t\t\t\tskipTouchMove = true;\n\t\t\t},\n\t\t\ttween, startScrollX, startScrollY, onStopDelayedCall,\n\t\t\tonResize = () => { // if the window resizes, like on an iPhone which Apple FORCES the address bar to show/hide even if we event.preventDefault(), it may be scrolling too far now that the address bar is showing, so we must dynamically adjust the momentum tween.\n\t\t\t\tupdateClamps();\n\t\t\t\tif (tween.isActive() && tween.vars.scrollY > maxY) {\n\t\t\t\t\tscrollFuncY() > maxY ? tween.progress(1) && scrollFuncY(maxY) : tween.resetTo(\"scrollY\", maxY);\n\t\t\t\t}\n\t\t\t};\n\t\tcontent && gsap.set(content, {y: \"+=0\"}); // to ensure there's a cache (element._gsap)\n\t\tvars.ignoreCheck = e => (_fixIOSBug && e.type === \"touchmove\" && ignoreDrag(e)) || (scale > 1.05 && e.type !== \"touchstart\") || self.isGesturing || (e.touches && e.touches.length > 1);\n\t\tvars.onPress = () => {\n\t\t\tlet prevScale = scale;\n\t\t\tscale = _round(((_win.visualViewport && _win.visualViewport.scale) || 1) / initialScale);\n\t\t\ttween.pause();\n\t\t\tprevScale !== scale && _allowNativePanning(target, scale > 1.01 ? true : normalizeScrollX ? false : \"x\");\n\t\t\tstartScrollX = scrollFuncX();\n\t\t\tstartScrollY = scrollFuncY();\n\t\t\tupdateClamps();\n\t\t\tlastRefreshID = _refreshID;\n\t\t}\n\t\tvars.onRelease = vars.onGestureStart = (self, wasDragging) => {\n\t\t\tscrollFuncY.offset && removeContentOffset();\n\t\t\tif (!wasDragging) {\n\t\t\t\tonStopDelayedCall.restart(true);\n\t\t\t} else {\n\t\t\t\t_scrollers.cache++; // make sure we're pulling the non-cached value\n\t\t\t\t// alternate algorithm: durX = Math.min(6, Math.abs(self.velocityX / 800)),\tdur = Math.max(durX, Math.min(6, Math.abs(self.velocityY / 800))); dur = dur * (0.4 + (1 - _power4In(dur / 6)) * 0.6)) * (momentumSpeed || 1)\n\t\t\t\tlet dur = resolveMomentumDuration(),\n\t\t\t\t\tcurrentScroll, endScroll;\n\t\t\t\tif (normalizeScrollX) {\n\t\t\t\t\tcurrentScroll = scrollFuncX();\n\t\t\t\t\tendScroll = currentScroll + (dur * 0.05 * -self.velocityX) / 0.227; // the constant .227 is from power4(0.05). velocity is inverted because scrolling goes in the opposite direction.\n\t\t\t\t\tdur *= _clampScrollAndGetDurationMultiplier(scrollFuncX, currentScroll, endScroll, _maxScroll(target, _horizontal));\n\t\t\t\t\ttween.vars.scrollX = scrollClampX(endScroll);\n\t\t\t\t}\n\t\t\t\tcurrentScroll = scrollFuncY();\n\t\t\t\tendScroll = currentScroll + (dur * 0.05 * -self.velocityY) / 0.227; // the constant .227 is from power4(0.05)\n\t\t\t\tdur *= _clampScrollAndGetDurationMultiplier(scrollFuncY, currentScroll, endScroll, _maxScroll(target, _vertical));\n\t\t\t\ttween.vars.scrollY = scrollClampY(endScroll);\n\t\t\t\ttween.invalidate().duration(dur).play(0.01);\n\t\t\t\tif (_fixIOSBug && tween.vars.scrollY >= maxY || currentScroll >= maxY-1) { // iOS bug: it'll show the address bar but NOT fire the window \"resize\" event until the animation is done but we must protect against overshoot so we leverage an onUpdate to do so.\n\t\t\t\t\tgsap.to({}, {onUpdate: onResize, duration: dur});\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\t\tvars.onWheel = () => {\n\t\t\ttween._ts && tween.pause();\n\t\t\tif (_getTime() - wheelRefresh > 1000) { // after 1 second, refresh the clamps otherwise that'll only happen when ScrollTrigger.refresh() is called or for touch-scrolling.\n\t\t\t\tlastRefreshID = 0;\n\t\t\t\twheelRefresh = _getTime();\n\t\t\t}\n\t\t};\n\t\tvars.onChange = (self, dx, dy, xArray, yArray) => {\n\t\t\t_refreshID !== lastRefreshID && updateClamps();\n\t\t\tdx && normalizeScrollX && scrollFuncX(scrollClampX(xArray[2] === dx ? startScrollX + (self.startX - self.x) : scrollFuncX() + dx - xArray[1])); // for more precision, we track pointer/touch movement from the start, otherwise it'll drift.\n\t\t\tif (dy) {\n\t\t\t\tscrollFuncY.offset && removeContentOffset();\n\t\t\t\tlet isTouch = yArray[2] === dy,\n\t\t\t\t\ty = isTouch ? startScrollY + self.startY - self.y : scrollFuncY() + dy - yArray[1],\n\t\t\t\t\tyClamped = scrollClampY(y);\n\t\t\t\tisTouch && y !== yClamped && (startScrollY += yClamped - y);\n\t\t\t\tscrollFuncY(yClamped);\n\t\t\t}\n\t\t\t(dy || dx) && _updateAll();\n\t\t};\n\t\tvars.onEnable = () => {\n\t\t\t_allowNativePanning(target, normalizeScrollX ? false : \"x\");\n\t\t\tScrollTrigger.addEventListener(\"refresh\", onResize);\n\t\t\t_addListener(_win, \"resize\", onResize);\n\t\t\tif (scrollFuncY.smooth) {\n\t\t\t\tscrollFuncY.target.style.scrollBehavior = \"auto\";\n\t\t\t\tscrollFuncY.smooth = scrollFuncX.smooth = false;\n\t\t\t}\n\t\t\tinputObserver.enable();\n\t\t};\n\t\tvars.onDisable = () => {\n\t\t\t_allowNativePanning(target, true);\n\t\t\t_removeListener(_win, \"resize\", onResize);\n\t\t\tScrollTrigger.removeEventListener(\"refresh\", onResize);\n\t\t\tinputObserver.kill();\n\t\t};\n\t\tvars.lockAxis = vars.lockAxis !== false;\n\t\tself = new Observer(vars);\n\t\tself.iOS = _fixIOSBug; // used in the Observer getCachedScroll() function to work around an iOS bug that wreaks havoc with TouchEvent.clientY if we allow scroll to go all the way back to 0.\n\t\t_fixIOSBug && !scrollFuncY() && scrollFuncY(1); // iOS bug causes event.clientY values to freak out (wildly inaccurate) if the scroll position is exactly 0.\n\t\t_fixIOSBug && gsap.ticker.add(_passThrough); // prevent the ticker from sleeping\n\t\tonStopDelayedCall = self._dc;\n\t\ttween = gsap.to(self, {ease: \"power4\", paused: true, scrollX: normalizeScrollX ? \"+=0.1\" : \"+=0\", scrollY: \"+=0.1\", onComplete: onStopDelayedCall.vars.onComplete});\n\t\treturn self;\n\t};\n\nScrollTrigger.sort = func => _triggers.sort(func || ((a, b) => (a.vars.refreshPriority || 0) * -1e6 + a.start - (b.start + (b.vars.refreshPriority || 0) * -1e6)));\nScrollTrigger.observe = vars => new Observer(vars);\nScrollTrigger.normalizeScroll = vars => {\n\tif (typeof(vars) === \"undefined\") {\n\t\treturn _normalizer;\n\t}\n\tif (vars === true && _normalizer) {\n\t\treturn _normalizer.enable();\n\t}\n\tif (vars === false) {\n\t\treturn _normalizer && _normalizer.kill();\n\t}\n\tlet normalizer = vars instanceof Observer ? vars : _getScrollNormalizer(vars);\n\t_normalizer && _normalizer.target === normalizer.target && _normalizer.kill();\n\t_isViewport(normalizer.target) && (_normalizer = normalizer);\n\treturn normalizer;\n};\n\n\nScrollTrigger.core = { // smaller file size way to leverage in ScrollSmoother and Observer\n\t_getVelocityProp,\n\t_inputObserver,\n\t_scrollers,\n\t_proxies,\n\tbridge: {\n\t\t// when normalizeScroll sets the scroll position (ss = setScroll)\n\t\tss: () => {\n\t\t\t_lastScrollTime || _dispatch(\"scrollStart\");\n\t\t\t_lastScrollTime = _getTime();\n\t\t},\n\t\t// a way to get the _refreshing value in Observer\n\t\tref: () => _refreshing\n\t}\n};\n\n_getGSAP() && gsap.registerPlugin(ScrollTrigger);\n\nexport { ScrollTrigger as default };"], "names": ["_getGSAP", "gsap", "window", "registerPlugin", "_getProxyProp", "element", "property", "_proxies", "indexOf", "_isViewport", "el", "_root", "_addListener", "type", "func", "nonPassive", "capture", "addEventListener", "passive", "_removeListener", "removeEventListener", "_onScroll", "_normalizer", "isPressed", "_scrollers", "cache", "_scrollCacheFunc", "f", "doNotCache", "cachingFunc", "value", "_startup", "_win", "history", "scrollRestoration", "isNormalizing", "v", "Math", "round", "iOS", "cacheID", "_bridge", "offset", "_getTarget", "t", "utils", "toArray", "config", "null<PERSON><PERSON><PERSON><PERSON><PERSON>n", "console", "warn", "_getScrollFunc", "s", "sc", "_doc", "scrollingElement", "_docEl", "i", "_vertical", "push", "prev", "arguments", "length", "target", "smooth", "getProperty", "_getVelocityProp", "minTimeRefresh", "useDel<PERSON>", "update", "force", "_getTime", "min", "t1", "v2", "v1", "t2", "dropToZeroTime", "max", "reset", "getVelocity", "latestValue", "tOld", "vOld", "_getEvent", "e", "preventDefault", "_gsapAllow", "changedTouches", "_getAbsoluteMax", "a", "abs", "_setScrollTrigger", "ScrollTrigger", "core", "globals", "_integrate", "data", "bridge", "scrollers", "proxies", "name", "_initCore", "document", "body", "documentElement", "_body", "clamp", "_pointerType", "_isTouch", "Observer", "is<PERSON><PERSON>ch", "matchMedia", "matches", "navigator", "maxTouchPoints", "msMaxTouchPoints", "_eventTypes", "eventTypes", "split", "setTimeout", "_coreInitted", "_observers", "Date", "now", "_scrollLeft", "_scrollTop", "_horizontal", "p", "p2", "os", "os2", "d", "d2", "scrollTo", "pageXOffset", "op", "pageYOffset", "init", "vars", "tolerance", "dragMinimum", "lineHeight", "debounce", "onStop", "onStopDelay", "ignore", "wheelSpeed", "event", "onDragStart", "onDragEnd", "onDrag", "onPress", "onRelease", "onRight", "onLeft", "onUp", "onDown", "onChangeX", "onChangeY", "onChange", "onToggleX", "onToggleY", "onHover", "onHoverEnd", "onMove", "<PERSON><PERSON><PERSON><PERSON>", "isNormalizer", "onGestureStart", "onGestureEnd", "onWheel", "onEnable", "onDisable", "onClick", "scrollSpeed", "allowClicks", "lockAxis", "onLockAxis", "clickCapture", "onClickTime", "_ignore<PERSON>heck", "isPointerOr<PERSON>ouch", "self", "limitToTouch", "pointerType", "dx", "deltaX", "dy", "deltaY", "changedX", "changedY", "prevDeltaX", "prevDeltaY", "moved", "dragged", "locked", "wheeled", "id", "onDelta", "x", "y", "index", "_vx", "_vy", "requestAnimationFrame", "onTouchOrPointerDelta", "axis", "_onDrag", "clientX", "clientY", "isDragging", "startX", "startY", "_onRelease", "ownerDoc", "wasDragging", "eventData", "delayedCall", "defaultPrevented", "click", "createEvent", "syntheticEvent", "initMouseEvent", "screenX", "screenY", "dispatchEvent", "isGesturing", "onStopDelayedCall", "restart", "_onGestureStart", "touches", "_onGestureEnd", "onScroll", "scrollFuncX", "scrollFuncY", "scrollX", "scrollY", "_onWheel", "multiplier", "deltaMode", "innerHeight", "_onMove", "_onHover", "_onHoverEnd", "_onClick", "parseFloat", "getComputedStyle", "this", "isViewport", "ownerDocument", "_onPress", "pause", "_dc", "onStopFunc", "enable", "isEnabled", "disable", "filter", "o", "kill", "splice", "version", "create", "register", "getAll", "slice", "getById", "_pointerDownHandler", "_pointerIsDown", "_pointerU<PERSON><PERSON><PERSON><PERSON>", "_passThrough", "_round", "_windowExists", "_getBoundsFunc", "_winOffsets", "width", "innerWidth", "height", "_getBounds", "_maxScroll", "_iterateAutoRefresh", "events", "_autoRefresh", "_isString", "_isFunction", "_isNumber", "_isObject", "_endAnimation", "animation", "reversed", "progress", "_callback", "enabled", "result", "totalTime", "callbackAnimation", "_getComputedStyle", "_setDefaults", "obj", "defaults", "_getSize", "_getLabelRatioArray", "timeline", "labels", "duration", "_snapDirectional", "snapIncrementOrArray", "snap", "Array", "isArray", "sort", "b", "direction", "threshold", "snapped", "_multiListener", "types", "callback", "for<PERSON>ach", "_wheelListener", "scrollFunc", "wheelHandler", "_offsetToPx", "size", "eqIndex", "relative", "char<PERSON>t", "substr", "_keywords", "_createMarker", "container", "matchWidthEl", "containerAnimation", "startColor", "endColor", "fontSize", "indent", "fontWeight", "createElement", "useFixedPosition", "isScroller", "parent", "isStart", "color", "css", "_right", "_bottom", "offsetWidth", "_isStart", "setAttribute", "style", "cssText", "innerText", "children", "insertBefore", "append<PERSON><PERSON><PERSON>", "_offset", "_position<PERSON><PERSON>er", "_sync", "_lastScrollTime", "_rafID", "_updateAll", "clientWidth", "_dispatch", "_setBaseDimensions", "_baseScreenWidth", "_baseScreenHeight", "_onResize", "_refreshing", "_ignoreResize", "fullscreenElement", "webkitFullscreenElement", "_ignoreMobileResize", "_resizeDelay", "_softRefresh", "_refreshAll", "_revertRecorded", "media", "_savedStyles", "query", "getBBox", "uncache", "_revertAll", "trigger", "_i", "_triggers", "_ctx", "revert", "_clearScrollMemory", "_refreshingAll", "rec", "_scrollRestoration", "_swapPinIn", "pin", "spacer", "cs", "spacerState", "_gsap", "swappedIn", "_propNamesToCopy", "spacerStyle", "pinStyle", "position", "display", "flexBasis", "overflow", "boxSizing", "_width", "_px", "_height", "_padding", "_margin", "_setState", "parentNode", "_getState", "l", "_stateProps", "state", "_parsePosition", "scrollerSize", "scroll", "marker", "markerScroller", "scrollerBounds", "borderWidth", "scrollerMax", "p1", "time", "seek", "bounds", "localOffset", "globalOffset", "offsets", "left", "top", "removeProperty", "m", "_caScrollDist", "_reparent", "_st<PERSON><PERSON>", "_prefixExp", "test", "getCache", "_getTweenCreator", "scroller", "getTween", "initialValue", "change1", "change2", "tween", "onComplete", "modifiers", "getScroll", "lastScroll1", "prop", "lastScroll2", "ratio", "call", "to", "_toArray", "_clamp", "_time2", "_syncInterval", "_transformProp", "_prevWidth", "_prevHeight", "_sort", "_suppressOverwrites", "_fixIOSBug", "_context", "_limitCallbacks", "_queueRefreshID", "_primary", "_time1", "_enabled", "_abs", "_Right", "_Left", "_Top", "_Bottom", "_Width", "_Height", "withoutTransforms", "xPercent", "yPercent", "rotation", "rotationX", "rotationY", "scale", "skewX", "skewY", "getBoundingClientRect", "_markerDefaults", "_defaults", "toggleActions", "anticipatePin", "center", "bottom", "right", "start", "flipped", "side", "oppositeSide", "_isFlipped", "set", "_ids", "_listeners", "_emptyArray", "map", "_refreshID", "<PERSON><PERSON><PERSON><PERSON>", "isRefreshing", "refreshInits", "scroll<PERSON>eh<PERSON>or", "refresh", "_subPinOffset", "horizontal", "original", "adjustPinSpacing", "end", "setPositions", "_dir", "render", "onRefresh", "_lastScroll", "_direction", "isUpdating", "recordVelocity", "concat", "_capsExp", "replace", "toLowerCase", "tweenTo", "pinCache", "snapFunc", "scroll1", "scroll2", "markerStart", "markerEnd", "markerStartTrigger", "markerEndTrigger", "markerVars", "change", "pinOriginalState", "pinActiveState", "pinState", "pinGetter", "pinSetter", "pinStart", "pinChange", "spacingStart", "markerStartSetter", "markerEndSetter", "snap1", "snap2", "scrubTween", "scrubSmooth", "snapDurClamp", "snapDelayedCall", "prevProgress", "prevScroll", "prevAnimProgress", "caMarkerSetter", "customRevertReturn", "onUpdate", "nodeType", "toggleClass", "onToggle", "scrub", "pinSpacing", "invalidateOnRefresh", "onScrubComplete", "onSnapComplete", "once", "pinReparent", "pinSpacer", "fastScrollEnd", "preventOverlaps", "isToggle", "scrollerCache", "pinType", "callbacks", "onEnter", "onLeave", "onEnterBack", "onLeaveBack", "markers", "onRefreshInit", "getScrollerSize", "_getSizeFunc", "getScrollerOffsets", "_getOffsetsFunc", "lastSnap", "lastRefresh", "bind", "refreshPriority", "tweenScroll", "scrubDuration", "ease", "totalProgress", "paused", "lazy", "_initted", "immediateRender", "scrollTrigger", "snapTo", "_getClosestLabel", "_getLabelAtDirection", "st", "directional", "delay", "refreshedRecently", "isActive", "velocity", "naturalEnd", "inertia", "endValue", "endScroll", "onStart", "onInterrupt", "stRevert", "targets", "className", "current", "nativeElement", "spacerIsNative", "classList", "add", "force3D", "quickSetter", "content", "_makePositionable", "oldOnUpdate", "oldParams", "onUpdateParams", "eventCallback", "apply", "previous", "next", "temp", "r", "prevRefreshing", "isReverted", "_swapPinOut", "<PERSON><PERSON><PERSON><PERSON>", "soft", "invalidate", "isVertical", "override", "curTrigger", "curPin", "oppositeScroll", "initted", "revertedPins", "otherPinOffset", "parsedEnd", "parsedEndTrigger", "endTrigger", "parsedStart", "<PERSON><PERSON><PERSON><PERSON>", "triggerIndex", "unshift", "_pinPush", "ceil", "_copyState", "omitOffsets", "_pinOffset", "endAnimation", "labelToScroll", "label", "getTrailing", "reverse", "forceFake", "toggleState", "action", "stateChanged", "toggled", "isAtMax", "isTakingAction", "clipped", "_dp", "_time", "_start", "resetTo", "_tTime", "_tDur", "n", "newStart", "newEnd", "amount", "allowAnimation", "onKill", "_queueRefreshAll", "clearInterval", "context", "suppressOverwrites", "userAgent", "mm", "bodyStyle", "border", "borderTopStyle", "AnimationProto", "Animation", "prototype", "Object", "defineProperty", "setInterval", "checkPrefix", "w", "h", "hidden", "limitCallbacks", "ms", "syncInterval", "ignoreMobileResize", "autoRefreshEvents", "scrollerProxy", "clearMatchMedia", "isInViewport", "positionInViewport", "referencePoint", "killAll", "allowListeners", "listeners", "saveStyles", "getAttribute", "safe", "clearScrollMemory", "maxScroll", "getScrollFunc", "isScrolling", "snapDirectional", "batch", "proxyCallback", "elements", "triggers", "interval", "batchMax", "varsCopy", "_clampScrollAndGetDurationMultiplier", "_allowNativePanning", "touchAction", "_nestedScroll", "node", "_isScrollT", "scrollHeight", "clientHeight", "_isScroll", "_overflow", "overflowY", "overflowX", "stopPropagation", "_inputObserver", "inputs", "nested", "_captureInputs", "_getScrollNormalizer", "resumeTouchMove", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "updateClamps", "maxY", "scrollClampY", "normalizeScrollX", "scrollClampX", "lastRefreshID", "removeContentOffset", "transform", "onResize", "startScrollX", "startScrollY", "momentum", "allowNestedScroll", "smoother", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smootherInstance", "get", "initialScale", "visualViewport", "outerWidth", "wheelRefresh", "resolveMomentumDuration", "inputObserver", "ignoreDrag", "prevScale", "currentScroll", "dur", "velocityX", "velocityY", "play", "_ts", "xArray", "yArray", "yClamped", "ticker", "_inputIsFocused", "auto", "_inputExp", "isInput", "tagName", "observe", "normalizeScroll", "normalizer", "ss", "ref"], "mappings": ";;;;;;;;;mYAYY,SAAXA,WAAiBC,IAA4B,oBAAZC,SAA4BD,GAAOC,OAAOD,OAASA,GAAKE,gBAAkBF,GAmB3F,SAAhBG,EAAiBC,EAASC,UAAcC,GAASC,QAAQH,IAAYE,GAASA,GAASC,QAAQH,GAAW,GAAGC,GAC/F,SAAdG,EAAcC,YAASC,EAAMH,QAAQE,GACtB,SAAfE,EAAgBP,EAASQ,EAAMC,EAAMC,EAAYC,UAAYX,EAAQY,iBAAiBJ,EAAMC,EAAM,CAACI,SAAUH,EAAYC,UAAWA,IAClH,SAAlBG,EAAmBd,EAASQ,EAAMC,EAAME,UAAYX,EAAQe,oBAAoBP,EAAMC,IAAQE,GAGlF,SAAZK,WAAmBC,IAAeA,GAAYC,WAAcC,GAAWC,QACpD,SAAnBC,EAAoBC,EAAGC,GACJ,SAAdC,GAAcC,MACbA,GAAmB,IAAVA,EAAa,CACzBC,IAAaC,GAAKC,QAAQC,kBAAoB,cAC1CC,EAAgBb,IAAeA,GAAYC,UAC/CO,EAAQD,GAAYO,EAAIC,KAAKC,MAAMR,KAAWR,IAAeA,GAAYiB,IAAM,EAAI,GACnFZ,EAAEG,GACFD,GAAYW,QAAUhB,GAAWC,MACjCU,GAAiBM,EAAQ,KAAMX,QACrBF,GAAcJ,GAAWC,QAAUI,GAAYW,SAAWC,EAAQ,UAC5EZ,GAAYW,QAAUhB,GAAWC,MACjCI,GAAYO,EAAIT,YAEVE,GAAYO,EAAIP,GAAYa,cAEpCb,GAAYa,OAAS,EACdf,GAAKE,GAIA,SAAbc,EAAaC,UAAK3C,GAAK4C,MAAMC,QAAQF,GAAG,KAAqB,iBAAPA,IAAoD,IAAjC3C,GAAK8C,SAASC,eAA2BC,QAAQC,KAAK,qBAAsBN,GAAK,MAEzI,SAAjBO,EAAkB9C,SAAU+C,IAAAA,EAAGC,IAAAA,GAC9B5C,EAAYJ,KAAaA,EAAUiD,GAAKC,kBAAoBC,QACxDC,EAAIjC,GAAWhB,QAAQH,GAC1BqC,EAASW,IAAOK,GAAUL,GAAK,EAAI,GAClCI,IAAMA,EAAIjC,GAAWmC,KAAKtD,GAAW,GACvCmB,GAAWiC,EAAIf,IAAWrC,EAAQY,iBAAiB,SAAUI,OACzDuC,EAAOpC,GAAWiC,EAAIf,GACzB5B,EAAO8C,IAASpC,GAAWiC,EAAIf,GAAUhB,EAAiBtB,EAAcC,EAAS+C,IAAI,KAAU3C,EAAYJ,GAAWgD,EAAK3B,EAAiB,SAASI,UAAgB+B,UAAUC,OAAUzD,EAAQ+C,GAAKtB,EAASzB,EAAQ+C,cACxNtC,EAAKiD,OAAS1D,EACduD,IAAS9C,EAAKkD,OAAyD,WAAhD/D,GAAKgE,YAAY5D,EAAS,mBAC1CS,EAEW,SAAnBoD,EAAoBpC,EAAOqC,EAAgBC,GAOhC,SAATC,GAAUvC,EAAOwC,OACZ1B,EAAI2B,KACJD,GAAkBE,EAAT5B,EAAI6B,GAChBC,EAAKC,EACLA,EAAK7C,EACL8C,EAAKH,EACLA,EAAK7B,GACKwB,EACVO,GAAM7C,EAEN6C,EAAKD,GAAM5C,EAAQ4C,IAAO9B,EAAIgC,IAAOH,EAAKG,OAhBzCD,EAAK7C,EACR4C,EAAK5C,EACL2C,EAAKF,KACLK,EAAKH,EACLD,EAAML,GAAkB,GACxBU,EAAiBxC,KAAKyC,IAAI,IAAW,EAANN,SAsBzB,CAACH,OAAAA,GAAQU,MARP,SAARA,QAAgBL,EAAKC,EAAKP,EAAW,EAAIO,EAAIC,EAAKH,EAAK,GAQjCO,YAPR,SAAdA,YAAcC,OACTC,EAAON,EACVO,EAAOT,EACP9B,EAAI2B,YACJU,GAA+B,IAAhBA,GAAsBA,IAAgBN,GAAMN,GAAOY,GAC3DR,IAAOG,GAAeC,EAATjC,EAAIgC,EAAuB,GAAKD,GAAMP,EAAWe,GAAQA,MAAWf,EAAWxB,EAAI6B,GAAMS,GAAQ,MAI7G,SAAZE,EAAaC,EAAGC,UACfA,IAAmBD,EAAEE,YAAcF,EAAEC,iBAC9BD,EAAEG,eAAiBH,EAAEG,eAAe,GAAKH,EAE/B,SAAlBI,EAAkBC,OACbZ,EAAMzC,KAAKyC,UAALzC,KAAYqD,GACrBlB,EAAMnC,KAAKmC,UAALnC,KAAYqD,UACZrD,KAAKsD,IAAIb,IAAQzC,KAAKsD,IAAInB,GAAOM,EAAMN,EAE3B,SAApBoB,KACCC,GAAgB5F,GAAK6F,KAAKC,UAAUF,gBACnBA,GAAcC,MA7FnB,SAAbE,iBACKF,EAAOD,GAAcC,KACxBG,EAAOH,EAAKI,QAAU,GACtBC,EAAYL,EAAKtE,WACjB4E,EAAUN,EAAKvF,SAChB4F,EAAUxC,WAAVwC,EAAkB3E,IAClB4E,EAAQzC,WAARyC,EAAgB7F,IAChBiB,GAAa2E,EACb5F,GAAW6F,EACX3D,EAAU,iBAAC4D,EAAMvE,UAAUmE,EAAKI,GAAMvE,IAoFCkE,GAE5B,SAAZM,EAAYR,UACX7F,GAAO6F,GAAQ9F,MACkB,oBAAduG,UAA6BA,SAASC,OACxDxE,GAAO9B,OAEPsD,IADAF,GAAOiD,UACOE,gBACdC,GAAQpD,GAAKkD,KACb7F,EAAQ,CAACqB,GAAMsB,GAAME,GAAQkD,IACpBzG,GAAK4C,MAAM8D,MACpBC,GAAe,mBAAoBF,GAAQ,UAAY,QAEvDG,GAAWC,EAASC,QAAU/E,GAAKgF,YAAchF,GAAKgF,WAAW,oCAAoCC,QAAU,EAAK,iBAAkBjF,IAAmC,EAA3BkF,UAAUC,gBAAmD,EAA7BD,UAAUE,iBAAwB,EAAI,EACpNC,GAAcP,EAASQ,YAAc,iBAAkB9D,GAAS,4CAAgD,kBAAmBA,GAAkD,kDAAxC,uCAA2F+D,MAAM,KAC9OC,WAAW,kBAAMzF,EAAW,GAAG,KAC/B6D,IACA6B,GAAe,GAETA,OAzHLxH,GAAMwH,GAAsBzF,GAAMsB,GAAME,GAAQkD,GAAOG,GAAUD,GAAcf,GAAelF,EAAOW,GAAa+F,GAGrHtF,EAAW,EACX2F,GAAa,GACblG,GAAa,GACbjB,GAAW,GACXgE,GAAWoD,KAAKC,IAChBnF,EAAU,iBAAC4D,EAAMvE,UAAUA,GAgB3B+F,EAAc,aACdC,EAAa,YAoBbC,GAAc,CAAC3E,EAAGyE,EAAaG,EAAG,OAAQC,GAAI,OAAQC,GAAI,QAASC,IAAK,QAASC,EAAG,QAASC,GAAI,QAAS3C,EAAG,IAAKrC,GAAI3B,EAAiB,SAASI,UAAgB+B,UAAUC,OAAS9B,GAAKsG,SAASxG,EAAO4B,GAAUL,MAAQrB,GAAKuG,aAAejF,GAAKuE,IAAgBrE,GAAOqE,IAAgBnB,GAAMmB,IAAgB,KAChTnE,GAAY,CAACN,EAAG0E,EAAYE,EAAG,MAAOC,GAAI,MAAOC,GAAI,SAAUC,IAAK,SAAUC,EAAG,SAAUC,GAAI,SAAU3C,EAAG,IAAK8C,GAAIT,GAAa1E,GAAI3B,EAAiB,SAASI,UAAgB+B,UAAUC,OAAS9B,GAAKsG,SAASP,GAAY1E,KAAMvB,GAASE,GAAKyG,aAAenF,GAAKwE,IAAetE,GAAOsE,IAAepB,GAAMoB,IAAe,KA8EhUC,GAAYS,GAAK9E,GACjBlC,GAAWC,MAAQ,MAENqF,sBAKZ4B,KAAA,cAAKC,GACJlB,IAAgBnB,EAAUrG,KAASgD,QAAQC,KAAK,wCAChD2C,IAAiBD,QACZgD,EAA6bD,EAA7bC,UAAWC,EAAkbF,EAAlbE,YAAahI,EAAqa8H,EAAra9H,KAAMkD,EAA+Z4E,EAA/Z5E,OAAQ+E,EAAuZH,EAAvZG,WAAYC,EAA2YJ,EAA3YI,SAAUzD,EAAiYqD,EAAjYrD,eAAgB0D,EAAiXL,EAAjXK,OAAQC,EAAyWN,EAAzWM,YAAaC,EAA4VP,EAA5VO,OAAQC,EAAoVR,EAApVQ,WAAYC,EAAwUT,EAAxUS,MAAOC,EAAiUV,EAAjUU,YAAaC,EAAoTX,EAApTW,UAAWC,EAAySZ,EAAzSY,OAAQC,EAAiSb,EAAjSa,QAASC,EAAwRd,EAAxRc,UAAWC,EAA6Qf,EAA7Qe,QAASC,EAAoQhB,EAApQgB,OAAQC,EAA4PjB,EAA5PiB,KAAMC,EAAsPlB,EAAtPkB,OAAQC,EAA8OnB,EAA9OmB,UAAWC,EAAmOpB,EAAnOoB,UAAWC,EAAwNrB,EAAxNqB,SAAUC,EAA8MtB,EAA9MsB,UAAWC,EAAmMvB,EAAnMuB,UAAWC,EAAwLxB,EAAxLwB,QAASC,EAA+KzB,EAA/KyB,WAAYC,EAAmK1B,EAAnK0B,OAAQC,EAA2J3B,EAA3J2B,YAAaC,EAA8I5B,EAA9I4B,aAAcC,EAAgI7B,EAAhI6B,eAAgBC,EAAgH9B,EAAhH8B,aAAcC,EAAkG/B,EAAlG+B,QAASC,EAAyFhC,EAAzFgC,SAAUC,EAA+EjC,EAA/EiC,UAAWC,EAAoElC,EAApEkC,QAASC,EAA2DnC,EAA3DmC,YAAa9J,EAA8C2H,EAA9C3H,QAAS+J,EAAqCpC,EAArCoC,YAAaC,EAAwBrC,EAAxBqC,SAAUC,EAActC,EAAdsC,WAyBpa,SAAfC,YAAqBC,GAAc5G,KACpB,SAAf6G,GAAgB/F,EAAGgG,UAAsBC,GAAKlC,MAAQ/D,IAAO6D,IAAWA,EAAO1I,QAAQ6E,EAAEtB,SAAasH,GAAoBE,IAAkC,UAAlBlG,EAAEmG,aAA6BlB,GAAeA,EAAYjF,EAAGgG,GAO9L,SAAThH,SACKoH,EAAKH,GAAKI,OAASjG,EAAgBiG,IACtCC,EAAKL,GAAKM,OAASnG,EAAgBmG,IACnCC,EAAWxJ,KAAKsD,IAAI8F,IAAO7C,EAC3BkD,EAAWzJ,KAAKsD,IAAIgG,IAAO/C,EAC5BoB,IAAa6B,GAAYC,IAAa9B,EAASsB,GAAMG,EAAIE,EAAID,GAAQE,IACjEC,IACHnC,GAAyB,EAAd4B,GAAKI,QAAchC,EAAQ4B,IACtC3B,GAAU2B,GAAKI,OAAS,GAAK/B,EAAO2B,IACpCxB,GAAaA,EAAUwB,IACvBrB,GAAeqB,GAAKI,OAAS,GAAQK,GAAa,GAAO9B,EAAUqB,IACnES,GAAaT,GAAKI,OAClBA,GAAO,GAAKA,GAAO,GAAKA,GAAO,GAAK,GAEjCI,IACHjC,GAAwB,EAAdyB,GAAKM,QAAc/B,EAAOyB,IACpC1B,GAAQ0B,GAAKM,OAAS,GAAKhC,EAAK0B,IAChCvB,GAAaA,EAAUuB,IACvBpB,GAAeoB,GAAKM,OAAS,GAAQI,GAAa,GAAO9B,EAAUoB,IACnEU,GAAaV,GAAKM,OAClBA,GAAO,GAAKA,GAAO,GAAKA,GAAO,GAAK,IAEjCK,IAASC,MACZ7B,GAAUA,EAAOiB,IACbY,KACH3C,EAAO+B,IACPY,IAAU,GAEXD,IAAQ,GAETE,MAAYA,IAAS,IAAUlB,GAAcA,EAAWK,IACpDc,KACH1B,EAAQY,IACRc,IAAU,GAEXC,GAAK,EAEI,SAAVC,GAAWC,EAAGC,EAAGC,GAChBf,GAAOe,IAAUF,EACjBX,GAAOa,IAAUD,EACjBlB,GAAKoB,IAAIrI,OAAOkI,GAChBjB,GAAKqB,IAAItI,OAAOmI,GAChBzD,EAAkBsD,GAAPA,IAAYO,sBAAsBvI,IAAWA,KAEjC,SAAxBwI,GAAyBN,EAAGC,GACvBxB,IAAa8B,KAChBxB,GAAKwB,KAAOA,GAAOzK,KAAKsD,IAAI4G,GAAKlK,KAAKsD,IAAI6G,GAAK,IAAM,IACrDL,IAAS,GAEG,MAATW,KACHpB,GAAO,IAAMa,EACbjB,GAAKoB,IAAIrI,OAAOkI,GAAG,IAEP,MAATO,KACHlB,GAAO,IAAMY,EACblB,GAAKqB,IAAItI,OAAOmI,GAAG,IAEpBzD,EAAkBsD,GAAPA,IAAYO,sBAAsBvI,IAAWA,KAE/C,SAAV0I,GAAU1H,OACL+F,GAAa/F,EAAG,QAEhBkH,GADJlH,EAAID,EAAUC,EAAGC,IACP0H,QACTR,EAAInH,EAAE4H,QACNxB,EAAKc,EAAIjB,GAAKiB,EACdZ,EAAKa,EAAIlB,GAAKkB,EACdU,EAAa5B,GAAK4B,WACnB5B,GAAKiB,EAAIA,EACTjB,GAAKkB,EAAIA,GACLU,GAAc7K,KAAKsD,IAAI2F,GAAK6B,OAASZ,IAAM1D,GAAexG,KAAKsD,IAAI2F,GAAK8B,OAASZ,IAAM3D,KAC1FU,IAAW2C,IAAU,GACrBgB,IAAe5B,GAAK4B,YAAa,GACjCL,GAAsBpB,EAAIE,GAC1BuB,GAAc7D,GAAeA,EAAYiC,MAkB9B,SAAb+B,GAAahI,OACR+F,GAAa/F,EAAG,IACpBlE,EAAgBoJ,EAAexG,EAASuJ,GAAUjG,GAAY,GAAI0F,IAAS,OACvEQ,EAAcjC,GAAK4B,aAAgD,EAAjC7K,KAAKsD,IAAI2F,GAAKiB,EAAIjB,GAAK6B,SAAgD,EAAjC9K,KAAKsD,IAAI2F,GAAKkB,EAAIlB,GAAK8B,SAClGI,EAAYpI,EAAUC,GAClBkI,IACJjC,GAAKoB,IAAI3H,QACTuG,GAAKqB,IAAI5H,QACLO,GAAkByF,GACrB9K,GAAKwN,YAAY,IAAM,cACS,IAA3BlJ,KAAa4G,KAAsB9F,EAAEqI,oBACpCrI,EAAEtB,OAAO4J,MACZtI,EAAEtB,OAAO4J,aACH,GAAIL,GAASM,YAAa,KAC5BC,EAAiBP,GAASM,YAAY,eAC1CC,EAAeC,eAAe,SAAS,GAAM,EAAM9L,GAAM,EAAGwL,EAAUO,QAASP,EAAUQ,QAASR,EAAUR,QAASQ,EAAUP,SAAS,GAAO,GAAO,GAAO,EAAO,EAAG,MACvK5H,EAAEtB,OAAOkK,cAAcJ,OAM5BvC,GAAK4B,WAAa5B,GAAK4C,YAAc5C,GAAK/J,WAAY,EACtDyH,IAAWuB,GAAgB4D,GAAkBC,SAAQ,GACrD9E,GAAaiE,GAAejE,EAAUgC,IACtC7B,GAAaA,EAAU6B,GAAMiC,IAEZ,SAAlBc,GAAkBhJ,UAAKA,EAAEiJ,SAA8B,EAAnBjJ,EAAEiJ,QAAQxK,SAAewH,GAAK4C,aAAc,IAAS1D,EAAenF,EAAGiG,GAAK4B,YAChG,SAAhBqB,YAAuBjD,GAAK4C,aAAc,IAAUzD,EAAaa,IACtD,SAAXkD,GAAWnJ,OACN+F,GAAa/F,QACbkH,EAAIkC,KACPjC,EAAIkC,KACLpC,IAASC,EAAIoC,IAAW7D,GAAc0B,EAAIoC,IAAW9D,EAAa,GAClE6D,GAAUpC,EACVqC,GAAUpC,EACVxD,GAAUmF,GAAkBC,SAAQ,IAE1B,SAAXS,GAAWxJ,OACN+F,GAAa/F,IACjBA,EAAID,EAAUC,EAAGC,GACjBoF,IAAY0B,IAAU,OAClB0C,GAA8B,IAAhBzJ,EAAE0J,UAAkBjG,EAA6B,IAAhBzD,EAAE0J,UAAkB/M,GAAKgN,YAAc,GAAK7F,EAC/FmD,GAAQjH,EAAEqG,OAASoD,EAAYzJ,EAAEuG,OAASkD,EAAY,GACtD9F,IAAWuB,GAAgB4D,GAAkBC,SAAQ,IAE5C,SAAVa,GAAU5J,OACL+F,GAAa/F,QACbkH,EAAIlH,EAAE2H,QACTR,EAAInH,EAAE4H,QACNxB,EAAKc,EAAIjB,GAAKiB,EACdZ,EAAKa,EAAIlB,GAAKkB,EACflB,GAAKiB,EAAIA,EACTjB,GAAKkB,EAAIA,EACTP,IAAQ,GACPR,GAAME,IAAOkB,GAAsBpB,EAAIE,IAE9B,SAAXuD,GAAW7J,GAAMiG,GAAKlC,MAAQ/D,EAAG8E,EAAQmB,IAC3B,SAAd6D,GAAc9J,GAAMiG,GAAKlC,MAAQ/D,EAAG+E,EAAWkB,IACpC,SAAX8D,GAAW/J,UAAK+F,GAAa/F,IAAOD,EAAUC,EAAGC,IAAmBuF,EAAQS,SAtLxEvH,OAASA,EAASpB,EAAWoB,IAAWP,QACxCmF,KAAOA,EACDO,EAAXA,GAAoBjJ,GAAK4C,MAAMC,QAAQoG,GACvCN,EAAYA,GAAa,KACzBC,EAAcA,GAAe,EAC7BM,EAAaA,GAAc,EAC3B2B,EAAcA,GAAe,EAC7BjK,EAAOA,GAAQ,sBACfkI,GAAwB,IAAbA,EACID,EAAfA,GAA4BuG,WAAWrN,GAAKsN,iBAAiB5I,IAAOoC,aAAe,OAC/EuD,GAAI8B,GAAmBjC,GAASD,GAAOG,GAASD,GAAQW,GAC3DxB,GAAOiE,KACPxD,GAAa,EACbC,GAAa,EACbyC,GAActL,EAAeY,EAAQgE,IACrC2G,GAAcvL,EAAeY,EAAQL,IACrCiL,GAAUF,KACVG,GAAUF,KACVnD,IAAgB1K,EAAKL,QAAQ,YAAcK,EAAKL,QAAQ,YAAiC,gBAAnB6G,GAAY,GAClFmI,GAAa/O,EAAYsD,GACzBuJ,GAAWvJ,EAAO0L,eAAiBnM,GACnCoI,GAAS,CAAC,EAAG,EAAG,GAChBE,GAAS,CAAC,EAAG,EAAG,GAChBT,GAAc,EAqFduE,GAAWpE,GAAK9B,QAAU,SAAAnE,GACrB+F,GAAa/F,EAAG,KACpBiG,GAAKwB,KAAOA,GAAO,KACnBqB,GAAkBwB,QAClBrE,GAAK/J,WAAY,EACjB8D,EAAID,EAAUC,GACd0G,GAAaC,GAAa,EAC1BV,GAAK6B,OAAS7B,GAAKiB,EAAIlH,EAAE2H,QACzB1B,GAAK8B,OAAS9B,GAAKkB,EAAInH,EAAE4H,QACzB3B,GAAKoB,IAAI3H,QACTuG,GAAKqB,IAAI5H,QACTnE,EAAa2J,EAAexG,EAASuJ,GAAUjG,GAAY,GAAI0F,GAASzH,GAAgB,GACxFgG,GAAKI,OAASJ,GAAKM,OAAS,EAC5BpC,GAAWA,EAAQ8B,MA+DrB6C,GAAoB7C,GAAKsE,IAAM3P,GAAKwN,YAAYxE,GAAe,IA9JjD,SAAb4G,aACCvE,GAAKoB,IAAI3H,QACTuG,GAAKqB,IAAI5H,QACToJ,GAAkBwB,QAClB3G,GAAUA,EAAOsC,MA0J8DqE,QAEjFrE,GAAKI,OAASJ,GAAKM,OAAS,EAC5BN,GAAKoB,IAAMxI,EAAiB,EAAG,IAAI,GACnCoH,GAAKqB,IAAMzI,EAAiB,EAAG,IAAI,GACnCoH,GAAKqD,QAAUF,GACfnD,GAAKsD,QAAUF,GACfpD,GAAK4B,WAAa5B,GAAK4C,YAAc5C,GAAK/J,WAAY,EACtD+J,GAAKwE,OAAS,SAAAzK,UACRiG,GAAKyE,YACTnP,EAAa4O,GAAalC,GAAWvJ,EAAQ,SAAU1C,GAC7B,GAA1BR,EAAKL,QAAQ,WAAkBI,EAAa4O,GAAalC,GAAWvJ,EAAQ,SAAUyK,GAAUlJ,EAAgBtE,GACvF,GAAzBH,EAAKL,QAAQ,UAAiBI,EAAamD,EAAQ,QAAS8K,GAAUvJ,EAAgBtE,IACxD,GAAzBH,EAAKL,QAAQ,UAAiBqG,IAAwC,GAA3BhG,EAAKL,QAAQ,cAC5DI,EAAamD,EAAQsD,GAAY,GAAIqI,GAAUpK,EAAgBtE,GAC/DJ,EAAa0M,GAAUjG,GAAY,GAAIgG,IACvCzM,EAAa0M,GAAUjG,GAAY,GAAIgG,IACvCtC,GAAenK,EAAamD,EAAQ,QAASmH,IAAc,GAAO,GAClEL,GAAWjK,EAAamD,EAAQ,QAASqL,IACzC5E,GAAkB5J,EAAa0M,GAAU,eAAgBe,IACzD5D,GAAgB7J,EAAa0M,GAAU,aAAciB,IACrDpE,GAAWvJ,EAAamD,EAAQ6C,GAAe,QAASsI,IACxD9E,GAAcxJ,EAAamD,EAAQ6C,GAAe,QAASuI,IAC3D9E,GAAUzJ,EAAamD,EAAQ6C,GAAe,OAAQqI,KAEvD3D,GAAKyE,WAAY,EACjB1K,GAAKA,EAAExE,MAAQ6O,GAASrK,GACxBsF,GAAYA,EAASW,KAEfA,IAERA,GAAK0E,QAAU,WACV1E,GAAKyE,YAERrI,GAAWuI,OAAO,SAAAC,UAAKA,IAAM5E,IAAQ7K,EAAYyP,EAAEnM,UAASD,QAAU3C,EAAgBqO,GAAalC,GAAWvJ,EAAQ,SAAU1C,GAC5HiK,GAAK/J,YACR+J,GAAKoB,IAAI3H,QACTuG,GAAKqB,IAAI5H,QACT5D,EAAgBoJ,EAAexG,EAASuJ,GAAUjG,GAAY,GAAI0F,IAAS,IAE5E5L,EAAgBqO,GAAalC,GAAWvJ,EAAQ,SAAUyK,GAAUxN,GACpEG,EAAgB4C,EAAQ,QAAS8K,GAAU7N,GAC3CG,EAAgB4C,EAAQsD,GAAY,GAAIqI,GAAU1O,GAClDG,EAAgBmM,GAAUjG,GAAY,GAAIgG,IAC1ClM,EAAgBmM,GAAUjG,GAAY,GAAIgG,IAC1ClM,EAAgB4C,EAAQ,QAASmH,IAAc,GAC/C/J,EAAgB4C,EAAQ,QAASqL,IACjCjO,EAAgBmM,GAAU,eAAgBe,IAC1ClN,EAAgBmM,GAAU,aAAciB,IACxCpN,EAAgB4C,EAAQ6C,GAAe,QAASsI,IAChD/N,EAAgB4C,EAAQ6C,GAAe,QAASuI,IAChDhO,EAAgB4C,EAAQ6C,GAAe,OAAQqI,IAC/C3D,GAAKyE,UAAYzE,GAAK/J,UAAY+J,GAAK4B,YAAa,EACpDtC,GAAaA,EAAUU,MAIzBA,GAAK6E,KAAO,WACX7E,GAAK0E,cACDvM,EAAIiE,GAAWlH,QAAQ8K,IACtB,GAAL7H,GAAUiE,GAAW0I,OAAO3M,EAAG,GAC/BnC,KAAgBgK,KAAShK,GAAc,IAGxCoG,GAAW/D,KAAK2H,IAChBf,GAAgB9J,EAAYsD,KAAYzC,GAAcgK,IAEtDA,GAAKwE,OAAO1G,8JAILmG,KAAK7C,IAAI1H,2DAGTuK,KAAK5C,IAAI3H,8CA1QL2D,QACND,KAAKC,GA8QZ7B,EAASuJ,QAAU,SACnBvJ,EAASwJ,OAAS,SAAA3H,UAAQ,IAAI7B,EAAS6B,IACvC7B,EAASyJ,SAAWjK,EACpBQ,EAAS0J,OAAS,kBAAM9I,GAAW+I,SACnC3J,EAAS4J,QAAU,SAAArE,UAAM3E,GAAWuI,OAAO,SAAAC,UAAKA,EAAEvH,KAAK0D,KAAOA,IAAI,IAElErM,KAAcC,GAAKE,eAAe2G,GC5YX,SAAtB6J,YAA4BC,GAAiB,EACzB,SAApBC,YAA0BD,GAAiB,EAC5B,SAAfE,GAAe1O,UAAKA,EACX,SAAT2O,GAASjP,UAASO,KAAKC,MAAc,IAARR,GAAkB,KAAU,EACzC,SAAhBkP,WAAyC,oBAAZ9Q,OAClB,SAAXF,YAAiBC,IAAS+Q,OAAoB/Q,GAAOC,OAAOD,OAASA,GAAKE,gBAAkBF,GAC9E,SAAdQ,GAAc4E,YAAQ1E,EAAMH,QAAQ6E,GACnB,SAAjB4L,GAAiB5Q,UAAWD,EAAcC,EAAS,2BAA6BI,GAAYJ,GAAW,kBAAO6Q,GAAYC,MAAQnP,GAAKoP,WAAYF,GAAYG,OAASrP,GAAKgN,YAAoBkC,IAAgB,kBAAMI,GAAWjR,KAGrN,SAAbkR,GAAclR,SAAU+C,IAAAA,EAAGiF,IAAAA,GAAID,IAAAA,EAAG1C,IAAAA,SAAQtC,EAAI,SAAWiF,KAAQ3C,EAAItF,EAAcC,EAAS+C,IAAMsC,IAAMuL,GAAe5Q,EAAf4Q,GAA0B7I,GAAK3H,GAAYJ,IAAYmD,GAAOJ,IAAMsD,GAAMtD,KAAOpB,GAAK,QAAUqG,IAAO7E,GAAO,SAAW6E,IAAO3B,GAAM,SAAW2B,IAAOhI,EAAQ+C,GAAK/C,EAAQ,SAAWgI,GAC1Q,SAAtBmJ,GAAuB1Q,EAAM2Q,OACvB,IAAIhO,EAAI,EAAGA,EAAIiO,EAAa5N,OAAQL,GAAK,EAC3CgO,KAAWA,EAAOjR,QAAQkR,EAAajO,EAAE,KAAQ3C,EAAK4Q,EAAajO,GAAIiO,EAAajO,EAAE,GAAIiO,EAAajO,EAAE,IAGjG,SAAZkO,GAAY7P,SAA2B,iBAAXA,EACd,SAAd8P,GAAc9P,SAA2B,mBAAXA,EAClB,SAAZ+P,GAAY/P,SAA2B,iBAAXA,EAChB,SAAZgQ,GAAYhQ,SAA2B,iBAAXA,EAUZ,SAAhBiQ,GAAiBC,EAAWC,EAAUtC,UAAUqC,GAAaA,EAAUE,SAASD,EAAW,EAAI,IAAMtC,GAASqC,EAAUrC,QAC5G,SAAZwC,GAAa7G,EAAMxK,MACdwK,EAAK8G,QAAS,KACbC,EAASvR,EAAKwK,GAClB+G,GAAUA,EAAOC,YAAchH,EAAKiH,kBAAoBF,IAqBtC,SAApBG,GAAoBnS,UAAW2B,GAAKsN,iBAAiBjP,GAKtC,SAAfoS,GAAgBC,EAAKC,OACf,IAAI3K,KAAK2K,EACZ3K,KAAK0K,IAASA,EAAI1K,GAAK2K,EAAS3K,WAE3B0K,EAQG,SAAXE,GAAYvS,SAAUgI,IAAAA,UAAQhI,EAAQ,SAAWgI,IAAOhI,EAAQ,SAAWgI,IAAO,EAC5D,SAAtBwK,GAAsBC,OAIpB9K,EAHGtC,EAAI,GACPqN,EAASD,EAASC,OAClBC,EAAWF,EAASE,eAEhBhL,KAAK+K,EACTrN,EAAE/B,KAAKoP,EAAO/K,GAAKgL,UAEbtN,EAGW,SAAnBuN,GAAmBC,OACdC,EAAOlT,GAAK4C,MAAMsQ,KAAKD,GAC1BxN,EAAI0N,MAAMC,QAAQH,IAAyBA,EAAqBzC,MAAM,GAAG6C,KAAK,SAAC5N,EAAG6N,UAAM7N,EAAI6N,WACtF7N,EAAI,SAAC5D,EAAO0R,EAAWC,OACzBhQ,cADyBgQ,IAAAA,EAAW,OAEnCD,SACGL,EAAKrR,MAEG,EAAZ0R,EAAe,KAClB1R,GAAS2R,EACJhQ,EAAI,EAAGA,EAAIiC,EAAE5B,OAAQL,OACrBiC,EAAEjC,IAAM3B,SACJ4D,EAAEjC,UAGJiC,EAAEjC,EAAE,OAEXA,EAAIiC,EAAE5B,OACNhC,GAAS2R,EACFhQ,QACFiC,EAAEjC,IAAM3B,SACJ4D,EAAEjC,UAILiC,EAAE,IACN,SAAC5D,EAAO0R,EAAWC,YAAAA,IAAAA,EAAW,UAC7BC,EAAUP,EAAKrR,UACX0R,GAAanR,KAAKsD,IAAI+N,EAAU5R,GAAS2R,GAAeC,EAAU5R,EAAQ,GAAO0R,EAAY,EAAKE,EAAUP,EAAKK,EAAY,EAAI1R,EAAQoR,EAAuBpR,EAAQoR,IAIjK,SAAjBS,GAAkB7S,EAAMT,EAASuT,EAAOC,UAAaD,EAAMrM,MAAM,KAAKuM,QAAQ,SAAAjT,UAAQC,EAAKT,EAASQ,EAAMgT,KAC3F,SAAfjT,GAAgBP,EAASQ,EAAMC,EAAMC,EAAYC,UAAYX,EAAQY,iBAAiBJ,EAAMC,EAAM,CAACI,SAAUH,EAAYC,UAAWA,IAClH,SAAlBG,GAAmBd,EAASQ,EAAMC,EAAME,UAAYX,EAAQe,oBAAoBP,EAAMC,IAAQE,GAC7E,SAAjB+S,GAAkBjT,EAAMJ,EAAIsT,UAAeA,GAAcA,EAAWC,cAAgBnT,EAAKJ,EAAI,QAASsT,GAIxF,SAAdE,GAAepS,EAAOqS,MACjBxC,GAAU7P,GAAQ,KACjBsS,EAAUtS,EAAMtB,QAAQ,KAC3B6T,GAAYD,GAAYtS,EAAMwS,OAAOF,EAAQ,GAAK,GAAK/E,WAAWvN,EAAMyS,OAAOH,EAAU,IAAM,GAC3FA,IACHtS,EAAMtB,QAAQ,KAAO4T,IAAaC,GAAYF,EAAO,KACtDrS,EAAQA,EAAMyS,OAAO,EAAGH,EAAQ,IAEjCtS,EAAQuS,GAAavS,KAAS0S,EAAaA,EAAU1S,GAASqS,GAAQrS,EAAMtB,QAAQ,KAAO6O,WAAWvN,GAASqS,EAAO,IAAM9E,WAAWvN,IAAU,UAE3IA,EAEQ,SAAhB2S,GAAiB5T,EAAMwF,EAAMqO,EAAWlB,IAAiE9Q,EAAQiS,EAAcC,OAA3EC,IAAAA,WAAYC,IAAAA,SAAUC,IAAAA,SAAUC,IAAAA,OAAQC,IAAAA,WACvF5P,EAAI/B,GAAK4R,cAAc,OAC1BC,EAAmB1U,GAAYiU,IAAsD,UAAxCtU,EAAcsU,EAAW,WACtEU,GAA2C,IAA9BvU,EAAKL,QAAQ,YAC1B6U,EAASF,EAAmBzO,GAAQgO,EACpCY,GAAqC,IAA3BzU,EAAKL,QAAQ,SACvB+U,EAAQD,EAAUT,EAAaC,EAC/BU,EAAM,gBAAkBD,EAAQ,cAAgBR,EAAW,UAAYQ,EAAQ,gBAAkBN,EAAa,8IAC/GO,GAAO,cAAgBJ,GAAcR,IAAuBO,EAAmB,SAAW,cACzFC,IAAcR,GAAuBO,IAAsBK,IAAQhC,IAAc9P,GAAY+R,EAASC,GAAW,KAAOhT,EAAS2M,WAAW2F,IAAW,OACxJL,IAAiBa,GAAO,+CAAiDb,EAAagB,YAAc,OACpGtQ,EAAEuQ,SAAWN,EACbjQ,EAAEwQ,aAAa,QAAS,eAAiBhV,GAAQwF,EAAO,WAAaA,EAAO,KAC5EhB,EAAEyQ,MAAMC,QAAUP,EAClBnQ,EAAE2Q,UAAY3P,GAAiB,IAATA,EAAaxF,EAAO,IAAMwF,EAAOxF,EACvDwU,EAAOY,SAAS,GAAKZ,EAAOa,aAAa7Q,EAAGgQ,EAAOY,SAAS,IAAMZ,EAAOc,YAAY9Q,GACrFA,EAAE+Q,QAAU/Q,EAAE,SAAWmO,EAAUhL,GAAGH,IACtCgO,EAAgBhR,EAAG,EAAGmO,EAAW8B,GAC1BjQ,EAiBA,SAARiR,YAA6C,GAA/B/R,KAAagS,KAAoCC,EAAXA,GAAoB5J,sBAAsB6J,IAClF,SAAZpV,KACMC,GAAgBA,EAAYC,aAAaD,EAAY6L,OAASzG,GAAMgQ,eACxElV,GAAWC,QACPH,EACQkV,EAAXA,GAAoB5J,sBAAsB6J,GAE1CA,IAEDF,IAAmBI,EAAU,eAC7BJ,GAAkBhS,MAGC,SAArBqS,KACCC,EAAmB7U,GAAKoP,WACxB0F,EAAoB9U,GAAKgN,YAEd,SAAZ+H,KACCvV,GAAWC,QACVuV,IAAgBC,GAAkB3T,GAAK4T,mBAAsB5T,GAAK6T,yBAA6BC,GAAuBP,IAAqB7U,GAAKoP,cAAc/O,KAAKsD,IAAI3D,GAAKgN,YAAc8H,GAAwC,IAAnB9U,GAAKgN,cAAuBqI,EAAajJ,SAAQ,GAInP,SAAfkJ,YAAqBnW,GAAgB0E,EAAe,YAAayR,KAAiBC,IAAY,GAG5E,SAAlBC,GAAkBC,OACZ,IAAIhU,EAAI,EAAGA,EAAIiU,EAAa5T,OAAQL,GAAG,IACtCgU,GAASC,EAAajU,EAAE,IAAMiU,EAAajU,EAAE,GAAGkU,QAAUF,KAC9DC,EAAajU,GAAGqS,MAAMC,QAAU2B,EAAajU,EAAE,GAC/CiU,EAAajU,GAAGmU,SAAWF,EAAajU,GAAGoS,aAAa,YAAa6B,EAAajU,EAAE,IAAM,IAC1FiU,EAAajU,EAAE,GAAGoU,QAAU,GAIlB,SAAbC,GAAc3H,EAAMsH,OACfM,MACCC,GAAK,EAAGA,GAAKC,GAAUnU,OAAQkU,OACnCD,EAAUE,GAAUD,MACHP,GAASM,EAAQG,OAAST,IACtCtH,EACH4H,EAAQ5H,KAAK,GAEb4H,EAAQI,QAAO,GAAM,IAIxBV,GAASD,GAAgBC,GACzBA,GAASd,EAAU,UAEC,SAArByB,GAAsBlW,EAAmBoC,GACxC9C,GAAWC,SACV6C,GAAU+T,IAAmB7W,GAAWsS,QAAQ,SAAApB,UAAOd,GAAYc,IAAQA,EAAIlQ,YAAckQ,EAAI4F,IAAM,KACxG3G,GAAUzP,KAAuBF,GAAKC,QAAQC,kBAAoBqW,EAAqBrW,GAyG3E,SAAbsW,GAAcC,EAAKC,EAAQC,EAAIC,OACzBH,EAAII,MAAMC,UAAW,SAIxB9Q,EAHGvE,EAAIsV,EAAiBjV,OACxBkV,EAAcN,EAAO5C,MACrBmD,EAAWR,EAAI3C,MAETrS,KAENuV,EADAhR,EAAI+Q,EAAiBtV,IACJkV,EAAG3Q,GAErBgR,EAAYE,SAA2B,aAAhBP,EAAGO,SAA0B,WAAa,WACjD,WAAfP,EAAGQ,UAA0BH,EAAYG,QAAU,gBACpDF,EAASvD,GAAWuD,EAASxD,GAAU,OACvCuD,EAAYI,UAAYT,EAAGS,WAAa,OACxCJ,EAAYK,SAAW,UACvBL,EAAYM,UAAY,aACxBN,EAAYO,IAAU3G,GAAS6F,EAAK1Q,IAAeyR,GACnDR,EAAYS,IAAW7G,GAAS6F,EAAK/U,IAAa8V,GAClDR,EAAYU,IAAYT,EAASU,IAAWV,EAAQ,IAASA,EAAQ,KAAU,IAC/EW,GAAUhB,GACVK,EAASM,IAAUN,EAAQ,SAAmBN,EAAGY,IACjDN,EAASQ,IAAWR,EAAQ,UAAoBN,EAAGc,IACnDR,EAASS,IAAYf,EAAGe,IACpBjB,EAAIoB,aAAenB,IACtBD,EAAIoB,WAAW3D,aAAawC,EAAQD,GACpCC,EAAOvC,YAAYsC,IAEpBA,EAAII,MAAMC,WAAY,GAsBZ,SAAZgB,GAAYzZ,WACP0Z,EAAIC,EAAYlW,OACnBgS,EAAQzV,EAAQyV,MAChBmE,EAAQ,GACRxW,EAAI,EACEA,EAAIsW,EAAGtW,IACbwW,EAAMtW,KAAKqW,EAAYvW,GAAIqS,EAAMkE,EAAYvW,YAE9CwW,EAAMrX,EAAIvC,EACH4Z,EAuBS,SAAjBC,GAAkBpY,EAAOiW,EAASoC,EAAc3G,EAAW4G,EAAQC,EAAQC,EAAgBhP,EAAMiP,EAAgBC,EAAarF,EAAkBsF,EAAa7F,GAC5JhD,GAAY9P,KAAWA,EAAQA,EAAMwJ,IACjCqG,GAAU7P,IAAgC,QAAtBA,EAAMyS,OAAO,EAAE,KACtCzS,EAAQ2Y,GAAmC,MAApB3Y,EAAMwS,OAAO,GAAaJ,GAAY,IAAMpS,EAAMyS,OAAO,GAAI4F,GAAgB,QAGpGO,EAAIzS,EAAI5H,EADLsa,EAAO/F,EAAqBA,EAAmB+F,OAAS,KAE5D/F,GAAsBA,EAAmBgG,KAAK,GACzC/I,GAAU/P,GAiBJwY,GACVjE,EAAgBiE,EAAgBH,EAAc3G,GAAW,OAlBnC,CACtB5B,GAAYmG,KAAaA,EAAUA,EAAQzM,QAE1CuP,EAAQC,EAAaC,EAAc5B,EADhC6B,GAAWlZ,GAAS,KAAKyF,MAAM,KAEnClH,EAAUsC,EAAWoV,IAAYrR,IACjCmU,EAASvJ,GAAWjR,IAAY,MACdwa,EAAOI,MAASJ,EAAOK,MAAgD,SAAvC1I,GAAkBnS,GAAS8Y,UAC5EA,EAAU9Y,EAAQyV,MAAMqD,QACxB9Y,EAAQyV,MAAMqD,QAAU,QACxB0B,EAASvJ,GAAWjR,GACpB8Y,EAAW9Y,EAAQyV,MAAMqD,QAAUA,EAAW9Y,EAAQyV,MAAMqF,eAAe,YAE5EL,EAAc5G,GAAY8G,EAAQ,GAAIH,EAAOrH,EAAUpL,IACvD2S,EAAe7G,GAAY8G,EAAQ,IAAM,IAAKb,GAC9CrY,EAAQ+Y,EAAOrH,EAAUxL,GAAKuS,EAAe/G,EAAUxL,GAAKwS,EAAcM,EAAcV,EAASW,EACjGT,GAAkBjE,EAAgBiE,EAAgBS,EAAcvH,EAAY2G,EAAeY,EAAe,IAAOT,EAAe1E,UAA2B,GAAfmF,GAC5IZ,GAAgBA,EAAeY,KAI5BV,EAAQ,KACPnB,EAAWpX,EAAQqY,EACtB7E,EAAU+E,EAAOzE,SAClB8E,EAAK,SAAWlH,EAAUnL,GAC1BgO,EAAgBgE,EAAQnB,EAAU1F,EAAY8B,GAAsB,GAAX4D,IAAoB5D,IAAYH,EAAmB9S,KAAKyC,IAAI4B,GAAMgU,GAAKlX,GAAOkX,IAAOL,EAAOR,WAAWa,KAAQxB,EAAW,GAC/K/D,IACHoF,EAAiBjJ,GAAWgJ,GAC5BnF,IAAqBkF,EAAOvE,MAAMtC,EAAUhL,GAAGR,GAAMuS,EAAe/G,EAAUhL,GAAGR,GAAKwL,EAAUhL,GAAG4S,EAAIf,EAAOjE,QAAWoD,YAGvH5E,GAAsBvU,IACzBqa,EAAKpJ,GAAWjR,GAChBuU,EAAmBgG,KAAKH,GACxBxS,EAAKqJ,GAAWjR,GAChBuU,EAAmByG,cAAgBX,EAAGlH,EAAUxL,GAAKC,EAAGuL,EAAUxL,GAClElG,EAAQA,EAAS8S,EAAmByG,cAAiBZ,GAEtD7F,GAAsBA,EAAmBgG,KAAKD,GACvC/F,EAAqB9S,EAAQO,KAAKC,MAAMR,GAGpC,SAAZwZ,GAAajb,EAASgV,EAAQ6F,EAAKD,MAC9B5a,EAAQwZ,aAAexE,EAAQ,KAEjCrN,EAAG2Q,EADA7C,EAAQzV,EAAQyV,SAEhBT,IAAW3O,GAAO,KAGhBsB,KAFL3H,EAAQkb,QAAUzF,EAAMC,QACxB4C,EAAKnG,GAAkBnS,IAEhB2H,GAAMwT,EAAWC,KAAKzT,KAAM2Q,EAAG3Q,IAA0B,iBAAb8N,EAAM9N,IAAyB,MAANA,IAC1E8N,EAAM9N,GAAK2Q,EAAG3Q,IAGhB8N,EAAMoF,IAAMA,EACZpF,EAAMmF,KAAOA,OAEbnF,EAAMC,QAAU1V,EAAQkb,QAEzBtb,GAAK6F,KAAK4V,SAASrb,GAASwX,QAAU,EACtCxC,EAAOc,YAAY9V,IAWF,SAAnBsb,GAAoBC,EAAUpI,GAIjB,SAAXqI,GAAYvT,EAAUK,EAAMmT,EAAcC,EAASC,OAC9CC,EAAQJ,GAASI,MACpBC,EAAavT,EAAKuT,WAClBC,EAAY,UACbL,EAAeA,GAAgBM,IAC/BJ,EAAWD,GAAWC,GAAY,EAClCD,EAAUA,GAAYzT,EAAWwT,EACjCG,GAASA,EAAM9L,OACfkM,EAAcha,KAAKC,MAAMwZ,GACzBnT,EAAK2T,GAAQhU,GACbK,EAAKwT,UAAYA,GACPG,GAAQ,SAAAxa,UACjBA,EAAQO,KAAKC,MAAM8Z,QACLC,GAAeva,IAAUya,GAA+C,EAAhCla,KAAKsD,IAAI7D,EAAQua,IAAoD,EAAhCha,KAAKsD,IAAI7D,EAAQya,IAC3GN,EAAM9L,OACN0L,GAASI,MAAQ,GAEjBna,EAAQga,EAAeC,EAAUE,EAAMO,MAAQR,EAAUC,EAAMO,MAAQP,EAAMO,MAE9ED,EAAcF,EACNA,EAAcha,KAAKC,MAAMR,IAElC6G,EAAKuT,WAAa,WACjBL,GAASI,MAAQ,EACjBC,GAAcA,EAAWO,KAAKR,IAE/BA,EAAQJ,GAASI,MAAQhc,GAAKyc,GAAGd,EAAUjT,OA3B5C0T,EAAaE,EAFVH,EAAYjZ,EAAeyY,EAAUpI,GACxC8I,EAAO,UAAY9I,EAAUvL,UA+B9B2T,EAASU,GAAQF,GACPnI,aAAe,kBAAM4H,GAASI,OAASJ,GAASI,MAAM9L,SAAW0L,GAASI,MAAQ,IAC5Frb,GAAagb,EAAU,QAASQ,EAAUnI,cACnC4H,OAjhBL5b,GAAMwH,EAAczF,GAAMsB,GAAME,GAAQkD,GAAO/F,EAAO0W,EAAcsF,GAAUC,GAAQC,GAAQC,EAAe9F,GAAapG,GAAgBmM,EAAgB/E,GAAIgF,EAAYC,EAAavL,EAAcwL,GAAOC,GAAqBlG,EAAe3V,EAAa8V,EAAqBN,EAAmBD,EAAkBuG,EAAYC,GAAU9E,EAChV+E,GA8KA9G,EAwDA6B,GAEAkF,GAmDAC,GA1RAzb,GAAW,EACXwC,GAAWoD,KAAKC,IAChB6V,EAASlZ,KACTgS,GAAkB,EAClBmH,GAAW,EAqCXC,GAAOtb,KAAKsD,IAKZ8P,EAAS,QACTC,EAAU,SACV6D,GAAS,QACTE,GAAU,SACVmE,GAAS,QACTC,GAAQ,OACRC,GAAO,MACPC,GAAU,SACVrE,GAAW,UACXC,GAAU,SACVqE,GAAS,QACTC,EAAU,SACVzE,GAAM,KAYNlI,GAAa,SAAbA,WAAcjR,EAAS6d,OAClBjC,EAAQiC,GAAoE,6BAA/C1L,GAAkBnS,GAAS0c,IAAkD9c,GAAKyc,GAAGrc,EAAS,CAACkM,EAAG,EAAGC,EAAG,EAAG2R,SAAU,EAAGC,SAAU,EAAGC,SAAU,EAAGC,UAAW,EAAGC,UAAW,EAAGC,MAAO,EAAGC,MAAO,EAAGC,MAAO,IAAIxM,SAAS,GACtP2I,EAASxa,EAAQse,+BAClB1C,GAASA,EAAM/J,SAAS,GAAG/B,OACpB0K,GAkDR+D,GAAkB,CAAC/J,WAAY,QAASC,SAAU,MAAOE,OAAQ,EAAGD,SAAU,OAAQE,WAAW,UACjG4J,GAAY,CAACC,cAAe,OAAQC,cAAe,GACnDvK,EAAY,CAAC0G,IAAK,EAAGD,KAAM,EAAG+D,OAAQ,GAAKC,OAAQ,EAAGC,MAAO,GAiC7D7I,EAAkB,SAAlBA,gBAAmBgE,EAAQ8E,EAAO3L,EAAW4L,OACxCzW,EAAO,CAACwQ,QAAS,SACpBkG,EAAO7L,EAAU4L,EAAU,MAAQ,MACnCE,EAAe9L,EAAU4L,EAAU,KAAO,OAC3C/E,EAAOkF,WAAaH,EACpBzW,EAAK6K,EAAU9N,EAAI,WAAa0Z,GAAW,IAAM,EACjDzW,EAAK6K,EAAU9N,GAAK0Z,EAAU,MAAQ,EACtCzW,EAAK,SAAW0W,EAAOrB,IAAU,EACjCrV,EAAK,SAAW2W,EAAetB,IAAU,EACzCrV,EAAK6K,EAAUxL,GAAKmX,EAAQ,KAC5Blf,GAAKuf,IAAInF,EAAQ1R,IAElBsP,GAAY,GACZwH,GAAO,GAuBPC,EAAa,GACbC,EAAc,GAEdhJ,EAAY,SAAZA,UAAY9V,UAAS6e,EAAW7e,IAAS6e,EAAW7e,GAAM+e,IAAI,SAAAje,UAAKA,OAASge,GAC5EjI,EAAe,GA+BfmI,GAAa,EAQbtI,GAAc,SAAdA,YAAejT,EAAOwb,OACjBvJ,IAAoBjS,GAIxB+T,GAAiBxS,EAAcka,cAAe,EAC9Cve,GAAWsS,QAAQ,SAAApB,UAAOd,GAAYc,IAAQA,EAAIlQ,YAAckQ,EAAI4F,IAAM5F,WACtEsN,EAAerJ,EAAU,eAC7BuG,IAASrX,EAAcyN,OACvBwM,GAAchI,KACdtW,GAAWsS,QAAQ,SAAApB,GACdd,GAAYc,KACfA,EAAI1O,SAAW0O,EAAI3O,OAAO+R,MAAMmK,eAAiB,QACjDvN,EAAI,MAGNuF,GAAUxH,MAAM,GAAGqD,QAAQ,SAAAlR,UAAKA,EAAEsd,YAClCjI,GAAUnE,QAAQ,SAAClR,EAAGa,MACjBb,EAAEud,eAAiBvd,EAAE6V,IAAK,KACzB6D,EAAO1Z,EAAE+F,KAAKyX,WAAa,cAAgB,eAC9CC,EAAWzd,EAAE6V,IAAI6D,GAClB1Z,EAAEuV,QAAO,EAAM,GACfvV,EAAE0d,iBAAiB1d,EAAE6V,IAAI6D,GAAQ+D,GACjCzd,EAAEuV,QAAO,EAAO,MAGlBF,GAAUnE,QAAQ,SAAAlR,SAAoB,QAAfA,EAAE+F,KAAK4X,KAAiB3d,EAAE4d,aAAa5d,EAAEuc,MAAO9c,KAAKyC,IAAIlC,EAAEuc,MAAM,EAAG5N,GAAW3O,EAAEgZ,SAAUhZ,EAAE6d,UACpHT,EAAalM,QAAQ,SAAAzB,UAAUA,GAAUA,EAAOqO,QAAUrO,EAAOqO,QAAQ,KACzElf,GAAWsS,QAAQ,SAAApB,GACdd,GAAYc,KACfA,EAAI1O,QAAU4I,sBAAsB,kBAAM8F,EAAI3O,OAAO+R,MAAMmK,eAAiB,WAC5EvN,EAAI4F,KAAO5F,EAAIA,EAAI4F,QAGrBF,GAAmBG,EAAoB,GACvClB,EAAa1H,QACbkQ,KACApJ,EAAW,GACXwB,GAAUnE,QAAQ,SAAAlR,UAAKgP,GAAYhP,EAAE+F,KAAKgY,YAAc/d,EAAE+F,KAAKgY,UAAU/d,KACzEyV,GAAiBxS,EAAcka,cAAe,EAC9CpJ,EAAU,gBAtCT/V,GAAaiF,EAAe,YAAayR,KAwC3CsJ,EAAc,EACdC,GAAa,EAEbpK,EAAa,SAAbA,WAAcnS,OACR+T,IAA4B,IAAV/T,EAAa,CACnCuB,EAAcib,YAAa,EAC3BtD,IAAYA,GAASnZ,OAAO,OACxB0V,EAAI9B,GAAUnU,OACjB6W,EAAOpW,KACPwc,EAAkC,IAAjBpG,EAAO8C,EACxBrD,EAASL,GAAK9B,GAAU,GAAGmC,YAC5ByG,GAA2BzG,EAAdwG,GAAwB,EAAI,EACzCA,EAAcxG,EACV2G,IACCxK,KAAoB3F,IAA2C,IAAzB+J,EAAOpE,KAChDA,GAAkB,EAClBI,EAAU,cAEXkG,GAASY,EACTA,EAAS9C,GAENkG,GAAa,EAAG,KACnB7I,GAAK+B,EACS,EAAP/B,MACNC,GAAUD,KAAOC,GAAUD,IAAI3T,OAAO,EAAG0c,GAE1CF,GAAa,WAER7I,GAAK,EAAGA,GAAK+B,EAAG/B,KACpBC,GAAUD,KAAOC,GAAUD,IAAI3T,OAAO,EAAG0c,GAG3Clb,EAAcib,YAAa,EAE5BtK,EAAS,GAEVuC,EAAmB,CAhRX,OACD,MA+Q0BrD,EAASD,EAAQkE,GAAUoE,GAASpE,GAAUiE,GAAQjE,GAAUmE,GAAMnE,GAAUkE,GAAO,UAAW,aAAc,QAAS,SAAU,kBAAmB,gBAAiB,eAAgB,aAAc,WAAY,cAAe,YAAa,YAAa,SAC3R7D,EAAcjB,EAAiBiI,OAAO,CAACzH,GAAQE,GAAS,YAAa,MAAQuE,GAAQ,MAAQC,EAAS,WAAYtE,GAASD,GAAUA,GAAWoE,GAAMpE,GAAWkE,GAAQlE,GAAWqE,GAASrE,GAAWmE,KA6CxMoD,EAAW,WACXrH,GAAY,SAAZA,UAAYK,MACPA,EAAO,KAITjS,EAAGlG,EAHAgU,EAAQmE,EAAMrX,EAAEkT,MACnBiE,EAAIE,EAAMnW,OACVL,EAAI,OAEJwW,EAAMrX,EAAEiW,OAAS5Y,GAAK6F,KAAK4V,SAASzB,EAAMrX,IAAIiV,QAAU,EAClDpU,EAAIsW,EAAGtW,GAAI,EACjB3B,EAAQmY,EAAMxW,EAAE,GAChBuE,EAAIiS,EAAMxW,GACN3B,EACHgU,EAAM9N,GAAKlG,EACDgU,EAAM9N,IAChB8N,EAAMqF,eAAenT,EAAEkZ,QAAQD,EAAU,OAAOE,iBA4BpDjQ,GAAc,CAAC+J,KAAK,EAAGC,IAAI,GAyD3BM,EAAa,qCAwED3V,2BAOZ6C,KAAA,cAAKC,EAAMqJ,WACLE,SAAW3C,KAAK4P,MAAQ,OACxBxW,MAAQ4G,KAAKY,MAAK,GAAM,GACxBuN,QAuBJ0D,EAASC,EAAUC,EAAUC,EAASC,EAASrC,EAAOoB,EAAKkB,EAAaC,EAAWC,EAAoBC,EAAkBC,EACzHC,EAAQC,EAAkBC,EAAgBC,EAAUvJ,EAAQhW,EAAQwf,EAAWC,EAAWC,EAAUC,EAAWC,EAAc1J,EAAa2J,EAC1IC,EAAiB7J,EAAI8J,EAAOC,EAAOC,EAAYC,EAAaC,EAAcC,EAAiBC,EAAcC,EAAYC,EAAkBC,EAAgBC,EApBnJC,GADLza,EAAO8J,GAAcd,GAAUhJ,IAASkJ,GAAUlJ,IAASA,EAAK0a,SAAY,CAACtL,QAASpP,GAAQA,EAAMkW,KAC/FuE,SAAUE,EAAsO3a,EAAtO2a,YAAajX,EAAyN1D,EAAzN0D,GAAIkX,GAAqN5a,EAArN4a,SAAU5C,GAA2MhY,EAA3MgY,UAAW6C,GAAgM7a,EAAhM6a,MAAOzL,GAAyLpP,EAAzLoP,QAASU,GAAgL9P,EAAhL8P,IAAKgL,GAA2K9a,EAA3K8a,WAAYC,GAA+J/a,EAA/J+a,oBAAqB3E,GAA0IpW,EAA1IoW,cAAe4E,EAA2Hhb,EAA3Hgb,gBAAiBC,EAA0Gjb,EAA1Gib,eAAgBC,GAA0Flb,EAA1Fkb,KAAM1Q,GAAoFxK,EAApFwK,KAAM2Q,GAA8Enb,EAA9Emb,YAAaC,EAAiEpb,EAAjEob,UAAWnP,GAAsDjM,EAAtDiM,mBAAoBoP,GAAkCrb,EAAlCqb,cAAeC,GAAmBtb,EAAnBsb,gBACjOzQ,GAAY7K,EAAKyX,YAAezX,EAAKiM,qBAA0C,IAApBjM,EAAKyX,WAAwBrY,GAAcrE,GACtGwgB,IAAYV,IAAmB,IAAVA,GACrB5H,GAAWjZ,EAAWgG,EAAKiT,UAAY5Z,IACvCmiB,EAAgBlkB,GAAK6F,KAAK4V,SAASE,IACnCpM,GAAa/O,GAAYmb,IACzBzG,GAA0H,WAAtG,YAAaxM,EAAOA,EAAKyb,QAAUhkB,EAAcwb,GAAU,YAAepM,IAAc,SAC5G6U,GAAY,CAAC1b,EAAK2b,QAAS3b,EAAK4b,QAAS5b,EAAK6b,YAAa7b,EAAK8b,aAChE3F,GAAgBoF,IAAYvb,EAAKmW,cAAcvX,MAAM,KACrDmd,EAAU,YAAa/b,EAAOA,EAAK+b,QAAU7F,GAAU6F,QACvDlK,GAAchL,GAAa,EAAIH,WAAWmD,GAAkBoJ,IAAU,SAAWpI,GAAUvL,GAAK+V,MAAY,EAC5G1S,GAAOiE,KACPoV,GAAgBhc,EAAKgc,eAAkB,kBAAMhc,EAAKgc,cAAcrZ,KAChEsZ,GApiBa,SAAfC,aAAgBjJ,EAAUpM,SAAapH,IAAAA,EAAGC,IAAAA,GAAI3C,IAAAA,SAAQA,EAAItF,EAAcwb,EAAU,0BAA4B,kBAAMlW,IAAI0C,IAAK,kBAAOoH,EAAaxN,GAAK,QAAUqG,GAAMuT,EAAS,SAAWvT,KAAQ,GAoiB9Kwc,CAAajJ,GAAUpM,GAAYgE,IACrDsR,GApiBgB,SAAlBC,gBAAmB1kB,EAASmP,UAAgBA,IAAejP,GAASC,QAAQH,GAAW4Q,GAAe5Q,GAAW,kBAAM6Q,IAoiBhG6T,CAAgBnJ,GAAUpM,IAC/CwV,GAAW,EACXC,GAAc,EACdjR,GAAa7Q,EAAeyY,GAAUpI,OAKvC6J,GAAS/R,IACTA,GAAKmV,KAAOjN,GACZuL,IAAiB,GACjBzT,GAAKsQ,SAAWA,GAChBtQ,GAAK8O,OAASxF,GAAqBA,GAAmB+F,KAAKuK,KAAKtQ,IAAsBZ,GACtFuN,EAAUvN,KACV1I,GAAK3C,KAAOA,EACZqJ,EAAYA,GAAarJ,EAAKqJ,UAC1B,oBAAqBrJ,IACxBuU,GAAQ,GACkB,OAA1BvU,EAAKwc,kBAA8B3H,GAAWlS,KAE/C6Y,EAAciB,YAAcjB,EAAciB,aAAe,CACxDlK,IAAKS,GAAiBC,GAAUlY,IAChCuX,KAAMU,GAAiBC,GAAU7T,KAElCuD,GAAK8V,QAAUA,EAAU+C,EAAciB,YAAY5R,GAAUxL,GAC7DsD,GAAK+Z,cAAgB,SAAAvjB,IACpB8gB,EAAc/Q,GAAU/P,IAAUA,GAKjC6gB,EAAaA,EAAW3P,SAASlR,GAAU6gB,EAAa1iB,GAAKyc,GAAG1K,EAAW,CAACsT,KAAM,OAAQC,cAAe,UAAWvS,SAAU4P,EAAa4C,QAAQ,EAAMtJ,WAAY,6BAAMyH,GAAmBA,EAAgBrY,QAH9MqX,GAAcA,EAAWzQ,SAAS,GAAG/B,OACrCwS,EAAa,IAKX3Q,IACHA,EAAUrJ,KAAK8c,MAAO,EACtBzT,EAAU0T,WAAgD,IAAnC1T,EAAUrJ,KAAKgd,kBAAsD,IAAzBhd,EAAKgd,iBAA6B3T,EAAUgB,YAAchB,EAAU0O,OAAO,GAAG,GAAM,GACvJpV,GAAK0G,UAAYA,EAAUrC,SAC3BqC,EAAU4T,cAAgBta,IACrB+Z,cAAc7B,IACnBf,EAAQ,EACDpW,EAAPA,GAAY2F,EAAUrJ,KAAK0D,IAE5B4L,GAAUtU,KAAK2H,IACX6H,KAEErB,GAAUqB,MAASA,GAAKxP,OAC5BwP,GAAO,CAAC0S,OAAQ1S,wBAEIzM,GAAMoP,OAAU7V,GAAKuf,IAAIhQ,GAAa,CAAC9I,GAAOlD,IAAUoY,GAAU,CAACqE,eAAgB,SACxGze,GAAWsS,QAAQ,SAAA5D,UAAK0B,GAAY1B,IAAMA,EAAEnM,UAAYyL,GAAalM,GAAKC,kBAAoBC,GAASoY,MAAc1L,EAAElM,QAAS,KAChIsd,EAAW1P,GAAYuB,GAAK0S,QAAU1S,GAAK0S,OAAyB,WAAhB1S,GAAK0S,OA9gBxC,SAAnBC,iBAAmB9T,UAAa,SAAAlQ,UAAS7B,GAAK4C,MAAMsQ,KAAKN,GAAoBb,GAAYlQ,IA8gBRgkB,CAAiB9T,GAA6B,sBAAhBmB,GAAK0S,OA9e7F,SAAvBE,qBAAuBjT,UAAY,SAAChR,EAAOkkB,UAAO/S,GAAiBJ,GAAoBC,GAArCG,CAAgDnR,EAAOkkB,EAAGxS,YA8eyCuS,CAAqB/T,IAAkC,IAArBmB,GAAK8S,YAAwB,SAACnkB,EAAOkkB,UAAO/S,GAAiBE,GAAK0S,OAAtB5S,CAA8BnR,EAAOyC,KAAa0gB,GAAc,IAAM,EAAIe,EAAGxS,YAAavT,GAAK4C,MAAMsQ,KAAKA,GAAK0S,QAChVhD,EAAe1P,GAAKH,UAAY,CAACxO,IAAK,GAAKM,IAAK,GAChD+d,EAAe/Q,GAAU+Q,GAAgBjG,GAAOiG,EAAare,IAAKqe,EAAa/d,KAAO8X,GAAOiG,EAAcA,GAC3GC,EAAkB7iB,GAAKwN,YAAY0F,GAAK+S,OAAUtD,EAAc,GAAM,GAAK,eACtExI,EAASpG,KACZmS,EAAoB5hB,KAAa0gB,GAAc,IAC/ChJ,EAAQmF,EAAQnF,WACZkK,GAAqB9jB,KAAKsD,IAAI2F,GAAKtG,eAAiB,KAAQiX,GAAUrL,IAAkBoU,KAAa5K,EA+B/F9O,GAAK8a,UAAYpB,KAAa5K,GACxC0I,EAAgB1U,SAAQ,OAhCyF,KAC7G8D,GAAYkI,EAAS+E,GAAS2C,EACjCyD,EAAgBvT,IAAckS,GAAWlS,EAAUuT,gBAAkBrT,EACrEmU,EAAWF,EAAoB,GAAMZ,EAAgB7C,IAAUne,KAAasY,IAAU,KAAS,EAC/Fd,EAAU9b,GAAK4C,MAAM8D,OAAOuL,EAAU,EAAIA,EAAUyL,GAAK0I,EAAW,GAAKA,EAAW,MACpFC,EAAapU,IAA6B,IAAjBiB,GAAKoT,QAAoB,EAAIxK,GACtDyK,EAAW5J,GAAO,EAAG,EAAG0E,EAASgF,EAAYhb,KAC7Cmb,EAAYpkB,KAAKC,MAAM6c,EAAQqH,EAAW1E,GACxC4E,EAAqCvT,GAArCuT,QAASC,EAA4BxT,GAA5BwT,YAAazK,EAAe/I,GAAf+I,cACrB9B,GAAUmG,GAAiBpB,GAAV/E,GAAmBqM,IAAcrM,EAAQ,IACzD6B,IAAUA,EAAMyJ,UAAYzJ,EAAMhW,MAAQ0X,GAAK8I,EAAYrM,WAG1C,IAAjBjH,GAAKoT,UACRxK,EAAUyK,EAAWtU,GAEtBkP,EAAQqF,EAAW,CAClBzT,SAAU6P,EAAalF,GAAoF,KAA7Etb,KAAKyC,IAAI6Y,GAAK2I,EAAaf,GAAgB5H,GAAK6I,EAAWjB,IAA0Bc,EAAW,KAAS,IACvIf,KAAMnS,GAAKmS,MAAQ,SACnBrf,KAAM0X,GAAK8I,EAAYrM,GACvBuM,YAAa,8BAAM7D,EAAgB1U,SAAQ,IAASuY,GAAeA,EAAYrb,KAC/E4Q,WAAY,sBACX5Q,GAAKjH,SACL2gB,GAAWhR,KACXyO,EAAQC,EAAQ1Q,IAAckS,GAAWlS,EAAUuT,gBAAkBja,GAAK4G,SAC1E0R,GAAkBA,EAAetY,IACjC4Q,GAAcA,EAAW5Q,MAExB8O,EAAQ2B,EAAU+F,EAAQ2E,EAAYrM,EAAS2B,EAAU+F,GAC5D4E,GAAWA,EAAQpb,GAAM8V,EAAQnF,WAKjCtM,SAEJtD,IAAOoT,GAAKpT,GAAMf,IAKK6X,GADvBA,GAHApL,GAAUzM,GAAKyM,QAAUpV,EAAWoV,IAAWU,MAGfV,GAAQc,OAASd,GAAQc,MAAM+N,WACnBzD,EAAmB7X,IAE/DmN,IAAc,IAARA,GAAeV,GAAUpV,EAAW8V,IAC1C9G,GAAU2R,KAAiBA,EAAc,CAACuD,QAAS9O,GAAS+O,UAAWxD,IACnE7K,MACa,IAAfgL,IAAwBA,KAAe9J,KAAa8J,MAAcA,IAAchL,GAAIoB,YAAcpB,GAAIoB,WAAW/D,OAAuD,SAA9CtD,GAAkBiG,GAAIoB,YAAYV,UAA6BO,IAC1LpO,GAAKmN,IAAMA,IACX4I,EAAWphB,GAAK6F,KAAK4V,SAASjD,KAChBC,OAYbqJ,EAAmBV,EAASY,UAXxB8B,KACHA,EAAYphB,EAAWohB,MACTA,EAAUV,WAAaU,EAAYA,EAAUgD,SAAWhD,EAAUiD,eAChF3F,EAAS4F,iBAAmBlD,EAC5BA,IAAc1C,EAASzI,YAAckB,GAAUiK,KAEhD1C,EAAS3I,OAASA,EAASqL,GAAazgB,GAAK4R,cAAc,OAC3DwD,EAAOwO,UAAUC,IAAI,cACrB9a,GAAMqM,EAAOwO,UAAUC,IAAI,cAAgB9a,GAC3CgV,EAASY,SAAWF,EAAmBjI,GAAUrB,MAIjC,IAAjB9P,EAAKye,SAAqBnnB,GAAKuf,IAAI/G,GAAK,CAAC2O,SAAS,IAClD9b,GAAKoN,OAASA,EAAS2I,EAAS3I,OAChCC,EAAKnG,GAAkBiG,IACvB6J,EAAe3J,EAAG8K,GAAajQ,GAAUrL,KACzC+Z,EAAYjiB,GAAKgE,YAAYwU,IAC7B0J,EAAYliB,GAAKonB,YAAY5O,GAAKjF,GAAU9N,EAAG8T,IAE/ChB,GAAWC,GAAKC,EAAQC,GACxBsJ,EAAWnI,GAAUrB,KAElBiM,EAAS,CACZ7C,EAAa/P,GAAU4S,GAAWjS,GAAaiS,EAAS9F,IAAmBA,GAC3E+C,EAAqBlN,GAAc,iBAAkBpI,EAAIuP,GAAUpI,GAAWqO,EAAY,GAC1FD,EAAmBnN,GAAc,eAAgBpI,EAAIuP,GAAUpI,GAAWqO,EAAY,EAAGF,GACzFjf,EAASif,EAAmB,SAAWnO,GAAUhL,GAAGH,QAChDif,EAAU3kB,EAAWvC,EAAcwb,GAAU,YAAcA,IAC/D6F,EAAclS,KAAKkS,YAAchN,GAAc,QAASpI,EAAIib,EAAS9T,GAAWqO,EAAYnf,EAAQ,EAAGkS,IACvG8M,EAAYnS,KAAKmS,UAAYjN,GAAc,MAAOpI,EAAIib,EAAS9T,GAAWqO,EAAYnf,EAAQ,EAAGkS,IACjGA,KAAuBsO,EAAiBjjB,GAAKonB,YAAY,CAAC5F,EAAaC,GAAYlO,GAAU9N,EAAG8T,KAC1FrE,IAAsB5U,GAASuD,SAAsD,IAA5C1D,EAAcwb,GAAU,kBAloBrD,SAApB2L,kBAAoBlnB,OACf6Y,EAAW1G,GAAkBnS,GAAS6Y,SAC1C7Y,EAAQyV,MAAMoD,SAAyB,aAAbA,GAAwC,UAAbA,EAAwBA,EAAW,WAioBtFqO,CAAkB/X,GAAa9I,GAAQkV,IACvC3b,GAAKuf,IAAI,CAACmC,EAAoBC,GAAmB,CAACwF,SAAS,IAC3D7E,EAAoBtiB,GAAKonB,YAAY1F,EAAoBnO,GAAU9N,EAAG8T,IACtEgJ,EAAkBviB,GAAKonB,YAAYzF,EAAkBpO,GAAU9N,EAAG8T,QAIhE5E,GAAoB,KACnB4S,EAAc5S,GAAmBjM,KAAKya,SACzCqE,EAAY7S,GAAmBjM,KAAK+e,eACrC9S,GAAmB+S,cAAc,WAAY,WAC5Crc,GAAKjH,OAAO,EAAG,EAAG,GAClBmjB,GAAeA,EAAYI,MAAMH,GAAa,MAIhDnc,GAAKuc,SAAW,kBAAM5P,GAAUA,GAAUzX,QAAQ8K,IAAQ,IAC1DA,GAAKwc,KAAO,kBAAM7P,GAAUA,GAAUzX,QAAQ8K,IAAQ,IAEtDA,GAAK6M,OAAS,SAACA,EAAQ4P,OACjBA,SAAezc,GAAK6E,MAAK,OAC1B6X,GAAe,IAAX7P,IAAqB7M,GAAK8G,QACjC6V,EAAiBjR,GACdgR,IAAM1c,GAAK4c,aACVF,IAKHhF,EAAa3gB,KAAKyC,IAAIkP,KAAc1I,GAAK8O,OAAO9B,KAAO,GACvDyK,EAAezX,GAAK4G,SACpB+Q,EAAmBjR,GAAaA,EAAUE,YAE3CuP,GAAe,CAACA,EAAaC,EAAWC,EAAoBC,GAAkB9N,QAAQ,SAAAsH,UAAKA,EAAEtF,MAAMqD,QAAU6O,EAAI,OAAS,UACtHA,IACHhR,GAAc,EACd1L,GAAKjH,OAAO2jB,IAETvP,KACCuP,EAxaM,SAAdG,YAAe1P,EAAKC,EAAQuB,GAC3BL,GAAUK,OACNxY,EAAQgX,EAAII,SACZpX,EAAMwlB,eACTrN,GAAUnY,EAAMmX,kBACV,GAAIH,EAAII,MAAMC,UAAW,KAC3BzD,EAASqD,EAAOmB,WAChBxE,IACHA,EAAOa,aAAauC,EAAKC,GACzBrD,EAAO+S,YAAY1P,IAGrBD,EAAII,MAAMC,WAAY,EA6ZlBqP,CAAY1P,GAAKC,EAAQqJ,GAEvB+B,IAAgBxY,GAAK8a,UAAa5N,GAAWC,GAAKC,EAAQlG,GAAkBiG,IAAMG,IAGtFoP,GAAK1c,GAAKjH,OAAO2jB,GACjBhR,GAAciR,EACd3c,GAAK4c,WAAaF,IAKpB1c,GAAK4U,QAAU,SAACmI,EAAM/jB,OAChB0S,IAAgB1L,GAAK8G,SAAa9N,KAGnCmU,IAAO4P,GAAQ9R,GAClB3V,GAAaiF,cAAe,YAAayR,UAGzCe,IAAkBsM,IAAiBA,GAAcrZ,IAClD0L,GAAc,EACdiO,GAAc1gB,KACV6c,EAAQnF,QACXmF,EAAQnF,MAAM9L,OACdiR,EAAQnF,MAAQ,GAEjB0G,GAAcA,EAAWhT,QACzB+T,IAAuB1R,GAAaA,EAAUmG,OAAO,CAAChI,MAAM,IAAQmY,aACpEhd,GAAK4c,YAAc5c,GAAK6M,QAAO,GAAM,GACrC7M,GAAK6U,eAAgB,UAYpBxH,EAAIkC,EAAQT,EAAQmO,EAAYC,EAAUC,EAAYC,EAAQC,EAAgBC,EAASC,EAXpF1U,EAAOyQ,KACVrK,EAAiBuK,KACjBhgB,EAAM8P,GAAqBA,GAAmB5B,WAAazB,GAAWqK,GAAUpI,IAChF9Q,EAAS,EACTomB,EAAiB,EACjBC,EAAYpgB,EAAK4X,IACjByI,EAAmBrgB,EAAKsgB,YAAclR,GACtCmR,EAAcvgB,EAAKwW,QAAyB,IAAfxW,EAAKwW,OAAgBpH,GAAeU,GAAM,MAAQ,SAAnB,GAC5D0Q,EAAkB7d,GAAK6d,gBAAkBxgB,EAAKwgB,iBAAmBxmB,EAAWgG,EAAKwgB,iBACjFC,EAAgBrR,IAAW1V,KAAKyC,IAAI,EAAGmT,GAAUzX,QAAQ8K,MAAW,EACpE7H,EAAI2lB,EAEE3lB,MACNglB,EAAaxQ,GAAUxU,IACZ8c,KAAOkI,EAAWvI,QAAQ,EAAG,KAAOlJ,GAAc,KAC7D0R,EAASD,EAAWhQ,MACLiQ,IAAW3Q,IAAW2Q,IAAWjQ,IAASgQ,EAAWP,cAClDW,EAAjBA,GAAgC,IACnBQ,QAAQZ,GACrBA,EAAWtQ,QAAO,GAAM,IAErBsQ,IAAexQ,GAAUxU,KAC5B2lB,IACA3lB,SAGFmO,GAAYsX,KAAiBA,EAAcA,EAAY5d,KACvD6T,EAAQjF,GAAegP,EAAanR,GAAS5D,EAAMX,GAAWQ,KAAcyN,EAAaE,EAAoBrW,GAAMiP,EAAgBC,GAAarF,GAAkBrQ,EAAK8P,MAAwB6D,IAAO,KAAQ,GAC9M7G,GAAYmX,KAAeA,EAAYA,EAAUzd,KAC7CqG,GAAUoX,KAAeA,EAAUvoB,QAAQ,SACzCuoB,EAAUvoB,QAAQ,KACtBuoB,GAAapX,GAAUuX,GAAeA,EAAY3hB,MAAM,KAAK,GAAK,IAAMwhB,GAExErmB,EAASwR,GAAY6U,EAAUxU,OAAO,GAAIJ,GAC1C4U,EAAYpX,GAAUuX,GAAeA,EAAc/J,EAAQzc,EAC3DsmB,EAAmBjR,KAGrBwI,EAAMle,KAAKyC,IAAIqa,EAAOjF,GAAe6O,IAAcC,EAAmB,SAAWlkB,GAAMkkB,EAAkB7U,EAAMX,GAAWQ,KAAetR,EAAQgf,EAAWE,EAAkBtW,GAAMiP,EAAgBC,GAAarF,GAAkBrQ,EAAK8P,OAAyB,KACjQkN,EAAUvB,EAAMpB,IAAYA,GAAS,MAAS,KAE9Czc,EAAS,EACTe,EAAI2lB,EACG3lB,MAENilB,GADAD,EAAaxQ,GAAUxU,IACHgV,MACNgQ,EAAWtJ,MAAQsJ,EAAWa,UAAYnK,IAAUvK,IAAuC,EAAjB6T,EAAWlI,MAClG5H,EAAK8P,EAAWlI,IAAMkI,EAAWtJ,OAC3BuJ,IAAW3Q,IAAW0Q,EAAWtJ,MAAQsJ,EAAWa,SAAWnK,GAAUuJ,IAAWS,KAAqBtX,GAAUqX,KACxHxmB,GAAUiW,GAAM,EAAI8P,EAAWvW,WAEhCwW,IAAWjQ,KAAQqQ,GAAkBnQ,OAGvCwG,GAASzc,EACT6d,GAAO7d,EACP4I,GAAKge,SAAWR,EACZrH,GAAe/e,KAClBiW,EAAK,IACFnF,GAAU9N,GAAK,KAAOhD,EACzBymB,IAAoBxQ,EAAGnF,GAAUxL,GAAK,KAAOgM,MAC7C/T,GAAKuf,IAAI,CAACiC,EAAaC,GAAY/I,IAGhCF,GACHE,EAAKnG,GAAkBiG,IACvB8P,EAAa/U,KAAc9P,GAC3B0W,EAASpG,KACToO,EAAW/S,WAAW6S,EAAU1O,GAAU9N,IAAMojB,GAC/ChkB,GAAa,EAANyb,KAAa/Q,GAAa9I,GAAQkV,IAAU9F,MAAM,YAActC,GAAU9N,GAAK,UACvF8S,GAAWC,GAAKC,EAAQC,GACxBsJ,EAAWnI,GAAUrB,IAErBoC,EAASvJ,GAAWmH,IAAK,GACzBkQ,EAAiBxT,IAAoBhS,EAAeyY,GAAU2M,EAAaxgB,GAAcrE,GAApDP,GACjCsgB,MACH7K,EAAc,CAAC6K,GAAajQ,GAAUrL,IAAK2Z,EAASgH,EAAiBtP,KACzD5W,EAAI8V,GAChBjV,EAAKggB,KAAe/J,GAAY9G,GAAS6F,GAAKjF,IAAasO,EAASgH,EAAiB,IAChFlQ,EAAYjV,KAAK6P,GAAUpL,EAAG3E,EAAI+V,IACvCI,GAAUhB,GACNuQ,GACHlR,GAAUnE,QAAQ,SAAAlR,GACbA,EAAE6V,MAAQ0Q,IAAyC,IAAtBvmB,EAAE+F,KAAK8a,aACvC7gB,EAAEud,eAAgB,KAIrBhL,IAAoBnB,GAAWgP,IAE5B7N,MACHqT,EAAW,CACVtN,IAAML,EAAOK,KAAOqN,EAAanO,EAAS+E,EAAQwJ,GAAmBnP,GACrEyB,KAAOJ,EAAOI,MAAQsN,EAAaI,EAAiBvO,EAAS+E,GAAU3F,GACvEF,UAAW,aACXJ,SAAU,UAEFK,IAAUiP,EAAQ,SAAmBnmB,KAAKknB,KAAK1O,EAAO1J,OAASqI,GACxEgP,EAAS/O,IAAW+O,EAAQ,UAAoBnmB,KAAKknB,KAAK1O,EAAOxJ,QAAUmI,GAC3EgP,EAAS7O,IAAW6O,EAAS7O,GAAUmE,IAAQ0K,EAAS7O,GAAUiE,IAAU4K,EAAS7O,GAAUoE,IAAWyK,EAAS7O,GAAUkE,IAAS,IACtI2K,EAAS9O,IAAYf,EAAGe,IACxB8O,EAAS9O,GAAWoE,IAAQnF,EAAGe,GAAWoE,IAC1C0K,EAAS9O,GAAWkE,IAAUjF,EAAGe,GAAWkE,IAC5C4K,EAAS9O,GAAWqE,IAAWpF,EAAGe,GAAWqE,IAC7CyK,EAAS9O,GAAWmE,IAASlF,EAAGe,GAAWmE,IAC3CmE,EAveS,SAAbwH,WAAcvP,EAAOuO,EAAUiB,WAI7BzhB,EAHGqK,EAAS,GACZ0H,EAAIE,EAAMnW,OACVL,EAAIgmB,EAAc,EAAI,EAEhBhmB,EAAIsW,EAAGtW,GAAK,EAClBuE,EAAIiS,EAAMxW,GACV4O,EAAO1O,KAAKqE,EAAIA,KAAKwgB,EAAYA,EAASxgB,GAAKiS,EAAMxW,EAAE,WAExD4O,EAAOzP,EAAIqX,EAAMrX,EACVyP,EA6damX,CAAWzH,EAAkByG,EAAU1E,IACxDzL,IAAkBrE,GAAW,IAE1BhC,GACH4W,EAAU5W,EAAU0T,SACpBvI,GAAoB,GACpBnL,EAAU0O,OAAO1O,EAAUgB,YAAY,GAAM,GAC7CqP,EAAYH,EAAU1O,GAAU9N,GAAK0c,EAAWN,EAASgH,EACzDhH,IAAWO,GAAalN,IAAoB6M,EAAe5R,OAAO4R,EAAele,OAAS,EAAG,GAC7FkO,EAAU0O,OAAO,GAAG,GAAM,GAC1BkI,GAAW5W,EAAUsW,YAAW,GAChCtW,EAAUqD,QAAUrD,EAAUM,UAAUN,EAAUM,aAClD6K,GAAoB,IAEpBkF,EAAYP,OAEP,GAAI/J,IAAW/D,OAAiBY,OACtCiG,EAAS9C,GAAQ8B,WACVgB,GAAUA,IAAWnU,IACvBmU,EAAO6O,aACVvK,GAAStE,EAAO6O,WAChBnJ,GAAO1F,EAAO6O,YAEf7O,EAASA,EAAOhB,WAGlBgP,GAAgBA,EAAa/U,QAAQ,SAAAlR,UAAKA,EAAEuV,QAAO,GAAO,KAC1D7M,GAAK6T,MAAQA,EACb7T,GAAKiV,IAAMA,EACXgB,EAAUC,EAAUnJ,GAAiB2K,EAAahP,KAC7CY,IAAuByD,KAC3BkJ,EAAUyB,GAAchP,GAAWgP,GACnC1X,GAAK8O,OAAO9B,IAAM,GAEnBhN,GAAK6M,QAAO,GAAO,GACf2K,IACHkC,IAAY,EACZ1Z,GAAK8a,UAAYpS,GAAWmL,EAAQ2C,EAASiB,GAC7CD,EAAgB1U,SAAQ,IAEzB4I,GAAc,EACdhF,GAAakS,KAAalS,EAAU0T,UAAYzC,IAAqBjR,EAAUE,aAAe+Q,GAAoBjR,EAAUE,SAAS+Q,GAAkB,GAAMvC,OAAO1O,EAAU2I,QAAQ,GAAM,GACxLoI,IAAiBzX,GAAK4G,WAAY0C,KACrC5C,IAAckS,IAAYlS,EAAUuT,cAAcxC,GAAc,GAChEzX,GAAK4G,UAAYqP,EAAUpC,GAAS2C,IAAWiB,EAAe,EAAIA,GAEnEtK,IAAOgL,KAAe/K,EAAOgR,WAAarnB,KAAKC,MAAMgJ,GAAK4G,SAAWmQ,IAErE1B,KAActI,IAAkBsI,GAAUrV,MAG3CA,GAAKtG,YAAc,kBAAQgP,KAAewN,IAAYjd,KAAasY,IAAU,KAAS,GAEtFvR,GAAKqe,aAAe,WACnB5X,GAAczG,GAAKiH,mBACfP,IACH2Q,EAAaA,EAAWzQ,SAAS,GAAOF,EAAUwT,SAA4DtB,IAAYnS,GAAcC,EAAW1G,GAAKkI,UAAY,EAAG,GAA1GzB,GAAcC,EAAWA,EAAUC,cAIlG3G,GAAKse,cAAgB,SAAAC,UAAS7X,GAAaA,EAAUe,SAAYoM,GAAS7T,GAAK4U,WAAaf,GAAUnN,EAAUe,OAAO8W,GAAS7X,EAAUgB,WAAc8O,GAAW,GAEnKxW,GAAKwe,YAAc,SAAAzjB,OACd5C,EAAIwU,GAAUzX,QAAQ8K,IACzB5F,EAAqB,EAAjB4F,GAAKkI,UAAgByE,GAAUxH,MAAM,EAAGhN,GAAGsmB,UAAY9R,GAAUxH,MAAMhN,EAAE,UACtEkO,GAAUtL,GAAQX,EAAEuK,OAAO,SAAArN,UAAKA,EAAE+F,KAAKsb,kBAAoB5d,IAAQX,GAAGuK,OAAO,SAAArN,UAAsB,EAAjB0I,GAAKkI,UAAgB5Q,EAAE2d,KAAOpB,EAAQvc,EAAEuc,OAASoB,KAI5IjV,GAAKjH,OAAS,SAACU,EAAOgc,EAAgBiJ,OACjCpV,IAAuBoV,GAAcjlB,OAOxCqhB,EAAqB6D,EAAaC,EAAQC,EAAcC,EAASC,EAASC,EAJvElQ,EAAS/B,GAAiB2K,EAAa1X,GAAK8O,SAC/CpS,EAAIjD,EAAQ,GAAKqV,EAAS+E,GAAS2C,EACnCyI,EAAUviB,EAAI,EAAI,EAAQ,EAAJA,EAAQ,EAAIA,GAAK,EACvC+a,EAAezX,GAAK4G,YAEjB6O,IACHS,EAAUD,EACVA,EAAU3M,GAAqBZ,KAAeoG,EAC1CjH,KACHuP,EAAQD,EACRA,EAAQzQ,IAAckS,GAAWlS,EAAUuT,gBAAkBgF,IAI9DxL,KAAkBwL,GAAW9R,KAAQzB,KAAgBjV,IAAYwU,IAAmB4I,EAAQ/E,GAAWA,EAASoH,IAAYjd,KAAasY,IAAWkC,KAAmBwL,EAAU,MAC9KA,IAAYxH,GAAgBzX,GAAK8G,QAAS,IAI7C+X,GADAC,GAFAhE,EAAW9a,GAAK8a,WAAamE,GAAWA,EAAU,OACpCxH,GAAgBA,EAAe,OAEjBwH,KAAcxH,EAC1CzX,GAAKkI,UAAsBuP,EAAVwH,EAAyB,GAAK,EAC/Cjf,GAAK4G,SAAWqY,EAEZJ,IAAiBnT,KACpBiT,EAAcM,IAAYxH,EAAe,EAAgB,IAAZwH,EAAgB,EAAqB,IAAjBxH,EAAqB,EAAI,EACtFmB,KACHgG,GAAWE,GAA8C,SAAnCtL,GAAcmL,EAAc,IAAiBnL,GAAcmL,EAAc,IAAOnL,GAAcmL,GACpHK,EAAiBtY,IAAyB,aAAXkY,GAAoC,UAAXA,GAAsBA,KAAUlY,KAI1FiS,KAAoBmG,GAAWE,KAAoBA,GAAkB9G,KAAUxR,KAAeJ,GAAYqS,IAAmBA,GAAgB3Y,IAAQA,GAAKwe,YAAY7F,IAAiBnQ,QAAQ,SAAAlR,UAAKA,EAAE+mB,kBAEjMzF,MACAvB,GAAe3L,IAAgBjV,GAQxBiQ,GACVA,EAAUuT,cAAcgF,IAAWvT,MARlCpC,IAAuB4I,IAAYA,KAAalS,KAAUqX,EAAWjC,OAAOiC,EAAW6H,IAAIC,MAAQ9H,EAAW+H,QAC3G/H,EAAWgI,QACdhI,EAAWgI,QAAQ,gBAAiBJ,EAASvY,EAAU4Y,OAAS5Y,EAAU6Y,QAE1ElI,EAAWha,KAAK4c,cAAgBgF,EAChC5H,EAAW2F,aAAala,aAMvBqK,MACH1T,GAAS0e,KAAe/K,EAAO5C,MAAM2N,GAAajQ,GAAUrL,KAAOma,GAC9DnN,IAEE,GAAIgV,EAAc,IACxBE,GAAWtlB,GAAmBge,EAAVwH,GAAoCnQ,EAAVmG,EAAM,GAAcnG,EAAS,GAAK7I,GAAWqK,GAAUpI,IACjGsQ,MACE/e,IAAUqhB,IAAYiE,EAK1B/O,GAAU7C,GAAKC,OALqB,KAChCmC,EAASvJ,GAAWmH,IAAK,GAC5B/V,EAAS0X,EAAS+E,EACnB7D,GAAU7C,GAAK/R,GAAQmU,EAAOK,KAAO1H,KAAc9P,GAAYhB,EAAS,GAAM8W,GAAMqB,EAAOI,MAAQzH,KAAc9P,GAAY,EAAIhB,GAAW8W,IAK9II,GAAUwM,GAAYiE,EAAUrI,EAAiBC,GAChDI,IAAcP,GAAUyI,EAAU,GAAKnE,GAAajE,EAAUC,GAAwB,IAAZmI,GAAkBF,EAAsB,EAAZhI,UAbvGF,EAAUpR,GAAOqR,EAAWC,EAAYkI,KAgB1CpX,IAASiO,EAAQnF,OAAUjF,IAAgBjV,IAAY+gB,EAAgB1U,SAAQ,GAC/EkV,IAAgB8G,GAAYvG,IAAQ0G,IAAYA,EAAU,IAAMjN,MAAsBX,GAAS2G,EAAYuD,SAAS/S,QAAQ,SAAApT,UAAMA,EAAGwmB,UAAUd,GAAYvC,GAAO,MAAQ,UAAUP,EAAYwD,cAChM1D,GAAac,IAAanf,GAASqe,EAAS9X,IACxC6e,IAAiBnT,IAChBkN,KACCoG,IACY,aAAXJ,EACHlY,EAAUrC,QAAQ4V,cAAc,GACX,UAAX2E,EACVlY,EAAU5D,SAAQ,GAAMuB,QACH,YAAXua,EACVlY,EAAU5D,SAAQ,GAElB4D,EAAUkY,MAGZ9G,GAAYA,EAAS9X,MAElB8e,GAAY9M,KACfiG,IAAY6G,GAAWjY,GAAU7G,GAAMiY,IACvCc,GAAU4F,IAAgB9X,GAAU7G,GAAM+Y,GAAU4F,IACpDpG,KAAqB,IAAZ0G,EAAgBjf,GAAK6E,MAAK,EAAO,GAAMkU,GAAU4F,GAAe,GACpEG,GAEJ/F,GADA4F,EAA0B,IAAZM,EAAgB,EAAI,IACRpY,GAAU7G,GAAM+Y,GAAU4F,KAGlDjG,KAAkBoC,GAAY/jB,KAAKsD,IAAI2F,GAAKtG,gBAAkB6M,GAAUmS,IAAiBA,GAAgB,QAC5GjS,GAAczG,GAAKiH,mBACnBoQ,EAAaA,EAAWzQ,SAAS,GAAKH,GAAcC,EAAsB,YAAXkY,EAAuB,GAAKK,EAAS,KAE3FrG,IAAYd,IAAapM,IACnCoM,EAAS9X,OAIPkX,EAAiB,KAChBsI,EAAIlW,GAAqBwF,EAASxF,GAAmB5B,YAAc4B,GAAmByG,eAAiB,GAAKjB,EAChHmI,EAAkBuI,GAAKnJ,EAAmBpC,WAAa,EAAI,IAC3DiD,EAAgBsI,GAEjB5H,GAAkBA,GAAgB9I,EAASxF,GAAmB5B,YAAc4B,GAAmByG,eAAiB,MAGjH/P,GAAKwE,OAAS,SAAC/K,EAAOmb,GAChB5U,GAAK8G,UACT9G,GAAK8G,SAAU,EACfxR,GAAagb,GAAU,SAAU7E,IACjCnW,GAAa4O,GAAalM,GAAOsY,GAAU,SAAUva,IACrDsjB,IAAiB/jB,GAAaiF,cAAe,cAAe8e,KAC9C,IAAV5f,IACHuG,GAAK4G,SAAW6Q,EAAe,EAC/BxB,EAAUC,EAAUwD,GAAWhR,OAEpB,IAAZkM,GAAqB5U,GAAK4U,YAI5B5U,GAAKuQ,SAAW,SAAA1I,UAAQA,GAAQiO,EAAUA,EAAQnF,MAAQ0G,GAE1DrX,GAAKkV,aAAe,SAACuK,EAAUC,GAC1BvS,KACH2J,GAAY2I,EAAW5L,EACvBkD,GAAc2I,EAASD,EAAYjJ,EACnC2B,KAAe/J,IAAYpO,GAAKgV,iBAAkB0K,EAASD,EAAYjJ,IAExExW,GAAK6T,MAAQA,EAAQ4L,EACrBzf,GAAKiV,IAAMA,EAAMyK,EACjBlJ,EAASkJ,EAASD,EAClBzf,GAAKjH,UAGNiH,GAAKgV,iBAAmB,SAAA2K,MACnBrS,EAAa,KACZnV,EAAImV,EAAYpY,QAAQgT,GAAUpL,GAAK,EAC3CwQ,EAAYnV,GAAM4L,WAAWuJ,EAAYnV,IAAMwnB,EAAUzR,GACzDZ,EAAY,GAAMvJ,WAAWuJ,EAAY,IAAMqS,EAAUzR,GACzDI,GAAUhB,KAIZtN,GAAK0E,QAAU,SAACjL,EAAOmmB,MAClB5f,GAAK8G,WACE,IAAVrN,GAAmBuG,GAAK6M,QAAO,GAAM,GACrC7M,GAAK8G,QAAU9G,GAAK8a,UAAW,EAC/B8E,GAAmBvI,GAAcA,EAAWhT,QAC5CqT,EAAa,EACb3B,IAAaA,EAASxJ,QAAU,GAChC8M,IAAiBxjB,GAAgB0E,cAAe,cAAe8e,IAC3D7B,IACHA,EAAgBnT,QAChByR,EAAQnF,OAASmF,EAAQnF,MAAM9L,SAAWiR,EAAQnF,MAAQ,KAEtDzM,IAAY,SACZ/L,EAAIwU,GAAUnU,OACXL,QACFwU,GAAUxU,GAAGmY,WAAaA,IAAY3D,GAAUxU,KAAO6H,UAI5DnK,GAAgBya,GAAU,SAAU7E,IACpC5V,GAAgBya,GAAU,SAAUva,MAKvCiK,GAAK6E,KAAO,SAACgI,EAAQ+S,GACpB5f,GAAK0E,QAAQmI,EAAQ+S,GACrBvI,IAAeuI,GAAkBvI,EAAWxS,OAC5C9D,UAAcoT,GAAKpT,OACf5I,EAAIwU,GAAUzX,QAAQ8K,IACrB,GAAL7H,GAAUwU,GAAU7H,OAAO3M,EAAG,GAC9BA,IAAMuU,IAAmB,EAAb6I,IAAkB7I,KAG9BvU,EAAI,EACJwU,GAAUnE,QAAQ,SAAAlR,UAAKA,EAAEgZ,WAAatQ,GAAKsQ,WAAanY,EAAI,KAC5DA,GAAK4U,KAAmB/M,GAAK8O,OAAO9B,IAAM,GAEtCtG,IACHA,EAAU4T,cAAgB,KAC1BzN,GAAUnG,EAAUmG,OAAO,CAAChI,MAAM,IAClC+a,GAAkBlZ,EAAU7B,QAE7BsR,GAAe,CAACA,EAAaC,EAAWC,EAAoBC,GAAkB9N,QAAQ,SAAAsH,UAAKA,EAAEvB,YAAcuB,EAAEvB,WAAWuO,YAAYhN,KACpIoC,KAAalS,KAASkS,GAAW,GAC7B/E,KACH4I,IAAaA,EAASxJ,QAAU,GAChCpU,EAAI,EACJwU,GAAUnE,QAAQ,SAAAlR,UAAKA,EAAE6V,MAAQA,IAAOhV,MACxCA,IAAM4d,EAAS3I,OAAS,IAEzB/P,EAAKwiB,QAAUxiB,EAAKwiB,OAAO7f,KAG5BA,GAAKwE,QAAO,GAAO,GACnBqT,GAAsBA,EAAmB7X,IACxC0G,GAAcA,EAAUmV,MAAOrF,EAA0B7hB,GAAKwN,YAAY,IAAM,kBAAM0R,GAASoB,GAAOjV,GAAK4U,cAAe4B,EAAS,OAAU3C,EAAQoB,EAAM,GAAnHjV,GAAK4U,UAC9CzH,IA35BkB,SAAnB2S,sBACK7N,KAAoBsC,GAAY,KAC/BxT,EAAKkR,GAAkBsC,GAC3BjT,sBAAsB,kBAAMP,IAAOwT,IAActI,IAAY,MAw5BvD6T,aAnmBD/mB,OAASkL,KAAK2Q,QAAU3Q,KAAKY,KAAOW,kBAumBpCP,SAAP,kBAAgBzK,UACV2B,IACJxH,GAAO6F,GAAQ9F,KACfgR,MAAmB9Q,OAAOqG,UAAYV,cAAciK,SACpDrI,EAAeiW,IAETjW,iBAGDkL,SAAP,kBAAgB5P,MACXA,MACE,IAAIiF,KAAKjF,EACb8b,GAAU7W,GAAKjF,EAAOiF,UAGjB6W,kBAGD7O,QAAP,iBAAejL,EAAOoL,GACrBuN,GAAW,EACXzF,GAAUnE,QAAQ,SAAAiE,UAAWA,EAAQ5H,EAAO,OAAS,WAAWpL,KAChE5D,GAAgBa,GAAM,QAASX,IAC/BF,GAAgBmC,GAAM,SAAUjC,IAChCgqB,cAAcvO,GACd3b,GAAgBmC,GAAM,cAAewN,IACrC3P,GAAgBuF,GAAO,aAAcoK,IACrC6C,GAAexS,GAAiBmC,GAAM,mCAAoCqN,IAC1EgD,GAAexS,GAAiBmC,GAAM,6BAA8BuN,IACpEwG,EAAalH,OACbqB,GAAoBrQ,QACf,IAAIsC,EAAI,EAAGA,EAAIjC,GAAWsC,OAAQL,GAAG,EACzCsQ,GAAe5S,GAAiBK,GAAWiC,GAAIjC,GAAWiC,EAAE,IAC5DsQ,GAAe5S,GAAiBK,GAAWiC,GAAIjC,GAAWiC,EAAE,mBAIvDqM,OAAP,qBACC9N,GAAO9B,OACPoD,GAAOiD,SACP/C,GAASF,GAAKmD,gBACdC,GAAQpD,GAAKkD,KACTvG,KACH0c,GAAW1c,GAAK4C,MAAMC,QACtB8Z,GAAS3c,GAAK4C,MAAM8D,MACpB0W,GAAWpd,GAAK6F,KAAKwlB,SAAWxa,GAChCqM,GAAsBld,GAAK6F,KAAKylB,oBAAsBza,GACtDyH,EAAqBvW,GAAKC,QAAQC,mBAAqB,OACvDjC,GAAK6F,KAAKC,QAAQ,gBAAiBF,eAC/Ba,IAAO,CACVgX,GAAW,EACX5W,EAASyJ,SAAStQ,IAElB4F,cAAckB,QAAUD,EAASC,QACjCqW,EAAatW,EAASC,SAAW,0BAA0B0U,KAAKvU,UAAUskB,WAC1E5qB,GAAaoB,GAAM,QAASX,IAC5BV,EAAQ,CAACqB,GAAMsB,GAAME,GAAQkD,IACzBzG,GAAK+G,YACRnB,cAAcmB,WAAa,SAAA2B,OAEzBX,EADGyjB,EAAKxrB,GAAK+G,iBAETgB,KAAKW,EACT8iB,EAAGtE,IAAInf,EAAGW,EAAKX,WAETyjB,GAERxrB,GAAKgB,iBAAiB,iBAAkB,kBAAM6W,OAC9C7X,GAAKgB,iBAAiB,mBAAoB,kBAAMuW,OAChDvX,GAAKgB,iBAAiB,aAAc,WACnCsW,GAAY,EAAG,GACfZ,EAAU,gBAEX1W,GAAK+G,WAAW,0BAA2B,kBAC1C4P,KACOA,MAGR3T,QAAQC,KAAK,iCAEd0T,KACAhW,GAAa0C,GAAM,SAAUjC,QAI5BwZ,EAAQpX,EAHLioB,EAAYhlB,GAAMoP,MACrB6V,EAASD,EAAUE,eACnBC,EAAiB5rB,GAAK6F,KAAKgmB,UAAUC,cAEtCF,EAAe1T,QAAU6T,OAAOC,eAAeJ,EAAgB,SAAU,CAAE/pB,MAAO,wBAAoByN,KAAKoL,MAAM,KAAM,MACvH+Q,EAAUE,eAAiB,QAC3B/Q,EAASvJ,GAAW5K,IACpBhD,GAAU0X,EAAI/Y,KAAKC,MAAMuY,EAAOK,IAAMxX,GAAUL,OAAS,EACzD0E,GAAYqT,EAAI/Y,KAAKC,MAAMuY,EAAOI,KAAOlT,GAAY1E,OAAS,EAC9DsoB,EAAUD,EAAUE,eAAiBD,EAAUD,EAAUvQ,eAAe,oBAExE2B,EAAgBoP,YAAY5V,GAAO,KACnCrW,GAAKwN,YAAY,GAAK,kBAAM1L,GAAW,IACvCnB,GAAa0C,GAAM,cAAewN,IAClClQ,GAAa8F,GAAO,aAAcoK,IAClC6C,GAAe/S,GAAc0C,GAAM,mCAAoCqN,IACvEgD,GAAe/S,GAAc0C,GAAM,6BAA8BuN,IACjEkM,EAAiB9c,GAAK4C,MAAMspB,YAAY,aACxCnS,EAAYrW,KAAKoZ,GACjBtV,EAAelD,KACf8S,EAAepX,GAAKwN,YAAY,GAAK8J,IAAa5H,QAClD+B,EAAe,CAACpO,GAAM,mBAAoB,eACrC8oB,EAAIpqB,GAAKoP,WACZib,EAAIrqB,GAAKgN,YACN1L,GAAKgpB,QACRtP,EAAaoP,EACbnP,EAAcoP,GACJrP,IAAeoP,GAAKnP,IAAgBoP,GAC9CtV,MAECzT,GAAM,mBAAoBiU,GAAavV,GAAM,OAAQuV,GAAavV,GAAM,SAAU+U,IACrFvF,GAAoB5Q,IACpBqX,GAAUnE,QAAQ,SAAAiE,UAAWA,EAAQjI,OAAO,EAAG,KAC1CrM,EAAI,EAAGA,EAAIjC,GAAWsC,OAAQL,GAAG,EACrCsQ,GAAe5S,GAAiBK,GAAWiC,GAAIjC,GAAWiC,EAAE,IAC5DsQ,GAAe5S,GAAiBK,GAAWiC,GAAIjC,GAAWiC,EAAE,oBAMzDV,OAAP,gBAAc4F,sBACQA,IAAU2U,KAAoB3U,EAAK4jB,oBACpDC,EAAK7jB,EAAK8jB,aACdD,GAAMnB,cAAcvO,KAAoBA,EAAgB0P,IAAON,YAAY5V,GAAOkW,0BACzD7jB,IAAUyO,EAAgD,IAA1BvR,cAAckB,SAAiB4B,EAAK+jB,oBACzF,sBAAuB/jB,IAC1B6I,GAAoBrQ,KAAoBqQ,GAAoB5Q,GAAc+H,EAAKgkB,mBAAqB,QACpG1V,GAAqE,KAApDtO,EAAKgkB,kBAAoB,IAAInsB,QAAQ,0BAIjDosB,cAAP,uBAAqB7oB,EAAQ4E,OACxB/F,EAAID,EAAWoB,GAClBN,EAAIjC,GAAWhB,QAAQoC,GACvB4M,EAAa/O,GAAYmC,IACrBa,GACJjC,GAAW4O,OAAO3M,EAAG+L,EAAa,EAAI,GAEnC7G,IACH6G,EAAajP,GAAS8oB,QAAQrnB,GAAM2G,EAAMjC,GAAOiC,EAAMnF,GAAQmF,GAAQpI,GAAS8oB,QAAQzmB,EAAG+F,mBAItFkkB,gBAAP,yBAAuBlV,GACtBM,GAAUnE,QAAQ,SAAAlR,UAAKA,EAAEsV,MAAQtV,EAAEsV,KAAKP,QAAUA,GAAS/U,EAAEsV,KAAK/H,MAAK,GAAM,oBAGvE2c,aAAP,sBAAoBzsB,EAASmc,EAAO4D,OAC/BvF,GAAUlJ,GAAUtR,GAAWsC,EAAWtC,GAAWA,GAASse,wBACjEjc,EAASmY,EAAOuF,EAAa7G,GAASE,IAAW+C,GAAS,SACpD4D,EAAqC,EAAxBvF,EAAOqE,MAAQxc,GAAcmY,EAAOI,KAAOvY,EAASV,GAAKoP,WAAsC,EAAzByJ,EAAOoE,OAASvc,GAAcmY,EAAOK,IAAMxY,EAASV,GAAKgN,2BAG7I+d,mBAAP,4BAA0B1sB,EAAS2sB,EAAgB5M,GAClDzO,GAAUtR,KAAaA,EAAUsC,EAAWtC,QACxCwa,EAASxa,EAAQse,wBACpBxK,EAAO0G,EAAOuF,EAAa7G,GAASE,IACpC/W,EAA2B,MAAlBsqB,EAAyB7Y,EAAO,EAAM6Y,KAAkBxY,EAAaA,EAAUwY,GAAkB7Y,GAAQ6Y,EAAexsB,QAAQ,KAAO6O,WAAW2d,GAAkB7Y,EAAO,IAAM9E,WAAW2d,IAAmB,SAClN5M,GAAcvF,EAAOI,KAAOvY,GAAUV,GAAKoP,YAAcyJ,EAAOK,IAAMxY,GAAUV,GAAKgN,2BAGtFie,QAAP,iBAAeC,MACdjV,GAAUnE,QAAQ,SAAAlR,SAAmB,mBAAdA,EAAE+F,KAAK0D,IAA2BzJ,EAAEuN,UACpC,IAAnB+c,EAAyB,KACxBC,EAAYzN,EAAWuN,SAAW,GACtCvN,EAAa,GACbyN,EAAUrZ,QAAQ,SAAAnS,UAAKA,8CAvxBbgH,EAAMqJ,GACjBvK,GAAgB5B,cAAc0K,SAAStQ,KAASgD,QAAQC,KAAK,kDACxDwF,KAAKC,EAAMqJ,KA2xBJ3B,QAAU,WACV+c,WAAa,SAAAvG,UAAWA,EAAUlK,GAASkK,GAAS/S,QAAQ,SAAA/P,MACrEA,GAAUA,EAAO+R,MAAO,KACvBrS,EAAIiU,EAAalX,QAAQuD,GACxB,GAALN,GAAUiU,EAAatH,OAAO3M,EAAG,GACjCiU,EAAa/T,KAAKI,EAAQA,EAAO+R,MAAMC,QAAShS,EAAO6T,SAAW7T,EAAOspB,aAAa,aAAcptB,GAAK6F,KAAK4V,SAAS3X,GAASsZ,SAE7H3F,KACSS,OAAS,SAACkQ,EAAM5Q,UAAUK,IAAYuQ,EAAM5Q,MAC5CnH,OAAS,SAAC3H,EAAMqJ,UAAc,IAAInM,EAAc8C,EAAMqJ,MACtDkO,QAAU,SAAAoN,UAAQA,EAAOvW,MAAetP,GAAgB5B,EAAc0K,aAAegH,IAAY,MACjGlT,OAASoS,IACT8W,kBAAoBnV,KACpBoV,UAAY,SAACntB,EAAS+f,UAAe7O,GAAWlR,EAAS+f,EAAarY,GAAcrE,OACpF+pB,cAAgB,SAACptB,EAAS+f,UAAejd,EAAeR,EAAWtC,GAAU+f,EAAarY,GAAcrE,OACxGgN,QAAU,SAAArE,UAAMoT,GAAKpT,MACrBmE,OAAS,kBAAMyH,GAAUhI,OAAO,SAAArN,SAAmB,mBAAdA,EAAE+F,KAAK0D,QAC5CqhB,YAAc,mBAAQnX,MACtBoX,gBAAkB1a,KAClBhS,iBAAmB,SAACJ,EAAMgT,OACnCnO,EAAIga,EAAW7e,KAAU6e,EAAW7e,GAAQ,KAC/C6E,EAAElF,QAAQqT,IAAanO,EAAE/B,KAAKkQ,MAElBzS,oBAAsB,SAACP,EAAMgT,OACtCnO,EAAIga,EAAW7e,GAClB4C,EAAIiC,GAAKA,EAAElF,QAAQqT,GACf,GAALpQ,GAAUiC,EAAE0K,OAAO3M,EAAG,MAETmqB,MAAQ,SAAC/G,EAASle,GAKd,SAAhBklB,GAAiBhtB,EAAMgT,OAClBia,EAAW,GACdC,EAAW,GACX7H,EAAQjmB,GAAKwN,YAAYugB,EAAU,WAAOna,EAASia,EAAUC,GAAWD,EAAW,GAAIC,EAAW,KAAMpe,eAClG,SAAArE,GACNwiB,EAAShqB,QAAUoiB,EAAM9X,SAAQ,GACjC0f,EAASnqB,KAAK2H,EAAKyM,SACnBgW,EAASpqB,KAAK2H,GACd2iB,GAAYH,EAAShqB,QAAUoiB,EAAMhU,SAAS,QAGhDlK,EAfGqK,EAAS,GACZ6b,EAAW,GACXF,EAAWrlB,EAAKqlB,UAAY,KAC5BC,EAAWtlB,EAAKslB,UAAY,QAaxBjmB,KAAKW,EACTulB,EAASlmB,GAAyB,OAAnBA,EAAEuM,OAAO,EAAG,IAAe3C,GAAYjJ,EAAKX,KAAa,kBAANA,EAAyB6lB,GAAc7lB,EAAGW,EAAKX,IAAMW,EAAKX,UAEzH4J,GAAYqc,KACfA,EAAWA,IACXrtB,GAAaiF,EAAe,UAAW,kBAAMooB,EAAWtlB,EAAKslB,cAE9DtR,GAASkK,GAAS/S,QAAQ,SAAA/P,OACrBhB,EAAS,OACRiF,KAAKkmB,EACTnrB,EAAOiF,GAAKkmB,EAASlmB,GAEtBjF,EAAOgV,QAAUhU,EACjBsO,EAAO1O,KAAKkC,EAAcyK,OAAOvN,MAE3BsP,GAKmC,SAAvC8b,GAAwCna,EAAY+S,EAASxG,EAAKzb,UAC1DA,EAAViiB,EAAgB/S,EAAWlP,GAAOiiB,EAAU,GAAK/S,EAAW,GAC/ClP,EAANyb,GAAazb,EAAMiiB,IAAYxG,EAAMwG,GAAWxG,EAAM,EAAIwG,GAAWA,EAAUxG,GAAO,EAExE,SAAtB6N,GAAuBrqB,EAAQyP,IACZ,IAAdA,EACHzP,EAAO+R,MAAMqF,eAAe,gBAE5BpX,EAAO+R,MAAMuY,aAA4B,IAAd7a,EAAqB,OAASA,EAAY,OAASA,GAAa1M,EAASC,QAAU,cAAgB,IAAM,OAErIhD,IAAWP,IAAU4qB,GAAoB1nB,GAAO8M,GAGjC,SAAhB8a,UAGqB3V,EAHHvP,IAAAA,MAAOrF,IAAAA,OAAQ+I,IAAAA,KAC5ByhB,GAAQnlB,EAAM5D,eAAiB4D,EAAM5D,eAAe,GAAK4D,GAAOrF,OACnEtC,EAAQ8sB,EAAK1V,OAAS5Y,GAAK6F,KAAK4V,SAAS6S,GACzC5T,EAAOpW,SACH9C,EAAM+sB,YAAwC,IAA1B7T,EAAOlZ,EAAM+sB,WAAmB,MACjDD,GAAQA,EAAKE,cAAgBF,EAAKG,cAAcH,EAAOA,EAAK1U,WACnEpY,EAAMktB,UAAYJ,IAAS9tB,GAAY8tB,IAASA,IAASxqB,IAAW6qB,IAAWjW,EAAKnG,GAAkB+b,IAAOM,YAAcD,GAAUjW,EAAGmW,YACxIrtB,EAAM+sB,WAAa7T,GAEhBlZ,EAAMktB,WAAsB,MAAT7hB,IACtB1D,EAAM2lB,kBACN3lB,EAAM7D,YAAa,GAIJ,SAAjBypB,GAAkBjrB,EAAQlD,EAAMouB,EAAQC,UAAWpoB,EAASwJ,OAAO,CAClEvM,OAAQA,EACR/C,SAAS,EACT+H,UAAU,EACViC,UAAU,EACVnK,KAAMA,EACN6J,QAAUwkB,EAASA,GAAUZ,GAC7B9kB,QAAS0lB,EACT3lB,OAAQ2lB,EACR1gB,SAAU0gB,EACVvkB,SAAU,2BAAMskB,GAAUruB,GAAa0C,GAAMwD,EAASQ,WAAW,GAAI6nB,IAAgB,GAAO,IAC5FvkB,UAAW,4BAAMzJ,GAAgBmC,GAAMwD,EAASQ,WAAW,GAAI6nB,IAAgB,MAWzD,SAAvBC,GAAuBzmB,GAoBH,SAAlB0mB,YAAwBC,GAAgB,EAGzB,SAAfC,KACCC,EAAOje,GAAWxN,EAAQL,IAC1B+rB,EAAe7S,GAAOQ,EAAa,EAAI,EAAGoS,GAC1CE,IAAqBC,EAAe/S,GAAO,EAAGrL,GAAWxN,EAAQgE,MACjE6nB,EAAgB/P,GAEK,SAAtBgQ,KACCvI,EAAQzO,MAAMrM,EAAIuE,GAAO1B,WAAWiY,EAAQzO,MAAMrM,GAAKkC,EAAYhM,QAAU,KAC7E4kB,EAAQxR,MAAMga,UAAY,mDAAqDzgB,WAAWiY,EAAQzO,MAAMrM,GAAK,UAC7GkC,EAAYhM,OAASgM,EAAYlM,QAAU,EAqBjC,SAAXutB,KACCR,KACItT,EAAMmK,YAAcnK,EAAMtT,KAAKiG,QAAU4gB,IAC5C9gB,IAAgB8gB,EAAOvT,EAAM/J,SAAS,IAAMxD,EAAY8gB,GAAQvT,EAAM0O,QAAQ,UAAW6E,IAvD5F1d,GAAUnJ,KAAUA,EAAO,IAC3BA,EAAKrD,eAAiBqD,EAAK4B,aAAe5B,EAAKoC,aAAc,EAC7DpC,EAAK9H,OAAS8H,EAAK9H,KAAO,eAC1B8H,EAAKI,WAAaJ,EAAKI,SACvBJ,EAAK0D,GAAK1D,EAAK0D,IAAM,iBAEpBf,EAAMkkB,EAWNI,EAAeN,EAkCfrT,EAAO+T,EAAcC,EAAc9hB,EA9C/BuhB,EAAiD/mB,EAAjD+mB,iBAAkBQ,EAA+BvnB,EAA/BunB,SAAUC,EAAqBxnB,EAArBwnB,kBAEhCpsB,EAASpB,EAAWgG,EAAK5E,SAAWP,GACpC4sB,EAAWnwB,GAAK6F,KAAKC,UAAUsqB,eAC/BC,EAAmBF,GAAYA,EAASG,MACxCjJ,EAAUlK,IAAgBzU,EAAK2e,SAAW3kB,EAAWgG,EAAK2e,UAAcgJ,IAAqC,IAAjB3nB,EAAK2e,UAAsBgJ,EAAiBtsB,UAAYssB,EAAiBhJ,WACrK5Y,EAAcvL,EAAeY,EAAQL,IACrC+K,EAActL,EAAeY,EAAQgE,IACrCyW,EAAQ,EACRgS,GAAgB1pB,EAASC,SAAW/E,GAAKyuB,eAAiBzuB,GAAKyuB,eAAejS,MAAQxc,GAAKyuB,eAAetf,MAAQnP,GAAK0uB,YAAc1uB,GAAKoP,WAC1Iuf,EAAe,EACfC,EAA0Bhf,GAAYse,GAAY,kBAAMA,EAAS5kB,IAAQ,kBAAM4kB,GAAY,KAE3FW,EAAgB7B,GAAejrB,EAAQ4E,EAAK9H,MAAM,EAAMsvB,GAExDR,EAAe7e,GACf2e,EAAe3e,UAqChBwW,GAAWrnB,GAAKuf,IAAI8H,EAAS,CAAC9a,EAAG,QACjC7D,EAAK2B,YAAc,SAAAjF,UAAM+X,GAAyB,cAAX/X,EAAExE,MA1B3B,SAAbiwB,gBACKxB,EAAe,CAClB1iB,sBAAsByiB,QAClB3sB,EAASqO,GAAOzF,EAAKM,OAAS,GACjCwO,EAASqV,EAAa/gB,EAAYtM,EAAIM,MACnC4kB,GAAWlN,IAAW1L,EAAYtM,EAAIsM,EAAYhM,OAAQ,CAC7DgM,EAAYhM,OAAS0X,EAAS1L,EAAYtM,MACtCoK,EAAIuE,IAAQ1B,WAAWiY,GAAWA,EAAQzO,MAAMrM,IAAM,GAAKkC,EAAYhM,QAC3E4kB,EAAQxR,MAAMga,UAAY,mDAAqDtjB,EAAI,UACnF8a,EAAQzO,MAAMrM,EAAIA,EAAI,KACtBkC,EAAYlM,QAAUhB,GAAWC,MACjCgV,WAEM,EAER/H,EAAYhM,QAAUmtB,KACtBP,GAAgB,EAU+CwB,IAA2B,KAARtS,GAA2B,eAAXnZ,EAAExE,MAA0ByK,EAAK4C,aAAgB7I,EAAEiJ,SAA8B,EAAnBjJ,EAAEiJ,QAAQxK,QAC5K6E,EAAKa,QAAU,eACVunB,EAAYvS,EAChBA,EAAQzN,IAAS/O,GAAKyuB,gBAAkBzuB,GAAKyuB,eAAejS,OAAU,GAAKgS,GAC3EvU,EAAMtM,QACNohB,IAAcvS,GAAS4P,GAAoBrqB,EAAgB,KAARya,IAAsBkR,GAA2B,KACpGM,EAAevhB,IACfwhB,EAAevhB,IACf6gB,KACAK,EAAgB/P,IAEjBlX,EAAKc,UAAYd,EAAK6B,eAAiB,SAACc,EAAMiC,MAC7CmB,EAAYhM,QAAUmtB,KACjBtiB,EAEE,CACN/L,GAAWC,YAGVuvB,EAAevK,EADZwK,EAAML,IAENlB,IAEHjJ,GADAuK,EAAgBviB,KACmB,IAANwiB,GAAc3lB,EAAK4lB,UAAa,KAC7DD,GAAO9C,GAAqC1f,EAAauiB,EAAevK,EAAWlV,GAAWxN,EAAQgE,KACtGkU,EAAMtT,KAAKgG,QAAUghB,EAAalJ,IAGnCA,GADAuK,EAAgBtiB,KACmB,IAANuiB,GAAc3lB,EAAK6lB,UAAa,KAC7DF,GAAO9C,GAAqCzf,EAAasiB,EAAevK,EAAWlV,GAAWxN,EAAQL,KACtGuY,EAAMtT,KAAKiG,QAAU6gB,EAAahJ,GAClCxK,EAAMqM,aAAatV,SAASie,GAAKG,KAAK,MAClChU,GAAcnB,EAAMtT,KAAKiG,SAAW4gB,GAAyBA,EAAK,GAAtBwB,IAC/C/wB,GAAKyc,GAAG,GAAI,CAAC0G,SAAU2M,GAAU/c,SAAUie,SAlB5C9iB,EAAkBC,SAAQ,IAsB5BzF,EAAK+B,QAAU,WACduR,EAAMoV,KAAOpV,EAAMtM,QACa,IAA5BpL,KAAaosB,IAChBf,EAAgB,EAChBe,EAAepsB,OAGjBoE,EAAKqB,SAAW,SAACsB,EAAMG,EAAIE,EAAI2lB,EAAQC,MACtC1R,KAAe+P,GAAiBL,KAChC9jB,GAAMikB,GAAoBjhB,EAAYkhB,EAAa2B,EAAO,KAAO7lB,EAAKukB,GAAgB1kB,EAAK6B,OAAS7B,EAAKiB,GAAKkC,IAAgBhD,EAAK6lB,EAAO,KACtI3lB,EAAI,CACP+C,EAAYhM,QAAUmtB,SAClB9oB,EAAUwqB,EAAO,KAAO5lB,EAC3Ba,EAAIzF,EAAUkpB,EAAe3kB,EAAK8B,OAAS9B,EAAKkB,EAAIkC,IAAgB/C,EAAK4lB,EAAO,GAChFC,EAAW/B,EAAajjB,GACzBzF,GAAWyF,IAAMglB,IAAavB,GAAgBuB,EAAWhlB,GACzDkC,EAAY8iB,IAEZ7lB,GAAMF,IAAOgL,KAEf9N,EAAKgC,SAAW,WACfyjB,GAAoBrqB,GAAQ2rB,GAA2B,KACvD7pB,EAAc5E,iBAAiB,UAAW8uB,IAC1CnvB,GAAaoB,GAAM,SAAU+tB,IACzBrhB,EAAY1K,SACf0K,EAAY3K,OAAO+R,MAAMmK,eAAiB,OAC1CvR,EAAY1K,OAASyK,EAAYzK,QAAS,GAE3C6sB,EAAc/gB,UAEfnH,EAAKiC,UAAY,WAChBwjB,GAAoBrqB,GAAQ,GAC5B5C,GAAgBa,GAAM,SAAU+tB,IAChClqB,EAAczE,oBAAoB,UAAW2uB,IAC7Cc,EAAc1gB,QAEfxH,EAAKqC,UAA6B,IAAlBrC,EAAKqC,WACrBM,EAAO,IAAIxE,EAAS6B,IACfpG,IAAM6a,KACI1O,KAAiBA,EAAY,GAC5C0O,GAAcnd,GAAKwxB,OAAOtK,IAAIrW,IAC9B3C,EAAoB7C,EAAKsE,IACzBqM,EAAQhc,GAAKyc,GAAGpR,EAAM,CAACga,KAAM,SAAUE,QAAQ,EAAM7W,QAAS+gB,EAAmB,QAAU,MAAO9gB,QAAS,QAASsN,WAAY/N,EAAkBxF,KAAKuT,aAChJ5Q,EA7LT,IA0CComB,GA9BA9C,GAAY,CAAC+C,KAAM,EAAGvX,OAAQ,GA6B9BwX,GAAY,iCAEZzC,GAAiB,SAAjBA,eAAiB9pB,OACZwsB,EAAUD,GAAUnW,KAAKpW,EAAEtB,OAAO+tB,UAClCD,GAAWH,MACdrsB,EAAEE,YAAa,EACfmsB,GAAkBG,MAiJPve,KAAO,SAAAxS,UAAQmX,GAAU3E,KAAKxS,GAAS,SAAC4E,EAAG6N,UAAuC,KAAhC7N,EAAEiD,KAAKwc,iBAAmB,GAAYzf,EAAEyZ,OAAS5L,EAAE4L,OAAyC,KAAhC5L,EAAE5K,KAAKwc,iBAAmB,SACxI4M,QAAU,SAAAppB,UAAQ,IAAI7B,EAAS6B,MAC/BqpB,gBAAkB,SAAArpB,WACV,IAAVA,SACHrH,MAEK,IAATqH,GAAiBrH,SACbA,EAAYwO,aAEP,IAATnH,SACIrH,GAAeA,EAAY6O,WAE/B8hB,EAAatpB,aAAgB7B,EAAW6B,EAAOymB,GAAqBzmB,UACxErH,GAAeA,EAAYyC,SAAWkuB,EAAWluB,QAAUzC,EAAY6O,OACvE1P,GAAYwxB,EAAWluB,UAAYzC,EAAc2wB,GAC1CA,KAIMnsB,KAAO,CACpB5B,iBAAAA,EACA8qB,eAAAA,GACAxtB,WAAAA,GACAjB,SAAAA,GACA2F,OAAQ,CAEPgsB,GAAI,cACH3b,IAAmBI,EAAU,eAC7BJ,GAAkBhS,MAGnB4tB,IAAK,sBAAMnb,YAIC/W,GAAKE,eAAe0F"}