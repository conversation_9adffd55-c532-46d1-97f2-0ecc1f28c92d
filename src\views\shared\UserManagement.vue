<template>
  <div class="user-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>用户管理</h2>
      <AdaptiveButton 
        type="primary" 
        @click="showAddModal = true"
        :block="isMobile"
      >
        <van-icon v-if="isMobile" name="plus" />
        <el-icon v-else><Plus /></el-icon>
        新增用户
      </AdaptiveButton>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-section">
      <AdaptiveForm
        v-model="searchForm"
        :fields="searchFields"
        @field-change="handleSearch"
      >
        <template v-if="!isMobile">
          <AdaptiveButton type="primary" @click="handleSearch">
            搜索
          </AdaptiveButton>
          <AdaptiveButton @click="handleReset">
            重置
          </AdaptiveButton>
        </template>
      </AdaptiveForm>
    </div>

    <!-- 用户列表 -->
    <div class="user-list">
      <AdaptiveTable
        :data="filteredUsers"
        :columns="userColumns"
        :show-selection="!isMobile"
        :show-search="isMobile"
        @row-click="handleUserClick"
        @selection-change="handleSelectionChange"
        @search="handleMobileSearch"
      >
        <template #avatar="{ row }">
          <el-avatar v-if="!isMobile" :src="row.avatar" :size="40">
            {{ row.name.charAt(0) }}
          </el-avatar>
          <van-image
            v-else
            :src="row.avatar"
            width="40"
            height="40"
            round
            fit="cover"
          >
            <template #error>
              <div class="avatar-placeholder">{{ row.name.charAt(0) }}</div>
            </template>
          </van-image>
        </template>

        <template #status="{ row }">
          <el-tag v-if="!isMobile" :type="getStatusType(row.status)">
            {{ row.status }}
          </el-tag>
          <van-tag v-else :type="getStatusType(row.status)">
            {{ row.status }}
          </van-tag>
        </template>

        <template #actions="{ row }">
          <div class="action-buttons">
            <AdaptiveButton 
              size="small" 
              type="primary" 
              @click.stop="editUser(row)"
            >
              编辑
            </AdaptiveButton>
            <AdaptiveButton 
              size="small" 
              type="danger" 
              @click.stop="deleteUser(row)"
            >
              删除
            </AdaptiveButton>
          </div>
        </template>

        <template #mobileCell="{ row }">
          <div class="mobile-user-card">
            <div class="user-info">
              <van-image
                :src="row.avatar"
                width="50"
                height="50"
                round
                fit="cover"
              >
                <template #error>
                  <div class="avatar-placeholder">{{ row.name.charAt(0) }}</div>
                </template>
              </van-image>
              <div class="user-details">
                <div class="user-name">{{ row.name }}</div>
                <div class="user-meta">
                  <span>{{ row.department }}</span>
                  <van-tag :type="getStatusType(row.status)">{{ row.status }}</van-tag>
                </div>
                <div class="user-contact">{{ row.email }}</div>
              </div>
            </div>
            <div class="user-actions">
              <van-button size="small" type="primary" @click.stop="editUser(row)">
                编辑
              </van-button>
            </div>
          </div>
        </template>
      </AdaptiveTable>
    </div>

    <!-- 分页 -->
    <div class="pagination-section">
      <el-pagination
        v-if="!isMobile"
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="totalUsers"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      
      <van-pagination
        v-else
        v-model="currentPage"
        :total-items="totalUsers"
        :items-per-page="pageSize"
        @change="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑用户模态框 -->
    <AdaptiveModal
      v-model="showAddModal"
      :title="editingUser ? '编辑用户' : '新增用户'"
      :width="isMobile ? '100%' : '600px'"
    >
      <AdaptiveForm
        v-model="userForm"
        :fields="userFormFields"
        ref="userFormRef"
      />
      
      <template #footer>
        <div class="modal-footer">
          <AdaptiveButton @click="showAddModal = false">
            取消
          </AdaptiveButton>
          <AdaptiveButton type="primary" @click="saveUser">
            {{ editingUser ? '更新' : '创建' }}
          </AdaptiveButton>
        </div>
      </template>
    </AdaptiveModal>
  </div>
</template>

<script setup lang="ts">
import { useDevice } from '@/composables/useDevice';
import AdaptiveButton from '@/components/adaptive/AdaptiveButton.vue';
import AdaptiveForm from '@/components/adaptive/AdaptiveForm.vue';
import AdaptiveTable from '@/components/adaptive/AdaptiveTable.vue';
import AdaptiveModal from '@/components/adaptive/AdaptiveModal.vue';

defineOptions({
  name: "UserManagement",
});

const { isMobile } = useDevice();

// 响应式数据
const showAddModal = ref(false);
const editingUser = ref(null);
const selectedUsers = ref([]);
const currentPage = ref(1);
const pageSize = ref(20);
const totalUsers = ref(100);
const userFormRef = ref();

// 搜索表单
const searchForm = ref({
  name: '',
  department: '',
  status: ''
});

// 用户表单
const userForm = ref({
  name: '',
  email: '',
  phone: '',
  department: '',
  role: '',
  status: '启用'
});

// 模拟用户数据
const users = ref([
  {
    id: 1,
    name: '张三',
    email: '<EMAIL>',
    phone: '13800138001',
    department: '技术部',
    role: '开发工程师',
    status: '启用',
    avatar: '',
    createTime: '2024-01-15'
  },
  {
    id: 2,
    name: '李四',
    email: '<EMAIL>',
    phone: '13800138002',
    department: '市场部',
    role: '市场专员',
    status: '禁用',
    avatar: '',
    createTime: '2024-01-14'
  },
  {
    id: 3,
    name: '王五',
    email: '<EMAIL>',
    phone: '13800138003',
    department: '销售部',
    role: '销售经理',
    status: '启用',
    avatar: '',
    createTime: '2024-01-13'
  }
]);

// 搜索字段配置
const searchFields = ref([
  {
    prop: 'name',
    label: '姓名',
    type: 'input',
    placeholder: '请输入姓名',
    span: isMobile.value ? 24 : 8
  },
  {
    prop: 'department',
    label: '部门',
    type: 'select',
    placeholder: '请选择部门',
    span: isMobile.value ? 24 : 8,
    options: [
      { label: '技术部', value: '技术部' },
      { label: '市场部', value: '市场部' },
      { label: '销售部', value: '销售部' }
    ]
  },
  {
    prop: 'status',
    label: '状态',
    type: 'select',
    placeholder: '请选择状态',
    span: isMobile.value ? 24 : 8,
    options: [
      { label: '启用', value: '启用' },
      { label: '禁用', value: '禁用' }
    ]
  }
]);

// 用户表单字段配置
const userFormFields = ref([
  {
    prop: 'name',
    label: '姓名',
    type: 'input',
    required: true,
    placeholder: '请输入姓名'
  },
  {
    prop: 'email',
    label: '邮箱',
    type: 'input',
    required: true,
    placeholder: '请输入邮箱'
  },
  {
    prop: 'phone',
    label: '手机号',
    type: 'input',
    placeholder: '请输入手机号'
  },
  {
    prop: 'department',
    label: '部门',
    type: 'select',
    required: true,
    placeholder: '请选择部门',
    options: [
      { label: '技术部', value: '技术部' },
      { label: '市场部', value: '市场部' },
      { label: '销售部', value: '销售部' }
    ]
  },
  {
    prop: 'role',
    label: '角色',
    type: 'input',
    placeholder: '请输入角色'
  },
  {
    prop: 'status',
    label: '状态',
    type: 'select',
    required: true,
    options: [
      { label: '启用', value: '启用' },
      { label: '禁用', value: '禁用' }
    ]
  }
]);

// 用户表格列配置
const userColumns = ref([
  {
    prop: 'avatar',
    label: '头像',
    slot: 'avatar',
    width: 80,
    mobileDisplay: 'hidden'
  },
  {
    prop: 'name',
    label: '姓名',
    mobileDisplay: 'title'
  },
  {
    prop: 'email',
    label: '邮箱',
    mobileDisplay: 'hidden'
  },
  {
    prop: 'department',
    label: '部门',
    mobileDisplay: 'label'
  },
  {
    prop: 'role',
    label: '角色',
    mobileDisplay: 'hidden'
  },
  {
    prop: 'status',
    label: '状态',
    slot: 'status',
    mobileDisplay: 'value'
  },
  {
    prop: 'actions',
    label: '操作',
    slot: 'actions',
    width: 150,
    mobileDisplay: 'hidden'
  }
]);

// 计算属性
const filteredUsers = computed(() => {
  let result = users.value;
  
  if (searchForm.value.name) {
    result = result.filter(user => 
      user.name.includes(searchForm.value.name)
    );
  }
  
  if (searchForm.value.department) {
    result = result.filter(user => 
      user.department === searchForm.value.department
    );
  }
  
  if (searchForm.value.status) {
    result = result.filter(user => 
      user.status === searchForm.value.status
    );
  }
  
  return result;
});

// 方法
const getStatusType = (status: string) => {
  return status === '启用' ? 'success' : 'danger';
};

const handleSearch = () => {
  console.log('搜索用户:', searchForm.value);
  currentPage.value = 1;
};

const handleReset = () => {
  searchForm.value = {
    name: '',
    department: '',
    status: ''
  };
  handleSearch();
};

const handleMobileSearch = (value: string) => {
  searchForm.value.name = value;
  handleSearch();
};

const handleUserClick = (user: any) => {
  console.log('点击用户:', user);
  editUser(user);
};

const handleSelectionChange = (selection: any[]) => {
  selectedUsers.value = selection;
  console.log('选中用户:', selection);
};

const editUser = (user: any) => {
  editingUser.value = user;
  userForm.value = { ...user };
  showAddModal.value = true;
};

const deleteUser = (user: any) => {
  console.log('删除用户:', user);
  // 这里可以添加确认对话框
  if (isMobile.value) {
    console.log(`删除用户: ${user.name}`);
  } else {
    ElMessageBox.confirm(
      `确定要删除用户 ${user.name} 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    ).then(() => {
      // 执行删除操作
      ElMessage.success('删除成功');
    });
  }
};

const saveUser = async () => {
  try {
    // 表单验证
    const valid = await userFormRef.value?.validate();
    if (!valid) return;
    
    console.log('保存用户:', userForm.value);
    
    if (editingUser.value) {
      // 更新用户
      const index = users.value.findIndex(u => u.id === editingUser.value.id);
      if (index !== -1) {
        users.value[index] = { ...userForm.value, id: editingUser.value.id };
      }
    } else {
      // 新增用户
      const newUser = {
        ...userForm.value,
        id: Date.now(),
        avatar: '',
        createTime: new Date().toISOString().split('T')[0]
      };
      users.value.unshift(newUser);
    }
    
    showAddModal.value = false;
    editingUser.value = null;
    userForm.value = {
      name: '',
      email: '',
      phone: '',
      department: '',
      role: '',
      status: '启用'
    };
    
    if (isMobile.value) {
      console.log('保存成功');
    } else {
      ElMessage.success('保存成功');
    }
  } catch (error) {
    console.error('保存失败:', error);
  }
};

const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
};

const handleCurrentChange = (page: number) => {
  currentPage.value = page;
};
</script>

<style lang="scss" scoped>
.user-management {
  padding: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    h2 {
      margin: 0;
      color: var(--el-text-color-primary);
    }
  }

  .search-section {
    margin-bottom: 24px;
    padding: 20px;
    background: var(--el-bg-color);
    border-radius: 8px;
    border: 1px solid var(--el-border-color);
  }

  .user-list {
    margin-bottom: 24px;
  }

  .action-buttons {
    display: flex;
    gap: 8px;
  }

  .mobile-user-card {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;

    .user-info {
      display: flex;
      gap: 12px;
      flex: 1;

      .avatar-placeholder {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: var(--el-color-primary);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
      }

      .user-details {
        flex: 1;

        .user-name {
          font-weight: 600;
          margin-bottom: 4px;
        }

        .user-meta {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 4px;
          font-size: 14px;
          color: var(--el-text-color-regular);
        }

        .user-contact {
          font-size: 12px;
          color: var(--el-text-color-secondary);
        }
      }
    }

    .user-actions {
      margin-left: 12px;
    }
  }

  .pagination-section {
    display: flex;
    justify-content: center;
    margin-top: 24px;
  }

  .modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

// 移动端适配
@media screen and (max-width: 991px) {
  .user-management {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      h2 {
        text-align: center;
      }
    }

    .search-section {
      padding: 16px;
    }

    .modal-footer {
      flex-direction: column-reverse;
      gap: 8px;
    }
  }
}
</style>
