<!DOCTYPE html>
<html>
<head>
  <title>stagger | anime.js</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <meta property="og:title" content="anime.js">
  <meta property="og:url" content="https://animejs.com">
  <meta property="og:description" content="Javascript Animation Engine">
  <meta property="og:image" content="https://animejs.com/documentation/assets/img/icons/og.png">
  <meta name="twitter:card" content="summary">
  <meta name="twitter:title" content="anime.js">
  <meta name="twitter:site" content="@juliangarnier">
  <meta name="twitter:description" content="Javascript Animation Engine">
  <meta name="twitter:image" content="https://animejs.com/documentation/assets/img/icons/twitter.png">
  <link rel="apple-touch-icon-precomposed" href="../assets/img/social-media-image.png">
  <link rel="icon" type="image/png" href="../assets/img/favicon.png" >
  <link href="../assets/css/animejs.css" rel="stylesheet">
  <link href="../assets/css/documentation.css" rel="stylesheet">
  <style>

    :root {
      font-size: 20px;
    }

    body {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      align-items: center;
      background-color: #FF4B4B;
    }

    .stagger-visualizer {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      align-items: center;
      width: 17rem;
      height: 17rem;
    }

    .stagger-visualizer div {
      /*position: absolute;*/
      width: 1rem;
      height: 1rem;
      border: 1px solid #FFF;
      background-color: #FFF;
    }

  </style>
</head>
<body>

  <div class="stagger-visualizer"></div>

</body>
<script type="module">

  import anime from '../../src/index.js';

  const staggerVisualizerEl = document.querySelector('.stagger-visualizer');
  const fragment = document.createDocumentFragment();
  const grid = [17, 17];
  const col = grid[0];
  const row = grid[1];
  const numberOfElements = col * row;

  for (let i = 0; i < numberOfElements; i++) {
    fragment.appendChild(document.createElement('div'));
  }

  staggerVisualizerEl.appendChild(fragment);

  const staggersAnimation = anime.timeline({
    targets: '.stagger-visualizer div',
    easing: 'easeInOutSine',
    delay: anime.stagger(50),
    loop: true,
    autoplay: false
  })
  .add({
    translateX: [
      {value: anime.stagger('-.1rem', {grid: grid, from: 'center', axis: 'x'}) },
      {value: anime.stagger('.1rem', {grid: grid, from: 'center', axis: 'x'}) }
    ],
    translateY: [
      {value: anime.stagger('-.1rem', {grid: grid, from: 'center', axis: 'y'}) },
      {value: anime.stagger('.1rem', {grid: grid, from: 'center', axis: 'y'}) }
    ],
    duration: 1000,
    scale: .5,
    delay: anime.stagger(100, {grid: grid, from: 'center'})
  })
  .add({
    translateX: () => anime.random(-10, 10),
    translateY: () => anime.random(-10, 10),
    delay: anime.stagger(8, {from: 'last'})
  })
  .add({
    translateX: anime.stagger('.25rem', {grid: grid, from: 'center', axis: 'x'}),
    translateY: anime.stagger('.25rem', {grid: grid, from: 'center', axis: 'y'}),
    rotate: 0,
    scaleX: 4,
    scaleY: .25,
    delay: anime.stagger(4, {from: 'center'})
  })
  .add({
    rotate: anime.stagger([90, 0], {grid: grid, from: 'center'}),
    delay: anime.stagger(50, {grid: grid, from: 'center'})
  })
  .add({
    translateX: 0,
    translateY: 0,
    scale: .5,
    scaleX: 1,
    rotate: 0,
    duration: 1000,
    delay: anime.stagger(100, {grid: grid, from: 'center'})
  })
  .add({
    scaleY: 1,
    scale: 1,
    delay: anime.stagger(20, {grid: grid, from: 'center'})
  })

  staggersAnimation.play();

</script>
</html>
