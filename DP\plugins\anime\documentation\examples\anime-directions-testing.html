<!DOCTYPE html>
<html>
<head>
  <title>Diretions Testing | anime.js</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <meta property="og:title" content="anime.js">
  <meta property="og:url" content="https://animejs.com">
  <meta property="og:description" content="Javascript Animation Engine">
  <meta property="og:image" content="https://animejs.com/documentation/assets/img/icons/og.png">
  <meta name="twitter:card" content="summary">
  <meta name="twitter:title" content="anime.js">
  <meta name="twitter:site" content="@juliangarnier">
  <meta name="twitter:description" content="Javascript Animation Engine">
  <meta name="twitter:image" content="https://animejs.com/documentation/assets/img/icons/twitter.png">
  <link rel="apple-touch-icon-precomposed" href="../assets/img/social-media-image.png">
  <link rel="icon" type="image/png" href="../assets/img/favicon.png">
  <link href="../assets/css/animejs.css" rel="stylesheet">
  <link href="../assets/css/documentation.css" rel="stylesheet">
  <script src="../../lib/anime.min.js"></script>
  <style>
    body {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: flex-start;
      height: auto;
    }
    article {
      padding: 1vw;
      position: relative;
      width: 33.33%;
      height: 20vh;
      background: currentColor;
      border: 1px solid #111116;
    }
    h2 {
      color: #111116;
    }
    .el {
      position: relative;
      top: 5vh;
      width: 2vw;
      height: 2vw;
      background: #111116;
    }
    .logs {
      display: flex;
      flex-direction: column;
      flex-wrap: wrap;
      align-items: center;
      justify-content: center;
      position: absolute;
      top: 1vh;
      left: 0;
      bottom: 0;
      height: 19vh;
      width: 100%;
    }
    input {
      text-align: center;
      background: none;
      border: none;
    }
  </style>
</head>
<body>
  <script>
    function createTest(title, numberOfTargets, params) {
      var testEl = document.createElement('article');
      var testZoneEl = document.createElement('div');
      var logsEl = document.createElement('div');
      var progressEl = document.createElement('input');
      var promiseEl = document.createElement('input');
      var h2El = document.createElement('h2');

      for (var i = 0; i < numberOfTargets; i++) {
        var el = document.createElement('div');
        el.classList.add('el');
        testZoneEl.appendChild(el);
      }

      h2El.innerHTML = title;
      testEl.appendChild(h2El);
      logsEl.classList.add('logs');
      logsEl.appendChild(progressEl);
      logsEl.appendChild(promiseEl);
      testEl.appendChild(logsEl);
      testEl.appendChild(testZoneEl);
      document.body.appendChild(testEl);

      params.targets = testZoneEl.querySelectorAll('div');
      params.translateX = '29vw';
      params.delay = 1000;
      params.endDelay = 1000;
      params.easing = 'easeInOutQuad';
      params.begin = function() { testEl.classList.add('color-lightorange') };
      params.update = function(a) { 
        testEl.classList.add('color-red');
        progressEl.value = 'Progress ' + Math.round(a.progress) + '%';
      };
      params.complete = function() { testEl.classList.add('color-darkgreen') };
      var animation = anime(params);
      animation.finished.then(function() {
        promiseEl.value = 'Promise resolved'
        console.log('Promise resolved');
      });
      testEl.onclick = function() {
        testEl.classList.add('color-red');
        testEl.classList.remove('color-lightorange');
        testEl.classList.remove('color-darkgreen');
        animation.restart();
      }
    }

    createTest('Normal', 1, {});

    createTest('Reverse', 1, {
      direction: 'reverse'
    });

    createTest('Alternate', 1, {
      direction: 'alternate'
    });

    createTest('Normal 1 time', 1, {
      loop: 1
    });

    createTest('Reverse 1 time', 1, {
      direction: 'reverse',
      loop: 1
    });

    createTest('Alternate 1 time (equals 2)', 1, {
      direction: 'alternate',
      loop: 1
    });

    createTest('Normal 2 times', 1, {
      loop: 2
    });

    createTest('Reverse 2 times', 1, {
      direction: 'reverse',
      loop: 2
    });

    createTest('Alternate 2 times', 1, {
      direction: 'alternate',
      loop: 2
    });

    createTest('Normal 3 times', 1, {
      loop: 3
    });

    createTest('Reverse 3 times', 1, {
      direction: 'reverse',
      loop: 3
    });

    createTest('Alternate 3 times', 1, {
      direction: 'alternate',
      loop: 3
    });

    createTest('Normal true', 1, {
      loop: true
    });

    createTest('Reverse true', 1, {
      direction: 'reverse',
      loop: true
    });

    createTest('Alternate true', 1, {
      direction: 'alternate',
      loop: true
    });



  </script>
</body>
</html>
