<!DOCTYPE html>
<html>
<head>
  <titletransforms demo • anime.js</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <meta property="og:title" content="anime.js">
  <meta property="og:url" content="https://animejs.com">
  <meta property="og:description" content="Javascript Animation Engine">
  <meta property="og:image" content="https://animejs.com/documentation/assets/img/icons/og.png">
  <meta name="twitter:card" content="summary">
  <meta name="twitter:title" content="anime.js">
  <meta name="twitter:site" content="@juliangarnier">
  <meta name="twitter:description" content="Javascript Animation Engine">
  <meta name="twitter:image" content="https://animejs.com/documentation/assets/img/icons/twitter.png">
  <link rel="apple-touch-icon-precomposed" href="../assets/img/social-media-image.png">
  <link rel="icon" type="image/png" href="../assets/img/favicon.png" >
  <link href="../assets/css/animejs.css" rel="stylesheet">
  <style>

    :root {
      font-size: 16px;
    }

    body {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      background-color: currentColor;
    }

    .transform-demo {
      position: relative;
      width: 1rem;
      height: 1rem;
    }

    .transform-demo .dot {
      position: absolute;
      width: 1rem;
      height: 1rem;
      /*border-radius: 50%;*/
      background-color: currentColor;
    }

  </style>
</head>
<body>

  <div class="transform-demo"></div>

</body>
<script type="module">

  import anime from '../../src/index.js';

  const easingVisualizerEl = document.querySelector('.transform-demo');
  const fragment = document.createDocumentFragment();
  const numberOfBars = 500;

  for (let i = 0; i < numberOfBars; i++) {
    const dotEl = document.createElement('div');
    dotEl.classList.add('dot');
    dotEl.classList.add('color-red');
    fragment.appendChild(dotEl);
  }

  easingVisualizerEl.appendChild(fragment);

  // anime.set('.transform-demo .dot', {rotate: function() { return anime.random(0, 360); } });

  anime({
    targets: '.transform-demo .dot',
    scale: (function() {
      var keyframes = [];
      for (var i = 0; i < 40; i++) {
        keyframes.push({
          value: function() { return anime.random(25, 125) / 100; },
          duration: anime.random(500, 1000),
        });
      }
      return keyframes;
    })(),
    translateX: (function() {
      var keyframes = [];
      for (var i = 0; i < 40; i++) {
        keyframes.push({
          value: function() { return anime.random(-25, 25) + 'vh'; },
          duration: anime.random(1000, 2000),
        });
      }
      return keyframes;
    })(),
    translateY: (function() {
      var keyframes = [];
      for (var i = 0; i < 40; i++) {
        keyframes.push({
          value: function() { return anime.random(-25, 25) + 'vh'; },
          duration: anime.random(2000, 4000),
        });
      }
      return keyframes;
    })(),
    easing: 'easeOutQuad',
    delay: anime.stagger(2)
  })

</script>
</html>
