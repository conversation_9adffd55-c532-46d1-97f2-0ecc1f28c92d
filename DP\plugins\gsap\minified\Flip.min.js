/*!
 * Flip 3.11.3
 * https://greensock.com
 * 
 * @license Copyright 2022, GreenSock. All rights reserved.
 * Subject to the terms at https://greensock.com/standard-license or for Club GreenSock members, the agreement issued with that membership.
 * @author: <PERSON>, <EMAIL>
 */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t=t||self).window=t.window||{})}(this,function(e){"use strict";function p(t){var e=t.ownerDocument||t;!(w in t.style)&&"msTransform"in t.style&&(S=(w="msTransform")+"Origin");for(;e.parentNode&&(e=e.parentNode););if(y=window,d=new _,e){r=(g=e).documentElement,b=e.body,(a=g.createElementNS("http://www.w3.org/2000/svg","g")).style.transform="none";var i=e.createElement("div"),n=e.createElement("div");b.appendChild(i),i.appendChild(n),i.style.position="static",i.style[w]="translate3d(0,0,1px)",m=n.offsetParent!==i,b.removeChild(i)}return e}function t(){return y.pageYOffset||g.scrollTop||r.scrollTop||b.scrollTop||0}function u(){return y.pageXOffset||g.scrollLeft||r.scrollLeft||b.scrollLeft||0}function v(t){return t.ownerSVGElement||("svg"===(t.tagName+"").toLowerCase()?t:null)}function x(t,e){if(t.parentNode&&(g||p(t))){var i=v(t),n=i?i.getAttribute("xmlns")||"http://www.w3.org/2000/svg":"http://www.w3.org/1999/xhtml",r=i?e?"rect":"g":"div",a=2!==e?0:100,s=3===e?100:0,o="position:absolute;display:block;pointer-events:none;margin:0;padding:0;",l=g.createElementNS?g.createElementNS(n.replace(/^https/,"http"),r):g.createElement(r);return e&&(i?(f=f||x(t),l.setAttribute("width",.01),l.setAttribute("height",.01),l.setAttribute("transform","translate("+a+","+s+")"),f.appendChild(l)):(c||((c=x(t)).style.cssText=o),l.style.cssText=o+"width:0.1px;height:0.1px;top:"+s+"px;left:"+a+"px",c.appendChild(l))),l}throw"Need document and parent."}function z(t){var e,i=t.getCTM();return i||(e=t.style[w],t.style[w]="none",t.appendChild(a),i=a.getCTM(),t.removeChild(a),e?t.style[w]=e:t.style.removeProperty(w.replace(/([A-Z])/g,"-$1").toLowerCase())),i||d.clone()}function A(t,e){var i,n,r,a,s,o,l=v(t),u=t===l,h=l?k:C,p=t.parentNode;if(t===y)return t;if(h.length||h.push(x(t,1),x(t,2),x(t,3)),i=l?f:c,l)u?(a=-(r=z(t)).e/r.a,s=-r.f/r.d,n=d):t.getBBox?(r=t.getBBox(),a=(n=(n=t.transform?t.transform.baseVal:{}).numberOfItems?1<n.numberOfItems?function _consolidate(t){for(var e=new _,i=0;i<t.numberOfItems;i++)e.multiply(t.getItem(i).matrix);return e}(n):n.getItem(0).matrix:d).a*r.x+n.c*r.y,s=n.b*r.x+n.d*r.y):(n=new _,a=s=0),e&&"g"===t.tagName.toLowerCase()&&(a=s=0),(u?l:p).appendChild(i),i.setAttribute("transform","matrix("+n.a+","+n.b+","+n.c+","+n.d+","+(n.e+a)+","+(n.f+s)+")");else{if(a=s=0,m)for(n=t.offsetParent,r=t;(r=r&&r.parentNode)&&r!==n&&r.parentNode;)4<(y.getComputedStyle(r)[w]+"").length&&(a=r.offsetLeft,s=r.offsetTop,r=0);if("absolute"!==(o=y.getComputedStyle(t)).position&&"fixed"!==o.position)for(n=t.offsetParent;p&&p!==n;)a+=p.scrollLeft||0,s+=p.scrollTop||0,p=p.parentNode;(r=i.style).top=t.offsetTop-s+"px",r.left=t.offsetLeft-a+"px",r[w]=o[w],r[S]=o[S],r.position="fixed"===o.position?"fixed":"absolute",t.parentNode.appendChild(i)}return i}function B(t,e,i,n,r,a,s){return t.a=e,t.b=i,t.c=n,t.d=r,t.e=a,t.f=s,t}var g,y,r,b,c,f,d,a,m,i,w="transform",S=w+"Origin",k=[],C=[],_=((i=Matrix2D.prototype).inverse=function inverse(){var t=this.a,e=this.b,i=this.c,n=this.d,r=this.e,a=this.f,s=t*n-e*i||1e-10;return B(this,n/s,-e/s,-i/s,t/s,(i*a-n*r)/s,-(t*a-e*r)/s)},i.multiply=function multiply(t){var e=this.a,i=this.b,n=this.c,r=this.d,a=this.e,s=this.f,o=t.a,l=t.c,u=t.b,h=t.d,p=t.e,c=t.f;return B(this,o*e+u*n,o*i+u*r,l*e+h*n,l*i+h*r,a+p*e+c*n,s+p*i+c*r)},i.clone=function clone(){return new Matrix2D(this.a,this.b,this.c,this.d,this.e,this.f)},i.equals=function equals(t){var e=this.a,i=this.b,n=this.c,r=this.d,a=this.e,s=this.f;return e===t.a&&i===t.b&&n===t.c&&r===t.d&&a===t.e&&s===t.f},i.apply=function apply(t,e){void 0===e&&(e={});var i=t.x,n=t.y,r=this.a,a=this.b,s=this.c,o=this.d,l=this.e,u=this.f;return e.x=i*r+n*s+l||0,e.y=i*a+n*o+u||0,e},Matrix2D);function Matrix2D(t,e,i,n,r,a){void 0===t&&(t=1),void 0===e&&(e=0),void 0===i&&(i=0),void 0===n&&(n=1),void 0===r&&(r=0),void 0===a&&(a=0),B(this,t,e,i,n,r,a)}function getGlobalMatrix(e,i,n,r){if(!e||!e.parentNode||(g||p(e)).documentElement===e)return new _;var a=function _forceNonZeroScale(t){for(var e,i;t&&t!==b;)(i=t._gsap)&&i.uncache&&i.get(t,"x"),i&&!i.scaleX&&!i.scaleY&&i.renderTransform&&(i.scaleX=i.scaleY=1e-4,i.renderTransform(1,i),e?e.push(i):e=[i]),t=t.parentNode;return e}(e),s=v(e)?k:C,o=A(e,n),l=s[0].getBoundingClientRect(),h=s[1].getBoundingClientRect(),c=s[2].getBoundingClientRect(),f=o.parentNode,d=!r&&function _isFixed(t){return"fixed"===y.getComputedStyle(t).position||((t=t.parentNode)&&1===t.nodeType?_isFixed(t):void 0)}(e),m=new _((h.left-l.left)/100,(h.top-l.top)/100,(c.left-l.left)/100,(c.top-l.top)/100,l.left+(d?0:u()),l.top+(d?0:t()));if(f.removeChild(o),a)for(l=a.length;l--;)(h=a[l]).scaleX=h.scaleY=0,h.renderTransform(1,h);return i?m.inverse():m}function K(t,e){return t.actions.forEach(function(t){return t.vars[e]&&t.vars[e](t)})}function R(t){return"string"==typeof t?t.split(" ").join("").split(","):t}function U(t){return T(t)[0]||console.warn("Element not found:",t)}function V(t){return Math.round(1e4*t)/1e4||0}function W(t,e,i){return t.forEach(function(t){return t.classList[i](e)})}function Z(t){return t.replace(/([A-Z])/g,"-$1").toLowerCase()}function $(t,e){var i,n={};for(i in t)e[i]||(n[i]=t[i]);return n}function aa(t){var e=st[t]=R(t);return et[t]=e.concat(nt),e}function da(t,e,i){return t.forEach(function(t){return t.d=function _getDOMDepth(t,e,i){void 0===i&&(i=0);for(var n=t.parentNode,r=1e3*Math.pow(10,i)*(e?-1:1),a=e?900*-r:0;t;)a+=r,t=t.previousSibling;return n?a+_getDOMDepth(n,e,i+1):a}(i?t.element:t.t,e)}),t.sort(function(t,e){return t.d-e.d}),t}function ea(t,e){for(var i,n,r=t.element.style,a=t.css=t.css||[],s=e.length;s--;)n=r[i=e[s]]||r.getPropertyValue(i),a.push(n?i:F[i]||(F[i]=Z(i)),n);return r}function fa(t){var e=t.css,i=t.element.style,n=0;for(t.cache.uncache=1;n<e.length;n+=2)e[n+1]?i[e[n]]=e[n+1]:i.removeProperty(e[n])}function ga(t,e){t.forEach(function(t){return t.a.cache.uncache=1}),e||t.finalStates.forEach(fa)}function ia(e,i,n){var r,a,s,o=e.element,l=e.width,h=e.height,p=e.uncache,c=e.getProp,f=o.style,d=4;if("object"!=typeof i&&(i=e),tt&&1!==n)return tt._abs.push({t:o,b:e,a:e,sd:0}),tt._final.push(function(){return(e.cache.uncache=1)&&fa(e)}),o;for(a="none"===c("display"),e.isVisible&&!a||(a&&(ea(e,["display"]).display=i.display),e.matrix=i.matrix,e.width=l=e.width||i.width,e.height=h=e.height||i.height),ea(e,I),s=window.getComputedStyle(o);d--;)f[I[d]]=s[I[d]];if(f.gridArea="1 / 1 / 1 / 1",f.transition="none",f.position="absolute",f.width=l+"px",f.height=h+"px",f.top||(f.top="0px"),f.left||(f.left="0px"),p)r=new ht(o);else if((r=$(e,M)).position="absolute",e.simple){var m=o.getBoundingClientRect();r.matrix=new _(1,0,0,1,m.left+u(),m.top+t())}else r.matrix=getGlobalMatrix(o,!1,!1,!0);return r=ot(r,e,!0),e.x=N(r.x,.01),e.y=N(r.y,.01),o}function ja(t,e){return!0!==e&&(e=T(e),t=t.filter(function(t){if(-1!==e.indexOf((t.sd<0?t.b:t.a).element))return!0;t.t._gsap.renderTransform(1),t.b.isVisible&&(t.t.style.width=t.b.width+"px",t.t.style.height=t.b.height+"px")})),t}function ka(t){return da(t,!0).forEach(function(t){return(t.a.isVisible||t.b.isVisible)&&ia(t.sd<0?t.b:t.a,t.b,1)})}function oa(t,e){var i,n=t.style||t;for(i in e)n[i]=e[i]}function qa(t){return t.map(function(t){return t.element})}function ra(t,e,i){return t&&e.length&&i.add(t(qa(e),i,new ut(e,0,!0)),0)}function ta(t,e){return t instanceof ut?t:new ut(t,e)}function ua(t,e,i){var n=t.idLookup[i],r=t.alt[i];return!r.isVisible||(e.getElementState(r.element)||r).isVisible&&n.isVisible?n:r}function ya(t){if(t!==l){var e=o.style,i=o.clientWidth===window.outerWidth,n=o.clientHeight===window.outerHeight,r=4;if(t&&(i||n)){for(;r--;)X[r]=e[D[r]];i&&(e.width=o.clientWidth+"px",e.overflowY="hidden"),n&&(e.height=o.clientHeight+"px",e.overflowX="hidden"),l=t}else if(l){for(;r--;)X[r]?e[D[r]]=X[r]:e.removeProperty(Z(D[r]));l=t}}}function za(t,e,r,i){t instanceof ut&&e instanceof ut||console.warn("Not a valid state object.");var a,s,o,l,u,h,p,c,f,n,d,m,g,y,v,x=(r=r||{}).clearProps,b=r.onEnter,w=r.onLeave,S=r.absolute,k=r.absoluteOnLeave,C=r.custom,V=r.delay,_=r.paused,E=r.repeat,B=r.repeatDelay,M=r.yoyo,F=r.toggleClass,I=r.nested,L=r.zIndex,T=r.scale,N=r.fade,O=r.stagger,P=r.spin,A=r.prune,X=("props"in r?r:t).props,z=$(r,rt),D=Q.timeline({delay:V,paused:_,repeat:E,repeatDelay:B,yoyo:M,data:"isFlip"}),R=z,Y=[],j=[],G=[],q=[],U=!0===P?1:P||0,H="function"==typeof P?P:function(){return U},Z=t.interrupted||e.interrupted,K=D[1!==i?"to":"from"];for(s in e.idLookup)d=e.alt[s]?ua(e,t,s):e.idLookup[s],u=d.element,n=t.idLookup[s],!t.alt[s]||u!==n.element||!t.alt[s].isVisible&&d.isVisible||(n=t.alt[s]),n?(h={t:u,b:n,a:d,sd:n.element===u?0:d.isVisible?1:-1},G.push(h),h.sd&&(h.sd<0&&(h.b=d,h.a=n),Z&&ea(h.b,X?et[X]:nt),N&&G.push(h.swap={t:n.element,b:h.b,a:h.a,sd:-h.sd,swap:h})),u._flip=n.element._flip=tt?tt.timeline:D):d.isVisible&&(G.push({t:u,b:$(d,{isVisible:1}),a:d,sd:0,entering:1}),u._flip=tt?tt.timeline:D);X&&(st[X]||aa(X)).forEach(function(e){return z[e]=function(t){return G[t].a.props[e]}}),G.finalStates=f=[],m=function run(){for(da(G),ya(!0),l=0;l<G.length;l++)h=G[l],g=h.a,y=h.b,!A||g.isDifferent(y)||h.entering?(u=h.t,!I||h.sd<0||!l||(g.matrix=getGlobalMatrix(u,!1,!1,!0)),y.isVisible&&g.isVisible?(h.sd<0?(p=new ht(u,X,t.simple),ot(p,g,T,0,0,p),p.matrix=getGlobalMatrix(u,!1,!1,!0),p.css=h.b.css,h.a=g=p,N&&(u.style.opacity=Z?y.opacity:g.opacity),O&&q.push(u)):0<h.sd&&N&&(u.style.opacity=Z?g.opacity-y.opacity:"0"),ot(g,y,T,X)):y.isVisible!==g.isVisible&&(y.isVisible?g.isVisible||(y.css=g.css,j.push(y),G.splice(l--,1),S&&I&&ot(g,y,T,X)):(g.isVisible&&Y.push(g),G.splice(l--,1))),T||(u.style.maxWidth=Math.max(g.width,y.width)+"px",u.style.maxHeight=Math.max(g.height,y.height)+"px",u.style.minWidth=Math.min(g.width,y.width)+"px",u.style.minHeight=Math.min(g.height,y.height)+"px"),I&&F&&u.classList.add(F)):G.splice(l--,1),f.push(g);var e;if(F&&(e=f.map(function(t){return t.element}),I&&e.forEach(function(t){return t.classList.remove(F)})),ya(!1),T?(z.scaleX=function(t){return G[t].a.scaleX},z.scaleY=function(t){return G[t].a.scaleY}):(z.width=function(t){return G[t].a.width+"px"},z.height=function(t){return G[t].a.height+"px"},z.autoRound=r.autoRound||!1),z.x=function(t){return G[t].a.x+"px"},z.y=function(t){return G[t].a.y+"px"},z.rotation=function(t){return G[t].a.rotation+(P?360*H(t,c[t],c):0)},z.skewX=function(t){return G[t].a.skewX},c=G.map(function(t){return t.t}),!L&&0!==L||(z.modifiers={zIndex:function zIndex(){return L}},z.zIndex=L,z.immediateRender=!1!==r.immediateRender),N&&(z.opacity=function(t){return G[t].sd<0?0:0<G[t].sd?G[t].a.opacity:"+=0"}),q.length){O=Q.utils.distribute(O);var i=c.slice(q.length);z.stagger=function(t,e){return O(~q.indexOf(e)?c.indexOf(G[t].swap.t):t,e,i)}}if(it.forEach(function(t){return r[t]&&D.eventCallback(t,r[t],r[t+"Params"])}),C&&c.length)for(s in R=$(z,rt),"scale"in C&&(C.scaleX=C.scaleY=C.scale,delete C.scale),C)(a=$(C[s],at))[s]=z[s],!("duration"in a)&&"duration"in z&&(a.duration=z.duration),a.stagger=z.stagger,K.call(D,c,a,0),delete R[s];(c.length||j.length||Y.length)&&(F&&D.add(function(){return W(e,F,D._zTime<0?"remove":"add")},0)&&!_&&W(e,F,"add"),c.length&&K.call(D,c,R,0)),ra(b,Y,D),ra(w,j,D);var n=tt&&tt.timeline;n&&(n.add(D,0),tt._final.push(function(){return ga(G,!x)})),o=D.duration(),D.call(function(){var t=D.time()>=o;t&&!n&&ga(G,!x),F&&W(e,F,t?"remove":"add")})},k&&(S=G.filter(function(t){return!t.sd&&!t.a.isVisible&&t.b.isVisible}).map(function(t){return t.a.element})),tt?(S&&(v=tt._abs).push.apply(v,ja(G,S)),tt._run.push(m)):(S&&ka(ja(G,S)),m());var J=tt?tt.timeline:D;return J.revert=function(){return lt(J,1)},J}function Ca(t){for(var e,i=t.idLookup={},n=t.alt={},r=t.elementStates,a=r.length;a--;)i[(e=r[a]).id]?n[e.id]=e:i[e.id]=e}var T,Q,tt,s,o,N,l,n,E=1,h={},O=180/Math.PI,P=Math.PI/180,M={},F={},et={},it=R("onStart,onUpdate,onComplete,onReverseComplete,onInterrupt"),nt=R("transform,transformOrigin,width,height,position,top,left,opacity,zIndex,maxWidth,maxHeight,minWidth,minHeight"),rt={zIndex:1,kill:1,simple:1,spin:1,clearProps:1,targets:1,toggleClass:1,onComplete:1,onUpdate:1,onInterrupt:1,onStart:1,delay:1,repeat:1,repeatDelay:1,yoyo:1,scale:1,fade:1,absolute:1,props:1,onEnter:1,onLeave:1,custom:1,paused:1,nested:1,prune:1,absoluteOnLeave:1},at={zIndex:1,simple:1,clearProps:1,scale:1,absolute:1,fitChild:1,getVars:1,props:1},st={},I="paddingTop,paddingRight,paddingBottom,paddingLeft,gridArea,transition".split(","),L=function _parseElementState(t,e,i,n){return t instanceof ht?t:t instanceof ut?function _findElStateInState(t,e){return e&&t.idLookup[L(e).id]||t.elementStates[0]}(t,n):new ht("string"==typeof t?U(t)||console.warn(t+" not found"):t,e,i)},ot=function _fit(t,e,i,n,r,a){var s,o,l,u,h,p,c,f=t.element,d=t.cache,m=t.parent,g=t.x,y=t.y,v=e.width,x=e.height,b=e.scaleX,w=e.scaleY,S=e.rotation,k=e.bounds,C=a&&f.style.cssText,_=a&&f.getBBox&&f.getAttribute("transform"),E=t,B=e.matrix,M=B.e,F=B.f,I=t.bounds.width!==k.width||t.bounds.height!==k.height||t.scaleX!==b||t.scaleY!==w||t.rotation!==S,L=!I&&t.simple&&e.simple&&!r;return L||!m?(b=w=1,S=s=0):(p=(h=function _getInverseGlobalMatrix(t){var e=t._gsap||Q.core.getCache(t);return e.gmCache===Q.ticker.frame?e.gMatrix:(e.gmCache=Q.ticker.frame,e.gMatrix=getGlobalMatrix(t,!0,!1,!0))}(m)).clone().multiply(e.ctm?e.matrix.clone().multiply(e.ctm):e.matrix),S=V(Math.atan2(p.b,p.a)*O),s=V(Math.atan2(p.c,p.d)*O+S)%360,b=Math.sqrt(Math.pow(p.a,2)+Math.pow(p.b,2)),w=Math.sqrt(Math.pow(p.c,2)+Math.pow(p.d,2))*Math.cos(s*P),r&&(r=T(r)[0],u=Q.getProperty(r),c=r.getBBox&&"function"==typeof r.getBBox&&r.getBBox(),E={scaleX:u("scaleX"),scaleY:u("scaleY"),width:c?c.width:Math.ceil(parseFloat(u("width","px"))),height:c?c.height:parseFloat(u("height","px"))}),d.rotation=S+"deg",d.skewX=s+"deg"),i?(b*=v!==E.width&&E.width?v/E.width:1,w*=x!==E.height&&E.height?x/E.height:1,d.scaleX=b,d.scaleY=w):(v=N(v*b/E.scaleX,0),x=N(x*w/E.scaleY,0),f.style.width=v+"px",f.style.height=x+"px"),n&&oa(f,e.props),L||!m?(g+=M-t.matrix.e,y+=F-t.matrix.f):I||m!==e.parent?(d.renderTransform(1,d),p=getGlobalMatrix(r||f,!1,!1,!0),o=h.apply({x:p.e,y:p.f}),g+=(l=h.apply({x:M,y:F})).x-o.x,y+=l.y-o.y):(h.e=h.f=0,g+=(l=h.apply({x:M-t.matrix.e,y:F-t.matrix.f})).x,y+=l.y),g=N(g,.02),y=N(y,.02),!a||a instanceof ht?(d.x=g+"px",d.y=y+"px",d.renderTransform(1,d)):(f.style.cssText=C,f.getBBox&&f.setAttribute("transform",_||""),d.uncache=1),a&&(a.x=g,a.y=y,a.rotation=S,a.skewX=s,i?(a.scaleX=b,a.scaleY=w):(a.width=v,a.height=x)),a||d},X=[],D="width,height,overflowX,overflowY".split(","),lt=function _killFlip(t,e){if(t&&t.progress()<1&&!t.paused())return e&&(function _interrupt(t){t.vars.onInterrupt&&t.vars.onInterrupt.apply(t,t.vars.onInterruptParams||[]),t.getChildren(!0,!1,!0).forEach(_interrupt)}(t),e<2&&t.progress(1),t.kill()),!0},ut=((n=FlipState.prototype).update=function update(t){var e=this;return this.elementStates=this.targets.map(function(t){return new ht(t,e.props,e.simple)}),Ca(this),this.interrupt(t),this.recordInlineStyles(),this},n.clear=function clear(){return this.targets.length=this.elementStates.length=0,Ca(this),this},n.fit=function fit(t,e,i){for(var n,r,a=da(this.elementStates.slice(0),!1,!0),s=(t||this).idLookup,o=0;o<a.length;o++)n=a[o],i&&(n.matrix=getGlobalMatrix(n.element,!1,!1,!0)),(r=s[n.id])&&ot(n,r,e,!0,0,n),n.matrix=getGlobalMatrix(n.element,!1,!1,!0);return this},n.getProperty=function getProperty(t,e){var i=this.getElementState(t)||M;return(e in i?i:i.props||M)[e]},n.add=function add(t){for(var e,i,n,r=t.targets.length,a=this.idLookup,s=this.alt;r--;)(n=a[(i=t.elementStates[r]).id])&&(i.element===n.element||s[i.id]&&s[i.id].element===i.element)?(e=this.elementStates.indexOf(i.element===n.element?n:s[i.id]),this.targets.splice(e,1,t.targets[r]),this.elementStates.splice(e,1,i)):(this.targets.push(t.targets[r]),this.elementStates.push(i));return t.interrupted&&(this.interrupted=!0),t.simple||(this.simple=!1),Ca(this),this},n.compare=function compare(t){function ih(t,e,i){return(t.isVisible!==e.isVisible?t.isVisible?f:d:t.isVisible?c:p).push(i)&&m.push(i)}function jh(t,e,i){return m.indexOf(i)<0&&ih(t,e,i)}var e,i,n,r,a,s,o,l,u=t.idLookup,h=this.idLookup,p=[],c=[],f=[],d=[],m=[],g=t.alt,y=this.alt;for(n in u)a=g[n],s=y[n],r=(e=a?ua(t,this,n):u[n]).element,i=h[n],s?(l=i.isVisible||!s.isVisible&&r===i.element?i:s,(o=!a||e.isVisible||a.isVisible||l.element!==a.element?e:a).isVisible&&l.isVisible&&o.element!==l.element?((o.isDifferent(l)?c:p).push(o.element,l.element),m.push(o.element,l.element)):ih(o,l,o.element),a&&o.element===a.element&&(a=u[n]),jh(o.element!==i.element&&a?a:o,i,i.element),jh(a&&a.element===s.element?a:o,s,s.element),a&&jh(a,s.element===a.element?s:i,a.element)):(i?i.isDifferent(e)?ih(e,i,r):p.push(r):f.push(r),a&&jh(a,i,a.element));for(n in h)u[n]||(d.push(h[n].element),y[n]&&d.push(y[n].element));return{changed:c,unchanged:p,enter:f,leave:d}},n.recordInlineStyles=function recordInlineStyles(){for(var t=et[this.props]||nt,e=this.elementStates.length;e--;)ea(this.elementStates[e],t)},n.interrupt=function interrupt(n){var r=this,a=[];this.targets.forEach(function(t){var e=t._flip,i=lt(e,n?0:1);n&&i&&a.indexOf(e)<0&&e.add(function(){return r.updateVisibility()}),i&&a.push(e)}),!n&&a.length&&this.updateVisibility(),this.interrupted||(this.interrupted=!!a.length)},n.updateVisibility=function updateVisibility(){this.elementStates.forEach(function(t){var e=t.element.getBoundingClientRect();t.isVisible=!!(e.width||e.height||e.top||e.left),t.uncache=1})},n.getElementState=function getElementState(t){return this.elementStates[this.targets.indexOf(U(t))]},n.makeAbsolute=function makeAbsolute(){return da(this.elementStates.slice(0),!0,!0).map(ia)},FlipState);function FlipState(t,e,i){if(this.props=e&&e.props,this.simple=!(!e||!e.simple),i)this.targets=qa(t),this.elementStates=t,Ca(this);else{this.targets=T(t);var n=e&&(!1===e.kill||e.batch&&!e.kill);tt&&!n&&tt._kill.push(this),this.update(n||!!tt)}}var Y,ht=((Y=ElementState.prototype).isDifferent=function isDifferent(t){var e=this.bounds,i=t.bounds;return e.top!==i.top||e.left!==i.left||e.width!==i.width||e.height!==i.height||!this.matrix.equals(t.matrix)||this.opacity!==t.opacity||this.props&&t.props&&JSON.stringify(this.props)!==JSON.stringify(t.props)},Y.update=function update(e,i){var n=this,r=n.element,a=Q.getProperty(r),s=Q.core.getCache(r),o=r.getBoundingClientRect(),l=r.getBBox&&"function"==typeof r.getBBox&&"svg"!==r.nodeName.toLowerCase()&&r.getBBox(),h=i?new _(1,0,0,1,o.left+u(),o.top+t()):getGlobalMatrix(r,!1,!1,!0);n.getProp=a,n.element=r,n.id=function _getID(t){var e=t.getAttribute("data-flip-id");return e||t.setAttribute("data-flip-id",e="auto-"+E++),e}(r),n.matrix=h,n.cache=s,n.bounds=o,n.isVisible=!!(o.width||o.height||o.left||o.top),n.display=a("display"),n.position=a("position"),n.parent=r.parentNode,n.x=a("x"),n.y=a("y"),n.scaleX=s.scaleX,n.scaleY=s.scaleY,n.rotation=a("rotation"),n.skewX=a("skewX"),n.opacity=a("opacity"),n.width=l?l.width:N(a("width","px"),.04),n.height=l?l.height:N(a("height","px"),.04),e&&function _recordProps(t,e){for(var i=Q.getProperty(t.element,null,"native"),n=t.props={},r=e.length;r--;)n[e[r]]=(i(e[r])+"").trim();n.zIndex&&(n.zIndex=parseFloat(n.zIndex)||0)}(n,st[e]||aa(e)),n.ctm=r.getCTM&&"svg"===r.nodeName.toLowerCase()&&z(r).inverse(),n.simple=i||1===V(h.a)&&!V(h.b)&&!V(h.c)&&1===V(h.d),n.uncache=0},ElementState);function ElementState(t,e,i){this.element=t,this.update(e,i)}var j,G=((j=FlipAction.prototype).getStateById=function getStateById(t){for(var e=this.states.length;e--;)if(this.states[e].idLookup[t])return this.states[e]},j.kill=function kill(){this.batch.remove(this)},FlipAction);function FlipAction(t,e){this.vars=t,this.batch=e,this.states=[],this.timeline=e.timeline}var q,H=((q=FlipBatch.prototype).add=function add(e){var t=this.actions.filter(function(t){return t.vars===e});return t.length?t[0]:(t=new G("function"==typeof e?{animate:e}:e,this),this.actions.push(t),t)},q.remove=function remove(t){var e=this.actions.indexOf(t);return 0<=e&&this.actions.splice(e,1),this},q.getState=function getState(e){var i=this,t=tt,n=s;return(tt=this).state.clear(),this._kill.length=0,this.actions.forEach(function(t){t.vars.getState&&(t.states.length=0,(s=t).state=t.vars.getState(t)),e&&t.states.forEach(function(t){return i.state.add(t)})}),s=n,tt=t,this.killConflicts(),this},q.animate=function animate(){var t,e,i=this,n=tt,r=this.timeline,a=this.actions.length;for(tt=this,r.clear(),this._abs.length=this._final.length=this._run.length=0,this.actions.forEach(function(t){t.vars.animate&&t.vars.animate(t);var e,i,n=t.vars.onEnter,r=t.vars.onLeave,a=t.targets;a&&a.length&&(n||r)&&(e=new ut,t.states.forEach(function(t){return e.add(t)}),(i=e.compare(J.getState(a))).enter.length&&n&&n(i.enter),i.leave.length&&r&&r(i.leave))}),ka(this._abs),this._run.forEach(function(t){return t()}),e=r.duration(),t=this._final.slice(0),r.add(function(){e<=r.time()&&(t.forEach(function(t){return t()}),K(i,"onComplete"))}),tt=n;a--;)this.actions[a].vars.once&&this.actions[a].kill();return K(this,"onStart"),r.restart(),this},q.loadState=function loadState(n){n=n||function done(){return 0};var r=[];return this.actions.forEach(function(e){if(e.vars.loadState){var i,t=function f(t){t&&(e.targets=t),~(i=r.indexOf(f))&&(r.splice(i,1),r.length||n())};r.push(t),e.vars.loadState(t)}}),r.length||n(),this},q.setState=function setState(){return this.actions.forEach(function(t){return t.targets=t.vars.setState&&t.vars.setState(t)}),this},q.killConflicts=function killConflicts(e){return this.state.interrupt(e),this._kill.forEach(function(t){return t.interrupt(e)}),this},q.run=function run(t,e){var i=this;return this!==tt&&(t||this.getState(e),this.loadState(function(){i._killed||(i.setState(),i.animate())})),this},q.clear=function clear(t){this.state.clear(),t||(this.actions.length=0)},q.getStateById=function getStateById(t){for(var e,i=this.actions.length;i--;)if(e=this.actions[i].getStateById(t))return e;return this.state.idLookup[t]&&this.state},q.kill=function kill(){this._killed=1,this.clear(),delete h[this.id]},FlipBatch);function FlipBatch(t){this.id=t,this.actions=[],this._kill=[],this._final=[],this._abs=[],this._run=[],this.data={},this.state=new ut,this.timeline=Q.timeline()}var J=(Flip.getState=function getState(t,e){var i=ta(t,e);return s&&s.states.push(i),e&&e.batch&&Flip.batch(e.batch).state.add(i),i},Flip.from=function from(t,e){return"clearProps"in(e=e||{})||(e.clearProps=!0),za(t,ta(e.targets||t.targets,{props:e.props||t.props,simple:e.simple,kill:!!e.kill}),e,-1)},Flip.to=function to(t,e){return za(t,ta(e.targets||t.targets,{props:e.props||t.props,simple:e.simple,kill:!!e.kill}),e,1)},Flip.fromTo=function fromTo(t,e,i){return za(t,e,i)},Flip.fit=function fit(t,e,i){var n=i?$(i,at):{},r=i||n,a=r.absolute,s=r.scale,o=r.getVars,l=r.props,u=r.runBackwards,h=r.onComplete,p=r.simple,c=i&&i.fitChild&&U(i.fitChild),f=L(e,l,p,t),d=L(t,0,p,f),m=l?et[l]:nt;return l&&oa(n,f.props),u&&(ea(d,m),"immediateRender"in n||(n.immediateRender=!0),n.onComplete=function(){fa(d),h&&h.apply(this,arguments)}),a&&ia(d,f),n=ot(d,f,s||c,l,c,n.duration||o?n:0),o?n:n.duration?Q.to(d.element,n):null},Flip.makeAbsolute=function makeAbsolute(t,e){return(t instanceof ut?t:new ut(t,e)).makeAbsolute()},Flip.batch=function batch(t){return h[t=t||"default"]||(h[t]=new H(t))},Flip.killFlipsOf=function killFlipsOf(t,e){(t instanceof ut?t.targets:T(t)).forEach(function(t){return t&&lt(t._flip,!1!==e?1:2)})},Flip.isFlipping=function isFlipping(t){var e=Flip.getByTarget(t);return!!e&&e.isActive()},Flip.getByTarget=function getByTarget(t){return(U(t)||M)._flip},Flip.getElementState=function getElementState(t,e){return new ht(U(t),e)},Flip.convertCoordinates=function convertCoordinates(t,e,i){var n=getGlobalMatrix(e,!0,!0).multiply(getGlobalMatrix(t));return i?n.apply(i):n},Flip.register=function register(t){if(o="undefined"!=typeof document&&document.body){Q=t,p(o),T=Q.utils.toArray;var i=Q.utils.snap(.1);N=function _closestTenth(t,e){return i(parseFloat(t)+e)}}},Flip);function Flip(){}J.version="3.11.3","undefined"!=typeof window&&window.gsap&&window.gsap.registerPlugin(J),e.Flip=J,e.default=J;if (typeof(window)==="undefined"||window!==e){Object.defineProperty(e,"__esModule",{value:!0})} else {delete e.default}});

