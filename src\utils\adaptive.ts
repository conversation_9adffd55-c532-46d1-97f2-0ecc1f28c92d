import { DeviceEnum } from "@/enums/DeviceEnum";
import { useAppStore } from "@/store";

/**
 * 自适应工具类
 */
export class AdaptiveUtils {
  /**
   * 获取当前设备类型
   */
  static getCurrentDevice(): DeviceEnum {
    const appStore = useAppStore();
    return appStore.device;
  }

  /**
   * 判断是否为移动设备
   */
  static isMobile(): boolean {
    return this.getCurrentDevice() === DeviceEnum.MOBILE;
  }

  /**
   * 判断是否为桌面设备
   */
  static isDesktop(): boolean {
    return this.getCurrentDevice() === DeviceEnum.DESKTOP;
  }

  /**
   * 根据设备类型返回不同的值
   */
  static adaptiveValue<T>(mobileValue: T, desktopValue: T): T {
    return this.isMobile() ? mobileValue : desktopValue;
  }

  /**
   * 根据设备类型执行不同的函数
   */
  static adaptiveExecute(mobileCallback: () => void, desktopCallback: () => void): void {
    if (this.isMobile()) {
      mobileCallback();
    } else {
      desktopCallback();
    }
  }

  /**
   * 获取自适应的组件属性
   */
  static getAdaptiveProps(
    baseProps: Record<string, any>,
    mobileProps?: Record<string, any>,
    desktopProps?: Record<string, any>
  ): Record<string, any> {
    const platformProps = this.isMobile() ? mobileProps : desktopProps;
    return { ...baseProps, ...platformProps };
  }

  /**
   * 获取自适应的样式类
   */
  static getAdaptiveClasses(
    baseClasses: string | string[],
    mobileClasses?: string | string[],
    desktopClasses?: string | string[]
  ): string[] {
    const base = Array.isArray(baseClasses) ? baseClasses : [baseClasses];
    const platform = this.isMobile() ? mobileClasses : desktopClasses;
    const platformArray = platform ? (Array.isArray(platform) ? platform : [platform]) : [];

    return [...base, ...platformArray];
  }

  /**
   * 获取自适应的布局配置
   */
  static getLayoutConfig() {
    return this.adaptiveValue(
      // 移动端配置
      {
        sidebarWidth: 0,
        headerHeight: 44,
        tabbarHeight: 50,
        contentPadding: 12,
        showSidebar: false,
        showTabbar: true,
        showNavbar: true,
        gridColumns: 1,
        cardSpacing: 8,
      },
      // 桌面端配置
      {
        sidebarWidth: 200,
        headerHeight: 60,
        tabbarHeight: 0,
        contentPadding: 24,
        showSidebar: true,
        showTabbar: false,
        showNavbar: false,
        gridColumns: 4,
        cardSpacing: 16,
      }
    );
  }

  /**
   * 获取自适应的主题配置
   */
  static getThemeConfig() {
    return this.adaptiveValue(
      // 移动端主题
      {
        primaryColor: "#1989fa",
        fontSize: {
          small: "12px",
          normal: "14px",
          large: "16px",
          title: "18px",
        },
        spacing: {
          xs: "4px",
          sm: "8px",
          md: "12px",
          lg: "16px",
          xl: "20px",
        },
        borderRadius: "6px",
        buttonHeight: "44px",
      },
      // 桌面端主题
      {
        primaryColor: "#409eff",
        fontSize: {
          small: "12px",
          normal: "14px",
          large: "16px",
          title: "20px",
        },
        spacing: {
          xs: "4px",
          sm: "8px",
          md: "16px",
          lg: "24px",
          xl: "32px",
        },
        borderRadius: "4px",
        buttonHeight: "32px",
      }
    );
  }

  /**
   * 显示自适应消息
   */
  static showMessage(message: string, type: "success" | "error" | "warning" | "info" = "info") {
    this.adaptiveExecute(
      // 移动端使用 console.log 或 Toast（如果有的话）
      () => {
        console.log(`[${type.toUpperCase()}] ${message}`);
        // 这里可以集成 Vant 的 Toast
        // Toast({ message, type });
      },
      // 桌面端使用 Element Plus Message
      () => {
        if (typeof ElMessage !== "undefined") {
          ElMessage[type](message);
        } else {
          console.log(`[${type.toUpperCase()}] ${message}`);
        }
      }
    );
  }

  /**
   * 显示自适应确认对话框
   */
  static async showConfirm(message: string, title: string = "确认"): Promise<boolean> {
    return new Promise((resolve) => {
      this.adaptiveExecute(
        // 移动端使用简单的 confirm
        () => {
          const result = confirm(`${title}\n${message}`);
          resolve(result);
        },
        // 桌面端使用 Element Plus MessageBox
        () => {
          if (typeof ElMessageBox !== "undefined") {
            ElMessageBox.confirm(message, title, {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
              .then(() => {
                resolve(true);
              })
              .catch(() => {
                resolve(false);
              });
          } else {
            const result = confirm(`${title}\n${message}`);
            resolve(result);
          }
        }
      );
    });
  }

  /**
   * 获取自适应的表格配置
   */
  static getTableConfig() {
    return this.adaptiveValue(
      // 移动端表格配置
      {
        showPagination: false,
        pageSize: 10,
        showSelection: false,
        showSearch: true,
        displayMode: "list",
      },
      // 桌面端表格配置
      {
        showPagination: true,
        pageSize: 20,
        showSelection: true,
        showSearch: false,
        displayMode: "table",
      }
    );
  }

  /**
   * 获取自适应的表单配置
   */
  static getFormConfig() {
    return this.adaptiveValue(
      // 移动端表单配置
      {
        labelPosition: "top",
        labelWidth: "auto",
        size: "large",
        columnSpan: 24,
      },
      // 桌面端表单配置
      {
        labelPosition: "right",
        labelWidth: "100px",
        size: "default",
        columnSpan: 12,
      }
    );
  }

  /**
   * 格式化自适应的日期显示
   */
  static formatDate(date: Date | string, format?: string): string {
    const dateObj = typeof date === "string" ? new Date(date) : date;

    return this.adaptiveValue(
      // 移动端使用简短格式
      format || dateObj.toLocaleDateString("zh-CN"),
      // 桌面端使用完整格式
      format || dateObj.toLocaleString("zh-CN")
    );
  }

  /**
   * 获取自适应的图标大小
   */
  static getIconSize(): number {
    return this.adaptiveValue(20, 16); // 移动端20px，桌面端16px
  }

  /**
   * 获取自适应的头像大小
   */
  static getAvatarSize(): number {
    return this.adaptiveValue(40, 32); // 移动端40px，桌面端32px
  }

  /**
   * 获取自适应的按钮尺寸
   */
  static getButtonSize(): "large" | "default" | "small" {
    return this.adaptiveValue("large", "default");
  }

  /**
   * 获取自适应的输入框尺寸
   */
  static getInputSize(): "large" | "default" | "small" {
    return this.adaptiveValue("large", "default");
  }

  /**
   * 检查是否支持触摸
   */
  static isTouchDevice(): boolean {
    return "ontouchstart" in window || navigator.maxTouchPoints > 0;
  }

  /**
   * 获取安全区域样式
   */
  static getSafeAreaStyle(): Record<string, string> {
    if (!this.isMobile()) {
      return {};
    }

    return {
      paddingTop: "env(safe-area-inset-top)",
      paddingBottom: "env(safe-area-inset-bottom)",
      paddingLeft: "env(safe-area-inset-left)",
      paddingRight: "env(safe-area-inset-right)",
    };
  }

  /**
   * 防抖函数（移动端使用更长的延迟）
   */
  static debounce<T extends (...args: any[]) => any>(
    func: T,
    wait?: number
  ): (...args: Parameters<T>) => void {
    const delay = wait || this.adaptiveValue(300, 200); // 移动端300ms，桌面端200ms
    let timeout: NodeJS.Timeout;

    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(this, args), delay);
    };
  }

  /**
   * 节流函数（移动端使用更长的间隔）
   */
  static throttle<T extends (...args: any[]) => any>(
    func: T,
    wait?: number
  ): (...args: Parameters<T>) => void {
    const delay = wait || this.adaptiveValue(200, 100); // 移动端200ms，桌面端100ms
    let timeout: NodeJS.Timeout | null = null;
    let previous = 0;

    return (...args: Parameters<T>) => {
      const now = Date.now();
      const remaining = delay - (now - previous);

      if (remaining <= 0 || remaining > delay) {
        if (timeout) {
          clearTimeout(timeout);
          timeout = null;
        }
        previous = now;
        func.apply(this, args);
      } else if (!timeout) {
        timeout = setTimeout(() => {
          previous = Date.now();
          timeout = null;
          func.apply(this, args);
        }, remaining);
      }
    };
  }

  /**
   * 获取自适应的错误页面路径
   */
  static getErrorPagePath(errorCode: number | string): string {
    const code = errorCode.toString();
    return this.adaptiveValue(
      `/mobile/${code}`, // 移动端错误页面路径
      `/${code}` // PC端错误页面路径
    );
  }

  /**
   * 重定向到自适应错误页面
   */
  static redirectToErrorPage(errorCode: number | string, router: any) {
    const errorPath = this.getErrorPagePath(errorCode);
    router.push(errorPath);
  }

  /**
   * 检查是否为错误页面路径
   */
  static isErrorPagePath(path: string): boolean {
    const errorCodes = ["401", "403", "404", "500"];
    return errorCodes.some((code) => path.includes(`/${code}`));
  }

  /**
   * 从错误页面路径中提取错误代码
   */
  static extractErrorCode(path: string): string | null {
    const errorCodes = ["401", "403", "404", "500"];
    for (const code of errorCodes) {
      if (path.includes(`/${code}`)) {
        return code;
      }
    }
    return null;
  }
}

/**
 * 自适应组合式函数
 */
export function useAdaptive() {
  return {
    isMobile: AdaptiveUtils.isMobile(),
    isDesktop: AdaptiveUtils.isDesktop(),
    adaptiveValue: AdaptiveUtils.adaptiveValue,
    adaptiveExecute: AdaptiveUtils.adaptiveExecute,
    getAdaptiveProps: AdaptiveUtils.getAdaptiveProps,
    getAdaptiveClasses: AdaptiveUtils.getAdaptiveClasses,
    getLayoutConfig: AdaptiveUtils.getLayoutConfig,
    getThemeConfig: AdaptiveUtils.getThemeConfig,
    showMessage: AdaptiveUtils.showMessage,
    showConfirm: AdaptiveUtils.showConfirm,
    getTableConfig: AdaptiveUtils.getTableConfig,
    getFormConfig: AdaptiveUtils.getFormConfig,
    formatDate: AdaptiveUtils.formatDate,
    getIconSize: AdaptiveUtils.getIconSize,
    getAvatarSize: AdaptiveUtils.getAvatarSize,
    getButtonSize: AdaptiveUtils.getButtonSize,
    getInputSize: AdaptiveUtils.getInputSize,
    isTouchDevice: AdaptiveUtils.isTouchDevice,
    getSafeAreaStyle: AdaptiveUtils.getSafeAreaStyle,
    debounce: AdaptiveUtils.debounce,
    throttle: AdaptiveUtils.throttle,
    // 错误页面处理方法
    getErrorPagePath: AdaptiveUtils.getErrorPagePath,
    redirectToErrorPage: AdaptiveUtils.redirectToErrorPage,
    isErrorPagePath: AdaptiveUtils.isErrorPagePath,
    extractErrorCode: AdaptiveUtils.extractErrorCode,
  };
}
