<template>
  <div class="mobile-announcements">
    <!-- 顶部导航栏 -->
    <van-nav-bar title="通知公告" left-arrow fixed placeholder @click-left="goBack" />

    <!-- 搜索框 -->
    <div class="search-bar">
      <van-search
        v-model="searchValue"
        placeholder="请输入关键字"
        shape="round"
        show-action
        @search="onSearch"
      >
        <template #action>
          <div @click="onSearch">搜索</div>
        </template>
      </van-search>
    </div>

    <!-- 标签页 -->
    <van-tabs v-model:active="activeTab" sticky>
      <van-tab title="全部">
        <announcement-list
          :list="allAnnouncements"
          :loading="loading"
          :finished="finished"
          @load="onLoad"
        />
      </van-tab>
      <van-tab title="业务指南">
        <announcement-list
          :list="guideAnnouncements"
          :loading="loading"
          :finished="finished"
          @load="onLoad"
        />
      </van-tab>
      <van-tab title="系统公告">
        <announcement-list
          :list="systemAnnouncements"
          :loading="loading"
          :finished="finished"
          @load="onLoad"
        />
      </van-tab>
    </van-tabs>

    <!-- 公告详情弹窗 -->
    <van-popup
      v-model:show="showDetail"
      round
      position="bottom"
      :style="{ height: '80%' }"
      closeable
    >
      <div class="announcement-detail" v-if="currentAnnouncement">
        <div class="detail-header">
          <div class="detail-title">{{ currentAnnouncement.title }}</div>
          <div class="detail-meta">
            <span class="meta-item">{{ currentAnnouncement.publisher }}</span>
            <span class="meta-item">{{ currentAnnouncement.time }}</span>
          </div>
        </div>
        <div class="detail-content">
          <div v-html="currentAnnouncement.content"></div>

          <div
            class="detail-attachments"
            v-if="currentAnnouncement.attachments && currentAnnouncement.attachments.length > 0"
          >
            <div class="attachment-title">附件列表</div>
            <div
              v-for="(attachment, index) in currentAnnouncement.attachments"
              :key="index"
              class="attachment-item"
              @click="downloadAttachment(attachment)"
            >
              <van-icon name="description" />
              <span class="attachment-name">{{ attachment.name }}</span>
            </div>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, defineComponent } from "vue";
import { useRouter } from "vue-router";

// 公告列表组件
const AnnouncementList = defineComponent({
  props: {
    list: Array,
    loading: Boolean,
    finished: Boolean,
  },
  emits: ["load"],
  setup(props, { emit }) {
    const onLoad = () => {
      emit("load");
    };

    const getTypeTag = (type) => {
      const tagMap = {
        guide: "业务指南",
        system: "系统公告",
        notice: "通知",
      };
      return tagMap[type] || "公告";
    };

    const getTypeClass = (type) => {
      const classMap = {
        guide: "type-guide",
        system: "type-system",
        notice: "type-notice",
      };
      return classMap[type] || "";
    };

    const viewDetail = (item) => {
      currentAnnouncement.value = item;
      showDetail.value = true;
    };

    return {
      onLoad,
      getTypeTag,
      getTypeClass,
      viewDetail,
    };
  },
  template: `
    <van-list
      v-model:loading="loading"
      :finished="finished"
      finished-text="没有更多了"
      @load="onLoad"
    >
      <div
        v-for="(item, index) in list"
        :key="index"
        class="announcement-item"
        @click="viewDetail(item)"
      >
        <div class="announcement-header">
          <div :class="['type-tag', getTypeClass(item.type)]">
            {{ getTypeTag(item.type) }}
          </div>
          <div class="announcement-title">
            {{ item.title }}
          </div>
        </div>
        <div class="announcement-info">
          <div class="publisher">{{ item.publisher }}</div>
          <div class="time">{{ item.time }}</div>
        </div>
        <div v-if="item.hasAttachment" class="attachment-indicator">
          <van-icon name="description" />
          <span>附件</span>
        </div>
      </div>
    </van-list>
  `,
});

const router = useRouter();
const searchValue = ref("");
const activeTab = ref(0);
const loading = ref(false);
const finished = ref(false);
const showDetail = ref(false);
const currentAnnouncement = ref(null);

// 所有公告
const allAnnouncements = ref([
  {
    id: 1,
    type: "guide",
    title: "国有资产报损认定表",
    publisher: "资产管理处",
    time: "2025-01-16",
    content: `<p>各部门：</p>
    <p>为规范国有资产报损流程，现发布《国有资产报损认定表》，请各部门在申请资产报损时使用此表格。</p>
    <p>报损流程如下：</p>
    <ol>
      <li>填写《国有资产报损认定表》</li>
      <li>部门负责人签字确认</li>
      <li>资产管理处审核</li>
      <li>财务处审批</li>
      <li>资产处置执行</li>
    </ol>
    <p>请各部门严格按照流程执行，确保国有资产管理规范有序。</p>`,
    hasAttachment: true,
    attachments: [
      { name: "国有资产报损认定表.docx", url: "#" },
      { name: "资产报损流程说明.pdf", url: "#" },
    ],
  },
  {
    id: 2,
    type: "guide",
    title: "货物验收报告2024版",
    publisher: "资产管理处",
    time: "2024-09-08",
    content: `<p>各部门：</p>
    <p>为进一步规范货物验收流程，提高资产管理效率，现发布2024版《货物验收报告》，自发布之日起启用。</p>
    <p>主要变更内容：</p>
    <ol>
      <li>增加验收人员身份信息</li>
      <li>增加验收照片上传要求</li>
      <li>优化验收流程，简化操作步骤</li>
      <li>增加验收异常处理流程</li>
    </ol>
    <p>请各部门下载最新版本表格，并按照要求执行验收工作。</p>`,
    hasAttachment: true,
    attachments: [
      { name: "货物验收报告2024版.docx", url: "#" },
      { name: "验收流程说明.pdf", url: "#" },
    ],
  },
  {
    id: 3,
    type: "guide",
    title: "资产领用流程",
    publisher: "资产管理处",
    time: "2019-07-31",
    content: `<p>各部门：</p>
    <p>为规范资产领用管理，提高资产使用效率，现发布《资产领用流程》，请各部门严格遵照执行。</p>
    <p>资产领用流程如下：</p>
    <ol>
      <li>填写《资产领用申请表》</li>
      <li>部门负责人审批</li>
      <li>资产管理处审核</li>
      <li>领用人签字确认</li>
      <li>资产管理系统登记</li>
    </ol>
    <p>特别提醒：领用人对所领用资产负有保管责任，如有损坏或丢失，需按照相关规定进行赔偿。</p>`,
    hasAttachment: true,
    attachments: [{ name: "资产领用申请表.docx", url: "#" }],
  },
  {
    id: 4,
    type: "system",
    title: "资产管理系统升级通知",
    publisher: "信息中心",
    time: "2025-05-20",
    content: `<p>各位用户：</p>
    <p>为提升系统性能和用户体验，资产管理系统将于2025年5月25日进行升级维护，具体时间为：</p>
    <p>升级时间：2025年5月25日（星期六）20:00-24:00</p>
    <p>升级期间系统将暂停服务，请各位用户提前做好相关工作安排。</p>
    <p>本次升级主要内容：</p>
    <ol>
      <li>优化系统响应速度</li>
      <li>增加移动端功能</li>
      <li>完善资产盘点模块</li>
      <li>修复已知问题</li>
    </ol>
    <p>系统升级后，将提供更加便捷、高效的资产管理服务，感谢您的理解和支持！</p>`,
    hasAttachment: false,
    attachments: [],
  },
  {
    id: 5,
    type: "notice",
    title: "2025年度资产盘点工作通知",
    publisher: "资产管理处",
    time: "2025-04-15",
    content: `<p>各部门：</p>
    <p>根据学校资产管理规定，定于2025年6月开展年度资产盘点工作，现将有关事项通知如下：</p>
    <p>一、盘点时间：2025年6月1日至6月30日</p>
    <p>二、盘点范围：学校所有固定资产</p>
    <p>三、盘点方式：</p>
    <ol>
      <li>各部门自盘：6月1日-6月15日</li>
      <li>资产处抽盘：6月16日-6月25日</li>
      <li>盘点结果汇总：6月26日-6月30日</li>
    </ol>
    <p>请各部门高度重视，安排专人负责，确保盘点工作顺利完成。</p>`,
    hasAttachment: true,
    attachments: [
      { name: "资产盘点表.xlsx", url: "#" },
      { name: "盘点工作指南.pdf", url: "#" },
    ],
  },
]);

// 业务指南公告
const guideAnnouncements = ref(allAnnouncements.value.filter((item) => item.type === "guide"));

// 系统公告
const systemAnnouncements = ref(
  allAnnouncements.value.filter((item) => item.type === "system" || item.type === "notice")
);

// 返回上一页
const goBack = () => {
  router.go(-1);
};

// 搜索
const onSearch = () => {
  console.log("搜索:", searchValue.value);
  // 实现搜索逻辑
};

// 加载更多
const onLoad = () => {
  setTimeout(() => {
    // 模拟加载更多数据
    loading.value = false;
    finished.value = true;
  }, 1000);
};

// 下载附件
const downloadAttachment = (attachment) => {
  console.log("下载附件:", attachment);
  // 实现下载逻辑
};
</script>

<style lang="scss" scoped>
.mobile-announcements {
  min-height: 100vh;
  background-color: #f7f8fa;

  .search-bar {
    padding: 8px 0;
  }

  .announcement-item {
    margin: 12px;
    padding: 16px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    position: relative;

    .announcement-header {
      margin-bottom: 12px;

      .type-tag {
        display: inline-block;
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
        margin-bottom: 8px;

        &.type-guide {
          background-color: #f0f9ff;
          color: #1989fa;
        }

        &.type-system {
          background-color: #f0fff0;
          color: #07c160;
        }

        &.type-notice {
          background-color: #fff7e6;
          color: #ff976a;
        }
      }

      .announcement-title {
        font-size: 16px;
        font-weight: 500;
        line-height: 1.4;
      }
    }

    .announcement-info {
      display: flex;
      justify-content: space-between;
      font-size: 13px;
      color: #999;

      .publisher {
        color: #666;
      }
    }

    .attachment-indicator {
      position: absolute;
      top: 16px;
      right: 16px;
      display: flex;
      align-items: center;
      font-size: 12px;
      color: #1989fa;

      .van-icon {
        margin-right: 4px;
      }
    }
  }

  .announcement-detail {
    height: 100%;
    display: flex;
    flex-direction: column;

    .detail-header {
      padding: 20px 16px;
      border-bottom: 1px solid #f5f5f5;

      .detail-title {
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 12px;
        line-height: 1.4;
      }

      .detail-meta {
        display: flex;
        font-size: 13px;
        color: #999;

        .meta-item {
          margin-right: 16px;
        }
      }
    }

    .detail-content {
      flex: 1;
      padding: 16px;
      overflow-y: auto;
      line-height: 1.6;

      p {
        margin-bottom: 12px;
      }

      ol,
      ul {
        padding-left: 20px;
        margin-bottom: 12px;
      }

      .detail-attachments {
        margin-top: 24px;
        padding-top: 16px;
        border-top: 1px dashed #eee;

        .attachment-title {
          font-size: 15px;
          font-weight: 500;
          margin-bottom: 12px;
        }

        .attachment-item {
          display: flex;
          align-items: center;
          padding: 8px 0;
          color: #1989fa;

          .van-icon {
            margin-right: 8px;
          }

          .attachment-name {
            flex: 1;
          }
        }
      }
    }
  }
}

:deep(.van-nav-bar) {
  background-color: #1989fa;

  .van-nav-bar__title {
    color: #fff;
  }

  .van-icon {
    color: #fff;
  }
}

:deep(.van-search__action) {
  color: #1989fa;
}

:deep(.van-tabs__line) {
  background-color: #1989fa;
}

:deep(.van-popup) {
  overflow-y: hidden;
}
</style>
