<!DOCTYPE html>
<html>
<head>
  <title>layered animations • anime.js</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <meta property="og:title" content="anime.js">
  <meta property="og:url" content="https://animejs.com">
  <meta property="og:description" content="Javascript Animation Engine">
  <meta property="og:image" content="https://animejs.com/documentation/assets/img/icons/og.png">
  <meta name="twitter:card" content="summary">
  <meta name="twitter:title" content="anime.js">
  <meta name="twitter:site" content="@juliangarnier">
  <meta name="twitter:description" content="Javascript Animation Engine">
  <meta name="twitter:image" content="https://animejs.com/documentation/assets/img/icons/twitter.png">
  <link rel="apple-touch-icon-precomposed" href="../assets/img/social-media-image.png">
  <link rel="icon" type="image/png" href="../assets/img/favicon.png" >
  <link href="../assets/css/animejs.css" rel="stylesheet">
  <style>

    :root {
      font-size: 24px;
    }

    body {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      align-items: center;
      height: 100vh;
    }

    .layered-animations {
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 16rem;
      height: 16rem;
      /*border: 1px solid red;*/
    }

    .layered-animations .shape {
      position: absolute;
      overflow: visible;
      width: 8rem;
      height: 8rem;
      stroke: currentColor;
      fill: transparent;
    }

    .layered-animations .small.shape {
      width: 1.5rem;
      height: 1.5rem;
      stroke: currentColor;
      stroke-width: 2px;
      fill: currentColor;
    }

  </style>
</head>
<body>

  <div class="layered-animations">
    <svg class="small shape color-red" viewBox="0 0 96 96">
      <rect width="48" height="48" x="24" y="24" fill-rule="evenodd" stroke-linecap="square"/>
    </svg>
    <svg class="small shape color-red" viewBox="0 0 96 96">
      <polygon fill-rule="evenodd" points="48 17.28 86.4 80.11584 9.6 80.11584" stroke-linecap="square"/>
    </svg>
    <svg class="small shape color-red" viewBox="0 0 96 96">
      <circle cx="48" cy="48" r="32" fill-rule="evenodd" stroke-linecap="square"/>
    </svg>
    <svg class="shape" viewBox="0 0 96 96">
      <circle cx="48" cy="48" r="28" fill-rule="evenodd" stroke-linecap="square"/>
    </svg>
    <svg class="shape" viewBox="0 0 96 96">
      <rect width="48" height="48" x="24" y="24" fill-rule="evenodd" stroke-linecap="square"/>
    </svg>
    <svg class="shape" viewBox="0 0 96 96">
      <polygon fill-rule="evenodd" points="48 17.28 86.4 80.11584 9.6 80.11584" stroke-linecap="square"/>
    </svg>
  </div>

</body>
<script type="module">

  import anime from '../../src/index.js';

  var shapeEls = document.querySelectorAll('.shape');
  var triangleEl = document.querySelector('.layered-animations polygon');
  var trianglePoints = triangleEl.getAttribute('points').split(' ');
  var easings = ['easeInOutQuad', 'easeInOutCirc', 'easeInOutSine', 'spring'];

  function animateShape(el) {

    var circleEl = el.querySelector('circle');
    var rectEl = el.querySelector('rect');
    var polyEl = el.querySelector('polygon');

    function createKeyframes(total, min, max, unit) {
      var keyframes = [];
      var unit = unit || 0;
      for (var i = 0; i < total; i++) keyframes.push({ value: function() { return anime.random(min, max) + unit; } });
      return keyframes;
    }

    function createKeyframes(value) {
      var keyframes = [];
      for (var i = 0; i < 30; i++) keyframes.push({ value: value });
      return keyframes;
    }

    var animation = anime.timeline({
      targets: el,
      duration: function() { return anime.random(800, 2000); },
      easing: function() { return easings[anime.random(0, easings.length - 1)]; },
      complete: function(anim) { animateShape(anim.animatables[0].target); },
    })
    .add({
      translateX: createKeyframes(function() { return anime.random(-4, 4) + 'rem'; }),
      translateY: createKeyframes(function() { return anime.random(-4, 4) + 'rem'; }),
      rotate: createKeyframes(function() { return anime.random(-180, 180); }),
    }, 0);
    if (circleEl) {
      animation.add({
        targets: circleEl,
        r: createKeyframes(function() { return anime.random(24, 56); }),
      }, 0);
    }
    if (rectEl) {
      animation.add({
        targets: rectEl,
        width: createKeyframes(function() { return anime.random(56, 96); }),
        height: createKeyframes(function() { return anime.random(56, 96); }),
      }, 0);
    }
    if (polyEl) {
      animation.add({
        targets: polyEl,
        points: createKeyframes(function() { 
          var scale = anime.random(64, 148) / 100;
          return trianglePoints.map(function(p) { return p * scale; }).join(' ');
        }),
      }, 0);
    }

  }

  for (var i = 0; i < shapeEls.length; i++) {
    animateShape(shapeEls[i]);
  }

</script>
</html>
