import { createApp } from "vue";
import App from "./App.vue";
import setupPlugins from "@/plugins";

// 新增：导入设备监听设置
import { setupRouteDeviceListener } from "@/store/modules/permission";

// 本地SVG图标
import "virtual:svg-icons-register";
// 暗黑主题样式
import "element-plus/theme-chalk/dark/css-vars.css";
// 暗黑模式自定义变量
import "@/styles/dark/css-vars.css";
import "@/styles/index.scss";
import "uno.css";
import "animate.css";

import ScrollbarLoop from "@/components/GY/ScrollbarLoop/index.vue";
import Title from "@/components/GY/Title/index.vue";
import DDLXcode from "@/components/GY/DDLXCode/index.vue";
import DDLYcode from "@/components/GY/DDLYCode/index.vue";
import DDLDataList from "@/components/GY/DDLDataList/index.vue";
import DDLUserList from "@/components/GY/DDLUserList/index.vue";
import DDLDeptList from "@/components/GY/DDLDeptList/index.vue";
import DDLGkDeptList from "@/components/GY/DDLGkDeptList/index.vue";
import ImageList from "@/components/GY/ImageList/index.vue";
import FileView from "@/components/GY/FileView/index.vue";
import FileUpload from "@/components/GY/FileUpload/index.vue";
import DDLCheckStatus from "@/components/GY/DDLCheckStatus/index.vue";
import CardPanel from "@/components/Properties/CardPanel/index.vue";
import CardInfo from "@/components/Properties/CardInfo/index.vue";
const app = createApp(App);
// 注册插件
app.use(setupPlugins);

// 新增：设置设备变化监听
setupRouteDeviceListener();
//注册全局组件
app.component("ScrollbarLoop", ScrollbarLoop);
app.component("Title", Title);
app.component("DDLXcode", DDLXcode);
app.component("DDLDataList", DDLDataList);
app.component("DDLUserList", DDLUserList);
app.component("DDLDeptList", DDLDeptList);
app.component("DDLGkDeptList", DDLGkDeptList);
app.component("ImageList", ImageList);
app.component("FileView", FileView);
app.component("FileUpload", FileUpload);
app.component("DDLYcode", DDLYcode);
app.component("DDLCheckStatus", DDLCheckStatus);
app.component("CardPanel", CardPanel);
app.component("CardInfo", CardInfo);
app.mount("#app");
declare global {
  interface Window {
    CozeSDK: any;
  }
}

app.config.globalProperties.$coze = window.CozeSDK;
