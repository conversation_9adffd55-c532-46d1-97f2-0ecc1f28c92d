import type { NavigationGuardNext, RouteLocationNormalized, RouteRecordRaw } from "vue-router";
import NProgress from "@/utils/nprogress";
import { getToken } from "@/utils/auth";
import router from "@/router";
import { usePermissionStore, useUserStore, useAppStore } from "@/store";
import { DeviceEnum } from "@/enums/DeviceEnum";
import { AdaptiveUtils } from "@/utils/adaptive";

export function setupPermission() {
  // 白名单路由
  const whiteList = ["/login", "/warehouse", "/assetManagement", "/bigScreen"];

  router.beforeEach(async (to, from, next) => {
    NProgress.start();

    const isLogin = !!getToken(); // 判断是否登录
    const appStore = useAppStore();

    if (isLogin) {
      if (to.path === "/login") {
        // 已登录，访问登录页，跳转到首页
        next({ path: "/" });
      } else {
        const permissionStore = usePermissionStore();
        // 判断路由是否加载完成
        if (permissionStore.isRoutesLoaded) {
          if (to.matched.length === 0) {
            // 路由未匹配，根据设备类型跳转到对应的404页面
            const adaptiveNotFoundPath =
              appStore.device === DeviceEnum.MOBILE ? "/mobile/404" : "/404";
            next(adaptiveNotFoundPath);
          } else {
            // 新增：检查路由平台兼容性
            if (!isRoutePlatformCompatible(to, appStore.device)) {
              const adaptedRoute = findAdaptedRoute(to, appStore.device);
              if (adaptedRoute) {
                console.log(`Route incompatible, redirecting to: ${adaptedRoute}`);
                next(adaptedRoute);
                return;
              }
            }

            // 动态设置页面标题
            const title = (to.params.title as string) || (to.query.title as string);
            if (title) {
              to.meta.title = title;
            }
            next();
          }
        } else {
          try {
            // 生成动态路由
            const dynamicRoutes = await permissionStore.generateRoutes();
            dynamicRoutes.forEach((route: RouteRecordRaw) => router.addRoute(route));
            next({ ...to, replace: true });
          } catch (error) {
            console.error(error);
            // 路由加载失败，重置 token 并重定向到登录页
            await useUserStore().clearUserData();
            redirectToLogin(to, next);
            NProgress.done();
          }
        }
      }
    } else {
      // 未登录，判断是否在白名单中
      if (whiteList.includes(to.path)) {
        next();
      } else {
        // 不在白名单，重定向到登录页
        redirectToLogin(to, next);
        NProgress.done();
      }
    }
  });

  // 后置守卫，保证每次路由跳转结束时关闭进度条
  router.afterEach(() => {
    NProgress.done();
  });
}

// 新增：检查路由平台兼容性
function isRoutePlatformCompatible(route: RouteLocationNormalized, device: DeviceEnum): boolean {
  const platform = route.meta?.platform;

  if (!platform || platform === "both") {
    return true;
  }

  return (
    (device === DeviceEnum.MOBILE && platform === "mobile") ||
    (device === DeviceEnum.DESKTOP && platform === "desktop")
  );
}

// 新增：查找适配的路由
function findAdaptedRoute(route: RouteLocationNormalized, device: DeviceEnum): string | null {
  const currentPlatform = device === DeviceEnum.MOBILE ? "mobile" : "desktop";

  // 使用工具函数处理错误页面
  if (AdaptiveUtils.isErrorPagePath(route.path)) {
    const errorCode = AdaptiveUtils.extractErrorCode(route.path);
    if (errorCode) {
      return AdaptiveUtils.getErrorPagePath(errorCode);
    }
  }

  // 简单的路径转换逻辑
  if (route.path.startsWith("/mobile/") && device === DeviceEnum.DESKTOP) {
    return route.path.replace("/mobile/", "/");
  }

  if (!route.path.startsWith("/mobile/") && device === DeviceEnum.MOBILE) {
    // 检查是否有对应的移动端路由
    const mobilePath = `/mobile${route.path}`;
    // 这里可以检查路由是否存在，简化处理直接返回
    return mobilePath;
  }

  return null;
}

// 重定向到登录页
function redirectToLogin(to: RouteLocationNormalized, next: NavigationGuardNext) {
  const params = new URLSearchParams(to.query as Record<string, string>);
  const queryString = params.toString();
  const redirect = queryString ? `${to.path}?${queryString}` : to.path;
  next(`/login?redirect=${encodeURIComponent(redirect)}`);
}

/** 判断是否有权限 */
export function hasAuth(value: string | string[], type: "button" | "role" = "button") {
  const { roles, perms } = useUserStore().userInfo;

  // 超级管理员 拥有所有权限
  if (type === "button" && roles.includes("ROOT")) {
    return true;
  }

  const auths = type === "button" ? perms : roles;
  return typeof value === "string"
    ? auths.includes(value)
    : value.some((perm) => auths.includes(perm));
}
