<!DOCTYPE html>
<html>
<head>
  <title>Anime timeline | anime.js</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <meta property="og:title" content="anime.js">
  <meta property="og:url" content="https://animejs.com">
  <meta property="og:description" content="Javascript Animation Engine">
  <meta property="og:image" content="https://animejs.com/documentation/assets/img/icons/og.png">
  <meta name="twitter:card" content="summary">
  <meta name="twitter:title" content="anime.js">
  <meta name="twitter:site" content="@juliangarnier">
  <meta name="twitter:description" content="Javascript Animation Engine">
  <meta name="twitter:image" content="https://animejs.com/documentation/assets/img/icons/twitter.png">
  <link rel="apple-touch-icon-precomposed" href="../assets/img/social-media-image.png">
  <link rel="icon" type="image/png" href="../assets/img/favicon.png" >
  <link href="../assets/css/animejs.css" rel="stylesheet">
  <link href="../assets/css/documentation.css" rel="stylesheet">
  <!-- <script src="../../lib/anime.min.js"></script> -->
  <!-- <script src="../assets/js/anime/anime.2.2.0.js"></script> -->
  <style>

    body {
      position: absolute;
      width: 100%;
      height: 100%;
    }

    .debug,
    .demo {
      position: relative;
      width: 100%;
      height: 50vh;
    }

    .demo {
      display: flex;
      flex-wrap: no-wrap;
      justify-content: space-around;
      align-items: center;
    }

    .el {
      width: 10px;
      height: 10px;
      background: #FFF;
      margin: 10px;
    }

    .player {
      position: relative;
      top: 50px;
      height: calc(50vh - 50px);
    }

    .controls {
      opacity: 1;
      display: flex;
      position: absolute;
      right: 0;
      top: 0;
      left: 0;
      height: 46px;
      padding: 0 10px 0 10px;
      background: #000;
      color: #FFF;
    }

    .buttons {
      display: flex;
    }

    .buttons button {
      width: 100px;
    }

    .timeline {
      display: flex;
      flex-grow: 1;
    }

    .progress {
      flex-grow: 1;
    }

  </style>
</head>
<body>

  <div class="demo">

    <div class="el"></div>
    <div class="el"></div>
    <div class="el"></div>
    <div class="el"></div>
    <div class="el"></div>
    <div class="el"></div>
    <div class="el"></div>

  </div>

  <div class="debug">
    <div class="controls">
      <div class="buttons">
        <button class="play">Play</button>
        <button class="pause">Pause</button>
        <button class="reverse">Reverse</button>
        <button class="restart">Restart</button>
        <label>Is reversed : <input class="is-reversed"></input></label>
      </div>
      <div class="timeline">
        <input class="progress" type="range" min="0" max="100" step=".1" value="0">
      </div>
    </div>
    <div class="player"></div>
  </div>

</body>
<script type="module">

  import anime from '../../src/index.js';
  import animePlayer from '../assets/js/anime.player.js'

  var els = document.querySelectorAll('.el');
  var controlsProgressEl = document.querySelector('.controls .progress');
  var isReversedEl = document.querySelector('.is-reversed');

  window.tl = anime.timeline({
    targets: els,
    duration: 800,
    delay: function(el, i) { return 75 * i; },
    update: function(anim) {
      controlsProgressEl.value = anim.progress;
      isReversedEl.value = anim.reversePlayback;
    },
    loopComplete: (a) => console.log('TL loop completed'),
    autoplay: true,
    easing: 'easeOutQuad',
    direction: 'alternate'
  });

  document.querySelector('.controls .play').onclick = tl.play;
  document.querySelector('.controls .pause').onclick = tl.pause;
  document.querySelector('.controls .reverse').onclick = tl.reverse;
  document.querySelector('.controls .restart').onclick = tl.restart;

  tl
  .add({
    translateY: -100,
    scale: 8,
    background: '#FF1461',
    begin: function() {
      console.log('first began')
    },
    complete: function() {
      console.log('first completed')
    }
  })
  .add({
    background: '#18FF92',
    rotate: 45,
  }, '-=400')
  .add({
    background: '#5A87FF'
  }, '-=400')
  .add({
    background: '#FBF38C',
    rotate: 90,
  }, '-=400')
  .add({
    scaleX: 3,
    scaleY: .5,
    background: '#FBF38C',
    begin: function() {
      console.log('last began')
    },
    complete: function() {
      console.log('last completed')
    }
  }, '-=400');

  controlsProgressEl.addEventListener('input', function() {
    tl.seek(tl.duration * (controlsProgressEl.value / 100));
  });

  // for (var i = 0; i < els.length; i++) {
  //   els[i].addEventListener('mouseenter', function() {
  //     anime.remove(this);
  //     this.style.backgroundColor = 'red';
  //   }, false);
  // }

  // var playerEl = document.querySelector('.player');

  //animePlayer(tl, playerEl);

  </script>
  <!-- <script src="../assets/js/vendors/stats.min.js"></script> -->
</html>
