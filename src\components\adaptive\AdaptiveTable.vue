<template>
  <div class="adaptive-table">
    <!-- PC端表格 -->
    <el-table
      v-if="!isMobile"
      :data="tableData"
      v-bind="tableConfig"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
      @row-click="handleRowClick"
    >
      <el-table-column
        v-if="showSelection"
        type="selection"
        width="55"
      />
      <el-table-column
        v-for="column in adaptedColumns"
        :key="column.prop"
        v-bind="column"
      >
        <template v-if="column.slot" #default="scope">
          <slot :name="column.slot" :row="scope.row" :column="column" :index="scope.$index" />
        </template>
      </el-table-column>
    </el-table>

    <!-- 移动端列表 -->
    <div v-else class="mobile-table">
      <!-- 搜索栏 -->
      <van-search
        v-if="showSearch"
        v-model="searchValue"
        placeholder="请输入搜索关键词"
        @search="handleSearch"
      />

      <!-- 列表内容 -->
      <van-list
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="handleLoad"
      >
        <van-cell
          v-for="(item, index) in tableData"
          :key="getRowKey(item, index)"
          :title="getMobileTitle(item)"
          :value="getMobileValue(item)"
          :label="getMobileLabel(item)"
          is-link
          @click="handleRowClick(item, index)"
        >
          <template #right-icon>
            <van-icon name="arrow" />
          </template>
          
          <!-- 自定义插槽内容 -->
          <template v-if="$slots.mobileCell" #default>
            <slot name="mobileCell" :row="item" :index="index" />
          </template>
        </van-cell>
      </van-list>

      <!-- 空状态 -->
      <van-empty
        v-if="!loading && tableData.length === 0"
        description="暂无数据"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useDevice } from '@/composables/useDevice';
import type { TableColumn } from './index';

interface AdaptiveTableProps {
  data: any[];
  columns: TableColumn[];
  showSelection?: boolean;
  showSearch?: boolean;
  loading?: boolean;
  finished?: boolean;
  rowKey?: string | ((row: any, index: number) => string);
  mobileConfig?: {
    titleField?: string;
    valueField?: string;
    labelField?: string;
  };
}

const props = withDefaults(defineProps<AdaptiveTableProps>(), {
  showSelection: false,
  showSearch: false,
  loading: false,
  finished: false,
  rowKey: 'id'
});

const emit = defineEmits<{
  'selection-change': [selection: any[]];
  'sort-change': [sortInfo: any];
  'row-click': [row: any, index: number];
  'search': [value: string];
  'load': [];
}>();

const { isMobile } = useDevice();

// 响应式数据
const searchValue = ref('');
const tableData = toRef(props, 'data');

// 计算属性
const adaptedColumns = computed(() => {
  return props.columns.filter(column => {
    // 移动端隐藏某些列
    if (isMobile.value && column.mobileDisplay === 'hidden') {
      return false;
    }
    return true;
  });
});

const tableConfig = computed(() => ({
  stripe: true,
  border: true,
  size: 'default',
  'highlight-current-row': true
}));

// 移动端显示逻辑
const getMobileTitle = (row: any) => {
  const titleColumn = props.columns.find(col => col.mobileDisplay === 'title');
  return titleColumn ? row[titleColumn.prop] : row[props.mobileConfig?.titleField || 'name'];
};

const getMobileValue = (row: any) => {
  const valueColumn = props.columns.find(col => col.mobileDisplay === 'value');
  return valueColumn ? row[valueColumn.prop] : row[props.mobileConfig?.valueField || 'status'];
};

const getMobileLabel = (row: any) => {
  const labelColumn = props.columns.find(col => col.mobileDisplay === 'label');
  return labelColumn ? row[labelColumn.prop] : row[props.mobileConfig?.labelField || 'description'];
};

const getRowKey = (row: any, index: number) => {
  if (typeof props.rowKey === 'function') {
    return props.rowKey(row, index);
  }
  return row[props.rowKey] || index;
};

// 事件处理
const handleSelectionChange = (selection: any[]) => {
  emit('selection-change', selection);
};

const handleSortChange = (sortInfo: any) => {
  emit('sort-change', sortInfo);
};

const handleRowClick = (row: any, index?: number) => {
  emit('row-click', row, index || 0);
};

const handleSearch = (value: string) => {
  emit('search', value);
};

const handleLoad = () => {
  emit('load');
};
</script>

<style lang="scss" scoped>
.adaptive-table {
  width: 100%;

  .mobile-table {
    .van-search {
      padding: 12px 16px;
      background: #f8f9fa;
    }

    .van-cell {
      padding: 12px 16px;
      
      &:not(:last-child) {
        border-bottom: 1px solid #ebedf0;
      }
      
      &:active {
        background-color: #f2f3f5;
      }
    }
    
    .van-list {
      background: white;
    }
    
    .van-empty {
      padding: 60px 0;
    }
  }
}

// 响应式样式
@media screen and (max-width: 991px) {
  .adaptive-table {
    .el-table {
      display: none;
    }
    
    .mobile-table {
      display: block;
    }
  }
}

@media screen and (min-width: 992px) {
  .adaptive-table {
    .el-table {
      display: table;
    }
    
    .mobile-table {
      display: none;
    }
  }
}
</style>
