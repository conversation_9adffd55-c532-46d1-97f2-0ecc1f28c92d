<!DOCTYPE html>
<html>
<head>
  <title>domaku | anime.js</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <meta property="og:title" content="anime.js">
  <meta property="og:url" content="https://animejs.com">
  <meta property="og:description" content="Javascript Animation Engine">
  <meta property="og:image" content="https://animejs.com/documentation/assets/img/icons/og.png">
  <meta name="twitter:card" content="summary">
  <meta name="twitter:title" content="anime.js">
  <meta name="twitter:site" content="@juliangarnier">
  <meta name="twitter:description" content="Javascript Animation Engine">
  <meta name="twitter:image" content="https://animejs.com/documentation/assets/img/icons/twitter.png">
  <link rel="apple-touch-icon-precomposed" href="../assets/img/social-media-image.png">
  <link rel="icon" type="image/png" href="../assets/img/favicon.png">
  <link href="../assets/css/documentation.css" rel="stylesheet">
  <script src="../../lib/anime.min.js"></script>
  <!-- <script src="../assets/js/anime/anime.3.0.0-alpha.js"></script> -->
  <!-- <script src="../assets/js/anime/anime.2.2.0.js"></script> -->
  <!-- <script src="../assets/js/anime/anime.2.0.1.js"></script> -->
  <!-- <script src="../assets/js/anime/anime.1.3.js"></script> -->
  <style>
    body {
      overflow: hidden;
      position: absolute;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 8em;
      font-size: 24px;
    }
    .wrapper {
      display: flex;
      flex-direction: column;
      flex-wrap: wrap;
    }
    .particule {
      width: 1px;
      height: 10em;
      margin-left: 1px;
      transform-origin: 50 50%;
    }
  </style>
</head>
<body>
  <script>

    var numberOfEls = 500;
    var duration = 5000;
    var midScreenX = window.innerWidth / 2;
    var midScreenY = window.innerHeight / 2;
    var radius = Math.sqrt(midScreenX * midScreenX + midScreenY * midScreenY);
    var fragment = document.createDocumentFragment();

    for (var i = 0; i < numberOfEls; i++) {
      var hue = 10 + Math.round(40 / numberOfEls * i);
      var el = document.createElement('div');
      el.classList.add('particule');
      el.style.backgroundColor = 'hsl(' + hue + ', 100%, 50%)';
      el.style.transform = 'rotate(' + i * 1 + 'deg) translateX(100px)';

      document.body.appendChild(el);

      anime({
        targets: el,
        rotate: '+=360',
        //delay: i * (duration / numberOfEls),
        //endDelay: i * (duration / numberOfEls),
        easing: 'linear',
        duration: duration,
        loop: true
      });

    }

  </script>
  <script src="../assets/js/vendors/stats.min.js"></script>
</body>
</html>
