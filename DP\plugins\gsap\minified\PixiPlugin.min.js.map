{"version": 3, "file": "PixiPlugin.min.js", "sources": ["../src/PixiPlugin.js"], "sourcesContent": ["/*!\n * PixiPlugin 3.11.3\n * https://greensock.com\n *\n * @license Copyright 2008-2022, GreenSock. All rights reserved.\n * Subject to the terms at https://greensock.com/standard-license or for\n * Club GreenSock members, the agreement issued with that membership.\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nlet gsap, _win, _splitColor, _coreInitted, _PIXI, PropTween, _getSetter, _isV4,\n\t_windowExists = () => typeof(window) !== \"undefined\",\n\t_getGSAP = () => gsap || (_windowExists() && (gsap = window.gsap) && gsap.registerPlugin && gsap),\n\t_isFunction = value => typeof(value) === \"function\",\n\t_warn = message => console.warn(message),\n\t_idMatrix = [1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0],\n\t_lumR = 0.212671,\n\t_lumG = 0.715160,\n\t_lumB = 0.072169,\n\t_applyMatrix = (m, m2) => {\n\t\tlet temp = [],\n\t\t\ti = 0,\n\t\t\tz = 0,\n\t\t\ty, x;\n\t\tfor (y = 0; y < 4; y++) {\n\t\t\tfor (x = 0; x < 5; x++) {\n\t\t\t\tz = (x === 4) ? m[i + 4] : 0;\n\t\t\t\ttemp[i + x] = m[i]   * m2[x] + m[i+1] * m2[x + 5] +\tm[i+2] * m2[x + 10] + m[i+3] * m2[x + 15] +\tz;\n\t\t\t}\n\t\t\ti += 5;\n\t\t}\n\t\treturn temp;\n\t},\n\t_setSaturation = (m, n) => {\n\t\tlet inv = 1 - n,\n\t\t\tr = inv * _lumR,\n\t\t\tg = inv * _lumG,\n\t\t\tb = inv * _lumB;\n\t\treturn _applyMatrix([r + n, g, b, 0, 0, r, g + n, b, 0, 0, r, g, b + n, 0, 0, 0, 0, 0, 1, 0], m);\n\t},\n\t_colorize = (m, color, amount) => {\n\t\tlet c = _splitColor(color),\n\t\t\tr = c[0] / 255,\n\t\t\tg = c[1] / 255,\n\t\t\tb = c[2] / 255,\n\t\t\tinv = 1 - amount;\n\t\treturn _applyMatrix([inv + amount * r * _lumR, amount * r * _lumG, amount * r * _lumB, 0, 0, amount * g * _lumR, inv + amount * g * _lumG, amount * g * _lumB, 0, 0, amount * b * _lumR, amount * b * _lumG, inv + amount * b * _lumB, 0, 0, 0, 0, 0, 1, 0], m);\n\t},\n\t_setHue = (m, n) => {\n\t\tn *= Math.PI / 180;\n\t\tlet c = Math.cos(n),\n\t\t\ts = Math.sin(n);\n\t\treturn _applyMatrix([(_lumR + (c * (1 - _lumR))) + (s * (-_lumR)), (_lumG + (c * (-_lumG))) + (s * (-_lumG)), (_lumB + (c * (-_lumB))) + (s * (1 - _lumB)), 0, 0, (_lumR + (c * (-_lumR))) + (s * 0.143), (_lumG + (c * (1 - _lumG))) + (s * 0.14), (_lumB + (c * (-_lumB))) + (s * -0.283), 0, 0, (_lumR + (c * (-_lumR))) + (s * (-(1 - _lumR))), (_lumG + (c * (-_lumG))) + (s * _lumG), (_lumB + (c * (1 - _lumB))) + (s * _lumB), 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1], m);\n\t},\n\t_setContrast = (m, n) => _applyMatrix([n,0,0,0,0.5 * (1 - n), 0,n,0,0,0.5 * (1 - n), 0,0,n,0,0.5 * (1 - n), 0,0,0,1,0], m),\n\t_getFilter = (target, type) => {\n\t\tlet filterClass = _PIXI.filters[type],\n\t\t\tfilters = target.filters || [],\n\t\t\ti = filters.length,\n\t\t\tfilter;\n\t\tfilterClass || _warn(type + \" not found. PixiPlugin.registerPIXI(PIXI)\");\n\t\twhile (--i > -1) {\n\t\t\tif (filters[i] instanceof filterClass) {\n\t\t\t\treturn filters[i];\n\t\t\t}\n\t\t}\n\t\tfilter = new filterClass();\n\t\tif (type === \"BlurFilter\") {\n\t\t\tfilter.blur = 0;\n\t\t}\n\t\tfilters.push(filter);\n\t\ttarget.filters = filters;\n\t\treturn filter;\n\t},\n\t_addColorMatrixFilterCacheTween = (p, plugin, cache, vars) => { //we cache the ColorMatrixFilter components in a _gsColorMatrixFilter object attached to the target object so that it's easy to grab the current value at any time.\n\t\tplugin.add(cache, p, cache[p], vars[p]);\n\t\tplugin._props.push(p);\n\t},\n\t_applyBrightnessToMatrix = (brightness, matrix) => {\n\t\tlet temp = new _PIXI.filters.ColorMatrixFilter();\n\t\ttemp.matrix = matrix;\n\t\ttemp.brightness(brightness, true);\n\t\treturn temp.matrix;\n\t},\n\t_copy = obj => {\n\t\tlet copy = {},\n\t\t\tp;\n\t\tfor (p in obj) {\n\t\t\tcopy[p] = obj[p];\n\t\t}\n\t\treturn copy;\n\t},\n\t_CMFdefaults = {contrast:1, saturation:1, colorizeAmount:0, colorize:\"rgb(255,255,255)\", hue:0, brightness:1},\n\t_parseColorMatrixFilter = (target, v, pg) => {\n\t\tlet filter = _getFilter(target, \"ColorMatrixFilter\"),\n\t\t\tcache = target._gsColorMatrixFilter = target._gsColorMatrixFilter || _copy(_CMFdefaults),\n\t\t\tcombine = v.combineCMF && !(\"colorMatrixFilter\" in v && !v.colorMatrixFilter),\n\t\t\ti, matrix, startMatrix;\n\t\tstartMatrix = filter.matrix;\n\t\tif (v.resolution) {\n\t\t\tfilter.resolution = v.resolution;\n\t\t}\n\t\tif (v.matrix && v.matrix.length === startMatrix.length) {\n\t\t\tmatrix = v.matrix;\n\t\t\tif (cache.contrast !== 1) {\n\t\t\t\t_addColorMatrixFilterCacheTween(\"contrast\", pg, cache, _CMFdefaults);\n\t\t\t}\n\t\t\tif (cache.hue) {\n\t\t\t\t_addColorMatrixFilterCacheTween(\"hue\", pg, cache, _CMFdefaults);\n\t\t\t}\n\t\t\tif (cache.brightness !== 1) {\n\t\t\t\t_addColorMatrixFilterCacheTween(\"brightness\", pg, cache, _CMFdefaults);\n\t\t\t}\n\t\t\tif (cache.colorizeAmount) {\n\t\t\t\t_addColorMatrixFilterCacheTween(\"colorize\", pg, cache, _CMFdefaults);\n\t\t\t\t_addColorMatrixFilterCacheTween(\"colorizeAmount\", pg, cache, _CMFdefaults);\n\t\t\t}\n\t\t\tif (cache.saturation !== 1) {\n\t\t\t\t_addColorMatrixFilterCacheTween(\"saturation\", pg, cache, _CMFdefaults);\n\t\t\t}\n\n\t\t} else {\n\t\t\tmatrix = _idMatrix.slice();\n\t\t\tif (v.contrast != null) {\n\t\t\t\tmatrix = _setContrast(matrix, +v.contrast);\n\t\t\t\t_addColorMatrixFilterCacheTween(\"contrast\", pg, cache, v);\n\t\t\t} else if (cache.contrast !== 1) {\n\t\t\t\tif (combine) {\n\t\t\t\t\tmatrix = _setContrast(matrix, cache.contrast);\n\t\t\t\t} else {\n\t\t\t\t\t_addColorMatrixFilterCacheTween(\"contrast\", pg, cache, _CMFdefaults);\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (v.hue != null) {\n\t\t\t\tmatrix = _setHue(matrix, +v.hue);\n\t\t\t\t_addColorMatrixFilterCacheTween(\"hue\", pg, cache, v);\n\t\t\t} else if (cache.hue) {\n\t\t\t\tif (combine) {\n\t\t\t\t\tmatrix = _setHue(matrix, cache.hue);\n\t\t\t\t} else {\n\t\t\t\t\t_addColorMatrixFilterCacheTween(\"hue\", pg, cache, _CMFdefaults);\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (v.brightness != null) {\n\t\t\t\tmatrix = _applyBrightnessToMatrix(+v.brightness, matrix);\n\t\t\t\t_addColorMatrixFilterCacheTween(\"brightness\", pg, cache, v);\n\t\t\t} else if (cache.brightness !== 1) {\n\t\t\t\tif (combine) {\n\t\t\t\t\tmatrix = _applyBrightnessToMatrix(cache.brightness, matrix);\n\t\t\t\t} else {\n\t\t\t\t\t_addColorMatrixFilterCacheTween(\"brightness\", pg, cache, _CMFdefaults);\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (v.colorize != null) {\n\t\t\t\tv.colorizeAmount = (\"colorizeAmount\" in v) ? +v.colorizeAmount : 1;\n\t\t\t\tmatrix = _colorize(matrix, v.colorize, v.colorizeAmount);\n\t\t\t\t_addColorMatrixFilterCacheTween(\"colorize\", pg, cache, v);\n\t\t\t\t_addColorMatrixFilterCacheTween(\"colorizeAmount\", pg, cache, v);\n\t\t\t} else if (cache.colorizeAmount) {\n\t\t\t\tif (combine) {\n\t\t\t\t\tmatrix = _colorize(matrix, cache.colorize, cache.colorizeAmount);\n\t\t\t\t} else {\n\t\t\t\t\t_addColorMatrixFilterCacheTween(\"colorize\", pg, cache, _CMFdefaults);\n\t\t\t\t\t_addColorMatrixFilterCacheTween(\"colorizeAmount\", pg, cache, _CMFdefaults);\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (v.saturation != null) {\n\t\t\t\tmatrix = _setSaturation(matrix, +v.saturation);\n\t\t\t\t_addColorMatrixFilterCacheTween(\"saturation\", pg, cache, v);\n\t\t\t} else if (cache.saturation !== 1) {\n\t\t\t\tif (combine) {\n\t\t\t\t\tmatrix = _setSaturation(matrix, cache.saturation);\n\t\t\t\t} else {\n\t\t\t\t\t_addColorMatrixFilterCacheTween(\"saturation\", pg, cache, _CMFdefaults);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\ti = matrix.length;\n\t\twhile (--i > -1) {\n\t\t\tif (matrix[i] !== startMatrix[i]) {\n\t\t\t\tpg.add(startMatrix, i, startMatrix[i], matrix[i], \"colorMatrixFilter\");\n\t\t\t}\n\t\t}\n\t\tpg._props.push(\"colorMatrixFilter\");\n\t},\n\t_renderColor = (ratio, {t, p, color, set}) => {\n\t\tset(t, p, color[0] << 16 | color[1] << 8 | color[2]);\n\t},\n\t_renderDirtyCache = (ratio, {g}) => {\n\t\tif (g) { //in order for PixiJS to actually redraw GraphicsData, we've gotta increment the \"dirty\" and \"clearDirty\" values. If we don't do this, the values will be tween properly, but not rendered.\n\t\t\tg.dirty++;\n\t\t\tg.clearDirty++;\n\t\t}\n\t},\n\t_renderAutoAlpha = (ratio, data) => {\n\t\tdata.t.visible = !!data.t.alpha;\n\t},\n\t_addColorTween = (target, p, value, plugin) => {\n\t\tlet currentValue = target[p],\n\t\t\tstartColor = _splitColor(_isFunction(currentValue) ? target[ ((p.indexOf(\"set\") || !_isFunction(target[\"get\" + p.substr(3)])) ? p : \"get\" + p.substr(3)) ]() : currentValue),\n\t\t\tendColor = _splitColor(value);\n\t\tplugin._pt = new PropTween(plugin._pt, target, p, 0, 0, _renderColor, {t:target, p:p, color:startColor, set:_getSetter(target, p)});\n\t\tplugin.add(startColor, 0, startColor[0], endColor[0]);\n\t\tplugin.add(startColor, 1, startColor[1], endColor[1]);\n\t\tplugin.add(startColor, 2, startColor[2], endColor[2]);\n\t},\n\n\t_colorProps = {tint:1, lineColor:1, fillColor:1},\n\t_xyContexts = \"position,scale,skew,pivot,anchor,tilePosition,tileScale\".split(\",\"),\n\t_contexts = {x:\"position\", y:\"position\", tileX:\"tilePosition\", tileY:\"tilePosition\"},\n\t_colorMatrixFilterProps = {colorMatrixFilter:1, saturation:1, contrast:1, hue:1, colorize:1, colorizeAmount:1, brightness:1, combineCMF:1},\n\t_DEG2RAD = Math.PI / 180,\n\t_isString = value => typeof(value) === \"string\",\n\t_degreesToRadians = value => (_isString(value) && value.charAt(1) === \"=\") ? value.substr(0, 2) + (parseFloat(value.substr(2)) * _DEG2RAD) : value * _DEG2RAD,\n\t_renderPropWithEnd = (ratio, data) => data.set(data.t, data.p, ratio === 1 ? data.e : (Math.round((data.s + data.c * ratio) * 100000) / 100000), data),\n\t_addRotationalPropTween = (plugin, target, property, startNum, endValue, radians) => {\n\t\tlet cap = 360 * (radians ? _DEG2RAD : 1),\n\t\t\tisString = _isString(endValue),\n\t\t\trelative = (isString && endValue.charAt(1) === \"=\") ? +(endValue.charAt(0) + \"1\") : 0,\n\t\t\tendNum = parseFloat(relative ? endValue.substr(2) : endValue) * (radians ? _DEG2RAD : 1),\n\t\t\tchange = relative ? endNum * relative : endNum - startNum,\n\t\t\tfinalValue = startNum + change,\n\t\t\tdirection, pt;\n\t\tif (isString) {\n\t\t\tdirection = endValue.split(\"_\")[1];\n\t\t\tif (direction === \"short\") {\n\t\t\t\tchange %= cap;\n\t\t\t\tif (change !== change % (cap / 2)) {\n\t\t\t\t\tchange += (change < 0) ? cap : -cap;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (direction === \"cw\" && change < 0) {\n\t\t\t\tchange = ((change + cap * 1e10) % cap) - ~~(change / cap) * cap;\n\t\t\t} else if (direction === \"ccw\" && change > 0) {\n\t\t\t\tchange = ((change - cap * 1e10) % cap) - ~~(change / cap) * cap;\n\t\t\t}\n\t\t}\n\t\tplugin._pt = pt = new PropTween(plugin._pt, target, property, startNum, change, _renderPropWithEnd);\n\t\tpt.e = finalValue;\n\t\treturn pt;\n\t},\n\t_initCore = () => {\n\t\tif (_windowExists()) {\n\t\t\t_win = window;\n\t\t\tgsap = _getGSAP();\n\t\t\t_PIXI = _coreInitted = _PIXI || _win.PIXI;\n\t\t\t_isV4 = _PIXI && _PIXI.VERSION && _PIXI.VERSION.charAt(0) === \"4\";\n\t\t\t_splitColor = color => gsap.utils.splitColor((color + \"\").substr(0,2) === \"0x\" ? \"#\" + color.substr(2) : color); // some colors in PIXI are reported as \"0xFF4421\" instead of \"#FF4421\".\n\t\t}\n\t}, i, p;\n\n//context setup...\nfor (i = 0; i < _xyContexts.length; i++) {\n\tp = _xyContexts[i];\n\t_contexts[p + \"X\"] = p;\n\t_contexts[p + \"Y\"] = p;\n}\n\n\nexport const PixiPlugin = {\n\tversion:\"3.11.3\",\n\tname:\"pixi\",\n\tregister(core, Plugin, propTween) {\n\t\tgsap = core;\n\t\tPropTween = propTween;\n\t\t_getSetter = Plugin.getSetter;\n\t\t_initCore();\n\t},\n\tregisterPIXI(pixi) {\n\t\t_PIXI = pixi;\n\t},\n\tinit(target, values, tween, index, targets) {\n\t\t_PIXI || _initCore();\n\t\tif (!_PIXI || !(target instanceof _PIXI.DisplayObject)) {\n\t\t\tconsole.warn(target, \"is not a DisplayObject or PIXI was not found. PixiPlugin.registerPIXI(PIXI);\");\n\t\t\treturn false;\n\t\t}\n\t\tlet context, axis, value, colorMatrix, filter, p, padding, i, data;\n\t\tfor (p in values) {\n\t\t\tcontext = _contexts[p];\n\t\t\tvalue = values[p];\n\t\t\tif (context) {\n\t\t\t\taxis = ~p.charAt(p.length-1).toLowerCase().indexOf(\"x\") ? \"x\" : \"y\";\n\t\t\t\tthis.add(target[context], axis, target[context][axis], (context === \"skew\") ? _degreesToRadians(value) : value, 0, 0, 0, 0, 0, 1);\n\t\t\t} else if (p === \"scale\" || p === \"anchor\" || p === \"pivot\" || p === \"tileScale\") {\n\t\t\t\tthis.add(target[p], \"x\", target[p].x, value);\n\t\t\t\tthis.add(target[p], \"y\", target[p].y, value);\n\t\t\t} else if (p === \"rotation\" || p === \"angle\") { //PIXI expects rotation in radians, but as a convenience we let folks define it in degrees and we do the conversion.\n\t\t\t\t_addRotationalPropTween(this, target, p, target[p], value, p === \"rotation\");\n\t\t\t} else if (_colorMatrixFilterProps[p]) {\n\t\t\t\tif (!colorMatrix) {\n\t\t\t\t\t_parseColorMatrixFilter(target, values.colorMatrixFilter || values, this);\n\t\t\t\t\tcolorMatrix = true;\n\t\t\t\t}\n\t\t\t} else if (p === \"blur\" || p === \"blurX\" || p === \"blurY\" || p === \"blurPadding\") {\n\t\t\t\tfilter = _getFilter(target, \"BlurFilter\");\n\t\t\t\tthis.add(filter, p, filter[p], value);\n\t\t\t\tif (values.blurPadding !== 0) {\n\t\t\t\t\tpadding = values.blurPadding || Math.max(filter[p], value) * 2;\n\t\t\t\t\ti = target.filters.length;\n\t\t\t\t\twhile (--i > -1) {\n\t\t\t\t\t\ttarget.filters[i].padding = Math.max(target.filters[i].padding, padding); //if we don't expand the padding on all the filters, it can look clipped.\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else if (_colorProps[p]) {\n\t\t\t\tif ((p === \"lineColor\" || p === \"fillColor\") && target instanceof _PIXI.Graphics) {\n\t\t\t\t\tdata = (target.geometry || target).graphicsData; //\"geometry\" was introduced in PIXI version 5\n\t\t\t\t\tthis._pt = new PropTween(this._pt, target, p, 0, 0, _renderDirtyCache, {g: target.geometry || target});\n\t\t\t\t\ti = data.length;\n\t\t\t\t\twhile (--i > -1) {\n\t\t\t\t\t\t_addColorTween(_isV4 ? data[i] : data[i][p.substr(0, 4) + \"Style\"], _isV4 ? p : \"color\", value, this);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\t_addColorTween(target, p, value, this);\n\t\t\t\t}\n\t\t\t} else if (p === \"autoAlpha\") {\n\t\t\t\tthis._pt = new PropTween(this._pt, target, \"visible\", 0, 0, _renderAutoAlpha);\n\t\t\t\tthis.add(target, \"alpha\", target.alpha, value);\n\t\t\t\tthis._props.push(\"alpha\", \"visible\");\n\t\t\t} else if (p !== \"resolution\") {\n\t\t\t\tthis.add(target, p, \"get\", value);\n\t\t\t}\n\t\t\tthis._props.push(p);\n\t\t}\n\t}\n};\n\n_getGSAP() && gsap.registerPlugin(PixiPlugin);\n\nexport { PixiPlugin as default };"], "names": ["_windowExists", "window", "_getGSAP", "gsap", "registerPlugin", "_isFunction", "value", "_applyMatrix", "m", "m2", "y", "x", "temp", "i", "z", "_setSaturation", "n", "inv", "r", "_lumR", "g", "_lumG", "b", "_lumB", "_colorize", "color", "amount", "c", "_splitColor", "_setHue", "Math", "PI", "cos", "s", "sin", "_setContrast", "_getFilter", "target", "type", "filter", "filterClass", "_PIXI", "filters", "length", "_warn", "message", "console", "warn", "blur", "push", "_addColorMatrixFilterCacheTween", "p", "plugin", "cache", "vars", "add", "_props", "_applyBrightnessToMatrix", "brightness", "matrix", "ColorMatrixFilter", "_parseColorMatrixFilter", "v", "pg", "startMatrix", "_gsColorMatrixFilter", "_copy", "obj", "copy", "_CMFdefaults", "combine", "combineCMF", "colorMatrixFilter", "resolution", "contrast", "hue", "colorizeAmount", "saturation", "_idMatrix", "slice", "colorize", "_renderColor", "ratio", "t", "set", "_renderDirtyCache", "dirty", "clearDirty", "_renderAutoAlpha", "data", "visible", "alpha", "_addColorTween", "currentValue", "startColor", "indexOf", "substr", "endColor", "_pt", "PropTween", "_getSetter", "_isString", "_degreesToRadians", "char<PERSON>t", "parseFloat", "_DEG2RAD", "_renderPropWithEnd", "e", "round", "_addRotationalPropTween", "property", "startNum", "endValue", "radians", "direction", "pt", "cap", "isString", "relative", "endNum", "change", "finalValue", "split", "_initCore", "_win", "PIXI", "_isV4", "VERSION", "utils", "splitColor", "_colorProps", "tint", "lineColor", "fillColor", "_xyContexts", "_contexts", "tileX", "tileY", "_colorMatrixFilterProps", "PixiPlugin", "version", "name", "register", "core", "Plugin", "propTween", "getSetter", "registerPIXI", "pixi", "init", "values", "DisplayObject", "context", "axis", "colorMatrix", "padding", "toLowerCase", "this", "blurPadding", "max", "Graphics", "geometry", "graphicsData"], "mappings": ";;;;;;;;;6MAYiB,SAAhBA,UAAyC,oBAAZC,OAClB,SAAXC,WAAiBC,GAASH,MAAoBG,EAAOF,OAAOE,OAASA,EAAKC,gBAAkBD,EAC9E,SAAdE,EAAcC,SAA2B,mBAAXA,EAMf,SAAfC,EAAgBC,EAAGC,OAIjBC,EAAGC,EAHAC,EAAO,GACVC,EAAI,EACJC,EAAI,MAEAJ,EAAI,EAAGA,EAAI,EAAGA,IAAK,KAClBC,EAAI,EAAGA,EAAI,EAAGA,IAClBG,EAAW,IAANH,EAAWH,EAAEK,EAAI,GAAK,EAC3BD,EAAKC,EAAIF,GAAKH,EAAEK,GAAOJ,EAAGE,GAAKH,EAAEK,EAAE,GAAKJ,EAAGE,EAAI,GAAKH,EAAEK,EAAE,GAAKJ,EAAGE,EAAI,IAAMH,EAAEK,EAAE,GAAKJ,EAAGE,EAAI,IAAMG,EAEjGD,GAAK,SAECD,EAES,SAAjBG,EAAkBP,EAAGQ,OAChBC,EAAM,EAAID,EACbE,EAAID,EAAME,EACVC,EAAIH,EAAMI,EACVC,EAAIL,EAAMM,SACJhB,EAAa,CAACW,EAAIF,EAAGI,EAAGE,EAAG,EAAG,EAAGJ,EAAGE,EAAIJ,EAAGM,EAAG,EAAG,EAAGJ,EAAGE,EAAGE,EAAIN,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAIR,GAEnF,SAAZgB,EAAahB,EAAGiB,EAAOC,OAClBC,EAAIC,EAAYH,GACnBP,EAAIS,EAAE,GAAK,IACXP,EAAIO,EAAE,GAAK,IACXL,EAAIK,EAAE,GAAK,IACXV,EAAM,EAAIS,SACJnB,EAAa,CAACU,EAAMS,EAASR,EAAIC,EAAOO,EAASR,EAAIG,EAAOK,EAASR,EAAIK,EAAO,EAAG,EAAGG,EAASN,EAAID,EAAOF,EAAMS,EAASN,EAAIC,EAAOK,EAASN,EAAIG,EAAO,EAAG,EAAGG,EAASJ,EAAIH,EAAOO,EAASJ,EAAID,EAAOJ,EAAMS,EAASJ,EAAIC,EAAO,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAIf,GAEpP,SAAVqB,EAAWrB,EAAGQ,GACbA,GAAKc,KAAKC,GAAK,QACXJ,EAAIG,KAAKE,IAAIhB,GAChBiB,EAAIH,KAAKI,IAAIlB,UACPT,EAAa,CAAEY,EAASQ,GAAK,EAAIR,GAAYc,GAAMd,EAAUE,EAASM,GAAMN,EAAYY,GAAMZ,EAAUE,EAASI,GAAMJ,EAAYU,GAAK,EAAIV,GAAS,EAAG,EAAIJ,EAASQ,GAAMR,EAAgB,KAAJc,EAAaZ,EAASM,GAAK,EAAIN,GAAgB,IAAJY,EAAYV,EAASI,GAAMJ,GAAiB,KAALU,EAAa,EAAG,EAAId,EAASQ,GAAMR,EAAYc,IAAO,EAAId,GAAWE,EAASM,GAAMN,EAAYY,EAAIZ,EAASE,EAASI,GAAK,EAAIJ,GAAYU,EAAIV,EAAQ,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAIf,GAE9b,SAAf2B,EAAgB3B,EAAGQ,UAAMT,EAAa,CAACS,EAAE,EAAE,EAAE,EAAE,IAAO,EAAIA,GAAI,EAAEA,EAAE,EAAE,EAAE,IAAO,EAAIA,GAAI,EAAE,EAAEA,EAAE,EAAE,IAAO,EAAIA,GAAI,EAAE,EAAE,EAAE,EAAE,GAAIR,GAC3G,SAAb4B,EAAcC,EAAQC,OAIpBC,EAHGC,EAAcC,EAAMC,QAAQJ,GAC/BI,EAAUL,EAAOK,SAAW,GAC5B7B,EAAI6B,EAAQC,WAEbH,GA9CO,SAARI,MAAQC,GAAWC,QAAQC,KAAKF,GA8ChBD,CAAMN,EAAO,8CACd,IAALzB,MACJ6B,EAAQ7B,aAAc2B,SAClBE,EAAQ7B,UAGjB0B,EAAS,IAAIC,EACA,eAATF,IACHC,EAAOS,KAAO,GAEfN,EAAQO,KAAKV,GACbF,EAAOK,QAAUA,EACVH,EAE0B,SAAlCW,EAAmCC,EAAGC,EAAQC,EAAOC,GACpDF,EAAOG,IAAIF,EAAOF,EAAGE,EAAMF,GAAIG,EAAKH,IACpCC,EAAOI,OAAOP,KAAKE,GAEO,SAA3BM,EAA4BC,EAAYC,OACnC/C,EAAO,IAAI6B,EAAMC,QAAQkB,yBAC7BhD,EAAK+C,OAASA,EACd/C,EAAK8C,WAAWA,GAAY,GACrB9C,EAAK+C,OAWa,SAA1BE,EAA2BxB,EAAQyB,EAAGC,OAIpClD,EAAG8C,EAAQK,EAHRzB,EAASH,EAAWC,EAAQ,qBAC/BgB,EAAQhB,EAAO4B,qBAAuB5B,EAAO4B,sBAXvC,SAARC,MAAQC,OAENhB,EADGiB,EAAO,OAENjB,KAAKgB,EACTC,EAAKjB,GAAKgB,EAAIhB,UAERiB,EAK+DF,CAAMG,GAC3EC,EAAUR,EAAES,cAAgB,sBAAuBT,IAAMA,EAAEU,mBAE5DR,EAAczB,EAAOoB,OACjBG,EAAEW,aACLlC,EAAOkC,WAAaX,EAAEW,YAEnBX,EAAEH,QAAUG,EAAEH,OAAOhB,SAAWqB,EAAYrB,QAC/CgB,EAASG,EAAEH,OACY,IAAnBN,EAAMqB,UACTxB,EAAgC,WAAYa,EAAIV,EAAOgB,GAEpDhB,EAAMsB,KACTzB,EAAgC,MAAOa,EAAIV,EAAOgB,GAE1B,IAArBhB,EAAMK,YACTR,EAAgC,aAAca,EAAIV,EAAOgB,GAEtDhB,EAAMuB,iBACT1B,EAAgC,WAAYa,EAAIV,EAAOgB,GACvDnB,EAAgC,iBAAkBa,EAAIV,EAAOgB,IAErC,IAArBhB,EAAMwB,YACT3B,EAAgC,aAAca,EAAIV,EAAOgB,KAI1DV,EAASmB,EAAUC,QACD,MAAdjB,EAAEY,UACLf,EAASxB,EAAawB,GAASG,EAAEY,UACjCxB,EAAgC,WAAYa,EAAIV,EAAOS,IAC1B,IAAnBT,EAAMqB,WACZJ,EACHX,EAASxB,EAAawB,EAAQN,EAAMqB,UAEpCxB,EAAgC,WAAYa,EAAIV,EAAOgB,IAG5C,MAATP,EAAEa,KACLhB,EAAS9B,EAAQ8B,GAASG,EAAEa,KAC5BzB,EAAgC,MAAOa,EAAIV,EAAOS,IACxCT,EAAMsB,MACZL,EACHX,EAAS9B,EAAQ8B,EAAQN,EAAMsB,KAE/BzB,EAAgC,MAAOa,EAAIV,EAAOgB,IAGhC,MAAhBP,EAAEJ,YACLC,EAASF,GAA0BK,EAAEJ,WAAYC,GACjDT,EAAgC,aAAca,EAAIV,EAAOS,IAC1B,IAArBT,EAAMK,aACZY,EACHX,EAASF,EAAyBJ,EAAMK,WAAYC,GAEpDT,EAAgC,aAAca,EAAIV,EAAOgB,IAGzC,MAAdP,EAAEkB,UACLlB,EAAEc,eAAkB,mBAAoBd,GAAMA,EAAEc,eAAiB,EACjEjB,EAASnC,EAAUmC,EAAQG,EAAEkB,SAAUlB,EAAEc,gBACzC1B,EAAgC,WAAYa,EAAIV,EAAOS,GACvDZ,EAAgC,iBAAkBa,EAAIV,EAAOS,IACnDT,EAAMuB,iBACZN,EACHX,EAASnC,EAAUmC,EAAQN,EAAM2B,SAAU3B,EAAMuB,iBAEjD1B,EAAgC,WAAYa,EAAIV,EAAOgB,GACvDnB,EAAgC,iBAAkBa,EAAIV,EAAOgB,KAG3C,MAAhBP,EAAEe,YACLlB,EAAS5C,EAAe4C,GAASG,EAAEe,YACnC3B,EAAgC,aAAca,EAAIV,EAAOS,IAC1B,IAArBT,EAAMwB,aACZP,EACHX,EAAS5C,EAAe4C,EAAQN,EAAMwB,YAEtC3B,EAAgC,aAAca,EAAIV,EAAOgB,KAI5DxD,EAAI8C,EAAOhB,aACG,IAAL9B,GACJ8C,EAAO9C,KAAOmD,EAAYnD,IAC7BkD,EAAGR,IAAIS,EAAanD,EAAGmD,EAAYnD,GAAI8C,EAAO9C,GAAI,qBAGpDkD,EAAGP,OAAOP,KAAK,qBAED,SAAfgC,EAAgBC,SAAQC,IAAAA,EAAGhC,IAAAA,EAAG1B,IAAAA,OAC7B2D,IADoCA,KAChCD,EAAGhC,EAAG1B,EAAM,IAAM,GAAKA,EAAM,IAAM,EAAIA,EAAM,IAE9B,SAApB4D,EAAqBH,SAAQ9D,IAAAA,EACxBA,IACHA,EAAEkE,QACFlE,EAAEmE,cAGe,SAAnBC,EAAoBN,EAAOO,GAC1BA,EAAKN,EAAEO,UAAYD,EAAKN,EAAEQ,MAEV,SAAjBC,EAAkBvD,EAAQc,EAAG7C,EAAO8C,OAC/ByC,EAAexD,EAAOc,GACzB2C,EAAalE,EAAYvB,EAAYwF,GAAgBxD,EAAUc,EAAE4C,QAAQ,SAAW1F,EAAYgC,EAAO,MAAQc,EAAE6C,OAAO,KAAQ7C,EAAI,MAAQA,EAAE6C,OAAO,MAAUH,GAC/JI,EAAWrE,EAAYtB,GACxB8C,EAAO8C,IAAM,IAAIC,EAAU/C,EAAO8C,IAAK7D,EAAQc,EAAG,EAAG,EAAG8B,EAAc,CAACE,EAAE9C,EAAQc,EAAEA,EAAG1B,MAAMqE,EAAYV,IAAIgB,EAAW/D,EAAQc,KAC/HC,EAAOG,IAAIuC,EAAY,EAAGA,EAAW,GAAIG,EAAS,IAClD7C,EAAOG,IAAIuC,EAAY,EAAGA,EAAW,GAAIG,EAAS,IAClD7C,EAAOG,IAAIuC,EAAY,EAAGA,EAAW,GAAIG,EAAS,IAQvC,SAAZI,EAAY/F,SAA2B,iBAAXA,EACR,SAApBgG,EAAoBhG,UAAU+F,EAAU/F,IAA8B,MAApBA,EAAMiG,OAAO,GAAcjG,EAAM0F,OAAO,EAAG,GAAMQ,WAAWlG,EAAM0F,OAAO,IAAMS,EAAYnG,EAAQmG,EAChI,SAArBC,EAAsBxB,EAAOO,UAASA,EAAKL,IAAIK,EAAKN,EAAGM,EAAKtC,EAAa,IAAV+B,EAAcO,EAAKkB,EAAK7E,KAAK8E,MAAkC,KAA3BnB,EAAKxD,EAAIwD,EAAK9D,EAAIuD,IAAmB,IAASO,GACvH,SAA1BoB,EAA2BzD,EAAQf,EAAQyE,EAAUC,EAAUC,EAAUC,OAOvEC,EAAWC,EANRC,EAAM,KAAOH,EAAUR,EAAW,GACrCY,EAAWhB,EAAUW,GACrBM,EAAYD,GAAmC,MAAvBL,EAAST,OAAO,KAAgBS,EAAST,OAAO,GAAK,KAAO,EACpFgB,EAASf,WAAWc,EAAWN,EAAShB,OAAO,GAAKgB,IAAaC,EAAUR,EAAW,GACtFe,EAASF,EAAWC,EAASD,EAAWC,EAASR,EACjDU,EAAaV,EAAWS,SAErBH,IAEe,WADlBH,EAAYF,EAASU,MAAM,KAAK,MAE/BF,GAAUJ,KACKI,GAAUJ,EAAM,KAC9BI,GAAWA,EAAS,EAAKJ,GAAOA,GAGhB,OAAdF,GAAsBM,EAAS,EAClCA,GAAWA,EAAe,KAANJ,GAAcA,KAAUI,EAASJ,GAAOA,EACpC,QAAdF,GAAgC,EAATM,IACjCA,GAAWA,EAAe,KAANJ,GAAcA,KAAUI,EAASJ,GAAOA,IAG9DhE,EAAO8C,IAAMiB,EAAK,IAAIhB,EAAU/C,EAAO8C,IAAK7D,EAAQyE,EAAUC,EAAUS,EAAQd,GAChFS,EAAGR,EAAIc,EACAN,EAEI,SAAZQ,IACK3H,MACH4H,EAAO3H,OACPE,EAAOD,IACPuC,EAAuBA,GAASmF,EAAKC,KACrCC,EAAQrF,GAASA,EAAMsF,SAAuC,MAA5BtF,EAAMsF,QAAQxB,OAAO,GACvD3E,EAAc,qBAAAH,UAAStB,EAAK6H,MAAMC,WAAwC,QAA5BxG,EAAQ,IAAIuE,OAAO,EAAE,GAAc,IAAMvE,EAAMuE,OAAO,GAAKvE,KA7O5G,IAAItB,EAAMyH,EAAMhG,EAA2Ba,EAAO0D,EAAWC,EAAY0B,EA+OrEjH,EAAGsC,EA1ON2B,EAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GACnD3D,EAAQ,QACRE,EAAQ,OACRE,EAAQ,QA0ER8C,EAAe,CAACK,SAAS,EAAGG,WAAW,EAAGD,eAAe,EAAGI,SAAS,mBAAoBL,IAAI,EAAGjB,WAAW,GAmH3GwE,EAAc,CAACC,KAAK,EAAGC,UAAU,EAAGC,UAAU,GAC9CC,EAAc,0DAA0DZ,MAAM,KAC9Ea,EAAY,CAAC5H,EAAE,WAAYD,EAAE,WAAY8H,MAAM,eAAgBC,MAAM,gBACrEC,EAA0B,CAAClE,kBAAkB,EAAGK,WAAW,EAAGH,SAAS,EAAGC,IAAI,EAAGK,SAAS,EAAGJ,eAAe,EAAGlB,WAAW,EAAGa,WAAW,GACxIkC,EAAW3E,KAAKC,GAAK,IAyCtB,IAAKlB,EAAI,EAAGA,EAAIyH,EAAY3F,OAAQ9B,IACnCsC,EAAImF,EAAYzH,GAChB0H,EAAUpF,EAAI,KAAOA,EACrBoF,EAAUpF,EAAI,KAAOA,MAITwF,EAAa,CACzBC,QAAQ,SACRC,KAAK,OACLC,2BAASC,EAAMC,EAAQC,GACtB9I,EAAO4I,EACP5C,EAAY8C,EACZ7C,EAAa4C,EAAOE,UACpBvB,KAEDwB,mCAAaC,GACZ3G,EAAQ2G,GAETC,mBAAKhH,EAAQiH,MACZ7G,GAASkF,MACJlF,GAAWJ,aAAkBI,EAAM8G,sBACvCzG,QAAQC,KAAKV,EAAQ,iFACd,MAEJmH,EAASC,EAAMnJ,EAAOoJ,EAAanH,EAAQY,EAAGwG,EAAS9I,EAAG4E,MACzDtC,KAAKmG,EAAQ,IACjBE,EAAUjB,EAAUpF,GACpB7C,EAAQgJ,EAAOnG,GACXqG,EACHC,GAAQtG,EAAEoD,OAAOpD,EAAER,OAAO,GAAGiH,cAAc7D,QAAQ,KAAO,IAAM,SAC3DxC,IAAIlB,EAAOmH,GAAUC,EAAMpH,EAAOmH,GAASC,GAAoB,SAAZD,EAAsBlD,EAAkBhG,GAASA,EAAO,EAAG,EAAG,EAAG,EAAG,EAAG,QACzH,GAAU,UAAN6C,GAAuB,WAANA,GAAwB,UAANA,GAAuB,cAANA,OACzDI,IAAIlB,EAAOc,GAAI,IAAKd,EAAOc,GAAGxC,EAAGL,QACjCiD,IAAIlB,EAAOc,GAAI,IAAKd,EAAOc,GAAGzC,EAAGJ,QAChC,GAAU,aAAN6C,GAA0B,UAANA,EAC9B0D,EAAwBgD,KAAMxH,EAAQc,EAAGd,EAAOc,GAAI7C,EAAa,aAAN6C,QACrD,GAAIuF,EAAwBvF,GAC7BuG,IACJ7F,EAAwBxB,EAAQiH,EAAO9E,mBAAqB8E,EAAQO,MACpEH,GAAc,QAET,GAAU,SAANvG,GAAsB,UAANA,GAAuB,UAANA,GAAuB,gBAANA,MAC5DZ,EAASH,EAAWC,EAAQ,mBACvBkB,IAAIhB,EAAQY,EAAGZ,EAAOY,GAAI7C,GACJ,IAAvBgJ,EAAOQ,gBACVH,EAAUL,EAAOQ,aAA4C,EAA7BhI,KAAKiI,IAAIxH,EAAOY,GAAI7C,GACpDO,EAAIwB,EAAOK,QAAQC,QACL,IAAL9B,GACRwB,EAAOK,QAAQ7B,GAAG8I,QAAU7H,KAAKiI,IAAI1H,EAAOK,QAAQ7B,GAAG8I,QAASA,QAG5D,GAAIzB,EAAY/E,OACX,cAANA,GAA2B,cAANA,IAAsBd,aAAkBI,EAAMuH,aACvEvE,GAAQpD,EAAO4H,UAAY5H,GAAQ6H,kBAC9BhE,IAAM,IAAIC,EAAU0D,KAAK3D,IAAK7D,EAAQc,EAAG,EAAG,EAAGkC,EAAmB,CAACjE,EAAGiB,EAAO4H,UAAY5H,IAC9FxB,EAAI4E,EAAK9C,QACK,IAAL9B,GACR+E,EAAekC,EAAQrC,EAAK5E,GAAK4E,EAAK5E,GAAGsC,EAAE6C,OAAO,EAAG,GAAK,SAAU8B,EAAQ3E,EAAI,QAAS7C,EAAOuJ,WAGjGjE,EAAevD,EAAQc,EAAG7C,EAAOuJ,UAElB,cAAN1G,QACL+C,IAAM,IAAIC,EAAU0D,KAAK3D,IAAK7D,EAAQ,UAAW,EAAG,EAAGmD,QACvDjC,IAAIlB,EAAQ,QAASA,EAAOsD,MAAOrF,QACnCkD,OAAOP,KAAK,QAAS,YACV,eAANE,QACLI,IAAIlB,EAAQc,EAAG,MAAO7C,QAEvBkD,OAAOP,KAAKE,MAKpBjD,KAAcC,EAAKC,eAAeuI"}