<template>
  <div class="basic-stats-panel">
    <div class="panel-header">
      <div class="header-content">
        <div class="title-wrapper">
          <div class="title-icon">📋</div>
          <h3 class="title">资产基础统计</h3>
        </div>
        <div class="header-actions">
          <div class="time-filter">
            <select v-model="selectedPeriod" @change="updateData">
              <option value="month">本月</option>
              <option value="quarter">本季度</option>
              <option value="year">本年度</option>
            </select>
          </div>
        </div>
      </div>
      <div class="header-decoration"></div>
    </div>
    
    <div class="panel-content">
      <div class="stats-list">
        <div 
          class="stat-item" 
          v-for="(item, index) in basicStats" 
          :key="index"
          :style="{ '--delay': index * 0.1 + 's' }"
        >
          <div class="stat-header">
            <div class="stat-name">{{ item.name }}</div>
            <div class="stat-actions">
              <div class="stat-change" :class="item.changeType">
                <svg v-if="item.changeType === 'up'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M7 14L12 9L17 14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <svg v-else viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M7 10L12 15L17 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span>{{ item.change }}</span>
              </div>
            </div>
          </div>
          
          <div class="stat-body">
            <div class="stat-number" :style="{ color: item.color }">
              {{ item.value }}
            </div>
            <div class="stat-description">{{ item.description }}</div>
          </div>
          
          <div class="stat-footer">
            <div class="progress-container">
              <div class="progress-bar">
                <div class="progress-bg"></div>
                <div 
                  class="progress-fill" 
                  :style="{ 
                    width: item.percentage + '%', 
                    backgroundColor: item.color,
                    boxShadow: `0 0 10px ${item.color}40`
                  }"
                ></div>
              </div>
              <div class="progress-text">{{ item.percentage }}%</div>
            </div>
            <div class="stat-trend">
              <div class="trend-chart" :ref="el => setTrendRef(el, index)"></div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 汇总信息 -->
      <div class="summary-info">
        <div class="summary-item">
          <div class="summary-label">总资产价值</div>
          <div class="summary-value">{{ totalValue.toLocaleString() }}万元</div>
        </div>
        <div class="summary-item">
          <div class="summary-label">平均增长率</div>
          <div class="summary-value" :class="{ positive: avgGrowth > 0, negative: avgGrowth < 0 }">
            {{ avgGrowth > 0 ? '+' : '' }}{{ avgGrowth }}%
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, computed } from 'vue'
import * as echarts from 'echarts'

const selectedPeriod = ref('month')
const trendCharts = ref([])

// 基础统计数据
const basicStats = ref([
  {
    name: '数字化资产',
    value: '42,867万(15.4%)',
    description: '包含软件、系统等数字资产',
    change: '-5%',
    changeType: 'down',
    color: '#00d4ff',
    percentage: 85,
    trendData: [65, 70, 68, 75, 80, 85, 82, 85]
  },
  {
    name: '固定资产',
    value: '16,877万(7.0%)',
    description: '房屋、设备等固定资产',
    change: '-9%',
    changeType: 'down',
    color: '#00ff88',
    percentage: 70,
    trendData: [80, 75, 78, 70, 72, 70, 68, 70]
  },
  {
    name: '无形资产',
    value: '32,475万(4.8%)',
    description: '专利、商标等无形资产',
    change: '+27%',
    changeType: 'up',
    color: '#ffb800',
    percentage: 90,
    trendData: [60, 65, 70, 75, 80, 85, 88, 90]
  },
  {
    name: '流动资产',
    value: '28,156万(3.2%)',
    description: '现金、存货等流动资产',
    change: '+12%',
    changeType: 'up',
    color: '#ff6b6b',
    percentage: 78,
    trendData: [70, 72, 75, 73, 76, 78, 77, 78]
  }
])

// 计算总值和平均增长率
const totalValue = computed(() => {
  return basicStats.value.reduce((sum, item) => {
    const value = parseFloat(item.value.replace(/[^\d.]/g, ''))
    return sum + value
  }, 0)
})

const avgGrowth = computed(() => {
  const growthRates = basicStats.value.map(item => {
    return parseFloat(item.change.replace(/[^\d.-]/g, ''))
  })
  const avg = growthRates.reduce((sum, rate) => sum + rate, 0) / growthRates.length
  return Math.round(avg * 10) / 10
})

// 设置趋势图表引用
const setTrendRef = (el, index) => {
  if (el) {
    trendCharts.value[index] = el
  }
}

// 初始化趋势图表
const initTrendCharts = () => {
  trendCharts.value.forEach((container, index) => {
    if (!container) return
    
    const chart = echarts.init(container)
    const stat = basicStats.value[index]
    
    const option = {
      backgroundColor: 'transparent',
      grid: {
        left: 0,
        right: 0,
        top: 0,
        bottom: 0
      },
      xAxis: {
        type: 'category',
        show: false,
        data: Array.from({ length: stat.trendData.length }, (_, i) => i)
      },
      yAxis: {
        type: 'value',
        show: false
      },
      series: [
        {
          type: 'line',
          data: stat.trendData,
          smooth: true,
          lineStyle: {
            color: stat.color,
            width: 2
          },
          itemStyle: {
            color: stat.color,
            borderWidth: 0
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: stat.color + '40' },
              { offset: 1, color: stat.color + '10' }
            ])
          },
          symbol: 'none',
          animation: true,
          animationDuration: 2000,
          animationEasing: 'cubicOut'
        }
      ]
    }
    
    chart.setOption(option)
  })
}

// 更新数据
const updateData = () => {
  // 模拟根据时间周期更新数据
  const multiplier = selectedPeriod.value === 'year' ? 1.2 : 
                    selectedPeriod.value === 'quarter' ? 1.1 : 1
  
  basicStats.value = basicStats.value.map(item => ({
    ...item,
    percentage: Math.min(100, Math.round(item.percentage * multiplier)),
    trendData: item.trendData.map(val => Math.round(val * multiplier))
  }))
  
  // 重新初始化趋势图表
  setTimeout(() => {
    initTrendCharts()
  }, 100)
}

onMounted(async () => {
  await nextTick()
  setTimeout(() => {
    initTrendCharts()
  }, 500)
})

onUnmounted(() => {
  trendCharts.value.forEach(chart => {
    if (chart && chart.dispose) {
      chart.dispose()
    }
  })
})
</script>

<style lang="scss" scoped>
.basic-stats-panel {
  background: linear-gradient(135deg, 
    rgba(0, 255, 136, 0.05) 0%, 
    rgba(0, 212, 255, 0.03) 100%
  );
  border: 1px solid rgba(0, 255, 136, 0.2);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  
  &:hover {
    border-color: rgba(0, 255, 136, 0.4);
    box-shadow: 0 12px 40px rgba(0, 255, 136, 0.2);
  }
  
  .panel-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid rgba(0, 255, 136, 0.1);
    position: relative;
    
    .header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .title-wrapper {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .title-icon {
          font-size: 20px;
          filter: drop-shadow(0 0 8px rgba(0, 255, 136, 0.6));
        }
        
        .title {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #fff;
          text-shadow: 0 0 10px rgba(0, 255, 136, 0.3);
        }
      }
      
      .header-actions {
        .time-filter {
          select {
            background: rgba(0, 255, 136, 0.1);
            border: 1px solid rgba(0, 255, 136, 0.2);
            border-radius: 6px;
            color: #fff;
            padding: 4px 8px;
            font-size: 12px;
            outline: none;
            cursor: pointer;
            
            &:focus {
              border-color: rgba(0, 255, 136, 0.4);
            }
            
            option {
              background: #1a1f3a;
              color: #fff;
            }
          }
        }
      }
    }
    
    .header-decoration {
      position: absolute;
      bottom: 0;
      left: 24px;
      width: 60px;
      height: 2px;
      background: linear-gradient(90deg, #00ff88, #00d4ff);
      border-radius: 1px;
    }
  }
  
  .panel-content {
    padding: 20px 24px 24px;
    
    .stats-list {
      margin-bottom: 20px;
      
      .stat-item {
        padding: 16px;
        margin-bottom: 16px;
        background: rgba(0, 255, 136, 0.03);
        border: 1px solid rgba(0, 255, 136, 0.1);
        border-radius: 12px;
        transition: all 0.3s ease;
        animation: slideInUp 0.6s ease-out var(--delay);
        
        &:last-child {
          margin-bottom: 0;
        }
        
        &:hover {
          background: rgba(0, 255, 136, 0.05);
          border-color: rgba(0, 255, 136, 0.2);
          transform: translateY(-2px);
        }
        
        .stat-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 12px;
          
          .stat-name {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
          }
          
          .stat-actions {
            .stat-change {
              display: flex;
              align-items: center;
              gap: 4px;
              font-size: 12px;
              font-weight: 500;
              
              svg {
                width: 12px;
                height: 12px;
              }
              
              &.up {
                color: #00ff88;
              }
              
              &.down {
                color: #ff6b6b;
              }
            }
          }
        }
        
        .stat-body {
          margin-bottom: 12px;
          
          .stat-number {
            font-size: 16px;
            font-weight: 700;
            margin-bottom: 4px;
            text-shadow: 0 0 10px currentColor;
          }
          
          .stat-description {
            font-size: 11px;
            color: rgba(255, 255, 255, 0.6);
            line-height: 1.4;
          }
        }
        
        .stat-footer {
          display: flex;
          align-items: center;
          gap: 12px;
          
          .progress-container {
            flex: 1;
            
            .progress-bar {
              position: relative;
              height: 4px;
              margin-bottom: 4px;
              
              .progress-bg {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 2px;
              }
              
              .progress-fill {
                position: absolute;
                top: 0;
                left: 0;
                height: 100%;
                border-radius: 2px;
                transition: width 1.5s ease;
                animation: progressGlow 2s ease-in-out infinite;
              }
            }
            
            .progress-text {
              font-size: 10px;
              color: rgba(255, 255, 255, 0.7);
              text-align: right;
            }
          }
          
          .stat-trend {
            width: 60px;
            height: 30px;
            
            .trend-chart {
              width: 100%;
              height: 100%;
            }
          }
        }
      }
    }
    
    .summary-info {
      display: flex;
      justify-content: space-between;
      padding: 16px;
      background: rgba(0, 255, 136, 0.05);
      border-radius: 12px;
      border: 1px solid rgba(0, 255, 136, 0.1);
      
      .summary-item {
        text-align: center;
        
        .summary-label {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.7);
          margin-bottom: 4px;
        }
        
        .summary-value {
          font-size: 16px;
          font-weight: 700;
          color: #fff;
          
          &.positive {
            color: #00ff88;
          }
          
          &.negative {
            color: #ff6b6b;
          }
        }
      }
    }
  }
}

// 动画
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes progressGlow {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}
</style>
