# 移动端PC端整合方案 - 快速实施指南

## 🚀 快速开始

### 第一步：创建基础工具文件

1. **创建设备检测工具**
```bash
# 创建设备检测工具
mkdir -p src/utils
touch src/utils/device.ts
```

2. **创建自适应组合式API**
```bash
# 创建组合式API
mkdir -p src/composables
touch src/composables/useDevice.ts
```

3. **创建自适应组件目录**
```bash
# 创建自适应组件目录
mkdir -p src/components/adaptive
touch src/components/adaptive/AdaptiveTable.vue
touch src/components/adaptive/AdaptiveForm.vue
touch src/components/adaptive/AdaptiveButton.vue
```

### 第二步：修改现有文件

#### 1. 更新 Vite 配置 (vite.config.ts)

在现有的 `Components` 配置中添加自适应组件解析：

```typescript
// vite.config.ts
Components({
  resolvers: [
    ElementPlusResolver(),
    VantResolver(),
    // 新增：自适应组件解析器
    (componentName) => {
      if (componentName.startsWith('Adaptive')) {
        return { 
          name: componentName, 
          from: '@/components/adaptive' 
        };
      }
    }
  ],
  dirs: ["src/components", "src/**/components", "src/components/adaptive"],
  dts: false,
})
```

#### 2. 更新主布局文件 (src/layout/index.vue)

在现有布局中添加设备检测逻辑：

```vue
<!-- 在 <script setup> 中添加 -->
<script setup lang="ts">
import { useDevice } from '@/composables/useDevice';

// 现有代码...
const { isMobile, isTablet } = useDevice();

// 在现有的 classObj 计算属性中添加
const classObj = computed(() => ({
  hideSidebar: !appStore.sidebar.opened,
  openSidebar: appStore.sidebar.opened,
  mobile: appStore.device === DeviceEnum.MOBILE,
  tablet: isTablet.value, // 新增
  [`layout-${settingsStore.layout}`]: true,
}));
</script>
```

#### 3. 更新路由配置 (src/router/index.ts)

在现有路由中添加自适应路由：

```typescript
// 在现有的 constantRoutes 中添加
{
  path: "/adaptive",
  component: () => import("@/layout/AdaptiveLayout.vue"),
  meta: { hidden: true },
  children: [
    {
      path: "dashboard",
      component: () => import("@/views/shared/Dashboard.vue"),
      meta: { title: "自适应首页", adaptive: true }
    }
  ]
}
```

### 第三步：创建实际文件

#### 1. 设备检测工具 (src/utils/device.ts)

```typescript
import { DeviceEnum } from '@/enums/DeviceEnum';

export class DeviceDetector {
  private static readonly MOBILE_BREAKPOINT = 992;

  static isMobile(): boolean {
    return window.innerWidth < this.MOBILE_BREAKPOINT;
  }

  static getDeviceType(): DeviceEnum {
    return this.isMobile() ? DeviceEnum.MOBILE : DeviceEnum.DESKTOP;
  }

  static onDeviceChange(callback: (type: DeviceEnum) => void) {
    const handleResize = () => callback(this.getDeviceType());
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }
}
```

#### 2. 设备状态管理 (src/composables/useDevice.ts)

```typescript
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { DeviceDetector } from '@/utils/device';
import { useAppStore } from '@/store';

export function useDevice() {
  const appStore = useAppStore();
  
  const isMobile = computed(() => appStore.device === 'mobile');
  const isTablet = computed(() => {
    const width = window.innerWidth;
    return width >= 768 && width < 992;
  });
  const isDesktop = computed(() => appStore.device === 'desktop');

  let cleanup: (() => void) | null = null;

  onMounted(() => {
    cleanup = DeviceDetector.onDeviceChange((type) => {
      appStore.toggleDevice(type);
    });
  });

  onUnmounted(() => {
    cleanup?.();
  });

  return {
    isMobile,
    isTablet,
    isDesktop
  };
}
```

#### 3. 自适应布局容器 (src/layout/AdaptiveLayout.vue)

```vue
<template>
  <div class="adaptive-layout">
    <MobileLayout v-if="isMobile" />
    <component v-else :is="Layout" />
  </div>
</template>

<script setup lang="ts">
import { useDevice } from '@/composables/useDevice';
import MobileLayout from './MobileLayout.vue';

const Layout = () => import('./index.vue');
const { isMobile } = useDevice();
</script>
```

### 第四步：改造现有页面

#### 示例：改造仪表板页面

1. **创建共享仪表板** (src/views/shared/Dashboard.vue)

```vue
<template>
  <div class="shared-dashboard">
    <!-- 移动端布局 -->
    <div v-if="isMobile" class="mobile-dashboard">
      <van-grid :column-num="2" :gutter="10">
        <van-grid-item
          v-for="item in dashboardItems"
          :key="item.id"
          :text="item.title"
          :icon="item.icon"
          @click="handleItemClick(item)"
        />
      </van-grid>
    </div>

    <!-- PC端布局 -->
    <div v-else class="desktop-dashboard">
      <el-row :gutter="20">
        <el-col
          v-for="item in dashboardItems"
          :key="item.id"
          :span="6"
        >
          <el-card @click="handleItemClick(item)">
            <div class="card-content">
              <el-icon><component :is="item.icon" /></el-icon>
              <span>{{ item.title }}</span>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useDevice } from '@/composables/useDevice';

const { isMobile } = useDevice();

const dashboardItems = ref([
  { id: 1, title: '数据统计', icon: 'chart-bar', path: '/statistics' },
  { id: 2, title: '用户管理', icon: 'user', path: '/users' },
  { id: 3, title: '系统设置', icon: 'setting', path: '/settings' },
  { id: 4, title: '消息中心', icon: 'message', path: '/messages' }
]);

const handleItemClick = (item: any) => {
  // 根据设备类型跳转到不同的页面
  const targetPath = isMobile.value ? `/mobile${item.path}` : item.path;
  router.push(targetPath);
};
</script>

<style lang="scss" scoped>
.shared-dashboard {
  padding: 16px;

  .mobile-dashboard {
    .van-grid-item {
      height: 80px;
    }
  }

  .desktop-dashboard {
    .el-card {
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    }

    .card-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
    }
  }
}
</style>
```

### 第五步：测试验证

#### 1. 创建测试页面 (src/views/test/AdaptiveTest.vue)

```vue
<template>
  <div class="adaptive-test">
    <h2>自适应组件测试</h2>
    
    <!-- 设备信息显示 -->
    <div class="device-info">
      <p>当前设备: {{ isMobile ? '移动端' : 'PC端' }}</p>
      <p>屏幕宽度: {{ screenWidth }}px</p>
    </div>

    <!-- 自适应表格测试 -->
    <AdaptiveTable
      :data="tableData"
      :columns="tableColumns"
      @row-click="handleRowClick"
    />

    <!-- 自适应表单测试 -->
    <AdaptiveForm
      v-model="formData"
      :fields="formFields"
      @submit="handleSubmit"
    />
  </div>
</template>

<script setup lang="ts">
import { useDevice } from '@/composables/useDevice';
import AdaptiveTable from '@/components/adaptive/AdaptiveTable.vue';
import AdaptiveForm from '@/components/adaptive/AdaptiveForm.vue';

const { isMobile } = useDevice();
const screenWidth = ref(window.innerWidth);

// 测试数据
const tableData = ref([
  { id: 1, name: '张三', age: 25, status: '正常' },
  { id: 2, name: '李四', age: 30, status: '禁用' }
]);

const tableColumns = ref([
  { prop: 'name', label: '姓名', mobileDisplay: 'title' },
  { prop: 'age', label: '年龄', mobileDisplay: 'label' },
  { prop: 'status', label: '状态', mobileDisplay: 'value' }
]);

const formData = ref({
  name: '',
  email: '',
  phone: ''
});

const formFields = ref([
  { prop: 'name', label: '姓名', type: 'input', required: true },
  { prop: 'email', label: '邮箱', type: 'input', required: true },
  { prop: 'phone', label: '电话', type: 'input' }
]);

// 监听屏幕变化
window.addEventListener('resize', () => {
  screenWidth.value = window.innerWidth;
});

const handleRowClick = (row: any) => {
  console.log('点击行:', row);
};

const handleSubmit = (data: any) => {
  console.log('提交表单:', data);
};
</script>
```

#### 2. 添加测试路由

在 `src/router/index.ts` 中添加：

```typescript
{
  path: "/test",
  component: Layout,
  children: [
    {
      path: "adaptive",
      component: () => import("@/views/test/AdaptiveTest.vue"),
      meta: { title: "自适应测试" }
    }
  ]
}
```

### 第六步：样式优化

#### 创建自适应样式文件 (src/styles/adaptive.scss)

```scss
// 自适应样式
.adaptive-container {
  width: 100%;
  
  // 移动端样式
  @media screen and (max-width: 991px) {
    padding: 12px;
    
    .adaptive-spacing {
      margin-bottom: 16px;
    }
    
    .adaptive-button {
      width: 100%;
      height: 44px;
      font-size: 16px;
    }
  }
  
  // PC端样式
  @media screen and (min-width: 992px) {
    padding: 24px;
    
    .adaptive-spacing {
      margin-bottom: 24px;
    }
    
    .adaptive-button {
      height: 32px;
      font-size: 14px;
    }
  }
}

// 平台特定隐藏
.mobile-only {
  @media screen and (min-width: 992px) {
    display: none !important;
  }
}

.desktop-only {
  @media screen and (max-width: 991px) {
    display: none !important;
  }
}
```

在 `src/styles/index.scss` 中引入：

```scss
@import './adaptive.scss';
```

## 🎯 验证清单

完成上述步骤后，请验证以下功能：

- [ ] 浏览器窗口缩放时设备类型正确切换
- [ ] 移动端显示 Vant 组件，PC端显示 Element Plus 组件
- [ ] 自适应表格在不同设备上显示正确
- [ ] 自适应表单在不同设备上交互正常
- [ ] 路由跳转在不同设备上工作正常
- [ ] 样式在不同设备上显示正确

## 🔧 常见问题解决

### 1. 组件库样式冲突
```scss
// 解决方案：使用作用域样式
.platform-mobile {
  .el-button {
    display: none !important;
  }
}

.platform-desktop {
  .van-button {
    display: none !important;
  }
}
```

### 2. 设备检测不准确
```typescript
// 增加用户代理检测
static isMobileDevice(): boolean {
  const userAgent = navigator.userAgent.toLowerCase();
  const mobileKeywords = ['mobile', 'android', 'iphone'];
  return mobileKeywords.some(keyword => userAgent.includes(keyword));
}
```

### 3. 性能优化
```typescript
// 使用懒加载
const AdaptiveTable = defineAsyncComponent(() => 
  import('@/components/adaptive/AdaptiveTable.vue')
);
```

## 📈 下一步计划

1. **完善更多自适应组件**：按钮、模态框、导航等
2. **优化性能**：代码分割、懒加载
3. **增加动画效果**：页面切换动画
4. **完善测试**：单元测试、E2E测试
5. **文档完善**：组件使用文档

通过以上步骤，您可以在现有项目基础上快速实现移动端和PC端的整合，最小化代码改动，最大化复用现有资源。
