<template>
  <div class="big-screen">
    <!-- 加载动画 -->
    <LoadingAnimation v-if="isLoading" />

    <!-- 顶部标题栏 -->
    <ScreenHeader />

    <!-- 主要内容区域 -->
    <div class="data-main">
      <!-- 左侧面板 -->
      <div class="main-left">
        <AssetCategoryChart />
        <FixedAssetChart />
      </div>

      <!-- 中央面板 -->
      <div class="main-center">
        <AssetOverview />
        <TrendChart />
      </div>

      <!-- 右侧面板 -->
      <div class="main-right">
        <BasicStats />
        <AssetWarning />
        <DepartmentRanking />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import LoadingAnimation from "./components/LoadingAnimation.vue";
import ScreenHeader from "./components/ScreenHeader.vue";
import AssetCategoryChart from "./components/AssetCategoryChart.vue";
import FixedAssetChart from "./components/FixedAssetChart.vue";
import AssetOverview from "./components/AssetOverview.vue";
import TrendChart from "./components/TrendChart.vue";
import BasicStats from "./components/BasicStats.vue";
import AssetWarning from "./components/AssetWarning.vue";
import DepartmentRanking from "./components/DepartmentRanking.vue";

const isLoading = ref(true);

// 监听窗口大小变化
const handleResize = () => {
  // 触发所有图表重新调整大小
  window.dispatchEvent(new Event("dashboard-resize"));
};

onMounted(() => {
  // 模拟加载过程
  setTimeout(() => {
    isLoading.value = false;
  }, 1000);

  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
});
</script>

<style lang="scss" scoped>
// 导入字体
@font-face {
  font-family: "electronicFont";
  src: url("@/assets/fonts/DS-DIGIT.TTF");
}
.big-screen {
  width: 100vw;
  height: 100vh;
  background: url("@/assets/images/dashboard/background1.jpg") no-repeat center center;
  background-size: cover;
  overflow: hidden;
  position: relative;
  font-family: "Microsoft YaHei", sans-serif;
  color: white;

  // 设置根字体大小为视口宽度的1%
  font-size: 1vw;

  .data-main {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    height: 90%;
    margin-top: 2vh;

    .main-left,
    .main-center,
    .main-right {
      height: 100%;
      display: flex;
      flex-direction: column;
      gap: 3%;
    }

    .main-left,
    .main-right {
      width: 25%;
    }

    .main-center {
      width: 45%;
    }
  }

  // 响应式设计
  @media (max-width: 1600px) {
    .data-main {
      .main-left,
      .main-right {
        width: 23%;
      }
      .main-center {
        width: 50%;
      }
    }
  }

  @media (max-width: 1200px) {
    .data-main {
      flex-direction: column;
      height: auto;
      gap: 20px;

      .main-left,
      .main-center,
      .main-right {
        width: 100%;
        height: auto;
        flex-direction: row;
        gap: 20px;

        > * {
          flex: 1;
        }
      }
    }
  }

  @media (max-width: 768px) {
    font-size: 2vw;

    .data-main {
      margin-top: 1vh;
      gap: 10px;

      .main-left,
      .main-center,
      .main-right {
        flex-direction: column;
        gap: 10px;

        > * {
          flex: none;
        }
      }
    }
  }
}
</style>
