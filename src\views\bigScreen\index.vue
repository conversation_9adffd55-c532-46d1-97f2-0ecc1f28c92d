<template>
  <div class="big-screen">
    <!-- 背景装饰 -->
    <BackgroundDecoration />
    
    <!-- 顶部标题栏 -->
    <ScreenHeader />
    
    <!-- 主要内容区域 -->
    <main class="screen-content">
      <!-- 左侧面板 -->
      <section class="left-panel">
        <AssetCategoryChart />
        <FixedAssetChart />
      </section>
      
      <!-- 中央面板 -->
      <section class="center-panel">
        <AssetOverview />
        <TrendChart />
      </section>
      
      <!-- 右侧面板 -->
      <section class="right-panel">
        <BasicStats />
        <AssetWarning />
        <DepartmentRanking />
      </section>
    </main>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted } from 'vue'
import BackgroundDecoration from './components/BackgroundDecoration.vue'
import ScreenHeader from './components/ScreenHeader.vue'
import AssetCategoryChart from './components/AssetCategoryChart.vue'
import FixedAssetChart from './components/FixedAssetChart.vue'
import AssetOverview from './components/AssetOverview.vue'
import TrendChart from './components/TrendChart.vue'
import BasicStats from './components/BasicStats.vue'
import AssetWarning from './components/AssetWarning.vue'
import DepartmentRanking from './components/DepartmentRanking.vue'

// 监听窗口大小变化
const handleResize = () => {
  // 触发所有图表重新调整大小
  window.dispatchEvent(new Event('dashboard-resize'))
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
.big-screen {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0a0e27 0%, #1a1f3a 50%, #0a0e27 100%);
  overflow: hidden;
  position: relative;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  
  .screen-content {
    position: relative;
    z-index: 10;
    height: calc(100vh - 80px);
    display: flex;
    gap: 16px;
    padding: 16px;
    
    .left-panel,
    .right-panel {
      flex: 0 0 320px;
      display: flex;
      flex-direction: column;
      gap: 16px;
    }
    
    .center-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 16px;
    }
  }
  
  // 响应式设计
  @media (max-width: 1600px) {
    .screen-content {
      .left-panel,
      .right-panel {
        flex: 0 0 280px;
      }
    }
  }
  
  @media (max-width: 1200px) {
    .screen-content {
      flex-direction: column;
      
      .left-panel,
      .right-panel {
        flex: none;
        flex-direction: row;
        height: auto;
        
        > * {
          flex: 1;
        }
      }
    }
  }
  
  @media (max-width: 768px) {
    .screen-content {
      padding: 8px;
      height: calc(100vh - 60px);
      gap: 8px;
      
      .left-panel,
      .right-panel {
        flex-direction: column;
        
        > * {
          flex: none;
        }
      }
    }
  }
}
</style>
