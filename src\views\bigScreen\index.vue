<template>
  <div class="big-screen">
    <!-- 背景装饰 -->
    <div class="bg-decoration">
      <div class="grid-bg"></div>
      <div class="particle-bg" ref="particleContainer"></div>
    </div>

    <!-- 顶部标题栏 -->
    <header class="screen-header">
      <div class="header-left">
        <div class="logo">
          <i class="icon-logo"></i>
          <span>资产管理系统</span>
        </div>
      </div>
      <div class="header-center">
        <h1 class="main-title">资产智能驾驶舱</h1>
        <div class="subtitle">Asset Management Dashboard</div>
      </div>
      <div class="header-right">
        <div class="datetime">
          <div class="date">{{ currentDate }}</div>
          <div class="time">{{ currentTime }}</div>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="screen-content">
      <!-- 左侧面板 -->
      <section class="left-panel">
        <!-- 资产分类统计 -->
        <div class="panel-card">
          <div class="card-header">
            <h3>资产分类统计</h3>
            <div class="header-decoration"></div>
          </div>
          <div class="card-content">
            <div ref="assetCategoryChart" class="chart-container"></div>
          </div>
        </div>

        <!-- 固定资产分类统计 -->
        <div class="panel-card">
          <div class="card-header">
            <h3>固定资产分类统计</h3>
            <div class="header-decoration"></div>
          </div>
          <div class="card-content">
            <div ref="fixedAssetChart" class="chart-container"></div>
          </div>
        </div>
      </section>

      <!-- 中央面板 -->
      <section class="center-panel">
        <!-- 资产总览 -->
        <div class="overview-section">
          <div class="overview-card total-assets">
            <div class="card-bg"></div>
            <div class="card-content">
              <div class="asset-circle">
                <div class="circle-progress" :style="{ '--progress': totalProgress }">
                  <div class="circle-inner">
                    <div class="total-number">{{ totalAssets }}</div>
                    <div class="total-label">资产总数量</div>
                    <div class="total-unit">万元</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 四个统计卡片 -->
          <div class="stats-grid">
            <div class="stat-card" v-for="(stat, index) in assetStats" :key="index">
              <div class="stat-bg"></div>
              <div class="stat-content">
                <div class="stat-icon">
                  <i :class="stat.icon"></i>
                </div>
                <div class="stat-info">
                  <div class="stat-label">{{ stat.label }}</div>
                  <div class="stat-value">{{ stat.value }}</div>
                  <div class="stat-unit">{{ stat.unit }}</div>
                </div>
                <div class="stat-trend" :class="stat.trend">
                  <i :class="stat.trendIcon"></i>
                  <span>{{ stat.trendValue }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 趋势图表区域 -->
        <div class="trend-section">
          <div class="panel-card full-width">
            <div class="card-header">
              <h3>资产使用方向统计</h3>
              <div class="chart-tabs">
                <span class="tab active">资产使用</span>
                <span class="tab">资产分布</span>
                <span class="tab">行政部门资产统计</span>
              </div>
              <div class="header-decoration"></div>
            </div>
            <div class="card-content">
              <div ref="trendChart" class="chart-container large"></div>
            </div>
          </div>
        </div>
      </section>

      <!-- 右侧面板 -->
      <section class="right-panel">
        <!-- 资产基础统计 -->
        <div class="panel-card">
          <div class="card-header">
            <h3>资产基础统计</h3>
            <div class="header-decoration"></div>
          </div>
          <div class="card-content">
            <div class="basic-stats">
              <div class="basic-stat-item" v-for="(item, index) in basicStats" :key="index">
                <div class="stat-row">
                  <span class="stat-name">{{ item.name }}</span>
                  <span class="stat-number" :style="{ color: item.color }">{{ item.value }}</span>
                  <span class="stat-change" :class="item.changeType">{{ item.change }}</span>
                </div>
                <div class="stat-bar">
                  <div class="bar-bg"></div>
                  <div
                    class="bar-fill"
                    :style="{ width: item.percentage + '%', backgroundColor: item.color }"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 资产预警 -->
        <div class="panel-card">
          <div class="card-header">
            <h3>资产预警</h3>
            <div class="header-decoration"></div>
          </div>
          <div class="card-content">
            <div class="warning-stats">
              <div class="warning-item" v-for="(warning, index) in warningStats" :key="index">
                <div class="warning-icon" :style="{ backgroundColor: warning.color }">
                  <i :class="warning.icon"></i>
                </div>
                <div class="warning-info">
                  <div class="warning-label">{{ warning.label }}</div>
                  <div class="warning-value">{{ warning.value }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 部门资产金额排行 -->
        <div class="panel-card">
          <div class="card-header">
            <h3>部门资产金额排行</h3>
            <div class="header-decoration"></div>
          </div>
          <div class="card-content">
            <div ref="departmentChart" class="chart-container"></div>
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from "vue";
import * as echarts from "echarts";

// 响应式数据
const currentDate = ref("");
const currentTime = ref("");
const totalAssets = ref("30883.31");
const totalProgress = ref(75);

// 图表实例
const assetCategoryChart = ref(null);
const fixedAssetChart = ref(null);
const trendChart = ref(null);
const departmentChart = ref(null);
const particleContainer = ref(null);

// 统计数据
const assetStats = ref([
  {
    label: "设备资产数量",
    value: "11810",
    unit: "台",
    icon: "icon-device",
    trend: "up",
    trendIcon: "icon-arrow-up",
    trendValue: "+5%",
  },
  {
    label: "设备资产金额",
    value: "30831",
    unit: "万",
    icon: "icon-money",
    trend: "down",
    trendIcon: "icon-arrow-down",
    trendValue: "-2%",
  },
  {
    label: "设备资产数量",
    value: "3780",
    unit: "台",
    icon: "icon-equipment",
    trend: "up",
    trendIcon: "icon-arrow-up",
    trendValue: "+8%",
  },
  {
    label: "设备资产数量",
    value: "2543",
    unit: "台",
    icon: "icon-asset",
    trend: "up",
    trendIcon: "icon-arrow-up",
    trendValue: "+3%",
  },
]);

const basicStats = ref([
  {
    name: "数字化资产",
    value: "42867万(15.4%)",
    change: "-5%",
    changeType: "down",
    color: "#00D4FF",
    percentage: 85,
  },
  {
    name: "固定资产",
    value: "16877万(7.0%)",
    change: "-9%",
    changeType: "down",
    color: "#00FF88",
    percentage: 70,
  },
  {
    name: "无形资产",
    value: "32475万(4.8%)",
    change: "+27%",
    changeType: "up",
    color: "#FFB800",
    percentage: 90,
  },
]);

const warningStats = ref([
  {
    label: "资产超期预警",
    value: "180",
    icon: "icon-warning",
    color: "#FF6B6B",
  },
  {
    label: "资产单点预警",
    value: "9833",
    icon: "icon-alert",
    color: "#4ECDC4",
  },
  {
    label: "资产单点预警",
    value: "8",
    icon: "icon-danger",
    color: "#45B7D1",
  },
  {
    label: "公物仓预警",
    value: "8",
    icon: "icon-storage",
    color: "#96CEB4",
  },
]);

// 时间更新
const updateTime = () => {
  const now = new Date();
  currentDate.value = now
    .toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
    })
    .replace(/\//g, ".");
  currentTime.value = now.toLocaleTimeString("zh-CN", {
    hour12: false,
  });
};

// 定时器
let timeInterval = null;

onMounted(async () => {
  updateTime();
  timeInterval = setInterval(updateTime, 1000);

  await nextTick();
  initCharts();
  initParticles();

  // 监听窗口大小变化
  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval);
  }
  window.removeEventListener("resize", handleResize);
});

// 窗口大小变化处理
const handleResize = () => {
  // 重新调整图表大小
  if (assetCategoryChart.value) assetCategoryChart.value.resize();
  if (fixedAssetChart.value) fixedAssetChart.value.resize();
  if (trendChart.value) trendChart.value.resize();
  if (departmentChart.value) departmentChart.value.resize();
};

// 初始化图表
const initCharts = () => {
  initAssetCategoryChart();
  initFixedAssetChart();
  initTrendChart();
  initDepartmentChart();
};

// 资产分类统计图表
const initAssetCategoryChart = () => {
  const chartDom = document.querySelector('[ref="assetCategoryChart"]');
  if (!chartDom) return;

  const chart = echarts.init(chartDom);
  assetCategoryChart.value = chart;

  const option = {
    backgroundColor: "transparent",
    tooltip: {
      trigger: "item",
      backgroundColor: "rgba(0, 20, 40, 0.8)",
      borderColor: "#00D4FF",
      textStyle: { color: "#fff" },
    },
    legend: {
      orient: "vertical",
      right: "10%",
      top: "center",
      textStyle: { color: "#fff", fontSize: 12 },
      itemWidth: 10,
      itemHeight: 10,
    },
    series: [
      {
        type: "pie",
        radius: ["40%", "70%"],
        center: ["35%", "50%"],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 5,
          borderColor: "#001428",
          borderWidth: 2,
        },
        label: { show: false },
        emphasis: {
          label: {
            show: true,
            fontSize: 14,
            fontWeight: "bold",
            color: "#fff",
          },
        },
        data: [
          { value: 1048, name: "房屋及构筑物", itemStyle: { color: "#00D4FF" } },
          { value: 735, name: "设备", itemStyle: { color: "#00FF88" } },
          { value: 580, name: "文物", itemStyle: { color: "#FFB800" } },
          { value: 484, name: "生物", itemStyle: { color: "#FF6B6B" } },
          { value: 300, name: "图书档案", itemStyle: { color: "#8B5CF6" } },
          { value: 300, name: "软件及信息系统", itemStyle: { color: "#06D6A0" } },
        ],
      },
    ],
  };

  chart.setOption(option);
};

// 固定资产分类统计图表
const initFixedAssetChart = () => {
  const chartDom = document.querySelector('[ref="fixedAssetChart"]');
  if (!chartDom) return;

  const chart = echarts.init(chartDom);
  fixedAssetChart.value = chart;

  const option = {
    backgroundColor: "transparent",
    tooltip: {
      trigger: "item",
      backgroundColor: "rgba(0, 20, 40, 0.8)",
      borderColor: "#00D4FF",
      textStyle: { color: "#fff" },
    },
    series: [
      {
        type: "pie",
        radius: "70%",
        center: ["50%", "50%"],
        data: [
          { value: 14955, name: "房屋及构筑物", itemStyle: { color: "#FFB800" } },
          { value: 6844, name: "设备", itemStyle: { color: "#00D4FF" } },
          { value: 5954, name: "文物和陈列品", itemStyle: { color: "#00FF88" } },
          { value: 796, name: "图书档案", itemStyle: { color: "#FF6B6B" } },
          { value: 1154, name: "家具用具", itemStyle: { color: "#8B5CF6" } },
          { value: 298, name: "其他", itemStyle: { color: "#06D6A0" } },
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
        label: {
          show: true,
          position: "outside",
          color: "#fff",
          fontSize: 10,
        },
        labelLine: {
          show: true,
          lineStyle: { color: "#fff" },
        },
      },
    ],
  };

  chart.setOption(option);
};

// 趋势图表
const initTrendChart = () => {
  const chartDom = document.querySelector('[ref="trendChart"]');
  if (!chartDom) return;

  const chart = echarts.init(chartDom);
  trendChart.value = chart;

  const months = [
    "数字",
    "行政",
    "科研",
    "生产",
    "生活",
    "技术开发",
    "出租",
    "经营",
    "其他",
    "社会公益",
    "其他",
  ];
  const assetData = [60000, 45000, 38000, 42000, 35000, 28000, 32000, 25000, 30000, 22000, 18000];
  const equipmentData = [
    200000, 180000, 160000, 150000, 140000, 120000, 110000, 100000, 95000, 85000, 75000,
  ];

  const option = {
    backgroundColor: "transparent",
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(0, 20, 40, 0.8)",
      borderColor: "#00D4FF",
      textStyle: { color: "#fff" },
    },
    legend: {
      data: ["资产数量", "设备数量"],
      textStyle: { color: "#fff" },
      top: 20,
      right: 50,
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      top: "15%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: months,
      axisLine: { lineStyle: { color: "#1E3A8A" } },
      axisLabel: { color: "#fff", fontSize: 10 },
      splitLine: { show: false },
    },
    yAxis: [
      {
        type: "value",
        name: "资产数量",
        position: "left",
        axisLine: { lineStyle: { color: "#1E3A8A" } },
        axisLabel: { color: "#fff", fontSize: 10 },
        splitLine: { lineStyle: { color: "#1E3A8A", type: "dashed" } },
      },
      {
        type: "value",
        name: "设备数量",
        position: "right",
        axisLine: { lineStyle: { color: "#1E3A8A" } },
        axisLabel: { color: "#fff", fontSize: 10 },
        splitLine: { show: false },
      },
    ],
    series: [
      {
        name: "资产数量",
        type: "line",
        yAxisIndex: 0,
        data: assetData,
        smooth: true,
        lineStyle: { color: "#00D4FF", width: 2 },
        itemStyle: { color: "#00D4FF" },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "rgba(0, 212, 255, 0.3)" },
            { offset: 1, color: "rgba(0, 212, 255, 0.1)" },
          ]),
        },
      },
      {
        name: "设备数量",
        type: "line",
        yAxisIndex: 1,
        data: equipmentData,
        smooth: true,
        lineStyle: { color: "#00FF88", width: 2 },
        itemStyle: { color: "#00FF88" },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "rgba(0, 255, 136, 0.3)" },
            { offset: 1, color: "rgba(0, 255, 136, 0.1)" },
          ]),
        },
      },
    ],
  };

  chart.setOption(option);
};

// 部门资产排行图表
const initDepartmentChart = () => {
  const chartDom = document.querySelector('[ref="departmentChart"]');
  if (!chartDom) return;

  const chart = echarts.init(chartDom);
  departmentChart.value = chart;

  const departments = ["校园建设部", "中国海大", "基础教育部", "总务处", "医中心"];
  const values = [1613897, 634479, 123137, 92237, 47437];

  const option = {
    backgroundColor: "transparent",
    tooltip: {
      trigger: "axis",
      axisPointer: { type: "shadow" },
      backgroundColor: "rgba(0, 20, 40, 0.8)",
      borderColor: "#00D4FF",
      textStyle: { color: "#fff" },
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      top: "10%",
      containLabel: true,
    },
    xAxis: {
      type: "value",
      axisLine: { lineStyle: { color: "#1E3A8A" } },
      axisLabel: { color: "#fff", fontSize: 10 },
      splitLine: { lineStyle: { color: "#1E3A8A", type: "dashed" } },
    },
    yAxis: {
      type: "category",
      data: departments,
      axisLine: { lineStyle: { color: "#1E3A8A" } },
      axisLabel: { color: "#fff", fontSize: 10 },
      splitLine: { show: false },
    },
    series: [
      {
        type: "bar",
        data: values.map((value, index) => ({
          value,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: ["#FFB800", "#00D4FF", "#00FF88", "#FF6B6B", "#8B5CF6"][index] },
              { offset: 1, color: ["#FF8C00", "#0099CC", "#00CC66", "#CC4444", "#6633CC"][index] },
            ]),
          },
        })),
        barWidth: "60%",
        label: {
          show: true,
          position: "right",
          color: "#fff",
          fontSize: 10,
        },
      },
    ],
  };

  chart.setOption(option);
};

// 初始化粒子效果
const initParticles = () => {
  const container = particleContainer.value;
  if (!container) return;

  // 创建粒子
  for (let i = 0; i < 50; i++) {
    const particle = document.createElement("div");
    particle.className = "particle";
    particle.style.left = Math.random() * 100 + "%";
    particle.style.top = Math.random() * 100 + "%";
    particle.style.animationDelay = Math.random() * 20 + "s";
    particle.style.animationDuration = Math.random() * 10 + 10 + "s";
    container.appendChild(particle);
  }
};
</script>

<style lang="scss" scoped>
.big-screen {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0c1426 0%, #1a2332 50%, #0c1426 100%);
  overflow: hidden;
  position: relative;
  font-family: "Microsoft YaHei", sans-serif;

  // 背景装饰
  .bg-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;

    .grid-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image:
        linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px);
      background-size: 50px 50px;
      animation: gridMove 20s linear infinite;
    }

    .particle-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;

      .particle {
        position: absolute;
        width: 2px;
        height: 2px;
        background: #00d4ff;
        border-radius: 50%;
        animation: float linear infinite;
        opacity: 0.6;

        &::before {
          content: "";
          position: absolute;
          top: -1px;
          left: -1px;
          width: 4px;
          height: 4px;
          background: radial-gradient(circle, #00d4ff 0%, transparent 70%);
          border-radius: 50%;
        }
      }
    }
  }

  // 顶部标题栏
  .screen-header {
    position: relative;
    z-index: 10;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 40px;
    background: linear-gradient(90deg, rgba(0, 212, 255, 0.1) 0%, rgba(0, 255, 136, 0.1) 100%);
    border-bottom: 2px solid rgba(0, 212, 255, 0.3);
    backdrop-filter: blur(10px);

    .header-left {
      .logo {
        display: flex;
        align-items: center;
        color: #00d4ff;
        font-size: 18px;
        font-weight: 600;

        .icon-logo {
          width: 32px;
          height: 32px;
          margin-right: 12px;
          background: linear-gradient(45deg, #00d4ff, #00ff88);
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;

          &::before {
            content: "🏢";
            font-size: 16px;
          }
        }
      }
    }

    .header-center {
      text-align: center;

      .main-title {
        font-size: 36px;
        font-weight: 700;
        background: linear-gradient(90deg, #00d4ff 0%, #00ff88 50%, #ffb800 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin: 0;
        text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
      }

      .subtitle {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        margin-top: 4px;
        letter-spacing: 2px;
      }
    }

    .header-right {
      .datetime {
        text-align: right;
        color: #fff;

        .date {
          font-size: 16px;
          font-weight: 500;
          margin-bottom: 4px;
        }

        .time {
          font-size: 20px;
          font-weight: 700;
          color: #00d4ff;
          font-family: "Courier New", monospace;
        }
      }
    }
  }

  // 主要内容区域
  .screen-content {
    position: relative;
    z-index: 10;
    height: calc(100vh - 80px);
    display: flex;
    gap: 20px;
    padding: 20px;

    // 左侧面板
    .left-panel {
      flex: 0 0 320px;
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    // 中央面板
    .center-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 20px;

      .overview-section {
        display: flex;
        gap: 20px;
        height: 280px;

        .total-assets {
          flex: 0 0 280px;
          position: relative;

          .card-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(
              135deg,
              rgba(0, 212, 255, 0.1) 0%,
              rgba(0, 255, 136, 0.1) 100%
            );
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 12px;
            backdrop-filter: blur(10px);
          }

          .card-content {
            position: relative;
            z-index: 2;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;

            .asset-circle {
              .circle-progress {
                width: 200px;
                height: 200px;
                border-radius: 50%;
                background: conic-gradient(
                  from 0deg,
                  #00d4ff 0deg,
                  #00ff88 calc(var(--progress) * 3.6deg),
                  rgba(255, 255, 255, 0.1) calc(var(--progress) * 3.6deg),
                  rgba(255, 255, 255, 0.1) 360deg
                );
                display: flex;
                align-items: center;
                justify-content: center;
                position: relative;

                &::before {
                  content: "";
                  position: absolute;
                  width: 160px;
                  height: 160px;
                  background: #0c1426;
                  border-radius: 50%;
                  z-index: 1;
                }

                .circle-inner {
                  position: relative;
                  z-index: 2;
                  text-align: center;
                  color: #fff;

                  .total-number {
                    font-size: 32px;
                    font-weight: 700;
                    color: #00d4ff;
                    margin-bottom: 8px;
                  }

                  .total-label {
                    font-size: 14px;
                    color: rgba(255, 255, 255, 0.8);
                    margin-bottom: 4px;
                  }

                  .total-unit {
                    font-size: 12px;
                    color: rgba(255, 255, 255, 0.6);
                  }
                }
              }
            }
          }
        }

        .stats-grid {
          flex: 1;
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 20px;

          .stat-card {
            position: relative;

            .stat-bg {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background: linear-gradient(
                135deg,
                rgba(0, 212, 255, 0.05) 0%,
                rgba(0, 255, 136, 0.05) 100%
              );
              border: 1px solid rgba(0, 212, 255, 0.2);
              border-radius: 8px;
              backdrop-filter: blur(5px);
            }

            .stat-content {
              position: relative;
              z-index: 2;
              padding: 20px;
              height: 100%;
              display: flex;
              flex-direction: column;
              justify-content: space-between;

              .stat-icon {
                width: 40px;
                height: 40px;
                background: linear-gradient(45deg, #00d4ff, #00ff88);
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-bottom: 12px;

                i {
                  font-size: 20px;
                  color: #fff;

                  &.icon-device::before {
                    content: "💻";
                  }
                  &.icon-money::before {
                    content: "💰";
                  }
                  &.icon-equipment::before {
                    content: "⚙️";
                  }
                  &.icon-asset::before {
                    content: "📊";
                  }
                }
              }

              .stat-info {
                .stat-label {
                  font-size: 12px;
                  color: rgba(255, 255, 255, 0.7);
                  margin-bottom: 8px;
                }

                .stat-value {
                  font-size: 24px;
                  font-weight: 700;
                  color: #fff;
                  margin-bottom: 4px;
                }

                .stat-unit {
                  font-size: 12px;
                  color: rgba(255, 255, 255, 0.6);
                }
              }

              .stat-trend {
                display: flex;
                align-items: center;
                font-size: 12px;

                &.up {
                  color: #00ff88;

                  i.icon-arrow-up::before {
                    content: "↗";
                  }
                }

                &.down {
                  color: #ff6b6b;

                  i.icon-arrow-down::before {
                    content: "↘";
                  }
                }

                i {
                  margin-right: 4px;
                }
              }
            }
          }
        }
      }

      .trend-section {
        flex: 1;

        .full-width {
          height: 100%;
        }
      }
    }

    // 右侧面板
    .right-panel {
      flex: 0 0 320px;
      display: flex;
      flex-direction: column;
      gap: 20px;
    }
  }

  // 面板卡片通用样式
  .panel-card {
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.05) 0%, rgba(0, 255, 136, 0.05) 100%);
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    overflow: hidden;

    .card-header {
      padding: 16px 20px;
      border-bottom: 1px solid rgba(0, 212, 255, 0.2);
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #fff;
      }

      .chart-tabs {
        display: flex;
        gap: 20px;

        .tab {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.6);
          cursor: pointer;
          transition: color 0.3s;

          &.active {
            color: #00d4ff;
          }

          &:hover {
            color: #00ff88;
          }
        }
      }

      .header-decoration {
        position: absolute;
        bottom: 0;
        left: 20px;
        width: 60px;
        height: 2px;
        background: linear-gradient(90deg, #00d4ff, #00ff88);
      }
    }

    .card-content {
      padding: 20px;

      .chart-container {
        width: 100%;
        height: 200px;

        &.large {
          height: 300px;
        }
      }
    }
  }

  // 基础统计样式
  .basic-stats {
    .basic-stat-item {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      .stat-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;

        .stat-name {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.8);
        }

        .stat-number {
          font-size: 14px;
          font-weight: 600;
        }

        .stat-change {
          font-size: 12px;

          &.up {
            color: #00ff88;
          }

          &.down {
            color: #ff6b6b;
          }
        }
      }

      .stat-bar {
        position: relative;
        height: 4px;

        .bar-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 2px;
        }

        .bar-fill {
          position: absolute;
          top: 0;
          left: 0;
          height: 100%;
          border-radius: 2px;
          transition: width 0.8s ease;
        }
      }
    }
  }

  // 预警统计样式
  .warning-stats {
    .warning-item {
      display: flex;
      align-items: center;
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .warning-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;

        i {
          font-size: 16px;
          color: #fff;

          &.icon-warning::before {
            content: "⚠️";
          }
          &.icon-alert::before {
            content: "🚨";
          }
          &.icon-danger::before {
            content: "⚡";
          }
          &.icon-storage::before {
            content: "📦";
          }
        }
      }

      .warning-info {
        flex: 1;

        .warning-label {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.7);
          margin-bottom: 4px;
        }

        .warning-value {
          font-size: 18px;
          font-weight: 700;
          color: #fff;
        }
      }
    }
  }

  // 动画效果
  @keyframes gridMove {
    0% {
      transform: translate(0, 0);
    }
    100% {
      transform: translate(50px, 50px);
    }
  }

  @keyframes float {
    0% {
      transform: translateY(100vh) translateX(0);
      opacity: 0;
    }
    10% {
      opacity: 1;
    }
    90% {
      opacity: 1;
    }
    100% {
      transform: translateY(-100px) translateX(100px);
      opacity: 0;
    }
  }

  // 响应式设计
  @media (max-width: 1600px) {
    .screen-content {
      .left-panel,
      .right-panel {
        flex: 0 0 280px;
      }
    }

    .screen-header {
      .header-center {
        .main-title {
          font-size: 28px;
        }
      }
    }
  }

  @media (max-width: 1200px) {
    .screen-content {
      flex-direction: column;

      .left-panel,
      .right-panel {
        flex: none;
        flex-direction: row;
        height: auto;

        .panel-card {
          flex: 1;
        }
      }

      .center-panel {
        .overview-section {
          flex-direction: column;
          height: auto;

          .total-assets {
            flex: none;
            height: 200px;

            .circle-progress {
              width: 150px !important;
              height: 150px !important;

              &::before {
                width: 120px !important;
                height: 120px !important;
              }

              .circle-inner {
                .total-number {
                  font-size: 24px;
                }
              }
            }
          }

          .stats-grid {
            grid-template-columns: repeat(4, 1fr);
          }
        }
      }
    }
  }

  @media (max-width: 768px) {
    .screen-header {
      padding: 0 20px;
      height: 60px;

      .header-left,
      .header-right {
        display: none;
      }

      .header-center {
        .main-title {
          font-size: 20px;
        }

        .subtitle {
          font-size: 12px;
        }
      }
    }

    .screen-content {
      padding: 10px;
      height: calc(100vh - 60px);

      .overview-section {
        .stats-grid {
          grid-template-columns: 1fr 1fr;
        }
      }
    }
  }
}
</style>
