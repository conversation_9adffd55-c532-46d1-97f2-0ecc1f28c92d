.hljs {
  display: block;
  color: #F6F4F2;
}

.hljs-comment,
.hljs-quote {
  color: #A2A09F;
  font-style: italic;
}

.hljs-doctag,
.hljs-keyword,
.hljs-formula {
  color: #B08CFF;
}

.hljs-section,
.hljs-name,
.hljs-selector-tag,
.hljs-deletion,
.hljs-subst {
  color: #FF7C72;
}

.hljs-literal {
  color: #1CE2B2;
}

.hljs-string,
.hljs-regexp,
.hljs-addition,
.hljs-attribute,
.hljs-attr,
.hljs-meta-string {
  color: #A6FF8F;
}

.hljs-built_in,
.hljs-class .hljs-title {
  color: #FBF38C;
}

.hljs-variable,
.hljs-template-variable,
.hljs-type,
.hljs-selector-class,
.hljs-selector-attr,
.hljs-selector-pseudo,
.hljs-number {
  color: #FF7C72;
}

.hljs-symbol,
.hljs-bullet,
.hljs-link,
.hljs-meta,
.hljs-selector-id,
.hljs-title {
  color: #61C3FF;
}

.hljs-emphasis {
  font-style: italic;
}

.hljs-strong {
  font-weight: bold;
}

.hljs-link {
  text-decoration: underline;
}
