<!DOCTYPE html>
<html>
<head>
  <title>DOM stress test | anime.js</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <meta property="og:title" content="anime.js">
  <meta property="og:url" content="https://animejs.com">
  <meta property="og:description" content="Javascript Animation Engine">
  <meta property="og:image" content="https://animejs.com/documentation/assets/img/icons/og.png">
  <meta name="twitter:card" content="summary">
  <meta name="twitter:title" content="anime.js">
  <meta name="twitter:site" content="@juliangarnier">
  <meta name="twitter:description" content="Javascript Animation Engine">
  <meta name="twitter:image" content="https://animejs.com/documentation/assets/img/icons/twitter.png">
  <link rel="apple-touch-icon-precomposed" href="../assets/img/social-media-image.png">
  <link rel="icon" type="image/png" href="../assets/img/favicon.png" >
  <link href="../assets/css/animejs.css" rel="stylesheet">
  <link href="../assets/css/documentation.css" rel="stylesheet">
  <script src="../../lib/anime.min.js"></script>
  <!-- <script src="../assets/js/anime/anime.2.0.2.js"></script> -->
  <!-- <script src="../assets/js/anime/anime.1.0.js"></script> -->
  <style>

    .el {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 1em;
      height: 1em;
      margin: -.5em 0 0 -.5em;
      font-size: 20px;
    }

  </style>
</head>
<body></body>
<script>

  var wrapperEl = document.body;
  var numberOfEls = 500;
  var duration = numberOfEls * 10;
  var radius = window.innerWidth < window.innerHeight ? window.innerWidth : window.innerHeight;
  var distance = (radius / 4 <= 150 ? 150 : radius / 2.5) / 16; 

  function createEl(i) {
    var el = document.createElement('div');
    var hue = Math.round(360 / numberOfEls * i);
    el.classList.add('el');
    //el.style.border = '5px solid hsl(' + hue + ', 50%, 50%)';
    el.style.backgroundColor = 'hsl(' + hue + ', 50%, 50%)';
    wrapperEl.appendChild(el);
    anime({
      targets: el,
      translateX: Math.sin(i) * distance + 'rem',
      translateY: Math.cos(i) * distance + 'rem',
      scale: [
        {value: [.1, 2]},
        {value: .1}
      ],
      easing: 'easeInOutSine',
      loop: true,
      duration: duration,
      delay: i * (duration / numberOfEls)
    });
  };

  for (var i = 0; i < numberOfEls; i++) createEl(i);

  </script>
  <script src="../assets/js/vendors/stats.min.js"></script>
</html>
