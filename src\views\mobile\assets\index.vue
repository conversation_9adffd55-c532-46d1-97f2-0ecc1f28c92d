<template>
  <div class="mobile-assets">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="我的资产"
      left-arrow
      fixed
      placeholder
      @click-left="goBack"
    >
      <template #right>
        <van-icon name="filter-o" @click="showFilter = true" />
      </template>
    </van-nav-bar>
    
    <!-- 搜索框 -->
    <div class="search-bar">
      <van-search
        v-model="searchValue"
        placeholder="请输入资产名称"
        shape="round"
        show-action
        @search="onSearch"
      >
        <template #action>
          <div @click="onSearch">搜索</div>
        </template>
      </van-search>
    </div>
    
    <!-- 统计卡片 -->
    <div class="stats-card">
      <div class="stat-item">
        <div class="stat-value">{{ totalAssets }}</div>
        <div class="stat-label">总资产</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">{{ normalAssets }}</div>
        <div class="stat-label">正常</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">{{ warningAssets }}</div>
        <div class="stat-label">预警</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">{{ totalValue }}</div>
        <div class="stat-label">总价值(万元)</div>
      </div>
    </div>
    
    <!-- 资产列表 -->
    <div class="assets-list">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <div 
            v-for="(item, index) in assetsList" 
            :key="index" 
            class="asset-item"
            @click="goToDetail(item)"
          >
            <div class="asset-header">
              <div class="asset-title">
                <span class="asset-code">[{{ item.code }}]</span>
                {{ item.name }}
              </div>
              <div class="asset-status" :class="`status-${item.status}`">
                {{ getStatusText(item.status) }}
              </div>
            </div>
            <div class="asset-info">
              <div class="info-row">
                <span class="label">资产分类:</span>
                <span class="value">{{ item.category }}</span>
              </div>
              <div class="info-row">
                <span class="label">存放位置:</span>
                <span class="value">{{ item.location }}</span>
              </div>
              <div class="info-row">
                <span class="label">购置日期:</span>
                <span class="value">{{ item.purchaseDate }}</span>
              </div>
              <div class="info-row">
                <span class="label">资产价值:</span>
                <span class="value price">¥ {{ item.value }}</span>
              </div>
            </div>
            <div class="asset-footer">
              <van-button 
                size="small" 
                type="primary" 
                plain
                @click.stop="scanAsset(item)"
              >
                扫码
              </van-button>
              <van-button 
                size="small" 
                type="info" 
                plain
                @click.stop="editAsset(item)"
              >
                编辑
              </van-button>
              <van-button 
                size="small" 
                type="warning" 
                plain
                @click.stop="reportAsset(item)"
              >
                报修
              </van-button>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
    
    <!-- 筛选弹出层 -->
    <van-popup v-model:show="showFilter" position="right" :style="{ width: '80%', height: '100%' }">
      <div class="filter-panel">
        <div class="filter-header">
          <span>筛选条件</span>
          <van-icon name="cross" @click="showFilter = false" />
        </div>
        <div class="filter-content">
          <van-cell-group title="资产状态">
            <van-checkbox-group v-model="filterStatus">
              <van-cell clickable @click="toggleStatus('normal')">
                <template #title>
                  <van-checkbox name="normal">正常</van-checkbox>
                </template>
              </van-cell>
              <van-cell clickable @click="toggleStatus('warning')">
                <template #title>
                  <van-checkbox name="warning">预警</van-checkbox>
                </template>
              </van-cell>
              <van-cell clickable @click="toggleStatus('repair')">
                <template #title>
                  <van-checkbox name="repair">维修中</van-checkbox>
                </template>
              </van-cell>
            </van-checkbox-group>
          </van-cell-group>
          
          <van-cell-group title="资产分类">
            <van-checkbox-group v-model="filterCategory">
              <van-cell clickable @click="toggleCategory('computer')">
                <template #title>
                  <van-checkbox name="computer">计算机设备</van-checkbox>
                </template>
              </van-cell>
              <van-cell clickable @click="toggleCategory('furniture')">
                <template #title>
                  <van-checkbox name="furniture">办公家具</van-checkbox>
                </template>
              </van-cell>
              <van-cell clickable @click="toggleCategory('equipment')">
                <template #title>
                  <van-checkbox name="equipment">办公设备</van-checkbox>
                </template>
              </van-cell>
            </van-checkbox-group>
          </van-cell-group>
        </div>
        <div class="filter-footer">
          <van-button block @click="resetFilter">重置</van-button>
          <van-button type="primary" block @click="applyFilter">确定</van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();
const searchValue = ref('');
const refreshing = ref(false);
const loading = ref(false);
const finished = ref(false);
const showFilter = ref(false);
const filterStatus = ref([]);
const filterCategory = ref([]);

// 统计数据
const totalAssets = ref(156);
const normalAssets = ref(142);
const warningAssets = ref(14);
const totalValue = ref(89.6);

// 资产列表
const assetsList = ref([
  {
    code: 'ZC202501001',
    name: '联想ThinkPad笔记本电脑',
    status: 'normal',
    category: '计算机设备',
    location: '办公室A-201',
    purchaseDate: '2024-03-15',
    value: '5,699.00'
  },
  {
    code: 'ZC202501002',
    name: '惠普LaserJet打印机',
    status: 'warning',
    category: '办公设备',
    location: '办公室A-203',
    purchaseDate: '2024-02-20',
    value: '2,399.00'
  },
  {
    code: 'ZC202501003',
    name: '人体工学办公椅',
    status: 'normal',
    category: '办公家具',
    location: '办公室A-201',
    purchaseDate: '2024-01-10',
    value: '899.00'
  },
  {
    code: 'ZC202501004',
    name: '爱普生投影仪',
    status: 'repair',
    category: '办公设备',
    location: '会议室B-301',
    purchaseDate: '2023-12-05',
    value: '4,599.00'
  }
]);

// 返回上一页
const goBack = () => {
  router.go(-1);
};

// 搜索
const onSearch = () => {
  console.log('搜索:', searchValue.value);
  // 实现搜索逻辑
};

// 下拉刷新
const onRefresh = () => {
  setTimeout(() => {
    refreshing.value = false;
    // 重置列表数据
    finished.value = false;
    assetsList.value = [...assetsList.value];
  }, 1000);
};

// 加载更多
const onLoad = () => {
  setTimeout(() => {
    // 模拟加载更多数据
    loading.value = false;
    finished.value = true;
  }, 1000);
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'normal': '正常',
    'warning': '预警',
    'repair': '维修中'
  };
  return statusMap[status] || '未知状态';
};

// 跳转到详情页
const goToDetail = (item) => {
  console.log('跳转到详情页:', item);
  // router.push(`/mobile/assets/detail/${item.code}`);
};

// 扫码资产
const scanAsset = (item) => {
  console.log('扫码资产:', item);
  // 调用扫码功能
};

// 编辑资产
const editAsset = (item) => {
  console.log('编辑资产:', item);
  // router.push(`/mobile/assets/edit/${item.code}`);
};

// 报修资产
const reportAsset = (item) => {
  console.log('报修资产:', item);
  // router.push(`/mobile/assets/repair/${item.code}`);
};

// 切换状态筛选
const toggleStatus = (status) => {
  const index = filterStatus.value.indexOf(status);
  if (index > -1) {
    filterStatus.value.splice(index, 1);
  } else {
    filterStatus.value.push(status);
  }
};

// 切换分类筛选
const toggleCategory = (category) => {
  const index = filterCategory.value.indexOf(category);
  if (index > -1) {
    filterCategory.value.splice(index, 1);
  } else {
    filterCategory.value.push(category);
  }
};

// 重置筛选
const resetFilter = () => {
  filterStatus.value = [];
  filterCategory.value = [];
};

// 应用筛选
const applyFilter = () => {
  console.log('应用筛选:', { status: filterStatus.value, category: filterCategory.value });
  showFilter.value = false;
  // 实现筛选逻辑
};
</script>

<style lang="scss" scoped>
.mobile-assets {
  min-height: 100vh;
  background-color: #f7f8fa;
  
  .search-bar {
    padding: 8px 0;
  }
  
  .stats-card {
    margin: 12px;
    padding: 16px;
    background-color: #fff;
    border-radius: 8px;
    display: flex;
    justify-content: space-around;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    
    .stat-item {
      text-align: center;
      
      .stat-value {
        font-size: 18px;
        font-weight: 600;
        color: #1989fa;
        margin-bottom: 4px;
      }
      
      .stat-label {
        font-size: 12px;
        color: #666;
      }
    }
  }
  
  .assets-list {
    padding: 0 12px;
    
    .asset-item {
      margin-bottom: 12px;
      background-color: #fff;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      
      .asset-header {
        padding: 12px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #f5f5f5;
        
        .asset-title {
          flex: 1;
          font-size: 15px;
          font-weight: 500;
          
          .asset-code {
            color: #1989fa;
            margin-right: 4px;
          }
        }
        
        .asset-status {
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 12px;
          
          &.status-normal {
            background-color: #f0f9ff;
            color: #07c160;
          }
          
          &.status-warning {
            background-color: #fff7e6;
            color: #ff976a;
          }
          
          &.status-repair {
            background-color: #fff1f0;
            color: #ee0a24;
          }
        }
      }
      
      .asset-info {
        padding: 12px;
        
        .info-row {
          display: flex;
          margin-bottom: 8px;
          font-size: 14px;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .label {
            color: #666;
            width: 80px;
          }
          
          .value {
            flex: 1;
            color: #333;
            
            &.price {
              color: #ee0a24;
              font-weight: 500;
            }
          }
        }
      }
      
      .asset-footer {
        padding: 12px;
        display: flex;
        justify-content: flex-end;
        gap: 8px;
        border-top: 1px solid #f5f5f5;
      }
    }
  }
  
  .filter-panel {
    height: 100%;
    display: flex;
    flex-direction: column;
    
    .filter-header {
      padding: 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #f5f5f5;
      font-weight: 500;
    }
    
    .filter-content {
      flex: 1;
      overflow-y: auto;
    }
    
    .filter-footer {
      padding: 16px;
      display: flex;
      gap: 12px;
      border-top: 1px solid #f5f5f5;
    }
  }
}

:deep(.van-nav-bar) {
  background-color: #1989fa;
  
  .van-nav-bar__title {
    color: #fff;
  }
  
  .van-icon {
    color: #fff;
  }
}

:deep(.van-search__action) {
  color: #1989fa;
}
</style>
