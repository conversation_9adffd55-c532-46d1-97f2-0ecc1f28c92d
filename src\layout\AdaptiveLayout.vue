<template>
  <div class="adaptive-layout" :class="layoutClasses">
    <!-- 动态加载对应的布局组件 -->
    <component 
      :is="currentLayoutComponent" 
      v-bind="layoutProps"
      @layout-event="handleLayoutEvent"
    />
  </div>
</template>

<script setup lang="ts">
import { defineAsyncComponent } from 'vue';
import { useDevice } from '@/composables/useDevice';
import { useAppStore } from '@/store';
import { DeviceEnum } from '@/enums/DeviceEnum';

defineOptions({
  name: "AdaptiveLayout",
});

const { isMobile, isTablet, layoutConfig } = useDevice();
const appStore = useAppStore();

// 动态选择布局组件
const currentLayoutComponent = computed(() => {
  if (isMobile.value) {
    return defineAsyncComponent(() => import('./MobileLayout.vue'));
  } else {
    return defineAsyncComponent(() => import('./index.vue'));
  }
});

// 布局样式类
const layoutClasses = computed(() => ({
  'adaptive-layout--mobile': isMobile.value,
  'adaptive-layout--tablet': isTablet.value,
  'adaptive-layout--desktop': !isMobile.value && !isTablet.value,
  'adaptive-layout--sidebar-open': appStore.sidebar.opened,
  'adaptive-layout--landscape': layoutConfig.value.orientation === 'landscape',
  'adaptive-layout--portrait': layoutConfig.value.orientation === 'portrait'
}));

// 传递给子布局的属性
const layoutProps = computed(() => {
  return {
    adaptive: true,
    deviceType: appStore.device,
    layoutConfig: layoutConfig.value,
    // 布局控制属性
    showSidebar: layoutConfig.value.showSidebar,
    showTabbar: layoutConfig.value.showTabbar,
    showNavbar: layoutConfig.value.showNavbar,
    // 尺寸属性
    sidebarWidth: layoutConfig.value.sidebarWidth,
    headerHeight: layoutConfig.value.headerHeight,
    tabbarHeight: layoutConfig.value.tabbarHeight,
    contentPadding: layoutConfig.value.contentPadding
  };
});

// 处理来自子布局的事件
const handleLayoutEvent = (event: any) => {
  console.log('Adaptive layout event:', event);
  
  // 可以在这里处理各种布局事件
  switch (event.type) {
    case 'sidebar-toggle':
      // 处理侧边栏切换
      break;
    case 'theme-change':
      // 处理主题变化
      break;
    case 'orientation-change':
      // 处理屏幕方向变化
      break;
    default:
      console.log('Unknown layout event:', event);
  }
};

// 监听设备变化，提供调试信息
watch(
  () => appStore.device,
  (newDevice, oldDevice) => {
    if (oldDevice && newDevice !== oldDevice) {
      console.log(`AdaptiveLayout: Device changed from ${oldDevice} to ${newDevice}`);
    }
  }
);
</script>

<style lang="scss" scoped>
.adaptive-layout {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  position: relative;

  // 移动端样式
  &--mobile {
    .mobile-specific {
      display: block;
    }
    .desktop-specific {
      display: none;
    }
    
    // 移动端字体调整
    font-size: 14px;
    
    // 移动端触摸优化
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }

  // 桌面端样式
  &--desktop {
    .mobile-specific {
      display: none;
    }
    .desktop-specific {
      display: block;
    }
    
    // 桌面端字体
    font-size: 16px;
  }

  // 平板样式
  &--tablet {
    font-size: 15px;
    
    // 平板特定样式
    .tablet-optimized {
      display: block;
    }
  }

  // 横屏样式
  &--landscape {
    .landscape-only {
      display: block;
    }
    .portrait-only {
      display: none;
    }
  }

  // 竖屏样式
  &--portrait {
    .landscape-only {
      display: none;
    }
    .portrait-only {
      display: block;
    }
  }

  // 侧边栏打开状态
  &--sidebar-open {
    .sidebar-overlay {
      display: block;
    }
  }
}

// 全局自适应工具类
:global(.adaptive-container) {
  width: 100%;
  
  .adaptive-layout--mobile & {
    padding: 12px;
  }
  
  .adaptive-layout--desktop & {
    padding: 24px;
  }
}

:global(.adaptive-spacing) {
  .adaptive-layout--mobile & {
    margin-bottom: 16px;
  }
  
  .adaptive-layout--desktop & {
    margin-bottom: 24px;
  }
}

:global(.adaptive-button) {
  .adaptive-layout--mobile & {
    width: 100%;
    height: 44px;
    font-size: 16px;
    border-radius: 6px;
  }
  
  .adaptive-layout--desktop & {
    height: 32px;
    font-size: 14px;
    border-radius: 4px;
  }
}

// 平台特定显示控制
:global(.mobile-only) {
  .adaptive-layout--desktop &,
  .adaptive-layout--tablet & {
    display: none !important;
  }
}

:global(.desktop-only) {
  .adaptive-layout--mobile & {
    display: none !important;
  }
}

:global(.tablet-only) {
  .adaptive-layout--mobile &,
  .adaptive-layout--desktop & {
    display: none !important;
  }
}
</style>
