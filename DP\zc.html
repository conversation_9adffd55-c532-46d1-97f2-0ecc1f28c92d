<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="content-type" content="text/html;charset=gb2312">
    <title></title>
    <link href="css/index.css" rel="stylesheet" type="text/css" />
    <link href="css/zc.css" rel="stylesheet" type="text/css" />
    <script src="js/jquery.js"></script>
    <script src="js/main.js?v=1.0"></script>
    <script src="js/zc.js"></script>
    <script src="js/test.js"></script>
    <script src="plugins/echarts/echarts.min.js" type="text/javascript" charset="gb2312"></script>
    <script src="plugins/anime/lib/anime.min.js"></script>
    <style>
        .bg_header {
            background: url(images/title1.png) no-repeat;
            background-size: 100% 100%;
        }

        .header {
            width: 100rem;
            height: 8%;
        }

        .header_title {
            font-size: 1.8rem;
            font-weight: bold;
            letter-spacing: 6px;
        }

        .main {
            height: 60%;
        }

        .res-circle:before {
            top: 0
        }

        .res-circle:after {
            top: 7%
        }
    </style>


    <style>
        /* #customPanel {
            background: url(images/main.png) no-repeat 50% 50%;
            background-size: contain;
        } */

        /* .content-s-box {
            background: url(images/main_icon21.png) no-repeat 50% 50%;
            background-size: contain;
        } */

        /* .content-warn {
            background: url(images/right_con2.png) no-repeat;
            background-size: 100% 100%;
        } */

        .ylfwbox.fora1 {
            background: url(images/zl_icon1.png) no-repeat 50% 50%;
            background-size: contain;
        }

        .ylfwbox.fora2 {
            background: url(images/zl_icon2.png) no-repeat 50% 50%;
            background-size: contain;
        }

        .ylfwbox.fora3 {
            background: url(images/zl_icon3.png) no-repeat 50% 50%;
            background-size: contain;
        }

        .ylfwbox.fora4 {
            background: url(images/zl_icon4.png) no-repeat 50% 50%;
            background-size: contain;
        }

        .ylfwbox.fora5 {
            background: url(images/zl_icon5.png) no-repeat 50% 50%;
            background-size: contain;
        }

        .ylfwbox.fora6 {
            background: url(images/zl_icon6.png) no-repeat 50% 50%;
            background-size: contain;
        }
    </style>
    <style>
        .sec {
            height: 100%;
            padding: 0 20px;
        }

            .sec li {
                height: 30%;
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin: 3px 0;
            }

                .sec li > div:nth-child(1) {
                    width: 100%
                }

                .sec li > div:nth-child(2) {
                }

            .sec .bar2 {
                width: 45%;
                display: inline-block;
                vertical-align: middle;
                border: 1px solid #00deff;
                height: 0.4rem;
                padding: 0.3rem;
                border-radius: 1rem;
            }

                .sec .bar2 span {
                    display: block;
                    height: 100%;
                    background: linear-gradient(to right,#14b6ff,#9cefff);
                    border-radius: 1rem;
                }

            .sec p {
                display: flex;
                align-items: center;
                font-size: .8rem;
            }

                .sec p img {
                    width: .35rem;
                    margin-right: .15rem;
                }

        .barnav {
            position: relative
        }

            .barnav span {
                text-align: center;
                font-size: .8rem;
                padding-left: .1rem;
                line-height: 1rem;
            }

                .barnav span label {
                    color: #23fffc;
                }

            .barnav .tb {
                position: absolute;
                right: 0;
                top: 0;
                height: 100%;
                display: flex;
                align-items: center;
            }

                .barnav .tb img {
                    height: 90%;
                }

        .zaf {
            text-align: center;
        }

            .zaf p {
                justify-content: center
            }

                .zaf p img {
                    width: .18rem;
                    margin-right: 0;
                }

            .zaf span {
                color: #00d3ff;
                font-family: Impact;
                font-size: .25rem;
            }

                .zaf span i {
                    font-style: normal;
                    font-size: .18rem;
                }
    </style>
    <style>
        .res-border {
            width: 42%;
            position: relative;
            height: 39%;
            border: 1px solid #1070aa;
        }

        .res-box-img {
            width: 30%;
        }

        .res-box-content {
            width: 70%;
        }

        .bef {
            float: left;
            width: 10em;
        }

        .bef0:before {
            position: absolute;
            content: "";
            width: 0.375rem;
            height: 0.375rem;
            background: rgba(22, 214, 255, .9);
            box-shadow: 0 0 5px rgb(22 214 255 / 90%);
            border-radius: 10px;
            left: 0.625rem;
            top: 1rem;
        }

        .bef1:before {
            position: absolute;
            content: "";
            width: 0.375rem;
            height: 0.375rem;
            background: rgba(22, 214, 255, .9);
            box-shadow: 0 0 5px rgb(22 214 255 / 90%);
            border-radius: 10px;
            left: 10.625rem;
            top: 1rem;
        }

        .bef2:before {
            position: absolute;
            content: "";
            width: 0.375rem;
            height: 0.375rem;
            background: rgba(22, 214, 255, .9);
            box-shadow: 0 0 5px rgb(22 214 255 / 90%);
            border-radius: 10px;
            left: 20.625rem;
            top: 1rem;
        }
    </style>
    <style>
        /*������*/
        .scroll {
            overflow-y: auto;
            overflow-x: hidden
        }

            .scroll::-webkit-scrollbar {
                display:none;
                /*������������ʽ*/
                width: 5px; /*�߿��ֱ��Ӧ�����������ĳߴ�*/
            }

            .scroll::-webkit-scrollbar-thumb {
                /*����������С����*/
                background: #c0c0c0;
            }

            .scroll::-webkit-scrollbar-track {
                /*������������*/
                background: #efefef;
            }
    </style>
</head>

<body>
    <div id="load">
        <div class="load_img">
            <!-- ���ض��� -->
            <img class="jzxz1" src="images/jzxz1.png">
            <img class="jzxz2" src="images/jzxz2.png">
        </div>
    </div>
    <!--header-->
    <div id="header" class="header">
        <div id="bg_header" class="bg_header">
            <div class="header_nav header_title">
                <!-- <img src="images/logo.png">&nbsp; -->
                �ʲ���ʻ��
            </div>
            <div style="position: absolute;
                    top: 5%;
                    left: 20%;">
                <select id="year" onchange="update()">
                    <option value="2023" selected>2023</option>

                </select>
            </div>
            <style>
                #year {
                    background: 0;
                    border: 0;
                }

                    #year option {
                        background-color: rgb(0, 60, 255);
                    }
            </style>
        </div>
    </div>
    <!--main-->
    <div class="data_main">
        <div class="main_left">
            <div class="content_box" style="height: 48%;">
                <div class="alltitle">�ʲ�����ͳ��</div>
                <div class="allcontent" style="justify-content: flex-end;">
                    <div style="width: 100%;height:100%;" id="zcfl"></div>
                </div>
            </div>

            <div class="content_box" style="height:48%;">
                <div class="alltitle">�̶��ʲ�����ͳ��</div>
                <div class="allcontent">
                    <div style="width: 100%;height:100%;" id="gdzcfl">

                    </div>
                </div>
            </div>

        </div>

        <div class="main_center">
            <div class="content_box content_box_main" style="height: 61%;margin-bottom:2%">
                <div style="position: absolute; left: 0; right: 0; top: 20px; width: 5em; margin: auto; font-size: 20px; font-weight: bold; ">�ʲ�����</div>
                <div class="content-t content-main" style="height:72%;padding: 10px 1% 0% ;">
                    <div style="width:30%;margin-left: 2rem;" class="content-s">
                        <div class="content-s-c">
                            <div class="content-s-row">
                                <div class="content-s-box">
                                    <div class="content-s-word">���ʲ�����</div>
                                    <div class="content-s-value" id="zc_sl">0</div>

                                </div>
                            </div>
                            <div class="content-s-row">
                                <div class="content-s-box">
                                    <div class="content-s-word">���������ʲ�����</div>
                                    <div class="content-s-value" id="new_sl">0</div>
                                </div>
                            </div>
                            <div class="content-s-row">
                                <div class="content-s-box">
                                    <div class="content-s-word">���괦���ʲ�����</div>
                                    <div class="content-s-value" id="scrap_sl">0</div>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div style="width:40%">
                        <div id="customPanel" style="cursor: pointer;;width:100%;height:100%;display: flex;
                            justify-content: center;
                            align-items: center;
                            flex-direction: column;padding-top: 2vh;">
                            <div style="font-size:1rem">�ʲ��ܽ��</div>
                            <div style="font-size:2rem;color:#23fffc;font-family: electronicFont;" id="total"></div>
                            <p style="font-size: 12px;opacity: .5;">(��Ԫ)</p>
                        </div>
                    </div>
                    <div style="width:30%;margin-right: 2rem;justify-content: flex-end;" class="content-s">
                        <div class="content-s-c">
                            <div class="content-s-row">
                                <div class="content-s-box">
                                    <div class="content-s-word">���ʲ����</div>
                                    <div class="content-s-value" id="zc_je">0</div>
                                </div>
                            </div>
                            <div class="content-s-row">
                                <div class="content-s-box">
                                    <div class="content-s-word">���������ʲ�ԭֵ</div>
                                    <div class="content-s-value" id="new_je">0</div>
                                </div>
                            </div>
                            <div class="content-s-row">
                                <div class="content-s-box">
                                    <div class="content-s-word">���괦���ʲ�ԭֵ</div>
                                    <div class="content-s-value" id="scrap_je">0</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="content-t" style="height:21%;padding: 5px;height:calc(27% - 20px)">
                    <div style="width: 25%;height: 100%;" class="content-warn">
                        <div class="warn-box" style="cursor: pointer ">
                            <div class="warn-box-content">
                                <div id="r_chart" style="height: 100%;width: 100%;"></div>
                            </div>
                        </div>
                    </div>

                    <div style="width: 25%;" class="content-warn">
                        <div class="warn-box" style="cursor: pointer ">
                            <div class="warn-box-img"
                                 style="background: url(./images/blue.png) no-repeat center center;background-size: 65%;">
                            </div>
                            <div class="warn-box-content">
                                <div id="b_chart" style="height: 100%;width: 100%;"></div>
                            </div>
                        </div>
                    </div>

                    <div style="width: 25%;" class="content-warn">
                        <div class="warn-box" style="cursor: pointer ">
                            <div class="warn-box-img"
                                 style="background: url(./images/yellow.png) no-repeat center center;background-size: 65%;">
                            </div>
                            <div class="warn-box-content">
                                <div id="y_chart" style="height: 100%;width: 100%;"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div style="position: absolute;bottom:8px;right:8px;cursor: pointer;" onclick="tohtml('detail.html')"><img src="./images/scan.png" id="scan" style="width: 2.5rem;"></div>
            </div>
            <div style="display: flex;height: 35%;justify-content: space-between;">
                <div class="content_box" style="height: 100%;width: 100%;">
                    <div class="alltitle"><div class="bef bef0" onclick="syfx()">�ʲ�ʹ�÷���ͳ��</div><div class="bef bef1" onclick="jxbm()">��ѧ�����ʲ�ͳ��</div><div class="bef bef2" onclick="xzbm()">���������ʲ�ͳ��</div></div>
                    <div class="more" style="color:gray"><< ����ƶ� >></div>
                    <!--<div class="more" style="right: 10em; top: 1em; left: 25em; right: auto; display: flex;">
        <div style="text-align: center; border: 1px solid #7c95df; height: 24px; margin: auto; border-radius: 2px; display: flex; align-items: center; justify-content: center; cursor: pointer; padding: 0 10px; margin-top: -3px; background: #0063ff;"
             onclick="syfx()">
            ʹ�÷���ͳ��
        </div>
        <div style="margin-left: 2em; text-align: center; border: 1px solid #7c95df; height: 24px; border-radius: 2px; display: flex; align-items: center; justify-content: center; cursor: pointer; padding: 0 10px; margin-top: -3px; background: #0063ff;"
             onclick="jxbm()">
            ��ѧ�����ʲ�ͳ��
        </div>
        <div style="margin-left:2em;text-align: center; border: 1px solid #7c95df; height: 24px;  border-radius: 2px; display: flex; align-items: center; justify-content: center; cursor: pointer; padding: 0 10px; margin-top: -3px; background: #0063ff;"
             onclick="xzbm()">
            ���������ʲ�ͳ��
        </div>
    </div>-->
                    <div class="allcontent">
                        <div id="syfx" class='tjtab' style="height: 100%; display: none"></div>
                        <div id="jxbm" class='tjtab' style="height: 100%; display: none"></div>
                        <div id="xzbm" class='tjtab' style="height: 100%; display: none"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="main_right">
            <div class="content_box" style="height: 30%;">
                <div class="alltitle">�ʲ��䶯ͳ��</div>
                <div class="allcontent" style="display: block;">
                    <div style=" position: absolute; right: 30px; font-size: 14px;">ͬ��</div>
                    <ul class="sec h100">
                        <li>
                            <div>
                                <p>�Ǽ��ʲ�ԭֵ</p>
                                <div class="barnav">
                                    <div class="bar2"><span style="width: 0%;" id="djjebar"></span></div>
                                    <span><label id="djje">0</label>��(<label id="djsl">0</label>��)</span>
                                    <div class="tb"><label id="djtb" style="font-size: .9rem;"></label><img src="" id="djtbimg" /></div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div>
                                <p>�����ʲ�ԭֵ</p>
                                <div class="barnav">
                                    <div class="bar2"><span style="width: 0%;" id="dbjebar"></span></div>
                                    <span><label id="dbje">0</label>��(<label id="dbsl">0</label>��)</span>
                                    <div class="tb"><label id="dbtb" style="font-size: .9rem;"></label><img src="" id="dbtbimg" /></div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div>
                                <p>�����ʲ�ԭֵ</p>
                                <div class="barnav">
                                    <div class="bar2"><span style="width: 0%;" id="czjebar"></span></div>
                                    <span><label id="czje">0</label>��(<label id="czsl">0</label>��)</span>
                                    <div class="tb"><label id="cztb" style="font-size: .9rem;"></label><img src="" id="cztbimg" /></div>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="content_box" style="height: 29%;">
                <div class="alltitle" style="color:yellow">�ʲ�Ԥ��</div>
                <div class="allcontent">
                    <div style="height: 100%; display: flex; flex-wrap: wrap; justify-content: space-evenly; align-content: space-around;">
                        <div class="res-border" style="">
                            <div class="res-top"></div>
                            <div class="res-box">
                                <div class="res-box-img"
                                     style="background: url(./images/profile.png) no-repeat center center;background-size: 70%;">
                                </div>
                                <div class="res-box-content">
                                    <div class="res-box-name">
                                        ��Ա�䶯Ԥ��
                                    </div>
                                    <div class="res-box-val" id="b9_ry">455</div>
                                </div>
                            </div>
                            <div class="res-bottom"></div>
                        </div>

                        <div class="res-border" style="">
                            <div class="res-top"></div>
                            <div class="res-box">
                                <div class="res-box-img"
                                     style="background: url(./images/warning-sign.png) no-repeat center center;background-size: 70%;">
                                </div>
                                <div class="res-box-content">
                                    <div class="res-box-name">�������ű䶯Ԥ��</div>
                                    <div class="res-box-val" id="b9_ss">22920</div>
                                </div>
                            </div>
                            <div class="res-bottom"></div>
                        </div>
                        <div class="res-border" style="">
                            <div class="res-top"></div>
                            <div class="res-box">
                                <div class="res-box-img"
                                     style="background: url(./images/edit-levels.png) no-repeat center center;background-size: 70%;">
                                </div>
                                <div class="res-box-content">
                                    <div class="res-box-name">�ʲ��̵�Ԥ��</div>
                                    <div class="res-box-val" id="b9_dx">0</div>
                                </div>
                            </div>
                            <div class="res-bottom"></div>
                        </div>
                        <div class="res-border" style="">
                            <div class="res-top"></div>
                            <div class="res-box">
                                <div class="res-box-img"
                                     style="background: url(./images/news.png) no-repeat center center; background-size: 70%;">
                                </div>
                                <div class="res-box-content">
                                    <div class="res-box-name">
                                        �����Ԥ��
                                    </div>
                                    <div class="res-box-val" id="b9_zd">0</div>
                                </div>
                            </div>
                            <div class="res-bottom"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="content_box" style="height: 35%;">
                <div class="alltitle">�����ʲ��������</div>
                <div class="allcontent">
                    <div style="height: 100%; overflow-y: auto;" class="scroll">
                        <style>
                            .line {
                                width: 95%;
                                height: 20px;
                                display: flex;
                                align-items: center;
                                margin-top: 10px;
                            }

                            .line-name {
                                width: 22%;
                                font-size: 14px;
                                text-align: right;
                                margin-right: 5px
                            }

                            .line-bar {
                                width: 55%;
                                height: 100%;
                            }

                            .line-je {
                                width: 20%;
                                margin-left: 2%;
                                font-size: 12px;
                            }
                        </style>
                        <div id="sortList">
                      
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        $('#load').fadeOut(1000)
        setTimeout(function () {
            $('#load').remove()
        }, 1100);

        function toUrl(url) {
            location.href = "../index.aspx?url1=" + url;
        }
        function tohtml(url) {
            location.href = url;
        }
    </script>
</body>

</html>