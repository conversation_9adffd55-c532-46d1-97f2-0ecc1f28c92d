<!DOCTYPE html>
<html>
<head>
  <title>Anime MGS logo animation | anime.js</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <meta property="og:title" content="anime.js">
  <meta property="og:url" content="https://animejs.com">
  <meta property="og:description" content="Javascript Animation Engine">
  <meta property="og:image" content="https://animejs.com/documentation/assets/img/icons/og.png">
  <meta name="twitter:card" content="summary">
  <meta name="twitter:title" content="anime.js">
  <meta name="twitter:site" content="@juliangarnier">
  <meta name="twitter:description" content="Javascript Animation Engine">
  <meta name="twitter:image" content="https://animejs.com/documentation/assets/img/icons/twitter.png">
  <link rel="apple-touch-icon-precomposed" href="../assets/img/social-media-image.png">
  <link rel="icon" type="image/png" href="../assets/img/favicon.png">
  <link href="../assets/css/animejs.css" rel="stylesheet">
  <link href="../assets/css/documentation.css" rel="stylesheet">
  <script src="../../lib/anime.min.js"></script>
  <!-- <script src="../assets/js/anime/anime.2.2.0.js"></script> -->
  <style>

    html,
    body {
      background-color: black;
    }

    section {
      position: absolute;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
    }

    .mgs-logo { margin: auto; }
    .vert { transform-origin: center bottom; }
    .hori { transform-origin: 0% 50%; }
    .diag-left { transform-origin: 100% 100%; }
    .diag-right { transform-origin: 100% 0%; }

    .stripes path,
    .text-fills path,
    .katakana path,
    .line {
      opacity: 0
    }

  </style>
</head>
<body>
  <section>
    <svg class="mgs-logo" width="100%" height="448" viewBox="0 0 1024 448">
      <g class="stripes" transform="translate(321.000000, 160.000000)" fill="#FFF">
        <path d="M8,20 L12,16 L92,16 L88,20 L8,20 L8,20 L8,20 L8,20 Z" class="stripe stripe-left"></path>
        <path d="M4,24 L0,28 L80,28 L84,24 L4,24 L4,24 L4,24 L4,24 Z" class="stripe stripe-left"></path>
        <path d="M16,12 L20,8 L100,8 L96,12 L16,12 L16,12 L16,12 L16,12 Z" class="stripe stripe-left"></path>
        <path d="M24,4 L28,0 L108,7.10542736e-15 L104,4 L24,4 L24,4 L24,4 L24,4 Z" class="stripe stripe-left"></path>
      </g>
      <g class="stripes" transform="translate(592.000000, 160.000000)" fill="#FFF">
        <path d="M8,20 L12,16 L92,16 L88,20 L8,20 L8,20 L8,20 L8,20 Z" class="stripe stripe-right"></path>
        <path d="M4,24 L0,28 L80,28 L84,24 L4,24 L4,24 L4,24 L4,24 Z" class="stripe stripe-right"></path>
        <path d="M16,12 L20,8 L100,8 L96,12 L16,12 L16,12 L16,12 L16,12 Z" class="stripe stripe-right"></path>
        <path d="M24,4 L28,0 L108,7.10542736e-15 L104,4 L24,4 L24,4 L24,4 L24,4 Z" class="stripe stripe-right"></path>
      </g>
      <g class="katakana" transform="translate(202.000000, 128.000000)" fill="#FFF">
        <path d="M55,33 L56,32 L136,32 L135,33 L55,33 Z" data-d="M0,92 L60,32 L140,32 L80,92 L0,92 Z"></path>
        <path d="M28,28 L29,28 L1,0 L0,0 L28,28 Z" data-d="M32,28 L230,28 L202,0 L4,0 L32,28 Z"></path>
        <path d="M232,28 L204,0 L205,0 L233,28 L232,28 Z" data-d="M236,28 L208,0 L388,0 L416,28 L236,28 Z"></path>
        <path d="M169,92 L197,64 L199,64 L171,92 L169,92 Z" data-d="M173,92 L201,64 L381,64 L353,92 L173,92 Z"></path>
        <path d="M435,92 L355,92 L356,91 L436,91 L435,92 Z" data-d="M439,92 L359,92 L387,64 L467,64 L439,92 Z"></path>
        <path d="M497,28 L498,28 L470,0 L469,0 L497,28 Z" data-d="M422,28 L502,28 L474,0 L394,0 L422,28 Z"></path>
        <path d="M476,65 L556,65 L555,64 L475,64 L476,65 Z" data-d="M507,92 L587,92 L559,64 L479,64 L507,92 Z"></path>
        <path d="M554,59 L553,60 L473,60 L474,59 L554,59 Z" data-d="M617,0 L557,60 L477,60 L537,0 L617,0 Z"></path>
      </g>
      <g class="text-fills" transform="translate(128.000000, 240.000000)" fill="#FFF">
        <path d="M0,48 L0,68 L16,84 L96,84 L112,68 L112,16 L96,0 L20,0 L0,20 L92,20 L92,64 L20,64 L20,52 L60,52 L80,32 L16,32 L0,48 Z" id="a"></path>
        <path d="M216,84 L236,84 L236,16 L220,0 L140,0 L124,16 L124,84 L144,84 L144,20 L216,20 L216,84 L216,84 Z" id="n"></path>
        <path d="M248,0 L268,0 L268,84 L248,84 L248,0 Z" id="i"></path>
        <path d="M300,20 L300,84 L280,84 L280,16 L296,0 L408,0 L424,16 L424,84 L404,84 L404,20 L362,20 L362,84 L342,84 L342,20 L300,20 Z" id="m"></path>
        <path d="M548,36 L548,16 L532,2.55795385e-13 L452,2.4158453e-13 L436,16 L436,68 L452,84 L528,84 L548,64 L456,64 L456,20 L528,20 L528,32 L488,32 L468,52 L532,52 L548,36 L548,36 Z" id="e"></path>
        <path d="M562,64 L582,84 L626,84 L642,68 L642,0 L622,0 L622,64 L562,64 Z" id="j"></path>
        <path d="M674,20 L766,20 L746,0 L670,0 L654,16 L654,36 L670,52 L746,52 L746,64 L654,64 L674,84 L750,84 L766,68 L766,48 L750,32 L674,32 L674,20 Z" id="s"></path>
      </g>
      <g stroke="#FFF" stroke-width="2" fill="none" fill-rule="evenodd">
        <path d="M148,292 L148,304" class="line vert" stroke-linecap="square"></path>
        <path d="M128,288 L128,308" class="line vert" stroke-linecap="square"></path>
        <path d="M148,240 L128,260" class="line diag-right"></path>
        <path d="M128,308 L144,324" class="line diag-left"></path>
        <path d="M144,272 L128,288" class="line diag-right"></path>
        <path d="M148,240 L224,240" class="line hori" stroke-linecap="square"></path>
        <path d="M144,272 L208,272" class="line hori" stroke-linecap="square"></path>
        <path d="M148,292 L188,292" class="line hori" stroke-linecap="square"></path>
        <path d="M148,304 L220,304" class="line hori" stroke-linecap="square"></path>
        <path d="M144,324 L224,324" class="line hori" stroke-linecap="square"></path>
        <path d="M129,260 L220,260" class="line hori" stroke-linecap="square"></path>
        <path d="M208,272 L188,292" class="line diag-right"></path>
        <path d="M220,260 L220,304" class="line vert" stroke-linecap="square"></path>
        <path d="M224,240 L240,256" class="line diag-left"></path>
        <path d="M240,308 L224,324" class="line diag-right"></path>
        <path d="M240,256 L240,308" class="line vert" stroke-linecap="square"></path>
        <path d="M252,256 L252,324" class="line vert" stroke-linecap="square"></path>
        <path d="M268,240 L252,256" class="line diag-right"></path>
        <path d="M252,324 L272,324" class="line hori" stroke-linecap="square"></path>
        <path d="M272,260 L272,324" class="line vert" stroke-linecap="square"></path>
        <path d="M268,240 L348,240" class="line hori" stroke-linecap="square"></path>
        <path d="M272,260 L344,260" class="line hori" stroke-linecap="square"></path>
        <path d="M344,260 L344,324" class="line vert" stroke-linecap="square"></path>
        <path d="M348,240 L364,256" class="line diag-left"></path>
        <path d="M344,324 L364,324" class="line hori" stroke-linecap="square"></path>
        <path d="M364,256 L364,324" class="line vert" stroke-linecap="square"></path>
        <path d="M376,240 L376,324" class="line vert" stroke-linecap="square"></path>
        <path d="M376,324 L396,324" class="line hori" stroke-linecap="square"></path>
        <path d="M376,240 L396,240" class="line hori" stroke-linecap="square"></path>
        <path d="M396,240 L396,324" class="line vert" stroke-linecap="square"></path>
        <path d="M408,256 L408,324" class="line vert" stroke-linecap="square"></path>
        <path d="M424,240 L408,256" class="line diag-right"></path>
        <path d="M408,324 L428,324" class="line hori" stroke-linecap="square"></path>
        <path d="M428,261 L428,323" class="line vert" stroke-linecap="square"></path>
        <path d="M424,240 L536,240" class="line hori" stroke-linecap="square"></path>
        <path d="M428,260 L470,260" class="line hori" stroke-linecap="square"></path>
        <path d="M470,261 L470,323" class="line vert" stroke-linecap="square"></path>
        <path d="M470,324 L490,324" class="line hori" stroke-linecap="square"></path>
        <path d="M490,261 L490,323" class="line vert" stroke-linecap="square"></path>
        <path d="M490,260 L532,260" class="line hori" stroke-linecap="square"></path>
        <path d="M532,261 L532,323" class="line vert" stroke-linecap="square"></path>
        <path d="M536,240 L552,256" class="line diag-left"></path>
        <path d="M532,324 L552,324" class="line hori" stroke-linecap="square"></path>
        <path d="M552,256 L552,324" class="line vert" stroke-linecap="square"></path>
        <path d="M564,256 L564,308" class="line vert" stroke-linecap="square"></path>
        <path d="M564,308 L580,324" class="line diag-left"></path>
        <path d="M580,240 L564,256" class="line diag-right"></path>
        <path d="M584,260 L584,304" class="line vert" stroke-linecap="square"></path>
        <path d="M616,272 L596,292" class="line diag-right"></path>
        <path d="M580,240 L660,240" class="line hori" stroke-linecap="square"></path>
        <path d="M584,260 L656,260" class="line hori" stroke-linecap="square"></path>
        <path d="M580,324 L656,324" class="line hori" stroke-linecap="square"></path>
        <path d="M596,292 L660,292" class="line hori" stroke-linecap="square"></path>
        <path d="M616,272 L656,272" class="line hori" stroke-linecap="square"></path>
        <path d="M656,260 L656,272" class="line vert" stroke-linecap="square"></path>
        <path d="M660,240 L676,256" class="line diag-left"></path>
        <path d="M676,304 L656,324" class="line diag-right"></path>
        <path d="M676,276 L660,292" class="line diag-right"></path>
        <path d="M584,304 L676,304" class="line hori" stroke-linecap="square"></path>
        <path d="M676,256 L676,276" class="line vert" stroke-linecap="square"></path>
        <path d="M690,304 L710,324" class="line diag-left"></path>
        <path d="M690,304 L750,304" class="line hori" stroke-linecap="square"></path>
        <path d="M710,324 L754,324" class="line hori" stroke-linecap="square"></path>
        <path d="M750,240 L750,304" class="line vert" stroke-linecap="square"></path>
        <path d="M770,308 L754,324" class="line diag-right"></path>
        <path d="M750,240 L770,240" class="line hori" stroke-linecap="square"></path>
        <path d="M770,240 L770,308" class="line vert" stroke-linecap="square"></path>
        <path d="M782,256 L782,276" class="line vert" stroke-linecap="square"></path>
        <path d="M782,276 L798,292" class="line diag-left"></path>
        <path d="M782,304 L802,324" class="line diag-left"></path>
        <path d="M798,240 L782,256" class="line diag-right"></path>
        <path d="M802,260 L802,272" class="line vert" stroke-linecap="square"></path>
        <path d="M798,240 L874,240" class="line hori" stroke-linecap="square"></path>
        <path d="M802,272 L878,272" class="line hori" stroke-linecap="square"></path>
        <path d="M802,324 L878,324" class="line hori" stroke-linecap="square"></path>
        <path d="M798,292 L874,292" class="line hori" stroke-linecap="square"></path>
        <path d="M782,304 L874,304" class="line hori" stroke-linecap="square"></path>
        <path d="M874,292 L874,304" class="line vert" stroke-linecap="square"></path>
        <path d="M878,272 L894,288" class="line diag-left"></path>
        <path d="M874,240 L894,260" class="line diag-left"></path>
        <path d="M894,308 L878,324" class="line diag-right"></path>
        <path d="M802,260 L894,260" class="line hori" stroke-linecap="square"></path>
        <path d="M894,288 L894,308" class="line vert" stroke-linecap="square"></path>
      </g>
    </svg>
  </section>
  <script>

  var logoAnimation = anime.timeline({
    direction: 'alternate',
    loop: true
  });

  logoAnimation
  .add({
      targets: '.stripes path',
      translateX: [-1000, 0],
      opacity: {
        value: 1,
        duration: 100
      },
      fill: '#F9C100',
      delay: (el, i) => 2200 + (i * 75),
      duration: 400,
      easing: 'easeOutExpo',
    }, 0)
  .add({
      targets: '.katakana path',
      d: (el) => el.getAttribute('data-d'),
      opacity: {
        value: [0, 1],
        duration: 100
      },
      fill: '#F9C100',
      delay: (el, i) => 2400 + (i * 120),
      duration: 1200,
      easing: 'easeOutCirc',
    }, 0)
    .add({
      targets: '.text-fills path',
      opacity: [0, 1],
      fill: '#F9C100',
      easing: 'easeOutExpo',
      duration: 200,
      delay: (t, i) => 3300 + (anime.random(0, 450)),
    }, 0)
    .add({
      targets: '.line',
      translateX: (target) => {
        let x = 1200;
        let translate;
        if (target.classList.contains('hori')) translate = anime.random(0, 1) ? x : -x;
        if (target.classList.contains('diag-right') || target.classList.contains('diag-left')) translate =  x / 3;
        return [translate, 0];
      },
      translateY: (target) => {
        let y = 1200;
        let translate;
        if (target.classList.contains('vert')) translate = anime.random(0, 1) ? y : -y;
        if (target.classList.contains('diag-right')) translate =  -y / 3;
        if (target.classList.contains('diag-left')) translate =  y / 3;
        return [translate, 0];
      },
      stroke: '#F9C100',
      opacity: {
        value: [0, 1],
        duration: 100,
      },
      delay: (t, i) => (i * 25),
      duration: 500,
      easing: 'easeOutSine',
    }, 0);

  </script>
  <script src="../assets/js/vendors/stats.min.js"></script>
</body>
</html>
