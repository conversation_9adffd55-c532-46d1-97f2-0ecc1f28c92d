// 自适应组件库导出文件
export { default as AdaptiveTable } from './AdaptiveTable.vue';
export { default as AdaptiveForm } from './AdaptiveForm.vue';
export { default as AdaptiveButton } from './AdaptiveButton.vue';
export { default as AdaptiveCard } from './AdaptiveCard.vue';
export { default as AdaptiveModal } from './AdaptiveModal.vue';

// 类型定义
export interface AdaptiveComponentProps {
  adaptive?: boolean;
  mobileProps?: Record<string, any>;
  desktopProps?: Record<string, any>;
}

export interface TableColumn {
  prop: string;
  label: string;
  width?: string | number;
  minWidth?: string | number;
  sortable?: boolean;
  slot?: string;
  mobileDisplay?: 'title' | 'value' | 'label' | 'hidden';
}

export interface FormField {
  prop: string;
  label: string;
  type: 'input' | 'select' | 'date' | 'textarea' | 'number';
  required?: boolean;
  placeholder?: string;
  span?: number;
  options?: Array<{ label: string; value: any }>;
  rules?: any[];
  mobileProps?: Record<string, any>;
  desktopProps?: Record<string, any>;
}
