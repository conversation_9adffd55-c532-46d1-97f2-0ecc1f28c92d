<template>
  <div class="mobile-personal">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="个人中心"
      left-arrow
      fixed
      placeholder
      @click-left="goBack"
    />
    
    <!-- 用户信息卡片 -->
    <div class="user-card">
      <div class="user-avatar">
        <van-image
          round
          width="60"
          height="60"
          src="https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg"
          fit="cover"
        />
      </div>
      <div class="user-info">
        <div class="user-name">张三</div>
        <div class="user-dept">资产管理部</div>
      </div>
      <van-icon name="arrow" class="arrow-icon" />
    </div>
    
    <!-- 功能菜单 -->
    <van-cell-group inset title="资产管理">
      <van-cell title="我的资产" icon="balance-list-o" is-link to="/mobile/assets/index" />
      <van-cell title="资产盘点" icon="todo-list-o" is-link to="/mobile/inventory/index" />
      <van-cell title="资产处置" icon="delete-o" is-link to="/mobile/disposal/index" />
      <van-cell title="资产调拨" icon="exchange" is-link to="/mobile/transfer/index" />
      <van-cell title="管辖资产" icon="cluster-o" is-link to="/mobile/jurisdictional/index" />
    </van-cell-group>
    
    <van-cell-group inset title="工作中心">
      <van-cell title="资产警示" icon="warning-o" is-link to="/mobile/alerts/index" />
      <van-cell title="审批记录" icon="records" is-link to="/mobile/records/index" />
      <van-cell title="通知公告" icon="volume-o" is-link to="/mobile/announcements/index" />
      <van-cell title="待办中心" icon="flag-o" is-link to="/mobile/remind/index" />
    </van-cell-group>
    
    <van-cell-group inset title="系统设置">
      <van-cell title="账号安全" icon="shield-o" is-link />
      <van-cell title="消息设置" icon="setting-o" is-link />
      <van-cell title="关于系统" icon="info-o" is-link />
      <van-cell title="退出登录" icon="close" is-link @click="handleLogout" />
    </van-cell-group>
    
    <!-- 版本信息 -->
    <div class="version-info">
      <p>版本号: v1.0.0</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';

const router = useRouter();

// 返回上一页
const goBack = () => {
  router.go(-1);
};

// 退出登录
const handleLogout = () => {
  // 显示确认对话框
  showConfirmDialog({
    title: '提示',
    message: '确定要退出登录吗？',
  })
    .then(() => {
      // 执行退出登录逻辑
      console.log('用户确认退出');
      // 清除token
      // removeToken();
      // 跳转到登录页
      router.push('/login');
    })
    .catch(() => {
      // 取消退出
      console.log('用户取消退出');
    });
};

// 确认对话框
const showConfirmDialog = (options) => {
  return new Promise((resolve, reject) => {
    if (window.confirm(options.message)) {
      resolve();
    } else {
      reject();
    }
  });
};
</script>

<style lang="scss" scoped>
.mobile-personal {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 20px;
  
  .user-card {
    margin: 12px;
    padding: 16px;
    background-color: #fff;
    border-radius: 8px;
    display: flex;
    align-items: center;
    position: relative;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    
    .user-avatar {
      margin-right: 16px;
    }
    
    .user-info {
      flex: 1;
      
      .user-name {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 4px;
      }
      
      .user-dept {
        font-size: 14px;
        color: #999;
      }
    }
    
    .arrow-icon {
      color: #ccc;
    }
  }
  
  .van-cell-group {
    margin-bottom: 12px;
  }
  
  .version-info {
    text-align: center;
    padding: 16px;
    color: #999;
    font-size: 12px;
  }
}

:deep(.van-nav-bar) {
  background-color: #1989fa;
  
  .van-nav-bar__title {
    color: #fff;
  }
  
  .van-icon {
    color: #fff;
  }
}

:deep(.van-cell-group__title) {
  padding: 16px 16px 8px;
  font-size: 14px;
  color: #666;
}
</style>
