<template>
  <div class="mobile-records">
    <!-- 顶部导航栏 -->
    <van-nav-bar title="审批记录" left-arrow fixed placeholder @click-left="goBack" />

    <!-- 搜索框 -->
    <div class="search-bar">
      <van-search
        v-model="searchValue"
        placeholder="请输入申请单号或申请人"
        shape="round"
        show-action
        @search="onSearch"
      >
        <template #action>
          <div @click="onSearch">搜索</div>
        </template>
      </van-search>
    </div>

    <!-- 标签页 -->
    <van-tabs v-model:active="activeTab" sticky>
      <van-tab title="待审批">
        <record-list :list="pendingList" :loading="loading" :finished="finished" @load="onLoad" />
      </van-tab>
      <van-tab title="已审批">
        <record-list :list="approvedList" :loading="loading" :finished="finished" @load="onLoad" />
      </van-tab>
      <van-tab title="我发起的">
        <record-list
          :list="myApplications"
          :loading="loading"
          :finished="finished"
          @load="onLoad"
        />
      </van-tab>
    </van-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, defineComponent } from "vue";
import { useRouter } from "vue-router";

// 审批记录列表组件
const RecordList = defineComponent({
  props: {
    list: Array,
    loading: Boolean,
    finished: Boolean,
  },
  emits: ["load"],
  setup(props, { emit }) {
    const onLoad = () => {
      emit("load");
    };

    const getStatusClass = (status) => {
      const classMap = {
        pending: "status-pending",
        approved: "status-approved",
        rejected: "status-rejected",
        processing: "status-processing",
      };
      return classMap[status] || "";
    };

    const getStatusText = (status) => {
      const statusMap = {
        pending: "待审批",
        approved: "已通过",
        rejected: "已拒绝",
        processing: "审批中",
      };
      return statusMap[status] || "未知状态";
    };

    const getTypeIcon = (type) => {
      const iconMap = {
        disposal: "delete-o",
        transfer: "exchange",
        purchase: "add-o",
        repair: "setting-o",
      };
      return iconMap[type] || "records";
    };

    const getTypeText = (type) => {
      const typeMap = {
        disposal: "资产处置",
        transfer: "资产调拨",
        purchase: "资产采购",
        repair: "资产维修",
      };
      return typeMap[type] || "未知类型";
    };

    const goToDetail = (item) => {
      console.log("跳转到详情页:", item);
      // router.push(`/mobile/records/detail/${item.id}`);
    };

    const handleApproval = (item, action, e) => {
      e.stopPropagation();
      console.log("审批操作:", item, action);
      if (action === "approve") {
        // 通过审批
        // showApprovalDialog(item, 'approve');
      } else if (action === "reject") {
        // 拒绝审批
        // showApprovalDialog(item, 'reject');
      }
    };

    const viewDetail = (item, e) => {
      e.stopPropagation();
      console.log("查看详情:", item);
      // router.push(`/mobile/records/detail/${item.id}`);
    };

    return {
      onLoad,
      getStatusClass,
      getStatusText,
      getTypeIcon,
      getTypeText,
      goToDetail,
      handleApproval,
      viewDetail,
    };
  },
  template: `
    <van-list
      v-model:loading="loading"
      :finished="finished"
      finished-text="没有更多了"
      @load="onLoad"
    >
      <div
        v-for="(item, index) in list"
        :key="index"
        class="record-item"
        @click="goToDetail(item)"
      >
        <div class="record-header">
          <div class="record-icon">
            <van-icon :name="getTypeIcon(item.type)" />
          </div>
          <div class="record-title">
            <div class="title-text">
              <span class="record-id">[{{ item.id }}]</span>
              {{ item.title }}
            </div>
            <div class="record-type">{{ getTypeText(item.type) }}</div>
          </div>
          <div class="record-status" :class="getStatusClass(item.status)">
            {{ getStatusText(item.status) }}
          </div>
        </div>
        <div class="record-info">
          <div class="info-row">
            <span class="label">申请人:</span>
            <span class="value">{{ item.applicant }}</span>
          </div>
          <div class="info-row">
            <span class="label">申请部门:</span>
            <span class="value">{{ item.department }}</span>
          </div>
          <div class="info-row">
            <span class="label">申请时间:</span>
            <span class="value">{{ item.applyTime }}</span>
          </div>
          <div v-if="item.approver" class="info-row">
            <span class="label">审批人:</span>
            <span class="value">{{ item.approver }}</span>
          </div>
          <div v-if="item.approveTime" class="info-row">
            <span class="label">审批时间:</span>
            <span class="value">{{ item.approveTime }}</span>
          </div>
          <div v-if="item.reason" class="info-row">
            <span class="label">申请理由:</span>
            <span class="value reason">{{ item.reason }}</span>
          </div>
        </div>
        <div class="record-footer">
          <template v-if="item.status === 'pending'">
            <van-button
              size="small"
              type="success"
              plain
              @click.stop="handleApproval(item, 'approve', $event)"
            >
              通过
            </van-button>
            <van-button
              size="small"
              type="danger"
              plain
              @click.stop="handleApproval(item, 'reject', $event)"
            >
              拒绝
            </van-button>
          </template>
          <van-button
            size="small"
            type="info"
            plain
            @click.stop="viewDetail(item, $event)"
          >
            查看详情
          </van-button>
        </div>
      </div>
    </van-list>
  `,
});

const router = useRouter();
const searchValue = ref("");
const activeTab = ref(0);
const loading = ref(false);
const finished = ref(false);

// 待审批列表
const pendingList = ref([
  {
    id: "SP20250601001",
    title: "办公电脑处置申请",
    type: "disposal",
    status: "pending",
    applicant: "张三",
    department: "行政部",
    applyTime: "2025-06-01 09:30",
    reason: "设备老化，无法正常使用，申请报废处置",
  },
  {
    id: "SP20250601002",
    title: "打印机调拨申请",
    type: "transfer",
    status: "pending",
    applicant: "李四",
    department: "财务部",
    applyTime: "2025-06-01 10:15",
    reason: "部门业务调整，需要将打印机调拨至其他部门",
  },
  {
    id: "SP20250601003",
    title: "投影仪维修申请",
    type: "repair",
    status: "pending",
    applicant: "王五",
    department: "教务处",
    applyTime: "2025-06-01 11:00",
    reason: "投影仪显示异常，需要专业维修",
  },
]);

// 已审批列表
const approvedList = ref([
  {
    id: "SP20250531001",
    title: "办公椅采购申请",
    type: "purchase",
    status: "approved",
    applicant: "赵六",
    department: "人事部",
    applyTime: "2025-05-31 14:20",
    approver: "管理员",
    approveTime: "2025-06-01 09:00",
    reason: "新员工入职，需要采购办公椅",
  },
  {
    id: "SP20250530002",
    title: "旧设备处置申请",
    type: "disposal",
    status: "rejected",
    applicant: "孙七",
    department: "信息中心",
    applyTime: "2025-05-30 16:45",
    approver: "管理员",
    approveTime: "2025-05-31 10:30",
    reason: "设备仍有使用价值，暂不处置",
  },
  {
    id: "SP20250529003",
    title: "空调设备调拨",
    type: "transfer",
    status: "approved",
    applicant: "周八",
    department: "后勤部",
    applyTime: "2025-05-29 13:15",
    approver: "管理员",
    approveTime: "2025-05-30 08:45",
    reason: "季节性调整，需要重新分配空调设备",
  },
]);

// 我发起的申请
const myApplications = ref([
  {
    id: "SP20250528001",
    title: "显示器采购申请",
    type: "purchase",
    status: "processing",
    applicant: "当前用户",
    department: "技术部",
    applyTime: "2025-05-28 15:30",
    reason: "工作需要，申请采购高分辨率显示器",
  },
  {
    id: "SP20250525002",
    title: "键盘鼠标处置",
    type: "disposal",
    status: "approved",
    applicant: "当前用户",
    department: "技术部",
    applyTime: "2025-05-25 11:20",
    approver: "部门主管",
    approveTime: "2025-05-26 09:15",
    reason: "设备损坏严重，无法修复",
  },
]);

// 返回上一页
const goBack = () => {
  router.go(-1);
};

// 搜索
const onSearch = () => {
  console.log("搜索:", searchValue.value);
  // 实现搜索逻辑
};

// 加载更多
const onLoad = () => {
  setTimeout(() => {
    // 模拟加载更多数据
    loading.value = false;
    finished.value = true;
  }, 1000);
};
</script>

<style lang="scss" scoped>
.mobile-records {
  min-height: 100vh;
  background-color: #f7f8fa;

  .search-bar {
    padding: 8px 0;
  }

  .record-item {
    margin: 12px;
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    .record-header {
      padding: 12px;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #f5f5f5;

      .record-icon {
        margin-right: 12px;
        width: 32px;
        height: 32px;
        background-color: #f0f9ff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        .van-icon {
          color: #1989fa;
          font-size: 16px;
        }
      }

      .record-title {
        flex: 1;

        .title-text {
          font-size: 15px;
          font-weight: 500;
          margin-bottom: 4px;

          .record-id {
            color: #1989fa;
            margin-right: 4px;
          }
        }

        .record-type {
          font-size: 12px;
          color: #999;
        }
      }

      .record-status {
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;

        &.status-pending {
          background-color: #fff7e6;
          color: #ff976a;
        }

        &.status-approved {
          background-color: #f0fff0;
          color: #07c160;
        }

        &.status-rejected {
          background-color: #fff1f0;
          color: #ee0a24;
        }

        &.status-processing {
          background-color: #f0f9ff;
          color: #1989fa;
        }
      }
    }

    .record-info {
      padding: 12px;

      .info-row {
        display: flex;
        margin-bottom: 8px;
        font-size: 14px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          color: #666;
          width: 80px;
          flex-shrink: 0;
        }

        .value {
          flex: 1;
          color: #333;

          &.reason {
            line-height: 1.4;
          }
        }
      }
    }

    .record-footer {
      padding: 12px;
      display: flex;
      justify-content: flex-end;
      gap: 8px;
      border-top: 1px solid #f5f5f5;
    }
  }
}

:deep(.van-nav-bar) {
  background-color: #1989fa;

  .van-nav-bar__title {
    color: #fff;
  }

  .van-icon {
    color: #fff;
  }
}

:deep(.van-search__action) {
  color: #1989fa;
}

:deep(.van-tabs__line) {
  background-color: #1989fa;
}
</style>
