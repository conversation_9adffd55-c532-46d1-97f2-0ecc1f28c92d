<template>
  <div class="adaptive-403">
    <!-- 移动端403页面 -->
    <div v-if="isMobile" class="mobile-403">
      <div class="mobile-403-content">
        <van-empty
          image="error"
          :image-size="120"
          description="访问被拒绝"
        >
          <template #description>
            <div class="error-description">
              <h3>抱歉，您被拒绝访问此资源</h3>
              <p>您的权限不足，无法访问此页面</p>
              <p class="error-path">错误路径: {{ currentPath }}</p>
            </div>
          </template>
        </van-empty>
        
        <div class="mobile-403-actions">
          <van-button 
            type="primary" 
            block 
            @click="goHome"
          >
            返回首页
          </van-button>
          
          <van-button 
            block 
            @click="goBack"
            style="margin-top: 12px;"
          >
            返回上页
          </van-button>
          
          <van-button 
            block 
            plain 
            @click="contactAdmin"
            style="margin-top: 12px;"
          >
            联系管理员
          </van-button>
        </div>
      </div>
    </div>

    <!-- PC端403页面 -->
    <div v-else class="desktop-403">
      <div class="desktop-403-content">
        <el-result
          icon="warning"
          title="403"
          sub-title="抱歉，您被拒绝访问此资源"
        >
          <template #extra>
            <div class="error-info">
              <p class="error-path">错误路径: {{ currentPath }}</p>
              <p class="error-time">时间: {{ currentTime }}</p>
            </div>
            
            <div class="desktop-403-actions">
              <el-button type="primary" @click="goHome">
                返回首页
              </el-button>
              <el-button @click="goBack">
                返回上页
              </el-button>
              <el-button type="info" @click="contactAdmin">
                联系管理员
              </el-button>
            </div>
          </template>
        </el-result>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useDevice } from '@/composables/useDevice';
import { useRouter, useRoute } from 'vue-router';

defineOptions({
  name: "Adaptive403",
});

const { isMobile } = useDevice();
const router = useRouter();
const route = useRoute();

// 当前路径和时间
const currentPath = ref(route.fullPath);
const currentTime = ref(new Date().toLocaleString('zh-CN'));

// 返回首页
const goHome = () => {
  if (isMobile.value) {
    router.push('/mobile/dashboard').catch(() => {
      router.push('/');
    });
  } else {
    router.push('/dashboard').catch(() => {
      router.push('/');
    });
  }
};

// 返回上一页
const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1);
  } else {
    goHome();
  }
};

// 联系管理员
const contactAdmin = () => {
  if (isMobile.value) {
    console.log('联系管理员功能');
  } else {
    ElMessage.info('请联系系统管理员获取访问权限');
  }
};

// 监听路由变化，更新当前路径
watch(() => route.fullPath, (newPath) => {
  currentPath.value = newPath;
  currentTime.value = new Date().toLocaleString('zh-CN');
});
</script>

<style lang="scss" scoped>
.adaptive-403 {
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--el-bg-color-page, #f5f5f5);

  .mobile-403 {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    background: white;

    .mobile-403-content {
      width: 100%;
      max-width: 400px;
      text-align: center;

      .error-description {
        h3 {
          font-size: 18px;
          color: #323233;
          margin-bottom: 8px;
        }

        p {
          font-size: 14px;
          color: #646566;
          margin-bottom: 4px;
          line-height: 1.5;
        }

        .error-path {
          font-size: 12px;
          color: #969799;
          word-break: break-all;
          background: #f7f8fa;
          padding: 8px;
          border-radius: 4px;
          margin-top: 12px;
        }
      }
    }

    .mobile-403-actions {
      width: 100%;
      margin-top: 32px;
    }
  }

  .desktop-403 {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    .desktop-403-content {
      max-width: 600px;
      width: 100%;
      text-align: center;

      .error-info {
        margin-bottom: 24px;
        padding: 16px;
        background: var(--el-fill-color-light);
        border-radius: 8px;
        border-left: 4px solid var(--el-color-warning);

        p {
          margin: 4px 0;
          font-size: 14px;
          color: var(--el-text-color-regular);
        }

        .error-path {
          font-family: monospace;
          word-break: break-all;
          background: var(--el-fill-color-darker);
          padding: 8px;
          border-radius: 4px;
        }
      }

      .desktop-403-actions {
        display: flex;
        justify-content: center;
        gap: 16px;
        flex-wrap: wrap;
      }
    }
  }
}

// 确保在不同布局下都能正确显示
.layout-container .adaptive-403,
.mobile-layout .adaptive-403 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
}
</style>
