<!DOCTYPE html>
<html>
<head>
  <title>Colors test | anime.js</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <meta property="og:title" content="anime.js">
  <meta property="og:url" content="https://animejs.com">
  <meta property="og:description" content="Javascript Animation Engine">
  <meta property="og:image" content="https://animejs.com/documentation/assets/img/icons/og.png">
  <meta name="twitter:card" content="summary">
  <meta name="twitter:title" content="anime.js">
  <meta name="twitter:site" content="@juliangarnier">
  <meta name="twitter:description" content="Javascript Animation Engine">
  <meta name="twitter:image" content="https://animejs.com/documentation/assets/img/icons/twitter.png">
  <link rel="apple-touch-icon-precomposed" href="../assets/img/social-media-image.png">
  <link rel="icon" type="image/png" href="../assets/img/favicon.png">
  <link href="../assets/css/documentation.css" rel="stylesheet">
  <script src="../../lib/anime.min.js"></script>
  <!-- <script src="../assets/js/anime/anime.2.0.2.js"></script> -->
  <!-- <script src="../assets/js/anime/anime.1.3.js"></script> -->
  <style>
    body {
      width: 100%;
      height: 100vh;
    }
    .color-tests {
      display: flex;
      flex-wrap: wrap;
    }
    .section-title {
      display: block;
      width: 100%;
      margin-bottom: 10px;
      padding: 10px;
      border-bottom: 1px solid #FFF;
    }
    .color-test {
      flex-shrink: 2;
      text-align: center;
      font-size: 10px;
      line-height: 1em;
      font-family: monospace;
    }
    .color-el {
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      width: 12vw;
      height: 12vw;
      margin-top: 10px;
    }
  </style>
</head>
<body>
  <section class="color-section">
    <h1 class="section-title">HEX</h1>
    <div class="color-tests">
      <div class="color-test hex-to-hex-shorthand">#F47<br>▾<br>#6AF</div>
      <div class="color-test hex-to-hex">#FF4477<br>▾<br>#66AAFF</div>
      <div class="color-test hex-to-rgb">#FF4477<br>▾<br>rgb(102,170,255)</div>
      <div class="color-test hex-to-hsl">#FF4477<br>▾<br>hsl(213,100%,70%)</div>
      <div class="color-test hex-to-rgba">#FF4477<br>▾<br>rgba(102,170,255,.15)</div>
      <div class="color-test hex-to-hsla">#FF4477<br>▾<br>hsla(213,100%,70%,.15)</div>
    </div>
  </section>
  <section class="color-section">
    <h1 class="section-title">RGB</h1>
    <div class="color-tests">
      <div class="color-test rgb-to-hex-shorthand">rgb(255,68,119)<br>▾<br>#6AF</div>
      <div class="color-test rgb-to-hex">rgb(255,68,119)<br>▾<br>#66AAFF</div>
      <div class="color-test rgb-to-rgb">rgb(255,68,119)<br>▾<br>rgb(102,170,255)</div>
      <div class="color-test rgb-to-hsl">rgb(255,68,119)<br>▾<br>hsl(213,100%,70%)</div>
      <div class="color-test rgb-to-rgba">rgb(255,68,119)<br>▾<br>rgba(102,170,255,.15)</div>
      <div class="color-test rgb-to-hsla">rgb(255,68,119)<br>▾<br>hsla(213,100%,70%,.15)</div>
      <div class="color-test rgba-to-rgba">rgba(255,68,119,.5)<br>▾<br>rgba(102,170,255,.15)</div>
      <div class="color-test rgba-to-hsla">rgba(255,68,119,.5)<br>▾<br>hsla(213,100%,70%,.15)</div>
    </div>
  </section>
  <section class="color-section">
    <h1 class="section-title">HSL</h1>
    <div class="color-tests">
      <div class="color-test hsl-to-hex-shorthand">hsl(344,100%,63%)<br>▾<br>#6AF</div>
      <div class="color-test hsl-to-hex">hsl(344,100%,63%)<br>▾<br>#66AAFF</div>
      <div class="color-test hsl-to-rgb">hsl(344,100%,63%)<br>▾<br>rgb(102,170,255)</div>
      <div class="color-test hsl-to-hsl">hsl(344,100%,63%)<br>▾<br>hsl(213,100%,70%)</div>
      <div class="color-test hsl-to-rgba">hsl(344,100%,63%)<br>▾<br>rgba(102,170,255,.15)</div>
      <div class="color-test hsl-to-hsla">hsl(344,100%,63%)<br>▾<br>hsla(213,100%,70%,.15)</div>
      <div class="color-test hsla-to-rgba">hsla(344,100%,63%,.5)<br>▾<br>rgba(102,170,255,.15)</div>
      <div class="color-test hsla-to-hsla">hsla(344,100%,63%,.5)<br>▾<br>hsla(213,100%,70%,.15)</div>
    </div>
  </section>
  <script>

    var colorTestEls = document.querySelectorAll('.color-test');

    function createTest(el) {
      var testHtml = el.innerHTML;
      var testValues = testHtml.split('<br>▾<br>');
      var colorEl = document.createElement('div');
      colorEl.classList.add('color-el');
      el.appendChild(colorEl);
      anime({
        targets: colorEl,
        backgroundColor: [testValues[0], testValues[1]],
        scale: [.97, .75],
        direction: 'alternate',
        easing: 'easeInOutSine',
        duration: 4000,
        loop: true
      });
    }

    for (var i = 0; i < colorTestEls.length; i++) createTest(colorTestEls[i]);

  </script>
</body>
</html>
