/* 公共 */
@font-face {
    font-family: electronicFont;
    src: url(../fonts/DS-DIGIT.TTF)
}

html,
body {
    height: 100%
}

html {
    font-size: 1vw;
}

* {
    color: white;
}

body,
p,
ul {
    padding: 0;
    margin: 0;
}

li {
    list-style: none;
}

body {
    background: url(../images/background1.jpg);
    background-size: 100% 100%;
    /* background: #0b0952; */
}

/*头部*/
.header {
    width: 100rem;
    height: 6.3%;
}

.bg_header {
    background: url(../images/title.png) no-repeat;
    background-size: 100% 100%;
    height: 100%;
}

.header .header_nav {
    margin-left: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.header_title {
    text-align: center;
    color: #ffffff;
    font-size: 1.5rem;
    font-family: "Adobe 楷体 Std R";
    height: 100%;
}



/* 主体始 */
.data_main {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    height: 90%;
    margin-top: 2vh;
}

.main_left,
.main_center,
.main_right {
    height: 100%;
}

.more {
    position: absolute;
    right: 0.5rem;
    font-size: .5rem;
    cursor: pointer;
    top: 0;
    color: #dadada;
    line-height: 37px;
}


.content_box {
    border: 1px solid rgba(7, 118, 181, .5);
    box-shadow: inset 0 0 10px rgb(7 118 181 / 40%);
    margin-bottom: 3%;
    position: relative;
    flex-shrink: 0;
    overflow: hidden;
    box-sizing: border-box;
    width: 100%;
}
.content_box_main {
    border: 0;
    box-shadow: 0 0 0 0;
    background: url(../images/mainborder.png) no-repeat;
    background-size: 100% 100%;
}



.content_box:before {
    width: 100%;
    height: 1px;
    content: "";
    position: absolute;
    left: 0;
    background: linear-gradient(to right, #076ead, #4ba6e0, #076ead);
    box-shadow: 0 0 5px rgb(131 189 227);
    opacity: .6;
}

.content_box:before{
    content: "";
    box-shadow: 0 0 0 0;
    background: 0;
}

.content_box:before {
    top: 0px;
}

.alltitle:after {
    bottom: -1px;
}

/* 模块框 */
.boxall {
    padding: 0.5rem;
    z-index: 10;
}

/* 模块标题 */
.alltitle {
    padding: 10px 10px 10px 25px;
    border-bottom: 1px solid rgba(7, 118, 181, .5);
    font-size: 1rem;
    font-weight: 500;
    position: relative;
    line-height: 1rem;
    height: 16px;

}

.alltitle:before {
    position: absolute;
    content: "";
    width: 0.375rem;
    height: 0.375rem;
    background: rgba(22, 214, 255, .9);
    box-shadow: 0 0 5px rgb(22 214 255 / 90%);
    border-radius: 10px;
    left: 0.625rem;
    top: 1rem;
}

/* 模块体 */
.allcontent {
    height: 85%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: calc(100% - 37px);
}

/* 左侧 */
.main_left {
    width: 25rem
}

/* 居中 */
.main_center {
    width: 46rem
}

/* 右侧 */
.main_right {
    width: 25rem
}

/* 主体止 */

/* 动画 */
/* 加载旋转动画 */
#load {
    width: 100%;
    height: 100%;
    position: absolute;
    background: url(../images/data08.png) no-repeat #061537;
    background-size: cover;
    top: 0;
    left: 0;
    z-index: 999
}

#load .load_img {
    position: absolute;
    left: calc(50% - 182px);
    top: calc(50% - 182px);
}

.load_img img {
    position: absolute;
    left: 0;
    top: 0;
}

.load_img .jzxz1 {
    animation: xz1 8s infinite linear;
}

@keyframes xz1 {
    from {
        transform: rotate(0deg);
    }

    50% {
        transform: rotate(180deg);
    }

    to {
        transform: rotate(360deg);
    }
}

.load_img .jzxz2 {
    animation: xz2 7s infinite linear;
}

@keyframes xz2 {
    from {
        transform: rotate(0deg);
    }

    50% {
        transform: rotate(-180deg);
    }

    to {
        transform: rotate(-360deg);
    }
}
/* 公共止 */



/* 1号 */
.ylfw {
    display: flex;
    flex-wrap: wrap;
    align-content: center;
    justify-content: space-around;
    height: 100%;
}
.ylfw-t, .ylfw-b {
    height: 50%;
    display: flex;
    align-items: flex-end;
    width: 100%;
    justify-content: space-around;
}
.ylfwbox {
    margin: 0 0.5rem;
    position: relative;
    text-align: center;
    width: 7rem;
    margin-bottom: 15px;
    height: 95%;/* 用于兼容 */
    height: calc(100% - 30px);
    cursor:pointer;
}
.ylfwbox-text{
    margin-top: -20px;
}

.ylfwbox-text div {
    margin: 4% 0;
}

.ylfwbox p {
    font-size: 0.875rem;
    color:#CCD6D6;
}

.ylfwbox div span,
.ylfwbox div span label {
    font-size: 1.5rem;
    color: #23FFFC;
    text-shadow: 0 0 5px #23FFFC;
    font-family: electronicFont;
}


/* 4号 */
.clearfix {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    height: 100%;
    flex-direction: column;
    padding: 10px;
}
.clearfix-box{
    width: 48%;
    height: 93%;
}

.yqlist li {
    text-align: center;
    margin-top: 3%;
}

.yq {
    width: 5rem;
    height: 5rem;
    margin: 0 auto 0 auto;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.7rem;
    font-family: electronicFont;
    color: #cdb45c;

}

.yqlist li span {
    opacity: .8;
    font-size: 0.875rem;
}

/* .yq:before {
    position: absolute;
    width: 100%;
    height: 100%;
    content: "";
    background: url(../images/img1.png) center center;
    border-radius: 100px;
    background-size: 100% 100%;
    opacity: .5;
    left: 0;
    top: 0;
    animation: myfirst2 30s infinite linear;
}
#jz:hover:before {
    position: absolute;
    width: 100%;
    height: 100%;
    content: "";
    background: url(../images/img1_active.png) center center;
    border-radius: 100px;
    background-size: 100% 100%;
    opacity: .8;
    left: 0;
    top: 0;
    animation: myfirst2 30s infinite linear;
}

.yq:after {
    position: absolute;
    width: 100%;
    background: url(../images/img2.png) center center;
    border-radius: 100px;
    background-size: 100% 100%;
    opacity: .5;
    height: 100%;
    content: "";
    left: 0;
    top: 0;
    animation: myfirst 30s infinite linear;
}
#jz:hover:after {
    position: absolute;
    width: 100%;
    background: url(../images/img2_active.png) center center;
    border-radius: 100px;
    background-size: 100% 100%;
    opacity: .8;
    height: 100%;
    content: "";
    left: 0;
    top: 0;
    animation: myfirst 30s infinite linear;
} */
@keyframes myfirst {
    to {
        transform: rotate(-360deg)
    }
}

@keyframes myfirst2 {
    to {
        transform: rotate(360deg)
    }
}


/* 5号 */
.Main_top {
    height: 60%;
}

.Main_top ul {
    display: flex;
    flex-wrap: wrap;
    padding: 0;
    justify-content: space-evenly;
}

.Main_top ul li {
    padding-top: 3%;
    width: 22%;
}

.Main_top ul li .liIn {
    border: 1px solid rgba(14, 253, 255, 0.5);
    position: relative;
    padding: 2% 10%;
    height: 90%;
}

.Main_top ul li .liIn h3 {
    font-size: 0.8rem;
    color: #fff;
    margin: 4% 0;
}

.Main_top ul li .liIn .shu,
.Main_top ul li .liIn .shu1 {
    font-size: 1.2rem;
    color: rgba(14, 253, 255, 1);
    font-family: dig;
    margin-bottom: 1%
}

.Main_top ul li .liIn .shu i {
    font-size: 0.4rem;
    margin-left: 0.6rem;
    font-style: normal;
}

.Main_top ul li .liIn .zi {
    font-size: 0.4rem;
    color: #fff;
    position: relative;
    z-index: 10;
}

.border_bg_leftTop {
    position: absolute;
    left: -0.08rem;
    top: -0.4rem;
    width: 3.8rem;
    height: 0.5rem;
    display: block;
    background: url(../images/title_left_bg.png) no-repeat;
    background-size: cover;
}

.border_bg_rightBottom {
    position: absolute;
    right: -0.1rem;
    bottom: -0.1rem;
    width: 0.8rem;
    height: 0.8rem;
    display: block;
    background: url(../images/title_right_bg.png) no-repeat;
    background-size: cover;
}

.warn-box {
    width:100%;
    height:100%;
}

.warn-box-img {
    width: 35%;
}

.warn-box-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.warn-box-name {
    font-weight: bold;
    font-size: 1rem;
}

.warn-box-val {
    width: 100%;
    margin-bottom: 5%;
    font-weight: bold;
}

/* 6号 */
.biaoge_list {
    overflow: hidden;
    position: relative;
    height: 100%;
}

.biaoge_list .ul_title {
    background: linear-gradient(left, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.1));
    height: 17%;
}

.biaoge_list ul li {
    width: 20%;
    text-align: center;
    color: #fff;
    font-size: 0.6rem;
    line-height: 2rem;
}

.biaoge_list .biaoge_listIn {
    height: 100%;

}

.biaoge_list .biaoge_listIn .ul_list {
    overflow: hidden;
    position: relative;
}

.biaoge_list .biaoge_listIn .ul_listIn {
    animation: 14s gundong linear infinite normal;
    position: relative;
}

@keyframes gundong {
    0% {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }

    100% {
        -webkit-transform: translate3d(0, -30vh, 0);
        transform: translate3d(0, -10vh, 0);
    }
}

.biaoge_list ul {
    display: flex;
    width: 100%;
    align-items: center;
}

.biaoge_list .ul_con {
    border-bottom: 1px solid rgba(14, 253, 255, 0.5);
}

.pic
{
	position:relative;
	display: flex;
    justify-content: center;
    align-items: center;
}
.pic_red:before
{
	content:"";
	position:absolute;
	width:18px;
	height:18px;
	background:url(../images/icon_red.png) no-repeat center center;
	background-size: 90%;
}
.pic_yellow:before
{
	content:"";
	position:absolute;
	width:18px;
	height:18px;
	background:url(../images/icon_yellow.png) no-repeat center center;
	background-size: 90%;
}
.pic_blue:before
{
	content:"";
	position:absolute;
	width:16px;
	height:16px;
	background:url(../images/icon_blue.png) no-repeat center center;
	background-size: 90%;
}

/* 闭环 */
.bh-box{
    margin: 10px;
    height: 30%;
    height: calc(33% - 13px);
}
.bh-title{
    display: flex;
    width: 30%;
    height: 30%;
    font-size: .8rem;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
}
.q .bh-title{
    background:url(../images/bh-icon1.png) no-repeat;
    background-size: 100% 100%;
}

.z .bh-title{
    background:url(../images/bh-icon2.png) no-repeat;
    background-size: 100% 100%;
}
.h .bh-title{
    background:url(../images/bh-icon3.png) no-repeat;
    background-size: 100% 100%;
}

.bh-title div{
    text-align: center;
}
.bh-chart{
    height: 64%;
    margin: 10px 0;
    background:url(../images/bh_icon4.png) no-repeat;
    background-size: 100% 100%;
}
.bh-row{
    display: flex;
    margin:5px 0 ;
 
}
.bh-bar{
    height: 8px;
    min-width: 1%
}
.bh-red{
    background-color: red;
    background: linear-gradient(to right,rgba(255, 255, 255, 0.8), red);
}
.bh-yellow{
    background-color: yellow;
    background: linear-gradient(to right,rgba(255, 255, 255, 0.8), yellow);

}
.bh-blue{
    background-color: rgb(58, 127, 255);
    background: linear-gradient(to right,rgba(255, 255, 255, 0.8), rgb(0, 162, 255));
}
.bh-line{
    margin-left: 5% ;
    border-bottom: 1px dashed gray;
    height: 4px;
}
.bh-text{
    line-height: 10px;
    font-size: .8rem;
    margin-left: 2%;
    color:#23FFFC
}


/* 9号 */
.res-circle {
    position: relative;
    width: 5.5rem;
    height: 5.5rem;
    display: flex;
    flex-direction: column;
    background: url(../images/main_icon21.png) no-repeat 50% 50%;
    background-size: contain;
}

/* .res-circle:before {
    position: absolute;
    width: 100%;
    height: 100%;
    content: "";
    background: url(../images/img1.png) center center;
    border-radius: 100px;
    background-size: 100% 100%;
    opacity: .3;
    left: 0;
    top: 10%;
    animation: myfirst2 15s infinite linear;
}

.res-circle:after {
    position: absolute;
    width: 86%;
    background: url(../images/img2.png) center center;
    border-radius: 100px;
    background-size: 100% 100%;
    opacity: .3;
    height: 86%;
    content: "";
    left: 7%;
    top: 17%;
    animation: myfirst 15s infinite linear;
} */


.res-val {
    text-align: center;
    font-size: 1.1rem;
    color:#23FFFC
}

.res-name {
    text-align: center;
    font-size: .9rem;

}

.res-box {
    display: flex;
    height: 100%;
    width: 100%;
}

.res-border {
    width: 40%;
    position: relative;
    height: 28%;
    border: 1px solid #1070aa;
}

.res-top:before,
.res-top:after,
.res-bottom:before,
.res-bottom:after {
    content: "";
    width: 2px;
    height: 2px;
    border: 1px solid #00deff;
    opacity: .8;
    box-shadow: 0 0 5px #00deff;
}

.res-top:before {
    position: absolute;
    top: -2px;
    left: -2px;
}

.res-top:after {
    position: absolute;
    top: -2px;
    right: -2px;
}

.res-bottom:before {
    position: absolute;
    bottom: -2px;
    left: -2px;
}

.res-bottom:after {
    position: absolute;
    bottom: -2px;
    right: -2px;
}

.res-box-content {
    width: 65%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.res-box-val {
    margin: 5% 0;
    font-family: electronicFont;
    font-size: 1.5rem;
    /* color:#23FFFC */
}

.res-box-name {
    font-size: 0.875rem;
}

.res-box-img {
    width: 35%;

}











/* ----------------------------------------------------- */
.ul_con .warn_title{
    overflow: hidden;
    word-break: keep-all;
    white-space: nowrap;width:30%;    
    text-align: left;
    padding-left: 5px;
}
.content-t{
    display: flex;
    justify-content: space-evenly;
}
.content-main{
    background: url(../images/main1.png) no-repeat 50% 70%;
    background-size: 45%;
}
.content-s-c{
    height: 100%;
    width: 70%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}
.content-s-row{
    width: 80%;
    display: flex;
    height: 17%;
    justify-content: center;
    margin: 10%;
    background: url(../images/main_icon1.png) no-repeat;
    background-size: 100% 100%;
}
.content-s-box{
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}
.content-s-word{
    font-size: 12px;
}
.content-s{
    display: flex;
    align-items: center;
    justify-content: flex-start;
}
