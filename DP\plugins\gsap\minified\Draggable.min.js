/*!
 * Draggable 3.11.3
 * https://greensock.com
 * 
 * @license Copyright 2022, GreenSock. All rights reserved.
 * Subject to the terms at https://greensock.com/standard-license or for Club GreenSock members, the agreement issued with that membership.
 * @author: <PERSON>, <EMAIL>
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).window=e.window||{})}(this,function(e){"use strict";function w(e,t){if(e.parentNode&&(h||T(e))){var n=L(e),o=n?n.getAttribute("xmlns")||"http://www.w3.org/2000/svg":"http://www.w3.org/1999/xhtml",r=n?t?"rect":"g":"div",i=2!==t?0:100,a=3===t?100:0,l="position:absolute;display:block;pointer-events:none;margin:0;padding:0;",s=h.createElementNS?h.createElementNS(o.replace(/^https/,"http"),r):h.createElement(r);return t&&(n?(g=g||w(e),s.setAttribute("width",.01),s.setAttribute("height",.01),s.setAttribute("transform","translate("+i+","+a+")"),g.appendChild(s)):(f||((f=w(e)).style.cssText=l),s.style.cssText=l+"width:0.1px;height:0.1px;top:"+a+"px;left:"+i+"px",f.appendChild(s))),s}throw"Need document and parent."}function A(e,t,n,o,r,i,a){return e.a=t,e.b=n,e.c=o,e.d=r,e.e=i,e.f=a,e}var h,u,r,i,f,g,x,m,y,t,v="transform",b=v+"Origin",T=function _setDoc(e){var t=e.ownerDocument||e;!(v in e.style)&&"msTransform"in e.style&&(b=(v="msTransform")+"Origin");for(;t.parentNode&&(t=t.parentNode););if(u=window,x=new ge,t){r=(h=t).documentElement,i=t.body,(m=h.createElementNS("http://www.w3.org/2000/svg","g")).style.transform="none";var n=t.createElement("div"),o=t.createElement("div");i.appendChild(n),n.appendChild(o),n.style.position="static",n.style[v]="translate3d(0,0,1px)",y=o.offsetParent!==n,i.removeChild(n)}return t},D=function _forceNonZeroScale(e){for(var t,n;e&&e!==i;)(n=e._gsap)&&n.uncache&&n.get(e,"x"),n&&!n.scaleX&&!n.scaleY&&n.renderTransform&&(n.scaleX=n.scaleY=1e-4,n.renderTransform(1,n),t?t.push(n):t=[n]),e=e.parentNode;return t},E=[],M=[],S=function _getDocScrollTop(){return u.pageYOffset||h.scrollTop||r.scrollTop||i.scrollTop||0},N=function _getDocScrollLeft(){return u.pageXOffset||h.scrollLeft||r.scrollLeft||i.scrollLeft||0},L=function _svgOwner(e){return e.ownerSVGElement||("svg"===(e.tagName+"").toLowerCase()?e:null)},C=function _isFixed(e){return"fixed"===u.getComputedStyle(e).position||((e=e.parentNode)&&1===e.nodeType?_isFixed(e):void 0)},P=function _placeSiblings(e,t){var n,o,r,i,a,l,s=L(e),c=e===s,d=s?E:M,p=e.parentNode;if(e===u)return e;if(d.length||d.push(w(e,1),w(e,2),w(e,3)),n=s?g:f,s)c?(i=-(r=function _getCTM(e){var t,n=e.getCTM();return n||(t=e.style[v],e.style[v]="none",e.appendChild(m),n=m.getCTM(),e.removeChild(m),t?e.style[v]=t:e.style.removeProperty(v.replace(/([A-Z])/g,"-$1").toLowerCase())),n||x.clone()}(e)).e/r.a,a=-r.f/r.d,o=x):e.getBBox?(r=e.getBBox(),i=(o=(o=e.transform?e.transform.baseVal:{}).numberOfItems?1<o.numberOfItems?function _consolidate(e){for(var t=new ge,n=0;n<e.numberOfItems;n++)t.multiply(e.getItem(n).matrix);return t}(o):o.getItem(0).matrix:x).a*r.x+o.c*r.y,a=o.b*r.x+o.d*r.y):(o=new ge,i=a=0),t&&"g"===e.tagName.toLowerCase()&&(i=a=0),(c?s:p).appendChild(n),n.setAttribute("transform","matrix("+o.a+","+o.b+","+o.c+","+o.d+","+(o.e+i)+","+(o.f+a)+")");else{if(i=a=0,y)for(o=e.offsetParent,r=e;(r=r&&r.parentNode)&&r!==o&&r.parentNode;)4<(u.getComputedStyle(r)[v]+"").length&&(i=r.offsetLeft,a=r.offsetTop,r=0);if("absolute"!==(l=u.getComputedStyle(e)).position&&"fixed"!==l.position)for(o=e.offsetParent;p&&p!==o;)i+=p.scrollLeft||0,a+=p.scrollTop||0,p=p.parentNode;(r=n.style).top=e.offsetTop-a+"px",r.left=e.offsetLeft-i+"px",r[v]=l[v],r[b]=l[b],r.position="fixed"===l.position?"fixed":"absolute",e.parentNode.appendChild(n)}return n},ge=((t=Matrix2D.prototype).inverse=function inverse(){var e=this.a,t=this.b,n=this.c,o=this.d,r=this.e,i=this.f,a=e*o-t*n||1e-10;return A(this,o/a,-t/a,-n/a,e/a,(n*i-o*r)/a,-(e*i-t*r)/a)},t.multiply=function multiply(e){var t=this.a,n=this.b,o=this.c,r=this.d,i=this.e,a=this.f,l=e.a,s=e.c,c=e.b,d=e.d,p=e.e,u=e.f;return A(this,l*t+c*o,l*n+c*r,s*t+d*o,s*n+d*r,i+p*t+u*o,a+p*n+u*r)},t.clone=function clone(){return new Matrix2D(this.a,this.b,this.c,this.d,this.e,this.f)},t.equals=function equals(e){var t=this.a,n=this.b,o=this.c,r=this.d,i=this.e,a=this.f;return t===e.a&&n===e.b&&o===e.c&&r===e.d&&i===e.e&&a===e.f},t.apply=function apply(e,t){void 0===t&&(t={});var n=e.x,o=e.y,r=this.a,i=this.b,a=this.c,l=this.d,s=this.e,c=this.f;return t.x=n*r+o*a+s||0,t.y=n*i+o*l+c||0,t},Matrix2D);function Matrix2D(e,t,n,o,r,i){void 0===e&&(e=1),void 0===t&&(t=0),void 0===n&&(n=0),void 0===o&&(o=1),void 0===r&&(r=0),void 0===i&&(i=0),A(this,e,t,n,o,r,i)}function getGlobalMatrix(e,t,n,o){if(!e||!e.parentNode||(h||T(e)).documentElement===e)return new ge;var r=D(e),i=L(e)?E:M,a=P(e,n),l=i[0].getBoundingClientRect(),s=i[1].getBoundingClientRect(),c=i[2].getBoundingClientRect(),d=a.parentNode,p=!o&&C(e),u=new ge((s.left-l.left)/100,(s.top-l.top)/100,(c.left-l.left)/100,(c.top-l.top)/100,l.left+(p?0:N()),l.top+(p?0:S()));if(d.removeChild(a),r)for(l=r.length;l--;)(s=r[l]).scaleX=s.scaleY=0,s.renderTransform(1,s);return t?u.inverse():u}function V(){return"undefined"!=typeof window}function W(){return xe||V()&&(xe=window.gsap)&&xe.registerPlugin&&xe}function X(e){return"function"==typeof e}function Y(e){return"object"==typeof e}function Z(e){return void 0===e}function $(){return!1}function ba(e){return Math.round(1e4*e)/1e4}function da(e,t){var n=ye.createElementNS?ye.createElementNS((t||"http://www.w3.org/1999/xhtml").replace(/^https/,"http"),e):ye.createElement(e);return n.style?n:ye.createElement(e)}function pa(e,t){var n,o={};for(n in e)o[n]=t?e[n]*t:e[n];return o}function ra(e,t){for(var n,o=e.length;o--;)t?e[o].style.touchAction=t:e[o].style.removeProperty("touch-action"),(n=e[o].children)&&n.length&&ra(n,t)}function sa(){return Re.forEach(function(e){return e()})}function ua(){return!Re.length&&xe.ticker.remove(sa)}function va(e){for(var t=Re.length;t--;)Re[t]===e&&Re.splice(t,1);xe.to(ua,{overwrite:!0,delay:15,duration:0,onComplete:ua,data:"_draggable"})}function xa(e,t,n,o){if(e.addEventListener){var r=Ee[t];o=o||(d?{passive:!1}:null),e.addEventListener(r||t,n,o),r&&t!==r&&e.addEventListener(t,n,o)}}function ya(e,t,n){if(e.removeEventListener){var o=Ee[t];e.removeEventListener(o||t,n),o&&t!==o&&e.removeEventListener(t,n)}}function za(e){e.preventDefault&&e.preventDefault(),e.preventManipulation&&e.preventManipulation()}function Ba(e){Me=e.touches&&Ye<e.touches.length,ya(e.target,"touchend",Ba)}function Ca(e){Me=e.touches&&Ye<e.touches.length,xa(e.target,"touchend",Ba)}function Da(e){return me.pageYOffset||e.scrollTop||e.documentElement.scrollTop||e.body.scrollTop||0}function Ea(e){return me.pageXOffset||e.scrollLeft||e.documentElement.scrollLeft||e.body.scrollLeft||0}function Fa(e,t){xa(e,"scroll",t),ze(e.parentNode)||Fa(e.parentNode,t)}function Ga(e,t){ya(e,"scroll",t),ze(e.parentNode)||Ga(e.parentNode,t)}function Ia(e,t){var n="x"===t?"Width":"Height",o="scroll"+n,r="client"+n;return Math.max(0,ze(e)?Math.max(ve[o],l[o])-(me["inner"+n]||ve[r]||l[r]):e[o]-e[r])}function Ja(e,t){var n=Ia(e,"x"),o=Ia(e,"y");ze(e)?e=He:Ja(e.parentNode,t),e._gsMaxScrollX=n,e._gsMaxScrollY=o,t||(e._gsScrollX=e.scrollLeft||0,e._gsScrollY=e.scrollTop||0)}function Ka(e,t,n){var o=e.style;o&&(Z(o[t])&&(t=c(t,e)||t),null==n?o.removeProperty&&o.removeProperty(t.replace(/([A-Z])/g,"-$1").toLowerCase()):o[t]=n)}function La(e){return me.getComputedStyle(e instanceof Element?e:e.host||(e.parentNode||{}).host||e)}function Na(e){if(e===me)return p.left=p.top=0,p.width=p.right=ve.clientWidth||e.innerWidth||l.clientWidth||0,p.height=p.bottom=(e.innerHeight||0)-20<ve.clientHeight?ve.clientHeight:e.innerHeight||l.clientHeight||0,p;var t=e.ownerDocument||ye,n=Z(e.pageX)?e.nodeType||Z(e.left)||Z(e.top)?Te(e)[0].getBoundingClientRect():e:{left:e.pageX-Ea(t),top:e.pageY-Da(t),right:e.pageX-Ea(t)+1,bottom:e.pageY-Da(t)+1};return Z(n.right)&&!Z(n.width)?(n.right=n.left+n.width,n.bottom=n.top+n.height):Z(n.width)&&(n={width:n.right-n.left,height:n.bottom-n.top,right:n.right,left:n.left,bottom:n.bottom,top:n.top}),n}function Oa(e,t,n){var o,r=e.vars,i=r[n],a=e._listeners[t];return X(i)&&(o=i.apply(r.callbackScope||e,r[n+"Params"]||[e.pointerEvent])),a&&!1===e.dispatchEvent(t)&&(o=!1),o}function Pa(e,t){var n,o,r,i=Te(e)[0];return i.nodeType||i===me?R(i,t):Z(e.left)?{left:o=e.min||e.minX||e.minRotation||0,top:n=e.min||e.minY||0,width:(e.max||e.maxX||e.maxRotation||0)-o,height:(e.max||e.maxY||0)-n}:(r={x:0,y:0},{left:e.left-r.x,top:e.top-r.y,width:e.width,height:e.height})}function Sa(r,i,e,t,a,n){var o,l,s,c={};if(i)if(1!==a&&i instanceof Array){if(c.end=o=[],s=i.length,Y(i[0]))for(l=0;l<s;l++)o[l]=pa(i[l],a);else for(l=0;l<s;l++)o[l]=i[l]*a;e+=1.1,t-=1.1}else X(i)?c.end=function(e){var t,n,o=i.call(r,e);if(1!==a)if(Y(o)){for(n in t={},o)t[n]=o[n]*a;o=t}else o*=a;return o}:c.end=i;return!e&&0!==e||(c.max=e),!t&&0!==t||(c.min=t),n&&(c.velocity=0),c}function Ta(e){var t;return!(!e||!e.getAttribute||e===l)&&(!("true"!==(t=e.getAttribute("data-clickable"))&&("false"===t||!e.onclick&&!o.test(e.nodeName+"")&&"true"!==e.getAttribute("contentEditable")))||Ta(e.parentNode))}function Ua(e,t){for(var n,o=e.length;o--;)(n=e[o]).ondragstart=n.onselectstart=t?null:$,xe.set(n,{lazy:!0,userSelect:t?"text":"none"})}function Ya(i,r){i=xe.utils.toArray(i)[0],r=r||{};var a,l,s,e,c,d,p=document.createElement("div"),u=p.style,t=i.firstChild,h=0,f=0,g=i.scrollTop,x=i.scrollLeft,m=i.scrollWidth,y=i.scrollHeight,v=0,w=0,b=0;O&&!1!==r.force3D?(c="translate3d(",d="px,0px)"):_&&(c="translate(",d="px)"),this.scrollTop=function(e,t){if(!arguments.length)return-this.top();this.top(-e,t)},this.scrollLeft=function(e,t){if(!arguments.length)return-this.left();this.left(-e,t)},this.left=function(e,t){if(!arguments.length)return-(i.scrollLeft+f);var n=i.scrollLeft-x,o=f;if((2<n||n<-2)&&!t)return x=i.scrollLeft,xe.killTweensOf(this,{left:1,scrollLeft:1}),this.left(-x),void(r.onKill&&r.onKill());(e=-e)<0?(f=e-.5|0,e=0):w<e?(f=e-w|0,e=w):f=0,(f||o)&&(this._skip||(u[_]=c+-f+"px,"+-h+d),0<=f+v&&(u.paddingRight=f+v+"px")),i.scrollLeft=0|e,x=i.scrollLeft},this.top=function(e,t){if(!arguments.length)return-(i.scrollTop+h);var n=i.scrollTop-g,o=h;if((2<n||n<-2)&&!t)return g=i.scrollTop,xe.killTweensOf(this,{top:1,scrollTop:1}),this.top(-g),void(r.onKill&&r.onKill());(e=-e)<0?(h=e-.5|0,e=0):b<e?(h=e-b|0,e=b):h=0,(h||o)&&(this._skip||(u[_]=c+-f+"px,"+-h+d)),i.scrollTop=0|e,g=i.scrollTop},this.maxScrollTop=function(){return b},this.maxScrollLeft=function(){return w},this.disable=function(){for(t=p.firstChild;t;)e=t.nextSibling,i.appendChild(t),t=e;i===p.parentNode&&i.removeChild(p)},this.enable=function(){if((t=i.firstChild)!==p){for(;t;)e=t.nextSibling,p.appendChild(t),t=e;i.appendChild(p),this.calibrate()}},this.calibrate=function(e){var t,n,o,r=i.clientWidth===a;g=i.scrollTop,x=i.scrollLeft,r&&i.clientHeight===l&&p.offsetHeight===s&&m===i.scrollWidth&&y===i.scrollHeight&&!e||((h||f)&&(n=this.left(),o=this.top(),this.left(-i.scrollLeft),this.top(-i.scrollTop)),t=La(i),r&&!e||(u.display="block",u.width="auto",u.paddingRight="0px",(v=Math.max(0,i.scrollWidth-i.clientWidth))&&(v+=parseFloat(t.paddingLeft)+(k?parseFloat(t.paddingRight):0))),u.display="inline-block",u.position="relative",u.overflow="visible",u.verticalAlign="top",u.boxSizing="content-box",u.width="100%",u.paddingRight=v+"px",k&&(u.paddingBottom=t.paddingBottom),a=i.clientWidth,l=i.clientHeight,m=i.scrollWidth,y=i.scrollHeight,w=i.scrollWidth-a,b=i.scrollHeight-l,s=p.offsetHeight,u.display="block",(n||o)&&(this.left(n),this.top(o)))},this.content=p,this.element=i,this._skip=!1,this.enable()}function Za(e){if(V()&&document.body){var t=window&&window.navigator;me=window,ye=document,ve=ye.documentElement,l=ye.body,s=da("div"),Le=!!window.PointerEvent,(we=da("div")).style.cssText="visibility:hidden;height:1px;top:-1px;pointer-events:none;position:relative;clear:both;cursor:grab",Ne="grab"===we.style.cursor?"grab":"move",Xe=t&&-1!==t.userAgent.toLowerCase().indexOf("android"),De="ontouchstart"in ve&&"orientation"in me||t&&(0<t.MaxTouchPoints||0<t.msMaxTouchPoints),o=da("div"),r=da("div"),i=r.style,a=l,i.display="inline-block",i.position="relative",o.style.cssText="width:90px;height:40px;padding:10px;overflow:auto;visibility:hidden",o.appendChild(r),a.appendChild(o),n=r.offsetHeight+18>o.scrollHeight,a.removeChild(o),k=n,Ee=function(e){for(var t=e.split(","),n=(("onpointerdown"in s?"pointerdown,pointermove,pointerup,pointercancel":"onmspointerdown"in s?"MSPointerDown,MSPointerMove,MSPointerUp,MSPointerCancel":e).split(",")),o={},r=4;-1<--r;)o[t[r]]=n[r],o[n[r]]=t[r];try{ve.addEventListener("test",null,Object.defineProperty({},"passive",{get:function get(){d=1}}))}catch(e){}return o}("touchstart,touchmove,touchend,touchcancel"),xa(ye,"touchcancel",$),xa(me,"touchmove",$),l&&l.addEventListener("touchstart",$),xa(ye,"contextmenu",function(){for(var e in Ae)Ae[e].isPressed&&Ae[e].endDrag()}),xe=be=W()}var n,o,r,i,a;xe?(Se=xe.plugins.inertia,c=xe.utils.checkPrefix,_=c(_),Ce=c(Ce),Te=xe.utils.toArray,O=!!c("perspective")):e&&console.warn("Please gsap.registerPlugin(Draggable)")}var xe,me,ye,ve,l,s,we,be,c,Te,d,De,Ee,Me,Xe,Se,Ne,Le,O,k,n,Ye=0,_="transform",Ce="transformOrigin",Pe=Array.isArray,Oe=180/Math.PI,ke=1e20,a=new ge,_e=Date.now||function(){return(new Date).getTime()},Re=[],Ae={},Be=0,o=/^(?:a|input|textarea|button|select)$/i,Ie=0,Fe={},He={},ze=function _isRoot(e){return!(e&&e!==ve&&9!==e.nodeType&&e!==ye.body&&e!==me&&e.nodeType&&e.parentNode)},p={},We={},R=function _getElementBounds(e,t){t=Te(t)[0];var n,o,r,i,a,l,s,c,d,p,u,h,f,g=e.getBBox&&e.ownerSVGElement,x=e.ownerDocument||ye;if(e===me)r=Da(x),o=(n=Ea(x))+(x.documentElement.clientWidth||e.innerWidth||x.body.clientWidth||0),i=r+((e.innerHeight||0)-20<x.documentElement.clientHeight?x.documentElement.clientHeight:e.innerHeight||x.body.clientHeight||0);else{if(t===me||Z(t))return e.getBoundingClientRect();n=r=0,g?(u=(p=e.getBBox()).width,h=p.height):(e.viewBox&&(p=e.viewBox.baseVal)&&(n=p.x||0,r=p.y||0,u=p.width,h=p.height),u||(p="border-box"===(f=La(e)).boxSizing,u=(parseFloat(f.width)||e.clientWidth||0)+(p?0:parseFloat(f.borderLeftWidth)+parseFloat(f.borderRightWidth)),h=(parseFloat(f.height)||e.clientHeight||0)+(p?0:parseFloat(f.borderTopWidth)+parseFloat(f.borderBottomWidth)))),o=u,i=h}return e===t?{left:n,top:r,width:o-n,height:i-r}:(l=(a=getGlobalMatrix(t,!0).multiply(getGlobalMatrix(e))).apply({x:n,y:r}),s=a.apply({x:o,y:r}),c=a.apply({x:o,y:i}),d=a.apply({x:n,y:i}),{left:n=Math.min(l.x,s.x,c.x,d.x),top:r=Math.min(l.y,s.y,c.y,d.y),width:Math.max(l.x,s.x,c.x,d.x)-n,height:Math.max(l.y,s.y,c.y,d.y)-r})},B=((n=EventDispatcher.prototype).addEventListener=function addEventListener(e,t){var n=this._listeners[e]||(this._listeners[e]=[]);~n.indexOf(t)||n.push(t)},n.removeEventListener=function removeEventListener(e,t){var n=this._listeners[e],o=n&&n.indexOf(t);0<=o&&n.splice(o,1)},n.dispatchEvent=function dispatchEvent(t){var n,o=this;return(this._listeners[t]||[]).forEach(function(e){return!1===e.call(o,{type:t,target:o.target})&&(n=!1)}),n},EventDispatcher);function EventDispatcher(e){this._listeners={},this.target=e||this}var Ge,I=(function _inheritsLoose(e,t){e.prototype=Object.create(t.prototype),(e.prototype.constructor=e).__proto__=t}(Draggable,Ge=B),Draggable.register=function register(e){xe=e,Za()},Draggable.create=function create(e,t){return be||Za(!0),Te(e).map(function(e){return new Draggable(e,t)})},Draggable.get=function get(e){return Ae[(Te(e)[0]||{})._gsDragID]},Draggable.timeSinceDrag=function timeSinceDrag(){return(_e()-Ie)/1e3},Draggable.hitTest=function hitTest(e,t,n){if(e===t)return!1;var o,r,i,a=Na(e),l=Na(t),s=a.top,c=a.left,d=a.right,p=a.bottom,u=a.width,h=a.height,f=l.left>d||l.right<c||l.top>p||l.bottom<s;return f||!n?!f:(i=-1!==(n+"").indexOf("%"),n=parseFloat(n)||0,(o={left:Math.max(c,l.left),top:Math.max(s,l.top)}).width=Math.min(d,l.right)-o.left,o.height=Math.min(p,l.bottom)-o.top,!(o.width<0||o.height<0)&&(i?u*h*(n*=.01)<=(r=o.width*o.height)||r>=l.width*l.height*n:o.width>n&&o.height>n))},Draggable);function Draggable(h,p){var e;e=Ge.call(this)||this,be||Za(1),h=Te(h)[0],Se=Se||xe.plugins.inertia,e.vars=p=pa(p||{}),e.target=h,e.x=e.y=e.rotation=0,e.dragResistance=parseFloat(p.dragResistance)||0,e.edgeResistance=isNaN(p.edgeResistance)?1:parseFloat(p.edgeResistance)||0,e.lockAxis=p.lockAxis,e.autoScroll=p.autoScroll||0,e.lockedAxis=null,e.allowEventDefault=!!p.allowEventDefault,xe.getProperty(h,"x");function Ng(e,t){return parseFloat(se.get(h,e,t))}function uh(e){return za(e),e.stopImmediatePropagation&&e.stopImmediatePropagation(),!1}function vh(e){if(q.autoScroll&&q.isDragging&&(te||P)){var t,n,o,r,i,a,l,s,c=h,d=15*q.autoScroll;for(te=!1,He.scrollTop=null!=me.pageYOffset?me.pageYOffset:null!=de.documentElement.scrollTop?de.documentElement.scrollTop:de.body.scrollTop,He.scrollLeft=null!=me.pageXOffset?me.pageXOffset:null!=de.documentElement.scrollLeft?de.documentElement.scrollLeft:de.body.scrollLeft,r=q.pointerX-He.scrollLeft,i=q.pointerY-He.scrollTop;c&&!n;)t=(n=ze(c.parentNode))?He:c.parentNode,o=n?{bottom:Math.max(ve.clientHeight,me.innerHeight||0),right:Math.max(ve.clientWidth,me.innerWidth||0),left:0,top:0}:t.getBoundingClientRect(),a=l=0,j&&((s=t._gsMaxScrollY-t.scrollTop)<0?l=s:i>o.bottom-re&&s?(te=!0,l=Math.min(s,d*(1-Math.max(0,o.bottom-i)/re)|0)):i<o.top+ne&&t.scrollTop&&(te=!0,l=-Math.min(t.scrollTop,d*(1-Math.max(0,i-o.top)/ne)|0)),l&&(t.scrollTop+=l)),$&&((s=t._gsMaxScrollX-t.scrollLeft)<0?a=s:r>o.right-oe&&s?(te=!0,a=Math.min(s,d*(1-Math.max(0,o.right-r)/oe)|0)):r<o.left+ie&&t.scrollLeft&&(te=!0,a=-Math.min(t.scrollLeft,d*(1-Math.max(0,r-o.left)/ie)|0)),a&&(t.scrollLeft+=a)),n&&(a||l)&&(me.scrollTo(t.scrollLeft,t.scrollTop),he(q.pointerX+a,q.pointerY+l)),c=t}if(P){var p=q.x,u=q.y;K?(q.deltaX=p-parseFloat(se.rotation),q.rotation=p,se.rotation=p+"deg",se.renderTransform(1,se)):f?(j&&(q.deltaY=u-f.top(),f.top(u)),$&&(q.deltaX=p-f.left(),f.left(p))):G?(j&&(q.deltaY=u-parseFloat(se.y),se.y=u+"px"),$&&(q.deltaX=p-parseFloat(se.x),se.x=p+"px"),se.renderTransform(1,se)):(j&&(q.deltaY=u-parseFloat(h.style.top||0),h.style.top=u+"px"),$&&(q.deltaX=p-parseFloat(h.style.left||0),h.style.left=p+"px")),!g||e||F||(!(F=!0)===Oa(q,"drag","onDrag")&&($&&(q.x-=q.deltaX),j&&(q.y-=q.deltaY),vh(!0)),F=!1)}P=!1}function wh(e,t){var n,o,r=q.x,i=q.y;h._gsap||(se=xe.core.getCache(h)),se.uncache&&xe.getProperty(h,"x"),G?(q.x=parseFloat(se.x),q.y=parseFloat(se.y)):K?q.x=q.rotation=parseFloat(se.rotation):f?(q.y=f.top(),q.x=f.left()):(q.y=parseFloat(h.style.top||(o=La(h))&&o.top)||0,q.x=parseFloat(h.style.left||(o||{}).left)||0),(O||k||_)&&!t&&(q.isDragging||q.isThrowing)&&(_&&(Fe.x=q.x,Fe.y=q.y,(n=_(Fe)).x!==q.x&&(q.x=n.x,P=!0),n.y!==q.y&&(q.y=n.y,P=!0)),O&&(n=O(q.x))!==q.x&&(q.x=n,K&&(q.rotation=n),P=!0),k&&((n=k(q.y))!==q.y&&(q.y=n),P=!0)),P&&vh(!0),e||(q.deltaX=q.x-r,q.deltaY=q.y-i,Oa(q,"throwupdate","onThrowUpdate"))}function xh(a,l,s,n){return null==l&&(l=-ke),null==s&&(s=ke),X(a)?function(e){var t=q.isPressed?1-q.edgeResistance:1;return a.call(q,(s<e?s+(e-s)*t:e<l?l+(e-l)*t:e)*n)*n}:Pe(a)?function(e){for(var t,n,o=a.length,r=0,i=ke;-1<--o;)(n=(t=a[o])-e)<0&&(n=-n),n<i&&l<=t&&t<=s&&(r=o,i=n);return a[r]}:isNaN(a)?function(e){return e}:function(){return a*n}}function zh(){var e,t,n,o;E=!1,f?(f.calibrate(),q.minX=S=-f.maxScrollLeft(),q.minY=L=-f.maxScrollTop(),q.maxX=M=q.maxY=N=0,E=!0):p.bounds&&(e=Pa(p.bounds,h.parentNode),K?(q.minX=S=e.left,q.maxX=M=e.left+e.width,q.minY=L=q.maxY=N=0):Z(p.bounds.maxX)&&Z(p.bounds.maxY)?(t=Pa(h,h.parentNode),q.minX=S=Math.round(Ng(V,"px")+e.left-t.left),q.minY=L=Math.round(Ng(U,"px")+e.top-t.top),q.maxX=M=Math.round(S+(e.width-t.width)),q.maxY=N=Math.round(L+(e.height-t.height))):(e=p.bounds,q.minX=S=e.minX,q.minY=L=e.minY,q.maxX=M=e.maxX,q.maxY=N=e.maxY),M<S&&(q.minX=M,q.maxX=M=S,S=q.minX),N<L&&(q.minY=N,q.maxY=N=L,L=q.minY),K&&(q.minRotation=S,q.maxRotation=M),E=!0),p.liveSnap&&(n=!0===p.liveSnap?p.snap||{}:p.liveSnap,o=Pe(n)||X(n),K?(O=xh(o?n:n.rotation,S,M,1),k=null):n.points?_=function buildPointSnapFunc(s,l,c,d,p,u,h){return u=u&&u<ke?u*u:ke,X(s)?function(e){var t,n,o,r=q.isPressed?1-q.edgeResistance:1,i=e.x,a=e.y;return e.x=i=c<i?c+(i-c)*r:i<l?l+(i-l)*r:i,e.y=a=p<a?p+(a-p)*r:a<d?d+(a-d)*r:a,(t=s.call(q,e))!==e&&(e.x=t.x,e.y=t.y),1!==h&&(e.x*=h,e.y*=h),u<ke&&(n=e.x-i,o=e.y-a,u<n*n+o*o&&(e.x=i,e.y=a)),e}:Pe(s)?function(e){for(var t,n,o,r,i=s.length,a=0,l=ke;-1<--i;)(r=(t=(o=s[i]).x-e.x)*t+(n=o.y-e.y)*n)<l&&(a=i,l=r);return l<=u?s[a]:e}:function(e){return e}}(o?n:n.points,S,M,L,N,n.radius,f?-1:1):($&&(O=xh(o?n:n.x||n.left||n.scrollLeft,S,M,f?-1:1)),j&&(k=xh(o?n:n.y||n.top||n.scrollTop,L,N,f?-1:1))))}function Ah(){q.isThrowing=!1,Oa(q,"throwcomplete","onThrowComplete")}function Bh(){q.isThrowing=!1}function Ch(e,t){var n,o,r,i;e&&Se?(!0===e&&(n=p.snap||p.liveSnap||{},o=Pe(n)||X(n),e={resistance:(p.throwResistance||p.resistance||1e3)/(K?10:1)},K?e.rotation=Sa(q,o?n:n.rotation,M,S,1,t):($&&(e[V]=Sa(q,o?n:n.points||n.x||n.left,M,S,f?-1:1,t||"x"===q.lockedAxis)),j&&(e[U]=Sa(q,o?n:n.points||n.y||n.top,N,L,f?-1:1,t||"y"===q.lockedAxis)),(n.points||Pe(n)&&Y(n[0]))&&(e.linkedProps=V+","+U,e.radius=n.radius))),q.isThrowing=!0,i=isNaN(p.overshootTolerance)?1===p.edgeResistance?0:1-q.edgeResistance+.2:p.overshootTolerance,e.duration||(e.duration={max:Math.max(p.minDuration||0,"maxDuration"in p?p.maxDuration:2),min:isNaN(p.minDuration)?0===i||Y(e)&&1e3<e.resistance?0:.5:p.minDuration,overshoot:i}),q.tween=r=xe.to(f||h,{inertia:e,data:"_draggable",onComplete:Ah,onInterrupt:Bh,onUpdate:p.fastMode?Oa:wh,onUpdateParams:p.fastMode?[q,"onthrowupdate","onThrowUpdate"]:n&&n.radius?[!1,!0]:[]}),p.fastMode||(f&&(f._skip=!0),r.render(1e9,!0,!0),wh(!0,!0),q.endX=q.x,q.endY=q.y,K&&(q.endRotation=q.x),r.play(0),wh(!0,!0),f&&(f._skip=!1))):E&&q.applyBounds()}function Dh(e){var t,n=R;R=getGlobalMatrix(h.parentNode,!0),e&&q.isPressed&&!R.equals(n||new ge)&&(t=n.inverse().apply({x:w,y:b}),R.apply(t,t),w=t.x,b=t.y),R.equals(a)&&(R=null)}function Eh(){var e,t,n,o=1-q.edgeResistance,r=ce?Ea(de):0,i=ce?Da(de):0;G&&(se.x=Ng(V,"px")+"px",se.y=Ng(U,"px")+"px",se.renderTransform()),Dh(!1),We.x=q.pointerX-r,We.y=q.pointerY-i,R&&R.apply(We,We),w=We.x,b=We.y,P&&(he(q.pointerX,q.pointerY),vh(!0)),d=getGlobalMatrix(h),f?(zh(),D=f.top(),T=f.left()):(pe()?(wh(!0,!0),zh()):q.applyBounds(),K?(e=h.ownerSVGElement?[se.xOrigin-h.getBBox().x,se.yOrigin-h.getBBox().y]:(La(h)[Ce]||"0 0").split(" "),C=q.rotationOrigin=getGlobalMatrix(h).apply({x:parseFloat(e[0])||0,y:parseFloat(e[1])||0}),wh(!0,!0),t=q.pointerX-C.x-r,n=C.y-q.pointerY+i,T=q.x,D=q.y=Math.atan2(n,t)*Oe):(D=Ng(U,"px"),T=Ng(V,"px"))),E&&o&&(M<T?T=M+(T-M)/o:T<S&&(T=S-(S-T)/o),K||(N<D?D=N+(D-N)/o:D<L&&(D=L-(L-D)/o))),q.startX=T=ba(T),q.startY=D=ba(D)}function Gh(){!we.parentNode||pe()||q.isDragging||we.parentNode.removeChild(we)}function Hh(e,t){var n;if(!u||q.isPressed||!e||!("mousedown"!==e.type&&"pointerdown"!==e.type||t)&&_e()-le<30&&Ee[q.pointerEvent.type])z&&e&&u&&za(e);else{if(A=pe(),W=!1,q.pointerEvent=e,Ee[e.type]?(v=~e.type.indexOf("touch")?e.currentTarget||e.target:de,xa(v,"touchend",fe),xa(v,"touchmove",ue),xa(v,"touchcancel",fe),xa(de,"touchstart",Ca)):(v=null,xa(de,"mousemove",ue)),I=null,Le&&v||(xa(de,"mouseup",fe),e&&e.target&&xa(e.target,"mouseup",fe)),y=ae.call(q,e.target)&&!1===p.dragClickables&&!t)return xa(e.target,"change",fe),Oa(q,"pressInit","onPressInit"),Oa(q,"press","onPress"),Ua(Q,!0),void(z=!1);if(B=!(!v||$==j||!1===q.vars.allowNativeTouchScrolling||q.vars.allowContextMenu&&e&&(e.ctrlKey||2<e.which))&&($?"y":"x"),(z=!B&&!q.allowEventDefault)&&(za(e),xa(me,"touchforcechange",za)),e.changedTouches?(e=x=e.changedTouches[0],m=e.identifier):e.pointerId?m=e.pointerId:x=m=null,Ye++,function _addToRenderQueue(e){Re.push(e),1===Re.length&&xe.ticker.add(sa)}(vh),b=q.pointerY=e.pageY,w=q.pointerX=e.pageX,Oa(q,"pressInit","onPressInit"),(B||q.autoScroll)&&Ja(h.parentNode),!h.parentNode||!q.autoScroll||f||K||!h.parentNode._gsMaxScrollX||we.parentNode||h.getBBox||(we.style.width=h.parentNode.scrollWidth+"px",h.parentNode.appendChild(we)),Eh(),q.tween&&q.tween.kill(),q.isThrowing=!1,xe.killTweensOf(f||h,o,!0),f&&xe.killTweensOf(h,{scrollTo:1},!0),q.tween=q.lockedAxis=null,!p.zIndexBoost&&(K||f||!1===p.zIndexBoost)||(h.style.zIndex=Draggable.zIndex++),q.isPressed=!0,g=!(!p.onDrag&&!q._listeners.drag),s=!(!p.onMove&&!q._listeners.move),!1!==p.cursor||p.activeCursor)for(n=Q.length;-1<--n;)xe.set(Q[n],{cursor:p.activeCursor||p.cursor||("grab"===Ne?"grabbing":Ne)});Oa(q,"press","onPress")}}function Lh(e){if(e&&q.isDragging&&!f){var t=e.target||h.parentNode,n=t.scrollLeft-t._gsScrollX,o=t.scrollTop-t._gsScrollY;(n||o)&&(R?(w-=n*R.a+o*R.c,b-=o*R.d+n*R.b):(w-=n,b-=o),t._gsScrollX+=n,t._gsScrollY+=o,he(q.pointerX,q.pointerY))}}function Mh(e){var t=_e(),n=t-le<100,o=t-ee<50,r=n&&H===le,i=q.pointerEvent&&q.pointerEvent.defaultPrevented,a=n&&c===le,l=e.isTrusted||null==e.isTrusted&&n&&r;if((r||o&&!1!==q.vars.suppressClickOnDrag)&&e.stopImmediatePropagation&&e.stopImmediatePropagation(),n&&(!q.pointerEvent||!q.pointerEvent.defaultPrevented)&&(!r||l&&!a))return l&&r&&(c=le),void(H=le);(q.isPressed||o||n)&&(l&&e.detail&&n&&!i||za(e)),n||o||W||(e&&e.target&&(q.pointerEvent=e),Oa(q,"click","onClick"))}function Nh(e){return R?{x:e.x*R.a+e.y*R.c+R.e,y:e.x*R.b+e.y*R.d+R.f}:{x:e.x,y:e.y}}var u,f,w,b,T,D,E,g,s,M,S,N,L,x,m,C,P,t,O,k,_,y,v,R,A,B,I,F,H,c,z,d,W,n=(p.type||"x,y").toLowerCase(),G=~n.indexOf("x")||~n.indexOf("y"),K=-1!==n.indexOf("rotation"),V=K?"rotation":G?"x":"left",U=G?"y":"top",$=!(!~n.indexOf("x")&&!~n.indexOf("left")&&"scroll"!==n),j=!(!~n.indexOf("y")&&!~n.indexOf("top")&&"scroll"!==n),J=p.minimumMovement||2,q=function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e),Q=Te(p.trigger||p.handle||h),o={},ee=0,te=!1,ne=p.autoScrollMarginTop||40,oe=p.autoScrollMarginRight||40,re=p.autoScrollMarginBottom||40,ie=p.autoScrollMarginLeft||40,ae=p.clickableTest||Ta,le=0,se=h._gsap||xe.core.getCache(h),ce=function _isFixed(e){return"fixed"===La(e).position||((e=e.parentNode)&&1===e.nodeType?_isFixed(e):void 0)}(h),de=h.ownerDocument||ye,pe=function isTweening(){return q.tween&&q.tween.isActive()},ue=function onMove(e){var t,n,o,r,i,a,l=e;if(u&&!Me&&q.isPressed&&e){if(t=(q.pointerEvent=e).changedTouches){if((e=t[0])!==x&&e.identifier!==m){for(r=t.length;-1<--r&&(e=t[r]).identifier!==m&&e.target!==h;);if(r<0)return}}else if(e.pointerId&&m&&e.pointerId!==m)return;v&&B&&!I&&(We.x=e.pageX-(ce?Ea(de):0),We.y=e.pageY-(ce?Da(de):0),R&&R.apply(We,We),n=We.x,o=We.y,((i=Math.abs(n-w))!==(a=Math.abs(o-b))&&(J<i||J<a)||Xe&&B===I)&&(I=a<i&&$?"x":"y",B&&I!==B&&xa(me,"touchforcechange",za),!1!==q.vars.lockAxisOnTouchScroll&&$&&j&&(q.lockedAxis="x"===I?"y":"x",X(q.vars.onLockAxis)&&q.vars.onLockAxis.call(q,l)),Xe&&B===I))?fe(l):(z=q.allowEventDefault||B&&(!I||B===I)||!1===l.cancelable?z&&!1:(za(l),!0),q.autoScroll&&(te=!0),he(e.pageX,e.pageY,s))}else z&&e&&u&&za(e)},he=function setPointerPosition(e,t,n){var o,r,i,a,l,s,c=1-q.dragResistance,d=1-q.edgeResistance,p=q.pointerX,u=q.pointerY,h=D,f=q.x,g=q.y,x=q.endX,m=q.endY,y=q.endRotation,v=P;q.pointerX=e,q.pointerY=t,ce&&(e-=Ea(de),t-=Da(de)),K?(a=Math.atan2(C.y-t,e-C.x)*Oe,180<(l=q.y-a)?(D-=360,q.y=a):l<-180&&(D+=360,q.y=a),i=q.x!==T||Math.abs(D-a)>J?(q.y=a,T+(D-a)*c):T):(R&&(s=e*R.a+t*R.c+R.e,t=e*R.b+t*R.d+R.f,e=s),(r=t-b)<J&&-J<r&&(r=0),(o=e-w)<J&&-J<o&&(o=0),(q.lockAxis||q.lockedAxis)&&(o||r)&&((s=q.lockedAxis)||(q.lockedAxis=s=$&&Math.abs(o)>Math.abs(r)?"y":j?"x":null,s&&X(q.vars.onLockAxis)&&q.vars.onLockAxis.call(q,q.pointerEvent)),"y"===s?r=0:"x"===s&&(o=0)),i=ba(T+o*c),a=ba(D+r*c)),(O||k||_)&&(q.x!==i||q.y!==a&&!K)&&(_&&(Fe.x=i,Fe.y=a,s=_(Fe),i=ba(s.x),a=ba(s.y)),O&&(i=ba(O(i))),k&&(a=ba(k(a)))),E&&(M<i?i=M+Math.round((i-M)*d):i<S&&(i=S+Math.round((i-S)*d)),K||(N<a?a=Math.round(N+(a-N)*d):a<L&&(a=Math.round(L+(a-L)*d)))),q.x===i&&(q.y===a||K)||(K?(q.endRotation=q.x=q.endX=i,P=!0):(j&&(q.y=q.endY=a,P=!0),$&&(q.x=q.endX=i,P=!0)),n&&!1===Oa(q,"move","onMove")?(q.pointerX=p,q.pointerY=u,D=h,q.x=f,q.y=g,q.endX=x,q.endY=m,q.endRotation=y,P=v):!q.isDragging&&q.isPressed&&(q.isDragging=W=!0,Oa(q,"dragstart","onDragStart")))},fe=function onRelease(e,t){if(u&&q.isPressed&&(!e||null==m||t||!(e.pointerId&&e.pointerId!==m&&e.target!==h||e.changedTouches&&!function _hasTouchID(e,t){for(var n=e.length;n--;)if(e[n].identifier===t)return!0}(e.changedTouches,m)))){q.isPressed=!1;var n,o,r,i,a,l=e,s=q.isDragging,c=q.vars.allowContextMenu&&e&&(e.ctrlKey||2<e.which),d=xe.delayedCall(.001,Gh);if(v?(ya(v,"touchend",onRelease),ya(v,"touchmove",ue),ya(v,"touchcancel",onRelease),ya(de,"touchstart",Ca)):ya(de,"mousemove",ue),ya(me,"touchforcechange",za),Le&&v||(ya(de,"mouseup",onRelease),e&&e.target&&ya(e.target,"mouseup",onRelease)),P=!1,s&&(ee=Ie=_e(),q.isDragging=!1),va(vh),y&&!c)return e&&(ya(e.target,"change",onRelease),q.pointerEvent=l),Ua(Q,!1),Oa(q,"release","onRelease"),Oa(q,"click","onClick"),void(y=!1);for(o=Q.length;-1<--o;)Ka(Q[o],"cursor",p.cursor||(!1!==p.cursor?Ne:null));if(Ye--,e){if((n=e.changedTouches)&&(e=n[0])!==x&&e.identifier!==m){for(o=n.length;-1<--o&&(e=n[o]).identifier!==m&&e.target!==h;);if(o<0&&!t)return}q.pointerEvent=l,q.pointerX=e.pageX,q.pointerY=e.pageY}return c&&l?(za(l),z=!0,Oa(q,"release","onRelease")):l&&!s?(z=!1,A&&(p.snap||p.bounds)&&Ch(p.inertia||p.throwProps),Oa(q,"release","onRelease"),Xe&&"touchmove"===l.type||-1!==l.type.indexOf("cancel")||(Oa(q,"click","onClick"),_e()-le<300&&Oa(q,"doubleclick","onDoubleClick"),i=l.target||h,le=_e(),a=function syntheticClick(){le===H||!q.enabled()||q.isPressed||l.defaultPrevented||(i.click?i.click():de.createEvent&&((r=de.createEvent("MouseEvents")).initMouseEvent("click",!0,!0,me,1,q.pointerEvent.screenX,q.pointerEvent.screenY,q.pointerX,q.pointerY,!1,!1,!1,!1,0,null),i.dispatchEvent(r)))},Xe||l.defaultPrevented||xe.delayedCall(.05,a))):(Ch(p.inertia||p.throwProps),q.allowEventDefault||!l||!1===p.dragClickables&&ae.call(q,l.target)||!s||B&&(!I||B!==I)||!1===l.cancelable?z=!1:(z=!0,za(l)),Oa(q,"release","onRelease")),pe()&&d.duration(q.tween.duration()),s&&Oa(q,"dragend","onDragEnd"),!0}z&&e&&u&&za(e)};return(t=Draggable.get(h))&&t.kill(),e.startDrag=function(e,t){var n,o,r,i;Hh(e||q.pointerEvent,!0),t&&!q.hitTest(e||q.pointerEvent)&&(n=Na(e||q.pointerEvent),o=Na(h),r=Nh({x:n.left+n.width/2,y:n.top+n.height/2}),i=Nh({x:o.left+o.width/2,y:o.top+o.height/2}),w-=r.x-i.x,b-=r.y-i.y),q.isDragging||(q.isDragging=W=!0,Oa(q,"dragstart","onDragStart"))},e.drag=ue,e.endDrag=function(e){return fe(e||q.pointerEvent,!0)},e.timeSinceDrag=function(){return q.isDragging?0:(_e()-ee)/1e3},e.timeSinceClick=function(){return(_e()-le)/1e3},e.hitTest=function(e,t){return Draggable.hitTest(q.target,e,t)},e.getDirection=function(e,t){var n,o,r,i,a,l,s="velocity"===e&&Se?e:Y(e)&&!K?"element":"start";return"element"===s&&(a=Na(q.target),l=Na(e)),n="start"===s?q.x-T:"velocity"===s?Se.getVelocity(h,V):a.left+a.width/2-(l.left+l.width/2),K?n<0?"counter-clockwise":"clockwise":(t=t||2,o="start"===s?q.y-D:"velocity"===s?Se.getVelocity(h,U):a.top+a.height/2-(l.top+l.height/2),i=(r=Math.abs(n/o))<1/t?"":n<0?"left":"right",r<t&&(""!==i&&(i+="-"),i+=o<0?"up":"down"),i)},e.applyBounds=function(e,t){var n,o,r,i,a,l;if(e&&p.bounds!==e)return p.bounds=e,q.update(!0,t);if(wh(!0),zh(),E&&!pe()){if(n=q.x,o=q.y,M<n?n=M:n<S&&(n=S),N<o?o=N:o<L&&(o=L),(q.x!==n||q.y!==o)&&(r=!0,q.x=q.endX=n,K?q.endRotation=n:q.y=q.endY=o,vh(P=!0),q.autoScroll&&!q.isDragging))for(Ja(h.parentNode),i=h,He.scrollTop=null!=me.pageYOffset?me.pageYOffset:null!=de.documentElement.scrollTop?de.documentElement.scrollTop:de.body.scrollTop,He.scrollLeft=null!=me.pageXOffset?me.pageXOffset:null!=de.documentElement.scrollLeft?de.documentElement.scrollLeft:de.body.scrollLeft;i&&!l;)a=(l=ze(i.parentNode))?He:i.parentNode,j&&a.scrollTop>a._gsMaxScrollY&&(a.scrollTop=a._gsMaxScrollY),$&&a.scrollLeft>a._gsMaxScrollX&&(a.scrollLeft=a._gsMaxScrollX),i=a;q.isThrowing&&(r||q.endX>M||q.endX<S||q.endY>N||q.endY<L)&&Ch(p.inertia||p.throwProps,r)}return q},e.update=function(e,t,n){if(t&&q.isPressed){var o=getGlobalMatrix(h),r=d.apply({x:q.x-T,y:q.y-D}),i=getGlobalMatrix(h.parentNode,!0);i.apply({x:o.e-r.x,y:o.f-r.y},r),q.x-=r.x-i.e,q.y-=r.y-i.f,vh(!0),Eh()}var a=q.x,l=q.y;return Dh(!t),e?q.applyBounds():(P&&n&&vh(!0),wh(!0)),t&&(he(q.pointerX,q.pointerY),P&&vh(!0)),q.isPressed&&!t&&($&&.01<Math.abs(a-q.x)||j&&.01<Math.abs(l-q.y)&&!K)&&Eh(),q.autoScroll&&(Ja(h.parentNode,q.isDragging),te=q.isDragging,vh(!0),Ga(h,Lh),Fa(h,Lh)),q},e.enable=function(e){var t,n,o,r={lazy:!0};if(!1!==p.cursor&&(r.cursor=p.cursor||Ne),xe.utils.checkPrefix("touchCallout")&&(r.touchCallout="none"),"soft"!==e){for(ra(Q,$==j?"none":p.allowNativeTouchScrolling&&h.scrollHeight===h.clientHeight==(h.scrollWidth===h.clientHeight)||p.allowEventDefault?"manipulation":$?"pan-y":"pan-x"),n=Q.length;-1<--n;)o=Q[n],Le||xa(o,"mousedown",Hh),xa(o,"touchstart",Hh),xa(o,"click",Mh,!0),xe.set(o,r),o.getBBox&&o.ownerSVGElement&&$!=j&&xe.set(o.ownerSVGElement,{touchAction:p.allowNativeTouchScrolling||p.allowEventDefault?"manipulation":$?"pan-y":"pan-x"}),p.allowContextMenu||xa(o,"contextmenu",uh);Ua(Q,!1)}return Fa(h,Lh),u=!0,Se&&"soft"!==e&&Se.track(f||h,G?"x,y":K?"rotation":"top,left"),h._gsDragID=t="d"+Be++,Ae[t]=q,f&&(f.enable(),f.element._gsDragID=t),(p.bounds||K)&&Eh(),p.bounds&&q.applyBounds(),q},e.disable=function(e){for(var t,n=q.isDragging,o=Q.length;-1<--o;)Ka(Q[o],"cursor",null);if("soft"!==e){for(ra(Q,null),o=Q.length;-1<--o;)t=Q[o],Ka(t,"touchCallout",null),ya(t,"mousedown",Hh),ya(t,"touchstart",Hh),ya(t,"click",Mh),ya(t,"contextmenu",uh);Ua(Q,!0),v&&(ya(v,"touchcancel",fe),ya(v,"touchend",fe),ya(v,"touchmove",ue)),ya(de,"mouseup",fe),ya(de,"mousemove",ue)}return Ga(h,Lh),u=!1,Se&&"soft"!==e&&Se.untrack(f||h,G?"x,y":K?"rotation":"top,left"),f&&f.disable(),va(vh),q.isDragging=q.isPressed=y=!1,n&&Oa(q,"dragend","onDragEnd"),q},e.enabled=function(e,t){return arguments.length?e?q.enable(t):q.disable(t):u},e.kill=function(){return q.isThrowing=!1,q.tween&&q.tween.kill(),q.disable(),xe.set(Q,{clearProps:"userSelect"}),delete Ae[h._gsDragID],q},~n.indexOf("scroll")&&(f=e.scrollProxy=new Ya(h,function _extend(e,t){for(var n in t)n in e||(e[n]=t[n]);return e}({onKill:function onKill(){q.isPressed&&fe(null)}},p)),h.style.overflowY=j&&!De?"auto":"hidden",h.style.overflowX=$&&!De?"auto":"hidden",h=f.content),K?o.rotation=1:($&&(o[V]=1),j&&(o[U]=1)),se.force3D=!("force3D"in p)||p.force3D,e.enable(),e}!function _setDefaults(e,t){for(var n in t)n in e||(e[n]=t[n])}(I.prototype,{pointerX:0,pointerY:0,startX:0,startY:0,deltaX:0,deltaY:0,isDragging:!1,isPressed:!1}),I.zIndex=1e3,I.version="3.11.3",W()&&xe.registerPlugin(I),e.Draggable=I,e.default=I;if (typeof(window)==="undefined"||window!==e){Object.defineProperty(e,"__esModule",{value:!0})} else {delete e.default}});

